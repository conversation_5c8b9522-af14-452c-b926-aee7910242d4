#!/bin/bash

# ===================================================================
# SCRIPT DE INSTALAÇÃO EVOLUTION API
# ===================================================================

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Funções de log
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Banner
echo -e "${BLUE}"
echo "====================================================================="
echo "         INSTALAÇÃO EVOLUTION API - FRAMEWORK MODULAR"
echo "====================================================================="
echo -e "${NC}"

# Verificar pré-requisitos
log_info "Verificando pré-requisitos..."

if ! command -v docker &> /dev/null; then
    log_error "Docker não está instalado!"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    log_error "Docker Compose não está instalado!"
    exit 1
fi

if ! docker info &> /dev/null; then
    log_error "Docker não está rodando!"
    exit 1
fi

log_success "Pré-requisitos verificados!"

# Criar diretório de trabalho
WORK_DIR="evolution-api"
log_info "Criando diretório de trabalho: $WORK_DIR"

if [ -d "$WORK_DIR" ]; then
    log_warning "Diretório $WORK_DIR já existe!"
    read -p "Deseja continuar? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "Instalação cancelada."
        exit 0
    fi
else
    mkdir -p "$WORK_DIR"
fi

cd "$WORK_DIR"

# Criar docker-compose.yml
log_info "Criando configurações..."

cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  postgres-evolution:
    image: postgres:15-alpine
    container_name: evolution-postgres
    environment:
      POSTGRES_DB: ${EVOLUTION_POSTGRES_DB:-evolution}
      POSTGRES_USER: ${EVOLUTION_POSTGRES_USER:-evolution_user}
      POSTGRES_PASSWORD: ${EVOLUTION_POSTGRES_PASSWORD:-evolution123}
    volumes:
      - postgres_evolution_data:/var/lib/postgresql/data
    ports:
      - "${EVOLUTION_POSTGRES_PORT:-5433}:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${EVOLUTION_POSTGRES_USER:-evolution_user} -d ${EVOLUTION_POSTGRES_DB:-evolution}"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - evolution-network

  redis-evolution:
    image: redis:7-alpine
    container_name: evolution-redis
    command: redis-server --appendonly yes --maxmemory ${EVOLUTION_REDIS_MAXMEMORY:-512mb}
    volumes:
      - redis_evolution_data:/data
    ports:
      - "${EVOLUTION_REDIS_PORT:-6380}:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - evolution-network

  evolution-api:
    image: atendai/evolution-api:v2.2.0
    container_name: evolution-api
    ports:
      - "${EVOLUTION_PORT:-8001}:8080"
    environment:
      DATABASE_PROVIDER: postgresql
      DATABASE_CONNECTION_URI: postgresql://${EVOLUTION_POSTGRES_USER:-evolution_user}:${EVOLUTION_POSTGRES_PASSWORD:-evolution123}@postgres-evolution:5432/${EVOLUTION_POSTGRES_DB:-evolution}?schema=public
      REDIS_URI: redis://redis-evolution:6379
      API_KEY: ${EVOLUTION_API_KEY:-evolution_api_key_123}
      JWT_SECRET: ${EVOLUTION_JWT_SECRET:-evolution_jwt_secret_456}
      SERVER_TYPE: http
      SERVER_PORT: 8080
      LOG_LEVEL: ${LOG_LEVEL:-info}
      LOG_COLOR: true
      DEL_INSTANCE: false
      QRCODE_LIMIT: 10
      AUTHENTICATION_TYPE: apikey
      AUTHENTICATION_API_KEY: ${EVOLUTION_API_KEY:-evolution_api_key_123}
      TZ: ${TIMEZONE:-America/Sao_Paulo}
    volumes:
      - evolution_instances:/evolution/instances
      - evolution_store:/evolution/store
    depends_on:
      postgres-evolution:
        condition: service_healthy
      redis-evolution:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - evolution-network

volumes:
  postgres_evolution_data:
  redis_evolution_data:
  evolution_instances:
  evolution_store:

networks:
  evolution-network:
    driver: bridge
EOF

# Criar arquivo .env
cat > .env << 'EOF'
# EVOLUTION API - CONFIGURAÇÕES
EVOLUTION_POSTGRES_DB=evolution
EVOLUTION_POSTGRES_USER=evolution_user
EVOLUTION_POSTGRES_PASSWORD=evolution123
EVOLUTION_POSTGRES_PORT=5433

EVOLUTION_REDIS_PORT=6380
EVOLUTION_REDIS_MAXMEMORY=512mb

EVOLUTION_PORT=8001
EVOLUTION_API_KEY=evolution_api_key_123
EVOLUTION_JWT_SECRET=evolution_jwt_secret_456

LOG_LEVEL=info
TIMEZONE=America/Sao_Paulo
EOF

log_success "Arquivos de configuração criados!"

# Iniciar serviços
log_info "Iniciando serviços..."
docker-compose up -d

# Aguardar inicialização
log_info "Aguardando inicialização dos serviços..."
sleep 45

# Verificar status
log_info "Verificando status dos serviços..."
docker-compose ps

# Verificar saúde dos serviços
log_info "Verificando saúde dos serviços..."

if docker-compose exec -T postgres-evolution pg_isready -U evolution_user -d evolution > /dev/null 2>&1; then
    log_success "PostgreSQL Evolution está funcionando!"
else
    log_error "PostgreSQL Evolution não está respondendo!"
fi

if docker-compose exec -T redis-evolution redis-cli ping > /dev/null 2>&1; then
    log_success "Redis Evolution está funcionando!"
else
    log_error "Redis Evolution não está respondendo!"
fi

# Verificar Evolution API
log_info "Verificando Evolution API..."
sleep 15

if curl -s http://localhost:8001 > /dev/null; then
    log_success "Evolution API está funcionando!"
else
    log_warning "Evolution API pode ainda estar inicializando..."
    log_info "Aguardando mais 30 segundos..."
    sleep 30
    if curl -s http://localhost:8001 > /dev/null; then
        log_success "Evolution API está funcionando!"
    else
        log_error "Evolution API não está respondendo!"
        log_info "Verifique os logs: docker-compose logs evolution-api"
    fi
fi

# Informações finais
echo -e "${GREEN}"
echo "====================================================================="
echo "                    INSTALAÇÃO CONCLUÍDA!"
echo "====================================================================="
echo -e "${NC}"

log_info "Acesso à Evolution API:"
echo "  URL: http://localhost:8001"
echo "  API Key: evolution_api_key_123"
echo "  Manager: http://localhost:8001"
echo ""

log_info "Bancos de dados dedicados:"
echo "  PostgreSQL: localhost:5433"
echo "  Redis: localhost:6380"
echo ""

log_info "Comandos úteis:"
echo "  Ver logs: docker-compose logs -f evolution-api"
echo "  Parar: docker-compose down"
echo "  Reiniciar: docker-compose restart"
echo "  Status: docker-compose ps"
echo ""

log_info "Próximos passos:"
echo "  1. Acesse http://localhost:8001"
echo "  2. Configure uma nova instância WhatsApp"
echo "  3. Escaneie o QR Code"
echo "  4. Teste o envio de mensagens"
echo ""

log_warning "IMPORTANTE: Altere a API_KEY em produção!"

log_success "Evolution API instalada com sucesso!"
