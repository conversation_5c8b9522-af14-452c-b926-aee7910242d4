# N8N Production Environment

Este é um ambiente de produção completo para n8n com PostgreSQL e Redis.

## 🚀 <PERSON><PERSON><PERSON>

### Pré-requisitos
- Docker Desktop instalado e rodando
- Portas 5678, 5432 e 6379 disponíveis

### Iniciar o ambiente
```bash
# Opção 1: Script automatizado (Windows)
start.bat

# Opção 2: Docker Compose manual
docker-compose up -d
```

### Parar o ambiente
```bash
# Opção 1: Script automatizado (Windows)
stop.bat

# Opção 2: Docker Compose manual
docker-compose down
```

## 🔧 Configuração

### Serviços Incluídos
- **n8n**: Interface principal na porta 5678
- **PostgreSQL**: Banco de dados na porta 5432
- **Redis**: Cache e queue na porta 6379

### Variáveis de Ambiente
Todas as configurações estão no arquivo `.env`:

```env
# N8N Configuration
N8N_HOST=localhost
N8N_PORT=5678
N8N_PROTOCOL=http
WEBHOOK_URL=http://localhost:5678

# Database
DB_TYPE=postgresdb
DB_POSTGRESDB_HOST=postgres
DB_POSTGRESDB_PORT=5432
DB_POSTGRESDB_DATABASE=n8n
DB_POSTGRESDB_USER=n8n
DB_POSTGRESDB_PASSWORD=n8n_password

# Redis
QUEUE_BULL_REDIS_HOST=redis
QUEUE_BULL_REDIS_PORT=6379

# Security
N8N_ENCRYPTION_KEY=your-encryption-key-here
```

### Volumes Persistentes
- `postgres_data`: Dados do PostgreSQL
- `redis_data`: Dados do Redis
- `n8n_data`: Workflows e configurações do n8n

## 🌐 Acesso

### Interface Web
- URL: http://localhost:5678
- Primeiro acesso: Configure usuário administrador

### Banco de Dados
- Host: localhost
- Porta: 5432
- Database: n8n
- Usuário: n8n
- Senha: n8n_password

### Redis
- Host: localhost
- Porta: 6379

## 📊 Monitoramento

### Ver logs
```bash
# Todos os serviços
docker-compose logs -f

# Serviço específico
docker-compose logs -f n8n
docker-compose logs -f postgres
docker-compose logs -f redis
```

### Status dos serviços
```bash
docker-compose ps
```

### Health checks
```bash
# PostgreSQL
docker-compose exec postgres pg_isready -U n8n

# Redis
docker-compose exec redis redis-cli ping

# N8N
curl http://localhost:5678/healthz
```

## 🔒 Segurança

### Configurações de Produção
1. **Altere as senhas padrão** no arquivo `.env`
2. **Configure HTTPS** com proxy reverso (nginx/traefik)
3. **Restrinja acesso** às portas do banco de dados
4. **Configure backups** regulares dos volumes

### Backup
```bash
# Backup dos dados
docker-compose exec postgres pg_dump -U n8n n8n > backup_$(date +%Y%m%d).sql

# Backup dos volumes
docker run --rm -v n8n-production_postgres_data:/data -v $(pwd):/backup alpine tar czf /backup/postgres_backup.tar.gz /data
```

## 🛠️ Troubleshooting

### Problemas Comuns

1. **Porta já em uso**
   ```bash
   # Verificar processos usando as portas
   netstat -ano | findstr :5678
   netstat -ano | findstr :5432
   netstat -ano | findstr :6379
   ```

2. **Containers não iniciam**
   ```bash
   # Verificar logs
   docker-compose logs
   
   # Recriar containers
   docker-compose down
   docker-compose up -d --force-recreate
   ```

3. **Problemas de permissão**
   ```bash
   # Resetar volumes
   docker-compose down -v
   docker-compose up -d
   ```

4. **N8N não conecta ao banco**
   - Verifique se o PostgreSQL está healthy
   - Confirme as credenciais no `.env`
   - Aguarde o health check do PostgreSQL

### Comandos Úteis
```bash
# Reiniciar serviço específico
docker-compose restart n8n

# Acessar container
docker-compose exec n8n sh
docker-compose exec postgres psql -U n8n

# Limpar tudo (CUIDADO: Remove todos os dados)
docker-compose down -v
docker system prune -a
```

## 📝 Notas

- Este ambiente é configurado para desenvolvimento/produção local
- Para produção real, considere usar orquestração (Kubernetes, Docker Swarm)
- Mantenha backups regulares dos dados importantes
- Monitore o uso de recursos (CPU, memória, disco)

## 🆘 Suporte

Se encontrar problemas:
1. Verifique os logs: `docker-compose logs`
2. Confirme que o Docker está rodando
3. Verifique se as portas estão disponíveis
4. Consulte a documentação oficial do n8n
