# 📚 **DOCUMENTAÇÃO TÉCNICA COMPLETA - FRAMEWORK N8N + EVOLUTION API**

## 📋 **ÍNDICE**

1. [Visão Geral Técnica](#visão-geral-técnica)
2. [Arquitetura dos Módulos](#arquitetura-dos-módulos)
3. [Configurações Detalhadas](#configurações-detalhadas)
4. [Troubleshooting Avançado](#troubleshooting-avançado)
5. [Monitoramento e Logs](#monitoramento-e-logs)
6. [Backup e Restore](#backup-e-restore)
7. [Segurança](#segurança)
8. [Performance e Otimização](#performance-e-otimização)

---

## 🏗️ **VISÃO GERAL TÉCNICA**

### **📊 Matriz de Compatibilidade:**

| Componente | Versão | Compatibilidade | Recursos Mínimos |
|------------|--------|-----------------|-------------------|
| Docker | 20.10+ | ✅ Testado | - |
| Docker Compose | 2.0+ | ✅ Testado | - |
| N8N | latest | ✅ Oficial | 1GB RAM |
| PostgreSQL | 15-16 | ✅ Testado | 512MB RAM |
| Redis | 7.x | ✅ Testado | 256MB RAM |
| Evolution API | v2.2.0 | ✅ Testado | 1GB RAM |

### **🌐 Topologia de Rede:**

```
┌─────────────────────────────────────────────────────────────┐
│                    FRAMEWORK INTEGRADO                      │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │   N8N NETWORK   │    │ EVOLUTION NET   │                │
│  │  **********/16  │    │ **********/16  │                │
│  │                 │    │                 │                │
│  │ ┌─────────────┐ │    │ ┌─────────────┐ │                │
│  │ │     N8N     │ │    │ │ Evolution   │ │                │
│  │ │   :5678     │ │    │ │    :8001    │ │                │
│  │ └─────────────┘ │    │ └─────────────┘ │                │
│  │                 │    │                 │                │
│  │ ┌─────────────┐ │    │ ┌─────────────┐ │                │
│  │ │ PostgreSQL  │ │    │ │ PostgreSQL  │ │                │
│  │ │   :5432     │ │    │ │   :5433     │ │                │
│  │ └─────────────┘ │    │ └─────────────┘ │                │
│  │                 │    │                 │                │
│  │ ┌─────────────┐ │    │ ┌─────────────┐ │                │
│  │ │    Redis    │ │    │ │    Redis    │ │                │
│  │ │   :6379     │ │    │ │   :6380     │ │                │
│  │ └─────────────┘ │    │ └─────────────┘ │                │
│  └─────────────────┘    └─────────────────┘                │
└─────────────────────────────────────────────────────────────┘
```

---

## 🔧 **ARQUITETURA DOS MÓDULOS**

### **📦 Módulo 1: N8N Básico**

#### **Componentes:**
```yaml
Services:
  - n8n-app-basic (N8N Application)
  - n8n-postgres-basic (Database)
  - n8n-redis-basic (Cache/Queue)

Networks:
  - n8n-network (**********/16)

Volumes:
  - postgres_data (Database persistence)
  - redis_data (Cache persistence)
  - n8n_data (N8N workflows/credentials)
```

#### **Fluxo de Dados:**
```
User Request → N8N App → PostgreSQL (workflows/executions)
                    ↓
              Redis (queue/cache)
```

#### **Portas Expostas:**
- **5678**: N8N Web Interface
- **5432**: PostgreSQL (opcional)
- **6379**: Redis (opcional)

#### **Variáveis de Ambiente Críticas:**
```env
DB_TYPE=postgresdb
DB_POSTGRESDB_HOST=postgres
QUEUE_BULL_REDIS_HOST=redis
N8N_BASIC_AUTH_ACTIVE=true
```

### **📦 Módulo 2: Evolution API**

#### **Componentes:**
```yaml
Services:
  - evolution-api (WhatsApp API)
  - evolution-postgres (Dedicated Database)
  - evolution-redis (Dedicated Cache)

Networks:
  - evolution-network (**********/16)

Volumes:
  - postgres_evolution_data (Database persistence)
  - redis_evolution_data (Cache persistence)
  - evolution_instances (WhatsApp instances)
  - evolution_store (Message store)
```

#### **Fluxo de Dados:**
```
WhatsApp ↔ Evolution API ↔ PostgreSQL (instances/messages)
                      ↓
                Redis (sessions/cache)
```

#### **Portas Expostas:**
- **8001**: Evolution API Manager
- **5433**: PostgreSQL Evolution (opcional)
- **6380**: Redis Evolution (opcional)

#### **Variáveis de Ambiente Críticas:**
```env
DATABASE_PROVIDER=postgresql
DATABASE_CONNECTION_URI=postgresql://...
REDIS_URI=redis://redis-evolution:6379
API_KEY=evolution_api_key_123
```

---

## ⚙️ **CONFIGURAÇÕES DETALHADAS**

### **🗄️ PostgreSQL - Configurações Otimizadas**

#### **N8N PostgreSQL:**
```sql
-- Configurações de performance
shared_buffers = 256MB
effective_cache_size = 1GB
maintenance_work_mem = 64MB
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100
random_page_cost = 1.1
effective_io_concurrency = 200
work_mem = 4MB
min_wal_size = 1GB
max_wal_size = 4GB
```

#### **Evolution PostgreSQL:**
```sql
-- Configurações específicas para Evolution
shared_buffers = 128MB
effective_cache_size = 512MB
maintenance_work_mem = 32MB
work_mem = 2MB
max_connections = 100
```

### **🔄 Redis - Configurações Otimizadas**

#### **N8N Redis:**
```conf
# Configurações de memória
maxmemory 256mb
maxmemory-policy allkeys-lru

# Persistência
appendonly yes
appendfsync everysec

# Performance
tcp-keepalive 300
timeout 0
```

#### **Evolution Redis:**
```conf
# Configurações de memória
maxmemory 512mb
maxmemory-policy allkeys-lru

# Persistência
appendonly yes
appendfsync everysec

# Configurações específicas
save 900 1
save 300 10
save 60 10000
```

### **🔐 Configurações de Segurança**

#### **N8N Security Headers:**
```env
N8N_SECURE_COOKIE=true
N8N_COOKIE_SAME_SITE_POLICY=strict
N8N_BASIC_AUTH_ACTIVE=true
N8N_JWT_AUTH_ACTIVE=false
```

#### **Evolution API Security:**
```env
AUTHENTICATION_TYPE=apikey
AUTHENTICATION_API_KEY=your_secure_key_here
JWT_SECRET=your_jwt_secret_here
AUTHENTICATION_EXPOSE_IN_FETCH_INSTANCES=false
```

---

## 🔍 **TROUBLESHOOTING AVANÇADO**

### **🚨 Problemas Comuns e Soluções**

#### **1. N8N não conecta ao PostgreSQL**

**Sintomas:**
```
Error: connect ECONNREFUSED 127.0.0.1:5432
```

**Diagnóstico:**
```bash
# Verificar status do PostgreSQL
docker-compose exec postgres pg_isready -U n8n_user -d n8n

# Verificar logs
docker-compose logs postgres

# Verificar rede
docker network ls
docker network inspect n8n-production_n8n-network
```

**Soluções:**
1. Verificar se o serviço postgres está rodando
2. Confirmar variáveis de ambiente
3. Verificar health checks
4. Reiniciar serviços na ordem correta

#### **2. Evolution API não conecta aos bancos dedicados**

**Sintomas:**
```
Error: Connection terminated unexpectedly
```

**Diagnóstico:**
```bash
# Verificar conexão PostgreSQL
docker-compose exec postgres-evolution pg_isready -U evolution_user -d evolution

# Verificar conexão Redis
docker-compose exec redis-evolution redis-cli ping

# Verificar logs da Evolution
docker-compose logs evolution-api
```

**Soluções:**
1. Verificar URI de conexão do banco
2. Confirmar credenciais
3. Verificar isolamento de rede
4. Reiniciar serviços dependentes

#### **3. Problemas de Performance**

**Sintomas:**
- Workflows lentos
- Timeouts frequentes
- Alto uso de CPU/RAM

**Diagnóstico:**
```bash
# Monitorar recursos
docker stats

# Verificar logs de performance
docker-compose logs n8n | grep -i "slow\|timeout\|error"

# Verificar filas Redis
docker-compose exec redis redis-cli info memory
```

**Soluções:**
1. Aumentar recursos dos containers
2. Otimizar workflows
3. Configurar workers adicionais
4. Implementar cache strategies

### **🔧 Scripts de Diagnóstico**

#### **Health Check Completo:**
```bash
#!/bin/bash
# health-check-complete.sh

echo "=== HEALTH CHECK COMPLETO ==="

# Verificar Docker
echo "1. Docker Status:"
docker info > /dev/null 2>&1 && echo "✅ Docker OK" || echo "❌ Docker FAIL"

# Verificar Containers
echo "2. Containers Status:"
docker-compose ps

# Verificar Conectividade
echo "3. Network Connectivity:"
docker-compose exec -T postgres pg_isready -U n8n_user -d n8n && echo "✅ PostgreSQL N8N OK" || echo "❌ PostgreSQL N8N FAIL"
docker-compose exec -T redis redis-cli ping > /dev/null && echo "✅ Redis N8N OK" || echo "❌ Redis N8N FAIL"

# Verificar Evolution (se existir)
if docker-compose ps | grep -q evolution; then
    docker-compose exec -T postgres-evolution pg_isready -U evolution_user -d evolution && echo "✅ PostgreSQL Evolution OK" || echo "❌ PostgreSQL Evolution FAIL"
    docker-compose exec -T redis-evolution redis-cli ping > /dev/null && echo "✅ Redis Evolution OK" || echo "❌ Redis Evolution FAIL"
fi

# Verificar URLs
echo "4. Service URLs:"
curl -s http://localhost:5678 > /dev/null && echo "✅ N8N Web OK" || echo "❌ N8N Web FAIL"
curl -s http://localhost:8001 > /dev/null && echo "✅ Evolution Web OK" || echo "❌ Evolution Web FAIL"

echo "=== HEALTH CHECK CONCLUÍDO ==="
```
