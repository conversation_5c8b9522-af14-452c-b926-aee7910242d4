﻿bem para mim tá tocando o sinin
ainda pessoal aqui eu vou começar
falando com vocês Tá da da dessa forma
dessa formação né a ideia aqui é o
seguinte pessoal eu eu eu consegui até
hoje até esse mês de outubro tocar
promov web sozinho né você sabe que é Um
Desafio porque eu tenho que mexer com
docker ten que mexer com Evolution Tenho
que mexer com TN tem que mexer com
outros sistemas só que em algum momento
pessoal a gente
precisa Como diz a a a frase né dividir
para conquistar né e eu eu optei por
procurar outros criadores de conteúdo
para poder me ajudar nessa nessa questão
de fazer uma coprodução pra gente poder
criar mais conteúdo com mais rapidez com
mais cabeças pensando mais ideias ideias
que são iguais ideias que são contrárias
porque eu realmente acredito que a gente
consegue profissionalmente aprendendo eu
eu sou partidário da tentativa e erro eu
sei que muitos de vocês gostam disso né
Eu também faço mas eu acho que atalos
são muito bem-vindos né
então o Emerson né que tá que vai fazer
a parceria comigo desse curso de a eu eu
montei um outro produto que vai ser uma
extensão da promov web para quem quiser
fazer né e assim eu consigo ter o edon
também vai participar e o Emerson pra
gente poder PR não ficar tudo Dea cabeça
só E H pessoal e de todas as áreas aqui
aqui a gente encontra mais dificuldade
para poder ensinar sozinho é a ia porque
eu vou fazer de um jeito aí vem o
Antônio e me explica alguma coisa Luiz
mas olha só tem essa maneira aqui de
fazer aí nessa mesma aula vem o Bruno e
fala não Luiz Olha eu vi um vídeo aqui
cara que o cara faz assim Então pessoal
quando a gente centraliza o conteúdo de
uma pessoa só tem conteúdo que não fica
legal porque não tem como a gente fazer
de todas as maneiras por isso que eu tô
buscando outras pessoas pra gente poder
explicar aquilo de uma ideia mais
prática para vocês inclusive ideias
conflitantes né então é legal porque
essa essa esse conflito vai fazer com
que vocês realmente ten uma visão melhor
das coisas e e e Escolhe um lado tudo na
vida pessoal é escolher um lado não dá
para ficar em cima do muro a vida
inteira né então a minha ideia é trazer
tem outras pessoas que vão fazer
conteúdo comigo de seo de tráfego pago
de WhatsApp tem muita coisa que eu tô
buscando aqui ou mais parceiros para
poder vir e falar assim ó vamos vamos
puxar só que pessoal eh uma coisa que
que a gente tem que entender e até esse
curso que começa hoje né que é da promov
web não tem a ver com o Emerson tá que é
o nosso curso aqui de emprendedorismo
pessoal eu tenho que pagar as pessoas
também então o que eu faço sozinho eu me
banco todo curso que eu fizer sozinho tá
incluso no no plano padrão da promov web
esse curso que vai começar hoje de
empreendedorismo ele é um curso padrão
da promov web mas quando eu chamar o
Edson quando chamar o Emerson quando
chamar outras pessoas Eu também preciso
monetizar eles né por isso que tem essa
diferença para vocês entrarem nesse
curso e e ali pessoal O que que a gente
fez qual que foi a nossa estratégia dá
um desconto para quem já é aluno um
desconto bom para quem já é aluno e
estender somar o tempo dele na promov
web e que eu acho que é a maior vantagem
inclusive em relação ao desconto então
por exemplo ó vamos supor que o S tem
três meses restantes de promov OAB ainda
se ele acenar o o o o o yamer hoje ele
vai ficar um ano mais esses três meses
aí então ele consegue estender o tempo
dele de curso eu vou somar os dois
tempos o restante que tinha na promov
web mais esse um ano então o Edson
renovaria o curso só em 2026 se ele se
ele tivesse três meses para poder então
a ideia foi fazer o quê Já que vocês
estão comigo há bastante tempo eu tenho
aluno aqui que tá três anos comigo já eu
tenho aqui o Custódio não sei se ele
entrou aqui o Custódio o Custódio me
acompanha desde a promov web 2010 então
assim essa galera pessoal essa essa
galera que me acompanha muito tempo F
ass ó eu consigo reduzir até onde dá
para reduzir o preço que fique viável
para mim pro tem que dividir por TRS e o
que que eu posso fazer da da minha parte
estender pro cara o curso para vocês
Pessoal vocês vão ficar mais tempo vai
demorar muito mais você renovar de vocês
então só que assim eu não tive tanto
tempo poder is na Live de ontem aí ficou
esse buraco de comunicação peço desculpa
PR vocês tá gente que a gente acerta mas
a gente também deixa de de de de Acertar
Às vezes a gente erra mesmo assim então
acho que o que ficou de barulho de ruído
foi que não ficou muito claro para vocês
isso né que eh o curso que é da promov
web normal eu vou continuar tocando
normal tá cada vez mais reciclando
conteúdo trazendo conteúdo para vocês
mas esses que eu fizer em parceria
pessoal vai existir uma atualização ali
para até para eu poder
repassar pro outro criador e sempre com
vantagem para vocês principalmente essa
vantagem de estender o tempo de vocês
para que vocês fiquem mais tempo com a
assinatura Entendeu Manda aí
Antônio só só para ver se eu entendi bem
então então é como se a gente tivesse
comprando mais um ano de curso
independente do tempo que a gente tem
para para vencer Por esse valor que essa
diferença que você mandou pra gente que
é mais barato do que um ano de curso
normal é mais barato que um curso normal
então assim é isso que esse buraco ficou
galera não
então falei assim ó por exemplo quem tem
6 meses de promov web e assinar esse
essa extensão vai ter um ano e seis
meses de curso pela frente aía entendo ó
ele vai fazer um M qualquer então ele
vai ficar muito mais tempo até mesmo
qualquer um que fosse renovar agora ia
pagar 597 né E se pagar 400 é
praticamente a renovação bar já é
renovação então só quem vai renovar
ainda ainda ganha continua com um ano
daí né que já tá acabando o prazo dele
mas fica com ano mais ainda paga mais
barato do que a renovação p é PR então
assim é que isso aí Antônio ficou um
buraco como a Live cara a Live ontem eu
gosto porque eu faço Live sozinho eu
falo tempo inteiro ali cara eu tava
assistindo Eu recebi um monte de
mensagem no WhatsApp aqui F luí Eu nunca
vi você assistindo falei cara eu tava
assistindo Eu ficava para cá eu ficava
para lá eu sentava eu coçava a cabeça eu
bebia água eu pedia mais água aqui aí
cara assim por ser um conteúdo muito
rico e a ideia assim a gente faz essas
lives para que vocês vejam como é que é
o Emerson como que é galera que tá que
tá junto com ele que são pessoas que
trabalham alguns ali pessoal são novos
outros já são mais experientes mas a
ideia de trazer os novos é para você ver
que o cara que é novo ele consegue fazer
também não é só só não é só quem é gênio
que faz aquilo ali não aquilo ali cara
qualquer pessoa faz é é claro que
depende do esforço próprio mas a ideia é
falar assim ó esse cara aqui ele tá três
meses Olha o que que ele faz com TRS
meses né esse cara aqui ó já usa há um
ano olha como é que é o de um ano porque
falta um pouco Antónia pra galera
entender o percurso eu sempre falo pra
galera para quem é aluno já ouviam
falando que eu enxergo que é uma jornada
é o Wilson mesmo já tive conversa com o
Wilson sobre isso né Wilson que é uma
jornada cara o o dia um ele é difícil
cara o primeiro servidor seu não é o
melhor o primeiro servidor seu com
certeza vai travar os primeiro os
primeiro e-mail que você fazer ontem eu
mandei e-mail importate Esqueci um n lá
no assunto faz parte Cara isso aí faz
parte entendeu Não só que com o tempo
você vai melhorando com o tempo você vai
melhorando por por isso que Eu opto por
ter uma assinatura anual porque eu eu
preciso ter uma chance com vocês de em
um ano mostrar para vocês que em um ano
é impossível olha pessoal não sou
convencido não tá longe disso mas eu
acho que em um ano é impossível você
falar assim ó luí eu tô melhorzinho hoje
do que quando eu entrei é assim é em um
mês cara talvez você não consiga em três
meses talvez não consiga mas em um ano é
é muito difícil alguma coisa você
aprendeu
cara e e e também pessoal eu a a ideia
geral era essa falou assim vamos vamos
estender esse prazo pra galera para
ficar um negócio justo para eles porque
eles eles vão poder fazer esse upgrade
tendo o maior número de benefícios
possível não só o financeiro porque eu
podia colocar ali pessoal um um 30% de
desconto para vocês eu podia falar assim
ah não vaiou ter desconto nada a gente a
gente eh você a gente conversou para
poder chegar nesse nesse mínimo que
fosse bom para todo mundo e mas que
fosse melhor para vocês entendendo então
e eu tenho casa imagina annio ontem
ontem mesmo o rapaz comprou Ele tem ele
tem s meses ainda e que que ele vai
então cara ele tem muito tempo pela
frente aí para ele poder entrar então
para você ver pessoal como é que que
ficou esse buraco ontem tá gente de de
explicar para vocês isso daí mas a Live
hoje é para colocar os pingos no z mesmo
Vocês perguntam qualquer dúvida que
vocês tiverem em relação ao curso daqui
a pouquinho eu vou mostrar a grade para
vocês com o Emerson aqui eu vi que o
Emerson entrou aqui PR gente explicar
para vocês a grade eu tô aqui se
precisar Ach estô aqui e outro ponto
importante também pessoal daqui a
pouquinho eu vou falar mais sobre o
hermis para vocês explicar também
algumas algumas situações que acontece
né hermis que também eu vi que tem um
ruid Dinho ali um ridin aqui a gente
deixa tudo alinhadinho com vocês e o
principal pessoal é que assim por mais
que o Emerson tá comigo o Edison também
tá comigo a a promov web ela manté a
mesma a mesma ideia eu quero eu eu eu
não quero pessoal dar itens para vocês
eu não quero montar uma um lugar que
você baixa um workflow importa o meu o
meu maior mérito pessoal o que eu fico
mais felizão mesmo é que nem é quando os
caras acha coisa errada e avisa eu fala
Putz cara olha só esse cara tava
perguntando esses dias hoje el tá ele tá
corrigindo é quando é quando eu vejo que
o cara conseguiu montar o negócio dele
ele roda a infra dele Com mais confiança
ele ele tem ele entendeu cara que por
mais que ele possa usar o instalador se
ele aprender se o cara um pouquinho que
ele aprende ali ó ele monta o dele não
Luiz os meus stacks são meus na minha
ordem com os meus comentários se eu
tiver uma reunião com cliente ao vivo
ali no Zoom no meet com ele eu consigo
abrir lá e mudar o parâmetro para ele
poder ver para ele poder ver que eu sei
para não ver que que eu que a grande
maioria das pessoas não sabem pessoal eu
Eu sempre busco acompanhar e eu quero
que e e o meu marketing ele é muito em
cima disso o cara que quer aprender né
porque eu eu penso pessoal eu eu bato
muito na Teca a galera do longo prazo né
eu não vou montar um curso para você
assistir por um mês vocês vão ficar um
ano comigo e e a minha taxa de renovação
é gigante cara por quê Porque eu penso
muito nesse novo prazo o cara o cara já
aprendeu ele fica ali porque numa Live
ele fala F com o jezi ele fala com o ele
fala com o Antônio ele fala com o
Fabrício ele tem sempre pessoas ali que
tão colaborando estão trocando ideia
estão trazendo para ele alguma coisa que
por mais que ele já saiba muito a gente
brinca né a gente tá aprendendo sempre
né então assim eu acho que é legal
pessoal porque é uma chance que a gente
tem de crescer junto esse curso é um
curso novo ele é um curso do zero mesmo
né Emerson a gente pegou do zero mesmo
zero absoluto por quê Porque é uma é uma
eu eu conheço vocês eu ve quem já é
aluno né eu conheço que vocês muitos de
vocês já usam alguma coisa mas eu tomo
para mim também isso aí pessoal nunca é
nunca espaço conhecimento não ocupa
espaço então dá para eu chegar ali e
falar assim ó cara vamos falar sobre
prompt eu uso prompt pessoal o Antônio
usa prompt o Edson usa prompt mas o
Emerson trabalha 24 horas por dia Ele
nem dorme ele tem equipe de pessoas que
fazem isso então ele é um cara que sabe
muito de prompt então eu falei Putz cara
vamos juntar Vamos juntar ele mexe com
workflow ele mexe com typeb com Defy ele
mexe com nhn falei putz é o cara
perfeito eu já venho conversando com ele
há algum tempo sobre isso daí para poder
unir forças e de novo não só com ele tem
outras pessoas que também vão vir para
para promov web com conteúdos que
opcionais é que esse de Emerson esse de
a ele é muito quisto né cara é um
negócio que todo mundo quer mas outos
conteúdos P muito essencial né É É hoje
em dia ele é meio que essencial e eu
falei para vocês pessoal eu se eu
pudesse fazer no plano básico eu [ __ ] eu
faria mas é que não tem não tem como
fazer e e e eu falei para vocês a
maneira que a gente encontrou de fazer
foi de esticar a sua sinatura né e e dar
um desconto para quem G é aluno pro cara
que é aluno é um desconto mais pelo no
sentido de [ __ ] obrigada por acompanhar
obrigado por estar junto por confiar né
porque isso aí pessoal ensina é
confiança né é a gente trabalha muito
com a confiança eu sei que muitos de
vocês realmente compensam Confiam em mim
né e e agora com o Emerson o Emerson não
é um cara novato é que o Emerson qual
que foi a nossa conversa né Deixa eu
apresentar para vocês o Emerson pessoal
o Emerson eu eu como Embaixador eu
recebo muita indicação de pessoas muita
indicação de pessoas um dos um dos
grandes benefícios que eu tenho é esse
nível de acesso primeiro todo mundo quer
virar Embaixador né e segundo todo mundo
tem um novo Embaixador para indicar
então eu consigo saber pessoal quem tá
se destacando nesse meio só que ali
pessoal tem um ponto que eu bato muito
também e e é um é um meia culpa da minha
categoria de trabalho que é de criador
de conteúdo que tem muita gente pessoal
que tem uma facilidade talvez de ensinar
mas ele acabou de aprender então o cara
tá ensinando algo que para ele ainda não
é consolidado também então a gente tem
que dar dar uma filtrada muito grande
pessoal assim quem realmente tá ali e
sabe vocês não tem noção pessoal do
desafio que é isso daí Porque o
marketing é um negócio que engana muito
né então às vezes você vê um vídeo bem
produzido um vídeo bem feito um vídeo
fala [ __ ] mas o cara passou muito por
cima aí você aí você percebe ele não faz
Live Pessoal vocês T noção que para você
fazer uma live eu faço Live ou Erson faz
Live vocês me sabatin se eu sou um cara
mais inseguro se eu não tenho noção de
que vai vai vir uma bomba para mim ou eu
mato ela no peito ou eu guardo ela no
bolso eu não posso voltar a bomo para
vocês é isso isso pessoal é um grande
indicador que ele sabe mesmo e ele faz
Live Vocês já viram o tamanho dos Z flou
da Live dele não tem pesso pessoal como
então assim Isso foi um fator para mim
de de escolher entre pessoas que eu que
eu eu eu pesquisei eu acompanhei o
Emerson de longe é o que mais se destaca
e tem um outro um outro parâmetro ali
pessoal que eu falo eu ensino só o que
eu sei e o que eu uso eu sempre falo
isso para vocês ah Luiz ensina sobre tal
cara eu não uso isso daí eu não uso Eng
ginex eu não uso mais eu usei por muito
tempo eu não uso mais ginex eu uso
Traffic para tudo eu vou ensinar para
vocês o Traffic não vou ensinar um
negócio que para mim já ficou trás não
não vou e o hermerson pessoal ele tem
uma empresa disso ele trabalha com isso
né então ele não é um cara que falou
assim nossa cara descobri aá faz TRS
meses gostei e tô não ele já tá há muito
tempo nisso né Eh e tanto no Defy quanto
na na no nen então eu eu eu tô buscando
esse tipo de parceiro pessoas que e
estão começando também nessa nessa
questão de criar conteúdo comigo eu vou
mentorar também eles eu vou ajudar eles
porque Andorinha sozinha pessoal não faz
verão não adianta eu saber fazer um
monte de coisa eu não passar isso pros
outros eu eu não só quero pegar vocês
ensinar você como fegar um servidor mas
eu quero pegar o falei Emerson vamos
vamos montar assim o curso e foi uma
aventura não foi se a gente poder
modelar o curso o Ed participou também
foi bacana é uma coisa que era muito
minha Emerson eu fazia aquilo ali e eu
guardo algumas coisas as sete chaves o
Edson agora sabe como é que é o também
sabe como é que é daqui a pouquinho
vocês vão saber também como é que é mas
assim e eu acredito pessoal eu real
acredito que assim é mais fácil é até
melhor para vocês porque não fica só com
meu viés eh V vamos pegar o docker você
pega o docker pessoal o Davidson ensina
de um jeito o oron ensina de outro eu
ensino de outro tem várias pessoas que
ensinam o mesmo assunto de maneiras
diferente então A ideia é fazer o quê
Vamos tentar juntar o máximo de pessoas
possível que D liga para que vocês
também ten essas visões diferentes né e
o Emerson foi é o é o primeiro projeto
que para mim é o mais
e não não diria não diria importante né
mas assim com certeza é o mais é o mais
procurado Emerson é o mais procurado que
é o ia e no tem pessoal se vocês verem
aqui só o que que é o que a gente chama
de básico vocês vão ver que eu sozinho
eu demorar muito para fazer aquilo ali
para pensar tudo aquilo sozinho para
poder e a gente vai gravar as aulas
junto né eu e ele junto vamos gravar a
aula o edon também tá oedon também vai
gravar aula com a gente vai ajudar a
gente então assim é um trabalho pesso
pessoal que não começou agora a gente
vem montando eu de novo eu tenho outros
projetos que também vocês vão receber
porque eu quero montar uma empresa mais
completa de oferecer o conteúdo de
marketing no geral então o cara hoje
pessoal ele precisa aprender o docker
sem o docker não tem NN sem o docker não
tem o Defy sem o docker não tem as
coisas que vocês usam Então você tem que
aprender o docker você vai ter que
aprender ia porque é ia meu irmão se
você não aprender alguém vai aprender
por você e vai tirar muita coisa sua né
O o você tem que aprend tráfego pago
você tem que aprender campanha de
WhatsApp estratégia de WhatsApp você tem
que aprender um monte de coisa pessoal
montar site fazer seo né então eh a
minha ideia é faz assim cara não eu não
consigo fazer isso sozinho pessoal é
impossível eu sei que vocês muitos de
vocês gostam muito de mim mas não dá não
dá fazer tudo sozinho eu tenho que ter
mais pessoas andando junto mais pessoas
que estão alinhadas comigo com o mesmo
propósito e o Emerson e o Edson são duas
pessoas né que estão a gente tá alinhado
nesse mesmo propósito né e conta um
pouquinho Emerson do que que você faz aí
pros cara que que quem que é o Emerson
onde você mora eu gosto muito do nome da
sua cedade
ah
gente Pessoal o Emerson ele tá ele tá
ele tá na casa dos pais dele tá numa
viagem por isso que eu vi na Live ontem
uns comentário o pessoal falou assim pô
a câmera do cara tá ruim o áudio do cara
tá ruim pessoal eu vou viajar também eu
levo o celular assim eu não vou viajar
não vou desmontar o aqui vou levar e nós
estamos nós estamos numa eleição aí
pessoal domingo tem eleição então eu não
voto na minha cidade mas eu moro
pertinho aqui eu consigo ir PR outra
cidade e volar lá mas tem gente tem que
fazer uma viagem mais longa tem família
tem filho tem um monte de coisa tem uma
logística maior então assim até para
pessoal entender né que assim o pessoal
acaba reparando muito eu não ligo de
reparar Mas é bom entender o contexto né
sim eu peço até desculpa vocês que eu
não tô com a câmera ligada é porque
assim gente é
eu tô tô em viagem né tô aqui na minha
cidade natal que é no interior do
interior do interior do interior né que
eu moro no Pará então assim
eh ontem por exemplo eu tava com o
iPhone mas a iluminação que tava tão
ruim de um jeito que lá em casa eu tenho
ring Light Eu tenho um microfone melhor
ontem para você sem Comprei aquele
microfone pra Live eu até falei pro pro
Luiz né porque eu tenho um microfone
melhor em casa
então tipo assim aí assim
e Mas isso
não é uma situação temporária tá essa
semana que vem já vou estar em casa já
vou estar gravando com vocês então assim
eu quero despreocupar vocês dessas
situações
eh eu eu eu tenho hoje que é uma
comunidade assim que tem um certo
reconhecimento nessa parte de
Inteligência
Artificial Se vocês forem dar uma olhada
ali nas nossas lives vocês vão ver
grandes automações ali que que tipo
assim coisas que você nunca viu na
internet você vai ver na nas nossas
lives lá o pessoal posta muita coisa ali
tem muita gente que tá em alta produção
tem gente que tem muito
cliente tá eu mesmo tenho a minha
empresa que é inovari que que eh
justamente para São inteligências
artificiais para atendimento então assim
eh ali o o o eh a nossa comunidade é um
clube né de ajuda mútua ali para para
pessoas que tão de fato ali em produção
então Eh durante todo o dia a gente vê
eh dos mais variados casos e a gente
construiu esse esse curso Justamente eu
eu arquitete com com o Luiz eh
preparando pros casos que a gente vê no
dia a dia porque os casos que chega para
mim de problema é isso é um cara que não
não não construiu um Prom de feito que
teve alguma coisa ali que falhou
eh nos fund deixou passar nos
fundamentos eu eu vejo caras muito
inteligentes errando em coisas que são
básicas eu vejo cara que consegue fazer
um um baita de um fluxo mas o cara errou
aqui no no comecinho entendeu Às vezes o
comecinho ali do do do promt é um é uma
llm que ele escolheu mal né é um é
alguma coisa ali que que ele deixou
passar que complicou ele lá na frente o
cara conseguiu construir todos eu tô
acostumado ver casas assim todos os dias
gente então assim eu tenho uma certa
experiência para passar para vocês esse
caminho de dizer olha não vai por aqui
que vai dar errado aí você pode ir
testar também se vai dar né Luiz mas e é
muito comp Com Emerson é é muito que eu
já faço com eles de falar assim ó Gente
esse caminho que existe mas vamos nesse
para dar menos problema né e queri Você
é de experiência pessoal porque a ideia
a ideia é quer você o Emerson ele mexe
todo dia com isso todo dia com isso
então não tem Professor melhor do que
esse cara do que o cara que vive isso né
Tem gente que sabe tem gente que usa tem
gente que v a camisa do negócio né eu
sou assim com nhn eu sou assim com o o o
o docker eu sou assim com com malk mas
não tem pessoal como eu ser assim com
tudo com tudo entendeu então eu tenho
que buscar essas pessoas que fala assim
[ __ ] [ __ ] esse aqui é um cara que eu
vi que ele é que nem eu ele ele é tão
maluco quanto eu em relação a esse
assunto aqui né E e trazer pessoal
porque PR você ver ó ó a experiência que
o cara tem né assim para trazer pr pra
aula né e e a gente vai fazer junto né a
gente vai fazer junto eu com o Erson
junto na gravação do vídeo né a
qualidade da do do conteúdo que vocês
vão consumir é o é a qualidade promov a
web tá é é é dessa qualidade para
superior a gente tá aqui para somar não
é para reduzir a qualidade do exatamente
mel porque é um assunto pessoal que é um
assunto que ele ele ele precisa que seja
TR assim ó eu tenho que tratar ele sério
e e a gente tem que desmembrar Ele
pessoal para poder tirar vício né
Emerson eu eu eu chamo muito de vício o
que que é o vício Vício eu quero usar o
instalador isso aí é vício Porque se o
cara for montar um pega o Antônio o
Antônio instala instala um servidor
inteiro cara em em 10 minutos ele ele
Talvez caraa consiga configurar mais
rápido que o estalador por quê Porque
ele tirou esse vício ele falou não não
não vou mais fazer isso aqui vou fazer
assim quanto Mel quanto mais você tem
mais você faz mais rápido você trabalha
e na ia também é assim né por isso eu
quis trazer ele pessoal porque ele é um
cara que ele ele enxerga esses vícios
ele enxerga o que que é um vício num
prompt que que é um vício na lógica do
workflow o que que o pessoal usa para
fazer uma memória que fala fala assim
luí cara eu já vi tanto que eu acho que
assim fica mais prático e ess esse
truque pessoal é um é um atalho gigante
para vocês porque vocês iam ter que
errar isso aí ao longo do tempo para
aprender eu já tenho um cara pessoal que
ele já viu o erro demais na vida dele
muito do curso meu Edson é ô Emerson é
economizando o tempo dos caras com os
err eu mesmo eu mesmo Errei muito eu
mesmo Errei muito né Não digo nem só
pelos outros não corrigi erro dos outros
não eu mesmo eu eu o Rafael a gente a
gente pegou Notes e Notes ali testando
Bora testar isso aqui ver se funciona
Bora colocar isso aqui para ver como é
que roda bora ver isso aqui para para
ver se presta então assim a gente
acompanhou desde o começo ali da da
desde do do do dos primeiros gpts né Eh
Desde quando começou a essa essas outras
llm né o o
o a gente pegou desde do Bart desde
colocar ali o Gemini um né a gente
conseguia extrair gente para vocês terem
uma ideia eh eh do Gemini 1 a gente
conseguia extrair coisas né conseguia
fazer ele atender coisa que que era
muito difícil porque o o Gê é uma
inteligência era um inteligência muito
pobre na época né então a gente
conseguia ainda assim extrair coisas
legais dele tudo assim experiência
quebrando a cara vendo as atualizações o
que que tinha saído como é que ficava
melhor era a temperatura era um ponto
era as versões então tipo assim most sim
eu vou mostrar k para eles também essa
questão da da grade porque vai
justamente explicar isso para eles assim
a gente pegou pessoal dessa experiência
que o Emerson tem de de saber aonde
trupica vamos dizer assim aonde a gente
meio que deixa de lado ali e lá na
frente vira um problema eh a gente a
gente fez de trás pra frente né Vamos
pegar o vamos pegar as coisas que não
dar problema e vamos já ensinando isso
No começo sim que é para não levar esse
problema pra frente já tira do cara ali
no começo Já ali ali quem não sabe
aprende e quem sabe confirma e quem e
quem tá fazendo algo um pouquinho
diferente já começa a pensar se vale a
pena ou não seguir Aquele modelo então
eu pego todo mundo eu pego o iniciante e
o avançado fal cara reflete então aí se
essa estratégia faz um teste pelo menos
testa isso que testa isso que você faz e
e faz a a comparação ali então é é
justamente isso é duas coisas que eu
digo na mentoria pode abrir aí pra gente
ir falando mas eu duas coisas que eu
digo na minha mentoria lá eu digo assim
primeiro não acredite em mim Vai lá e
testa vai lá e faz vai lá e testa e
outra coisa que eu digo Olha você não
você não precisa errar tanto porque o
seguinte
se hoje tu for construir uma lâmpada do
zero Tu já tem alguém que fez ela tu já
tem a engenharia dela ali feita mas o
primeiro cara que fez uma lâmpada teve
que repetir e dezenas de vezes até
conseguir chegar a um resultado Então
hoje você consegue fazer consegue
comprar
pronta Então vale a pena você pegar uma
experiência com quem já apanhou e de GPT
com quem já pegou pisa de Gê com quem já
levou surra de grock
com quem já é com quem já saiu já saiu
já entrou do Defy quem nunca gastou uma
fortuna no open air e deixar
lup
emon deixa só antes da gente continuar
aqui com a grade E então já expliquei
pra galera da renovação do preço
apresentei o Emerson para vocês tá
pessoal e deixa eu só ouvir aqui o
Anderson e o mel pode ser o Anderson
primeiro Anderson fica à vontade de
perguntar
aí Boa tarde pessoal tudo bem Luiz não é
rapidamente ontem eu ouv a Live eu fic
morrendo de rir da ansiedade de um
colega né aí a minha pergunta é eu sou
aluno e não recebi e-mail ainda tem
algum outro canal para m
falar eu vou mandar depois eu vou P nos
grupos Anderson o WhatsApp da da promov
para você poder conversar lá porque a
gente mandou o e-mail pro e-mail da
compra do hotmart
mas aí a gente vai fazer uma segunda
Leva de de mensagem para vocês daí aí
essa você recebe a Sarinha a Sarinha
colocou o link no no chat aí Luiz dá uma
olhada no chat por favor no chat também
o link para você poder entrar lá já fala
direto com a Sara pessoal certo é eu
acho que o meu foi do hotmart é que o
meu vence daqui dois vence 20 de
novembro né da eu falei se vai acabar a
promoção e não mandarem e-mail eu tô
roubado Tá bom então ganhar uma extensão
aí Opa obrigado nada
Manda aí
Micael acho acho que tá no mudo Micael
ou ou eu que não tô ouvindo
aqui não tá no mudo mas não tava saindo
o som Luiz tá melhorou agora tá saindo
agora foi beleza Boa tarde eh ó minha
dúvida é sobre é sobre como que vai ser
ensinado E aí eu vou vou dar a minha
impressão o que que acontece eu eu eu
cheguei a pegar o aquele fluxo que ele
ensina que ele mostra lá e aí depois eu
peguei
o aquela aula lá de 99 para você
entender o fluxo eu fiquei um pouco
receoso
[Música]
eh porque assim eu eu consegui entender
na aula sobre como que funcionam os nós
mas eu não consigo entender às vezes por
exemplo Ah por que que o nó Ele usou
conversacional a gente Por que em outro
Ele usou SQL porque que um usou buffer
eh Heads Por que usou post gre e eu só
fiquei um pouco re assim se na se no
curso vai ser bem detalhado ou se vai
ser uma explicação um pouquinho mais
corrida Excelente excelente pergunta né
é bom para deixar esse ponto bem bem é
excelente pergunta eh no curso
eh tipo assim hoje a galera que tá que
entra ali no A ideia é o quê Colocar o
fluxo em produção certo então como é
meio corrido eh eu vou esclarecendo as
dúvidas né ali da galera vai acontecendo
né Eh o pessoal vai perguntando e eu vou
falando não não existe ainda um curso né
que que porque tipo assim eu conto que o
cara entrou Ele já sabe o mínimo el
GN o curso Ele é justamente algo que eu
já tinha em mente de fazer porque eu
tenho que explicar com calma por que eu
por que eu uso o poges Por que no no
outro canto ali tá um RS Qual que é a
melhor situação para você utilizar cada
um para que você tenha condição de
construir um fluxo igual o o lá da
comunidade Ou até melhor né Ou até
melhor até ser mais criativo e mais além
então o fluxo ele a a o curso Ele é o qu
ele é a base tá ele é a base para que
você você consiga eh chegar até eh o
entendimento não não só de como é que
funciona ali mas por que que funciona
daquela forma Por que que é melhor
daquele jeito
né
e eu tambm imaginei que fosse algo assim
mesmo que eu falei assim a outra deve
ser só um overview porque realmente a
proposta lá é pessoal que já entende
tanto que eu entendi o o fluxo mas se
você pedisse para eu fazer ele do zero
eu não ia conseguir fazer a proposta Lá
é outra é a galera que quer correr quer
colocar em produção é tipo assim é o
cara que pegou ali gratuitamente e ele
quer ajuda tá entendendo ele quer ajuda
de alguém e aí quem melhor do que um dos
caras que ajudou mais a fazer né então a
proposta lá ela é Ela é produção ela é
ajudar para você produzir ela não é um
curso que vai te ensinar do zero
entende e assim a a ideia também Ô
Micael a a não promov web a gente tem
essa ideia de explicar um pouco mais até
até para que você fique mais confiante
para montar o seu entendeu não então ali
a gente vai ter os workflows que a gente
vai montar né nos workshop a gente vai
fazer os workshops mas a gente vai
explicar antes de aplicar aquilo ali
para que você não fique só copiando
colando vamos dizer assim sabe é porque
eu acho que isso isso que é o valor
porque i cara é ela não é difícil mas é
que assim são muitos detalhes cara então
esse curso ele vai ser nessa nessa
pegada de Fal assim ó vamos explicar
esses detalhes até para que você use de
referência Então a gente vai fazer uma
uma fase inicial bem boa que é para
ficar como se fosse um manual ali para
você eh então assim a gente a gente
pensou muito nisso porque depois o mel
essa fase essa fase inicial essa fase de
curso básico que a gente vai chamar eu
tenho que dar o nome pessoal o nome vai
ser curso básico eh é só uma fase mesmo
assim de colocar os pingos no z ali cara
de como que as coisas funcionam a
segunda fase que é o que a gente chama
de avançado é só é só prática e e a
ideia M cel é o qu a hora que a gente
for montar um work Flow aqueles monstrão
que você viu lá no no nos grupos lá do
Emerson e eu não tenho que explicar pro
cara o que que é o top P entendendo eu
não tenho que explicar por que que ele
pôs temperatura de ponto TR ali por que
na no outro noite tá com temperatura de
1.5 o cara já sabe que que é a
temperatura então assim fica fica até
mais dinâmico para poder gravar um vídeo
mais prático então A ideia é a gente
separar nesses dois momentos falar assim
ó vamos vamos todo mundo alinhar todo
mundo ficar igual o iniciante e o
avançado ficar igual nos termos nos
conceitos no que que é cada coisa ali
para quando a gente partir pra prática
tá todo mundo no mesmo nível entendeu
não e e sempre lembrando né pessoal tem
gente que tá entrando ali que já não não
sabe nada mesmo tem gente que vai entrar
que já tá um tempo comigo então esse
cara já manja até até do NN Então essa
fase inicial é é para é difícil eu sei
que é difícil tá mas é para todo mundo
entendeu n para todo mundo começar ali
né para para quem já tá nesse mercado
poder reciclar um pouco e para quem tá
começando poder também começar bem já
começar sem faltar esses conceitos sabe
M cel porque eh a gente vê muito no
docker né os caras cria a máquina V cal
sem backup os cara cria a máquina sem
Firewall os Car cria a máquina sem
monitoramento Mas no fim das contas cara
tá tudo bem criei minha máquina tipo tá
sem tá sem é um pato sem asa sem perna
sem nada sem bico mas tá voando cara
então o cara tá bom entendeu não eh A
ideia é a gente a gente começar assim
porque de novo pessoal esses conteúdos
eles é um conteúdo que vai eh a gente
vai cada vez mais avançar com eles então
se eu faço Miquel se se eu faço um
conteúdo já começo com avançado é muito
difícil cara pro cara poder engrenar ali
porque é que você falou né ele vê muita
coisa E aí talvez seja uma explicação
rápida por quê Porque a parte demorada
foi no outro módulo que ele não fez
entend não então a gente separar isso
daí sabe para ficar mais justo até assim
nessa aula Micael a gente conseguir é
puar o sraf mais para cima sabendo que
todo mundo todo mundo tá iG então fal ó
cara se você não assistiu aí puxa a
orelha dele entendeu Não mas assim
é é nessa a ideia Um Bom exemplo Micael
é por exemplo lá no pro eu não tenho
como ensinar e pronte pro cara eu tenho
que eu tenho que ensinar ele a usar o
gerador automático porque se eu for
parar para
ensinar tipo assim vai a vai a aula
todinha e eu nem comecei direito começou
entendeu aí tipo assim o cara chegou ou
viu e olha para mim e aí não consegui
produzir nada então tipo assim eh eh eh
eu não tenho como ensinar tudo que eu
vou ensinar no nos cursos né eu ponho o
cara eu vou ensinar o cara a fazer ali
no gerador
automático entende então aqui eu vou
poder ensinar passo a passo porque tem
que ser daquela forma e e que outras
formas você pode testar também sim
pessoal deixa eu compartilhar aqui a
tela acho que eu eu tirei
compartilhamento né deixa eu jogar aqui
aqui pessoal tem a a nossa o nosso
plano o plano para dominar o
mundo Deixa eu ver só aqui como é que
vai ficar que eu acho que eu vou
compartilhar a gentea
inteira porque Oi e só tem uma dúvida
que o pessoal perguntou um pouco mais
ali acima sei que agora você já vai
mostrar aí a grade do curso né O que fal
Po falar vai ser ensinado mas o pessoal
perguntou ali que foi uma dúvida acho
que de duas pessoas sobre até quando vai
a a promoção com os descontos né Eh que
era até sexta né você tinha eh
referenciado seria até sexta mas parece
que vai ter uma mudança né isso a gente
vai estender mais um pouco pessoal
porque eu eu recebi alguns feedback de
pessoal que vira cartão então a gente
vai estender mais um pouquinho para dar
tempo de virar cartão da galera de de
ficar mais justo para vocês poderem
entrar também sabe então acho que ao
longo da pelo menos umas duas semanas aí
a gente deixa esse esse cupom e a gente
vai conversando lá tem a Sarinha aí ó a
Sarinha a Sarinha que a Sarinha que
negocia com vocês aí para
poder agilizar isso aí e depois vão ter
que cantar parabéns para Sara aí no
final da da da Live aí é com certeza
fazendo 15 anos hoje Sarinha né o
perguntou paraaa fez 15 anos falei ô
Sarinha pessoal aqui ó aqui tem essa
essa de tá que a caneta que a caneta
fique caindo eu fico eu seguro el para
ela não cair que eu falei para vocês
essa aqui é a maneira que a gente tem de
de poder montar um mapa mental de como
que tem que ser o ensino como é que tem
que ser a sequência das coisas o que que
vem antes o que que vem depois o que que
um assunto um assunto vai ser mais fácil
passar ele no começo do que no no meio
ou no fim então a gente namorou bastante
mapa mental aqui né gastou uma uma uma
fiquei um pouco mais velho montando esse
negócio aqui mas a ideia aqui pessoal só
para você ter uma noção do tamanho dele
ó isso daqui pessoal que vocês estão
vendo ó Isso aqui é só a parte
básica é só a fase um que a gente vai
fazer por que pessoal porque eu falei
para vocês não é difícil só que são
conceitos que o o operador de a ele tem
que ter né ele ele tem que ter pessoal
até um pouco dessa Malícia que nem aqui
tem a parte de prompt a parte de
sequência de lógica a questões até
técnicas né como que é modelo como que
esse modelo funciona como que aquele
outro modelo Funciona porque vocês vão
lidar com o tempo com custo Às vezes o
Antônio fala assim luí eu monto o
workflow há muito tempo só que o cliente
cara ele conseguiu crédito lá no no no
no grock aí já quebrou a perna do
Antônio por qu porque o ninguém ensinou
o grock pro Antônio todo mundo ensina o
open aí então pessoal é no dia a dia o
Emerson trouxe Falou luí às vezes cara é
o mesmo workflow só que o cara conseguiu
crédito lá no no Microsoft azure lá por
exemplo e ele precisa adaptar ali cara
ele precisa fazer algum ajuste Então
pessoal até nisso a gente chegou nessa
nesse mínimo comum aqui né da gente da
gente da gente poder ter essa essa noção
de como as peças se encaixam como que as
coisas funcionam então um ponto
importante aqui né que eu acho que é o
mais importante que é a ferramenta
pessoal na fase um para para explicar os
conceitos para vocês a gente vai usar o
Defy só que na hora de montar os
workshops na hora de montar os exemplos
vamos também usar o
nhn só que por que que a gente optou por
usar o Defy nessa fase nessa fase do
projeto porque pelo menos pessoal como
ele tem uma abordagem mais simples para
poder até para você poder aprender fica
mais fácil a gente aprender no Defy para
poder explicar os conceitos Mas o foco
do curso pessoal vai ser muito mais unn
muito mais un porque até para que vocês
vejam e eu sei que o Luca deve est aí o
Luca vai brigar comigo não é nada contra
o Defy pessoal não é nada contra o Defy
né o Defy não precisa que ninguém
defenda ele mas quero que você entenda o
seguinte pessoal a gente consegue ir até
um ponto
comf e a ideia do curso é mostrar para
vocês esse ponto falar assim ó gente até
aqui o Defy é muito bom e é bom mesmo eu
sou um cara que eu defendo o Defy só que
eu também entendo pessoal que a partir
de um ponto o martelo não resolve
preciso de marreta a vida é assim
pessoal a gente não consegue usar só uma
Fer enta vamos pegar o swarm a gente tem
um docker normal que me resolve
problemas da minha máquina a gente tem
um swarm que resolve problema de custers
e e temos o kubernets kubernetes que já
aí já é uma coisa muito mais monstruosa
não quer dizer que o swarm é ruim porque
tem algo maior do que ele não quer dizer
que o outro é ruim porque tem não é isso
pessoal é que são fases são etapas são
necessidades que você vai ter então ah é
importante entender isso né é importante
a gente entender não uma não invalida a
outra tá talvez até vocês usem as duas
junto mas a gente a gente quer deixar
bem definid no curso para vocês fal
assim olha Pessoal talvez vocês usem o d
para muita coisa mas talvez vocês também
US n eu quero deixar a nossa objetivo
nessa fase inicial é deixar bem claro
essa marcação você falar assim Putz Luiz
esse projeto aqui cara Talvez noy
consiga fazer ele pessoal projeto bom é
o entregue Não é mas você tamb tem o
discernimento de falar assim Putz para
esse aqui cara já vai ter que ser
Então até até para isso a gente quer
usar essa fase inicial para não criar
uma guerra pessoal não é eu não vou
criar uma guerra de F versus nhn a gente
quer que você escolha a uma opção viável
pro seu projeto e Existem duas boas
opções só que elas não são iguais
pessoal o Defy tem um teto muito mais
baixo do que o nhn né então eh a gente
tem a gente tem que também ser realista
com vocês até por mais que eu goste da
ferramenta tem que ser realista com
vocês né fal assim ó eu vou até aqui
cara aqui eu consigo um pouquinho mais
mas quem vai decidir qual que vai usar
pessoal no fim das contas é vocês mesmo
né e e o projeto que vocês vão fazer o
acho que o Antônio falou na aula um dia
não sei não sei se foi O Antônio foi o
marco falou assim ó a gente meio que
mata muito mosquito com canhão e canhão
cara é pesado é caro é a bala é pesada
para você para você colocar tem que ter
cinco pessoas lá para manipular o canhão
prec matar um mosquetinho cara que com
um tiro você faz a mesma coisa então
assim é é a ideia pessoal é a gente
tirar um pouco pouco também desses mitos
né apresentar para vocês melhor a a
ferramenta para que fique tudo mais tudo
mais transparente e que vocês vejam que
eles concorrem um concorre com o outro
eles também se complementam né e que
vocês vão ter a oportunidade de usar
dois sistemas que são bem populares no
mercado né E vocês vão ver na prática
ali que sim eu consigo muito mais coisas
com o nhn não descendo de mas é que o
nhn pessoal ele tem 400 node entendeu
Não eu tenho 400 só de node eu tenho 400
eu posso criar componente para ele eu
posso usar node código com ele posso
fazer um monte de coisa com ele pessoal
então assim não tem não é uma briga tá
gente não é uma briga é só mesmo mostrar
isso daí para que vocês vejam que as
duas opções são válidas mais que uma
consegue avançar um pouco mais do que a
outra Manda aí emon que que você i
comentar aí Ah eu ia falar tip assim que
às vezes o cara tem um projeto simples
às vezes ele tem um algo que ele quer Ó
teve um amigo meu que que outro dia
pediu para mim fazer uma I ali para ele
né para ele usar mesmo ali algo simples
né E que que eu fiz eu peguei ali
rapidinho questão de 2 minutos ou e eh
ele ele me passou ali o texto digitei
ali na frente dele mesmo que eu gosto de
fazer esse tipo de coisa aí ele abriu
ali a a conta com Defy ali no o cloud
mesmo no no com a conta Google dele
rapidinho joguei o gerador no no chatbot
básico entendeu eh e conectei ele ali na
minha Evolution Olha amigo tá aí teu teu
teu chatbox
pega por exemplo Emerson é que assim que
eu falei PR você pessoal eu tenho o
Humano que faz isso e eu gosto muito
mais do humano fazer isso mas vamos
pegar esse exemplo do curso aqui do
desconto se eu fosse montar um bot ali
esse bot Eu montaria com dy que é só o
qu é só ir no meu banco pesquisar lá se
a pessoa é aluno ou não dá o desconto
para ele e beleza assim e para esse tipo
de operação Erson é legal você falou Eu
nem eu nem hospedaria eu usaria o sas
mesmo só ia conectar uma Evolution no
meio da história al então assim tem oper
tem casos e casos pessoal mas tem
situação quero conectar um sitezinho né
ali rapidinho is Ah o meu site é
conectado com Defy de cloud Entendeu
agora agora pega por exemplo pega
Emerson pega um acesso da banco de dados
eu vou usar um mais Kell depois eu vou
salvar o l no hubspot o cara que pediu o
cupom eu vou criar um item no CRM para
ele isso aí pessoal é muito mais à
vontade de fazer no NN então é essa é
essa a ideia que a gente quer colocar
para vocês uma não invalida a outra um
não é não é questão de ser melhor ou
pior do que o outro é você ou você tem
que escolher pessoal tem que usar um
entendeu não tem que usar um é igual o
carro é igual carro tem que usar um não
tem eds o Ed tem um carro lá eu tenho
outro o tem outro mas assim resolve pra
gente entendeu não é se não resolver não
é troca a ideia é essa tá pessoal A
ideia é muito de fazer isso daí com
certeza aqui pessoal aqui também pessoal
tem uma outra questão
nessa nesse nosso nesse nosso
planejamento aqui ó que é como vai
começar com dfy Então a gente vai vai
explicar muitos conceitos para vocês já
no dy por ele ser mais amigável para
isso né não que a ele não seja mas é que
aqui fica até mais até a aula fica mais
e interessante pela pela interface dele
de fazer isso mas lembrando sempre P PR
vai ser quase tudo dentro do Defy isso e
no nen a gente vai vai ter o workshop
que onde a gente vai fazer no de e
também no n para vocês verem essa
comparação de um para outro uma uma boa
parte aqui pessoal vai ter uma
comparação mas o a gente quer comparar
para que vocês entendam também essas
diferenças entre eles né e eu falei para
vocês quem vai escolher no final é vocês
mesmo né a ideia de ter esse fundamento
e essa base pessoal é é para que você
possa até usar outra ferramenta se você
quiser né Emerson a o o dia a dia
pessoal ele exige isso de você ele exige
flexibilidade não dá para morrer ninguém
aqui cara é o capitão do do do Titan né
o barco tá afundando vou afundar junto
com ele não é assim que funciona né eu
ten eu tenho que ter essa esse
conhecimento para me dar essa liberdade
entendeu E aqui questão voltando
voltando ao exemplo do carro que você
deu né Luiz eh eu tenho habilitação de
carro e moto eu ando com o que para mim
na hora é melhor vai ter lugar que eu
preciso ir de moto que o carro não vai
entrar então é Ué sabendo pilotar os
dois você vai com o que for melhor e e
eu falei para vocês pessoal a moto tem
os prós e contras o carro tem os pró PR
e contras é o de é assim um tem pro
outro tem cont os dois tem pro e contra
e não não não quero fazer uma guerra em
relação a isso tá pessoal não quero
fazer uma guerra a Evolution é o brigo
mesmo que eu acho que a Evolution é
melhor não tem aí aí eu aí eu dou Bicudo
na porta mas o de a gente deixa até o ru
concorda comigo que a Evolution D Dá de
10 a zer nos cara aí mas aqui ó aí
pessoal a gente vai ter aqui esses
workshop que são serão sessões mais
práticas tá ah ponto importante pessoal
também daqui a pouquinho eu vou eu vou
explicar como que vai ser essa Cadência
tá bom pessoal aqui ó é o seguinte ó
aqui nessa nessa tela
eh a gente vai explicar muito da Open ai
para vocês ferramentas da Open ai então
nessas ferramentas da Open ai a a nossa
principal ideia aqui é a gente poder
fazer essa como é que eu vou explicar
para vocês assim
essa explicar o open Com certeza é o
mais popular e explicar o quais uns
parâmetros da api porque vocês vão
manipular muito o open ai como que
funciona lá dentro da Open ai né Emerson
a melhor forma de usar o open Ai acho
que seria extrair o máximo do de novo
pessoal Às vezes a gente fica muito no
Raso né Emerson fica muito no Raso e é
legal porque até pouco de a você já faz
muito né mas eu acho que se você
conhecer mais da ferramenta você extrai
muito mais dela seja em relação a custo
a recurso então a ideia pessoal é é
fazer sim um módulo mais eh como é que
eu vou dizer para vocês mais mais
potente em relação a Open a porque com
certeza vai ser o que que você é o que
você
vai vai poder acompanhar mais né Então
essa essa que essa que é a ideia de ter
essa esse módulo sobre sobre a Open ai
tá gente então a gente vai passar
bastante por ela aqui vai ter um módulo
só sobre as ferramentinhas da Open a que
você vai usar no dfy e também vai usar
no nateen nos dois você vai usar o open
ai por isso que é importante conhecer a
Open a e não e não a implementação dela
na ferramenta né e e ainda pessoal nessa
fase um essa fase um pessoal ela vai
acontecer agora nesse mês de outubro
Então nesse mês de outubro a gente vai
ter essa fase um aqui que é falar do
Defy falar da Open ai né E falar também
de engenharia de prompt gravar para
vocês essas aulas de engenharia de
prompt que é aqui que eu acho que o
Emerson vai brilhar para caramba porque
e no YouTube Pessoal vocês acham
bastante coisa de Open aí é muito
condensado a ideia aqui é é é trazer
para vocês mesmo conteúdo mais legal mas
esse é porque pode falar é porque assim
na na na questão de prompt até no
YouTube você vai ver muita coisa
espedaçado D uma dica ali outra D outa
dica cular aí tu vê outro vídeo de dica
tu não tem assim um guia guia assim
definitivo não é isso aqui então assim e
eh na parte de Prom e é algo que vai é
por isso que é bas
porque o que você vai usar em qualquer
ferramenta se daqui a pouco sair uma
ferramenta melhor do que o ntn vamos
dizer né que
saísse essa parte de Prom vai ser a
mesma que você vai usar lá na outra
ferramenta mes mesma base sa por porque
muitos deles usam outras ferramentas de
a e a ideia dessa fase inicial pessoal
trazer para você esse conhecimento
quando você for usar um outro sistema de
ess me passou um outro ali que pesso Us
isso essa aula vai servir também para
qualquer porque não é que é genérico É
que é um conteúdo base é Um fundamento
entendeu não então aqui nem ele falou né
aqui a aqui ele vai apresentar um
gerador de prompt que eu achei um
negócio Fantástico isso aqui né isso
aqui pessoal é para você ganhar rapidez
Sabe aquele projetinho Emerson que o
cliente fala assim para você assim ó
você pergunta e qual que é o prazo ô luí
então era isso aí cara tá rodando
segunda-feira fal [ __ ] todo mundo passa
por isso então vai ter até esse atalho
aqui para ajudar vocês mas além do
atalho também vai ter explicação mas aí
daí é apo a ferramentinha né para poder
ajudar os caras a montar isso daí né a
ajuda e é gostoso Luiz você chegar eu te
falei outro dia que eu fechei com o
cliente que eu tava na frente dele e
esse meu amigo também foi o mesmo caso
Eh o cara olha tal foi me dando detalhe
enquanto ele tava me dando os detalhes
eu tava ali escrevendo no celular eu
peguei coloquei dentro do Prom e disse
então como é que funciona assim disse
não cara enquanto eu tava conversando
contigo aqui eu já fiz vou jogar aqui
para você testar aqui rapidinho ele foi
pegou meu celular testou ó tá aqui ó e
disse pô cara bacana isso aí isso aí
pessoal você ganha é isso aí Heron já é
um assunto também para esse curso que
vai começar hoje né de empreendedorismo
às vezes pessoal você quebra muito
objeção com futuros clientes ou até você
ganha confiança de atuais clientes
fazendo algo na frente dele como é que
Faustão fala mesmo é é
quem sabe faz ao vivo é quem sabe se
vira no faz ao vivo se vira nos 30
porque isso aí isso aí pessoal é é um
dos pontos de autoridade que é
importante para você por isso eu gosto
de
aprender o conhecimento em si pessoal
beleza mas quando você aplica ele isso
aí ganha imagina e outra pessoal esses
projetos de a eles não são barato né É
um tipo de coisa cara que assim a pessoa
que vai comprar Tem muita dúvida então
isso aí é mais uma coisa que ajuda você
a ganhar criança do cara pô fo na minha
frente cara é é claro que tu não vai
montar um negócio inteiro na frente do
cara mas pelo menos algo né cara O
pessoal falando sério mesmo assim Alguém
teve experiência de conectar um CR code
na Evolution jogar ali no nin no Defy em
qualquer lugar que tiver ali na no no
assistente da Open aí e o cara perguntar
o negócio responder isso aí pessoal é é
o melhor vendedor que tem é o cara ver
na prática e essas coisas não é tudo pro
não é tudo é só um gostinho mas isso aí
pessoal para quem não é do nosso mundo m
para quem não não tá na nossa comunidade
aqui para quem não é desse lado do
balcão que desenvolve isso aí pessoal é
coisa outro mundo você ganha pessoa de
um jeito cara que assim chega no
dentista e mostra para ele chega no
advogado e mostra para ele e e é legal
ter isso daqui né porque isso aqui vai
dar para vocês um pouco mais de
agilidade também né E aí pessoal a gente
chega e agora então agora em outubro a
gente vai correr com isso daqui com
vocês para poder passar para vocês essas
aulas aqui a gente vai liberar aula para
vocês pessoal toda semana toda semana
vai ter leva de aula para vocês tá na
quinta-feira a gente vai fazer com vocês
um um um horário no período da tarde
para vocês poderem tirar dúvida a gente
bater papo então toda quinta-feira vai
ser isso daí eh só para ficar claro
também pessoal e ficar registrado aqui a
ideia de não fazer essas aulas ao vivo
que eu faço aulas ao vivo com ele sabe
Emerson mas tem conteúdo pessoal que não
dá tem conteúdo que não dá porque a
gente tem que se organizar bastante
antes e eu tenho que me concentrar mais
no que eu tô explicando Tem coisa que
não dá o curso de docker eu fiz dois
cursos de docker ao vivo e fiz três
gravado por qu porque na hora ali her
você cara você dispersa cara Às vez você
tá com você tá certinho você treinou
você planejou chega na hora eu falo com
o Fabrício eu falo com o vai tudo embora
então assim a ideia pessoal e como é um
conteúdo mais denso mais técnico a gente
vai gravar essas aulas disponibilizar
para vocês e um dia da semana vocês vão
poder bater um papo eu Edson Emerson
para poder tirar a dúvida mas assim a
ideia é todo mundo caminhar junto toda
semana vocês vão ter acesso a essas mais
aulas pra gente ter uma Cadência legal
Erson e também pessoal para poder vocês
fazerem com calma né porque Cadê o j o
Jad um se eu publicar hoje tudo até
amanhã meu irmão ele acabou ele ele para
tudo ele nem banho não toma não dorme
não almoça não janta lá ele sabe que ele
é assim entendeu não então assim é só
ter visto aí ele já deve estar se
costando ele ri mas ele sabe que ele é
assimu então é é só para evitar pessoal
para evitar mais J Elder é que ele é bom
a sorte dele é que ele é bom entendeu
Não já ativou já ativou a urticária dele
lá já ó lá já ficou vermelho já ó já
ficou vermelhão já e aqui pessoal nessa
fase um a gente pretende fazer ela agora
em outubro né no máximo comecinho de
Novembro também para finalizar e porque
não tem como fixar pessoal o calendário
vai ser vai ser nessa data acabar nessa
data pessoal porque é Depende de uma
série de fatores e a gente vai o máximo
possível cumprir o calendário mas né
Vamos vamos entender que pode ser que
adiante uma semana pode ser que atrás
uma semana mas vocês vão ter conteúdo
toda semana tá gente e aqui no básico
pessoal então a fase dois aqui ó essa
fase dois aqui aqui a gente pode chamar
de intermediário Só que ele ainda é no
básico lá tá gente ainda é um conteúdo
que vocês T que aprender antes de pôr a
mão na massa 100% que é o que pessoal
conhecer os modelos o
é muito difícil cara eu eu crio conteúdo
eu faço uma pesquisa gigante de conteúdo
cara é muito difícil você ver alguém
explicar o que que é o
Mistral você vai achar o que que é open
a você vai achar o que que é o grock o
grock cara você ainda acha mais ou menos
pessoal quem aqui sabe o que que é a
empresa grock que que ela faz que que é
o que que é o core dela do
grock é montar chip cara a grock pessoal
a Nvidia Open a Ela depende da NVidia
montar um hardware e vender essas placas
PR PR Open aí para Open aí montar o
modelo o grock não cara ele faz tudo ele
faz o chip que ele mesmo usa então assim
é um outro n é a Apple o grock é a Apple
do ia ele é mais rápido é um chip
próprio ele treina o modelo num hardware
que ele desenvolve para ser cada vez
mais potente e e você não acha isso você
não acha isso da a verdade gente é que
olha não sei se vocês algun não sei se
vocês assistiram do Lu por exemplo
falando do grock com owan né de forma de
pensar o grock responde mais rápido que
o que a Open mais rápido que Open mais
rápido que Open então tipo assim e não
só isso mas se for perguntar aqui para
vocês por exemplo vamos mudando de
esfera Se eu dissesse assim Gente o que
que o Gê é melhor que o op
aí o que que o g alguns sim alguns de
vocês talvez até me diri que em nada que
o open é melhor em tudo mas no curso
vocês vão aprender que tem coisa que o g
é melhor do que o open A então vocês vão
aprender a explorar o melhor do g o
melhor do grock O Melhor do da da Open a
o melhor da Cloud que tem coisa que a
Cloud é é é
melhor até o até o Mistral vocês vão
aprender é a Microsoft tem outra né
esses dias no grupo meso al gerou um o
que que é um l que que é um slm né o que
por que um é large o outro é SM né E
isso também isso também interessante
cara Dependendo do que você for montar
dependendo do que não dá para você usar
o open mini o 4 o mini porque ele ele é
um smal você vai ter que usar o 4 o
mesmo cara que é mais caro mas ele tem
toda a base de conhecimento dele então
esses pequenos detalhes pessoal a gente
vai passar com vocês pelos modelos para
que vocês se localizem e na hora de
conversar com o cliente na hora de
negociar com o cliente aqui envolve
custo aqui envolve e Vocês perguntam
muito sobre custo aqui envolve né é
longevidade de projeto né ô hermerson
porque vou dizer para você o grock
pessoal o grock você pega lá um olama
você usa no grock vamos supor que amanhã
o grock fecha fal não quero mais brincar
não vou queimar o chip tudo aqui e
acabou eu pego esse mesmo lama e rodo no
meu PC aqui ó o cliente não fica na mão
agora vamos supor o open a não se o open
a amanhã dá uma de Bubble lembra o
Bubble que o Bubble quebrou com a galera
mudando o preço se é Penha aí amanhã dá
uma de Coringa e faz um e dá uma de
Bubble E aí que que a gente faz eu não
tenho outro Open aí entende não é e e
não é só isso Luiz tipo
eh que nem tu falou a questão do do
slm você tem você tem Aliás o eh você
tem os submodelos que são os modelos né
Eh por exemplo tem mais ainda o 30b 7B
8b sim se se você abrir por exemplo o
Google e Studio tu vai abrir lá no
modelo tu tem mais de que 10 modelo tem
quase 15 modelo Gemini Flash Tem vários
tipos de Gemini Flash Gemini pro tem
vários Gemini pro tem gema do tem tem eh
os o os bits os com mais uns com menos
então assim tem tem n situações dentro
daquele modelo
ainda aqui o o Mateus perguntou se esses
encontros de quinta vão ser gravado vão
sim Mateus como eles são plantão de
dúvida e e também vão vão V servir pra
gente poder interagir então eu vou
gravar também essas conversas para quem
não puder participar à tarde poder
assistir de noite quantas vezes quiser
porque a ideia pessoal é gerar para
vocês o máximo de conteúdo para poder
auxiliar também vocês na jornada é legal
porque a dúvida de um já é resposta pro
outro então eu vou gravar assim viu
Mateus tudo que for desse conteúdo de a
cara eu vou gravar e vai ficar na
comodidade para vocês porque daí ali
falei para vocês né aquele naquele
padrãozinho pessoal vamos transcrever
vídeo vamos pôr Resumindo vídeo na aula
para vocês para vocês poderem encontrar
a aula depois esse mesmo padrãozinho que
eu tô seguindo na comunidade para ficar
um conteúdo mais mais amigável para
vocês tá pessoal aqui ó a gente vai vai
falar um pouco também sobre LM local é
uma coisa que eu mexo bastante né de
você poder rodar o lama e o gema para
quem tem MEC aí eu sei que o o Antônio
deve rodar uns uns localz nervoso no no
no mecão pro que ele tem ali o meu aqui
é um Air e um mini Eu tenho um MacBook
Air e um mini Então eu tenho que rodar
um modelo mais mais Modesto nele né mas
é uma é uma chance pessoal até na Live
de ontem a gente comentou né ô ô de lgpd
de de privacidade tudo isso tá incluso
aqui nessa questão de usar modelo local
porque para vamos supor pessoal eh eu
sei que muitos de vocês vão usar a ideia
de montar bot mas eh até o exemplo que
usaram na Live ontem o rapaz montou um
bote pra farmácia Pessoal esse tipo de
de negócio ele é muito lucrativo cara
você tira você consegue ganhar muito
dinheiro fazendo isso daí o mesmo botzin
que você vende para o cara da padaria o
cara da farmácia é outro poder de compra
Talvez o mesmo bote funciona lá só para
atender lá o pessoal no no WhatsApp
pedindo remédio o cara que pede pão no
na padaria é o mesmo esquema de bote do
cara que pede remédio né claro que tem
uma consultinha ou outra al mas a
mecânica é a mesma só que esse cara da
farmácia pessoal ó é muito mais caro
para você poder fazer para ele só que
ele vai ele vai ter esses detalhezinhos
pô Luiz não posso salvar lá cara e então
é a chance que vocês tenham até de
entender como é que esses modelos locais
funcionam explicar um pouco mais para
vocês em questão a legislação lgpd né
que eu eu mexo bastante com isso isso
também então tá aqui incluso porque de
novo pode ser que você pegue um projeto
e a e a diferença desse projeto o o
problema o que vai empacar o seu projeto
é que ele precisa rodar local e hoje em
dia é possível rodar local pessoal uma
maquinazinha com GPU aqui ó você roda e
não é questão de preço não viu gente tem
muita empresa que não é questão de preço
não de custo é é é privacidade o cara
tem auditor o cara tem uma tem alguém lá
que não quer que isso daí circule esse
cara é o que vai cobrar de você e é ali
que tá a Grand viu gente a emesa gharar
grana é o m é muito disso sabe Emerson
muita muitas muitos malicos eu trabalhei
na pandemia muita fintech porque ia
lançar o pix e nessa de lançar o pix né
que fo da pandemia ali as fintech não
podiam circular essa informação cara em
em provedores de terceiro então muita
gente que usava muita finte usava R
Station usava outros sistemas campan
usava o se Force teve que trazer esse
esquema do pix para para interno cara a
gente fez a vida com mtic ali cara por
quê Porque tá na infa do cara o banco tá
com o cara o mtic tá com cara ninguém
acessa é só lá os dois TR qu da ti dele
que acessa Eu mesmo não tinha acesso eu
só mentor mesmo a galera então assim ali
pessoal não é não é financeiro não é a
empresa precisa daquilo e existe ess
essa opção né e falei para vocês vocês
que TM a oportunidade de Navegar nesse
oceano aí isso aqui ó Isso aqui é uma
praia boa para você parar viu Manda aí
jeel luí Oi
pode falar falar não eu só ia comentar
Pode falar não só ia comentar que
assim por exemplo aí as llms é
importante você explorar porque chega
numa hora por Ah tu pega o projeto desse
mas aí tu sempre trabalhou com Open E aí
como é que tu vai trabalhar com olama
sem conhecer nada do olama porque o cara
te pedi o local ou G coisa n muda muo
coisa ição a maneira como cobra Muda
então às vezes o cliente fala assim ó
luí eu vou montar um bote grande aqui
cara eu vou ter que ele vai ele vai
optar pelo mais barato e aí se você não
conhece você fica na mão porque você
ficou meio de novo pessoal tirar os
vícios você ficou tão viciado a usar o
open ali ali cara que você Você só sabe
manipular aquilo ali você nem sabe como
é que chama lá o htp para chamar o grock
entendeu não então isso aí pessoal é o
tipo de coisa que no dia a dia auxilia
vocês né por isso ess pração eu fiz no
curs docker Emerson eu tenho no curs
docker estratégia de update lá de você
mudar o update config donate n para
atualizar ele sem sem ele ficar fora do
ar por exemplo isso aí cara ajuda o cara
ajuda o cara nessa questão de de de
requisitos que o cliente pede para ele e
ele vai ter que atender então eu
coloquei ali cara só para ele saber que
existe Aquilo em algum momento ele vai
usar aquilo ali mas ele sabe que tem é
impossível o cara ver aquela aula lá e
não não não querer usar aquilo né mas
mas assim eu tenho eu tenho que passar
isso para eles Emerson tenho que passar
que existe local existem outros modelos
além da Open ai e e que vocês com
certeza e cada vez mais vão ficar
populares vocês vão ter que aprender de
alguma forma a a manipular eles né he
luí Oi você falando aí do vocês falando
do dos modelos né que são executados
localmente né já foi falado em outras
ocasiões também sobre a questão da
proteção de dados e além
não só a a a questão da lei da proteção
de dados Mas também muitas empresas
começaram a compreender o
valor dos dados do cliente dele
manipular
internamente aquilo que ele tem acerca
dos clientes e eu tô vendo uma
movimentação muito grande eu trabalho eu
presto serviço para uma empresa que eu
eu dou manutenção no software deles lá
mesmo
e Só que essa empresa ela ela ela
especializada em segurança e também
trabalhava com a área de redes e e e
servidores cara tá uma movimentação tão
grande de de de serviço na área de
transformação da
infraestrutura e muitas dessas empresas
já estão adquirindo equipamento para
trabalhar com a ia internamente até
porque então assim hã daqui a pouco muda
a lei a gente é assim o Brasil é o país
da regulamentação né daqui a pouquinho
algum maluco vai querer regulamentar Iá
aí aí meu irmão aí roda roda aqui dentro
você faz o que você quiser mas assim é
até pessoal eu fui gravar um vídeo aqui
ô jz Alder eu eu devo ter o rascunho
desse vídeo e eu tava eu tava testando
aqui um vídeo testando aqui o a conversa
fluida do do GPT do chat GPT e eu falei
assim ó me fale sobre mais me fale mais
sobre o dekim o dekim é uma assinatura
do e-mail ele ele explicou chegou no
meio da explicação a voz estava em a voz
feminina veio a voz masculina falou
assim ó eh as minhas políticas de
privacidade não permite que eu fale
sobre isso autenticação de e--mail por
algum motivo aquela palavrinha tá tá tá
proibida entendeu não então assim daqui
a pouco começa a pegar isso aí cara
daqui a pouquinho chega nisso entendeu
então aí a local ela é sua cara a meta
ela vai montar o lama você baixa o lama
roda aqui meu irmão ninguém sabe o que
acontece aqui dentro endeu não eh você
você não tem essa preocupação e até
pessoal para quem vai montar produto
serviço ou Jad eu sou fã de rodar isso
daqui porque eu eu consigo legar o
sistema eh o o modelo vai ser sempre o
mesmo então imagina Emerson que você
conseguiu manipular lá o o o olama o
olama 8b que é pequenininho e ele ficou
perfeito pro seu bote cara eu vou usar a
vida inteira esse modelo cara eu não
tenho por atualizar e eu também fico eu
não fico refém do mercado me impor um
produto que eu não quero então daqui a
pouco a Open ai lança lá uma opção Ó
quem vai montar chatbot tem que fazer
isso daqui Pô cara pro meu sistema aqui
vai me quebrar Porque eu já tenho tanto
que o Bubble fez né Eu tenho tanto
cliente rodando esse nesse modelo que a
hora que eu mudo vai vai impactar os
clientes então assim e até até essa
segurança G para quem vai montar um
produto que vai rodar que vai escalar
que vai ser algum tipo de sas o o esse
selfie hosted ele vale a pena seja na
infra local ou ou seja num numa infra in
nuvem mesmo que tem lá GPU e que você
possa rodar lá né então é
importante hiper convergente né que é é
a nuvem híbrida né o pessoal tá
trabalhando ou seja porque tem muita
coisa que ainda que é vantajosa a
empresa manter na nuvem ali ou né por
diversos motivos mas tem coisas que
agora ela tá sendo obrigada a manter
localmente
um cara um youtuber comentou falou assim
ó nós voltamos em 2018 tá todo mundo
voltando pro escritório trabalhar e as
empresas estão voltando a comprar
servidor
voltando né não eu eu só iria comentar
assim que que essa questão por exemplo
das ias você eh vai ver mais vocês vão
ver isso mais no avançado né Mas vocês
vão ver que tipo assim cada I ela tem um
lugarzinho dela e você não precisa para
um projeto Ah eu vou vou ter que
escolher uma não eu vou ter que escolher
o olama Ou eu vou ter que escolher o GPT
o melhor para não tu pode usar várias
llm diferentes no dentro do mesmo
projeto Ah tem tem sa aqui que o que que
vai passar por ela ah ela vai só
selecionar qual que vai ser o ag gente
que vai essa aqui pode ser eh uma boa
matemática né uma exata essa não essa
daqui vai responder essa daqui é mais
séria essa aqui tu pode usar um GPT
então assim tu não precisa escolher uma
pro
a ideia do do multi llm é você usar
várias llms cada uma é a melhor naquilo
que vai ser que aquela que aquele bot
precisa fazer que aquele agente precisa
fazer a melhor naquela E aí você vai
pode pode usar várias entende não
precisa Ah eu vou ter que escolher essa
daqui não a graça do mulm é isso é você
juntar tudo e extrair o melhor de cada
uma Como é que chama lá o light Light
llm que você falou Light LM é
um a gente vai ter o LM vocês é um prox
rever Bal cara é algo sensacional porque
tipo assim você já pensou Quem de vocês
já teve problema do modelo não responder
aí não sei se vocês já chegaram nesse
ponto Acabou o crédito acabou o crédito
acabou o crédito pronto acabou o crédito
aí o cliente liga para vocês puto olha
não tá respondendo aqui
crédito e rte limit né crdito Ó o Edson
já bateu no rte limit esses dias a gente
estava brincando ali ele brigou com no
rmit do GPT se você tem uma na tua infra
lá Light llm ele ele Ah beleza deu
problema aqui no GPT ele entra um gê deu
problema no Gê ele entra um grock deu
problema no grock ele entra o Mistral
deu problema no Mistral entra o então
tipo assim tudo isso vocês vão aprender
como fazer esse esse fallback de de llms
né como eh fazer esse roteamento né da
das chaves
eh eh como é que você vai configurar
isso da melhor forma Inclusive eu ia até
te dizer Luiz que que o light llm acho
que foi até o Gabriel cara ele viu o
projeto do Light LM ele fez no
no ele fez no n8n um roteamento no
próprio n8n ele fez ele fez um
roteamento você pega pela resposta
código de resposta faz um head para
poder saber para onde vai jogar ali dá
para fazer load balancer dá para fazer
até prox reverse vai fazer al
disponibilidade vai fazer várias coisas
até pel pelo n mesmo ô pessoal vamos
criar múltiplas contas no grock né
Emerson é só só 20 só pra gente não
estender muito aqui sua gravação aqui ó
eu quero passar por esses dois outros
pontos que faltam aqui ó que esse aqui
pessoal também é um que a galera não dá
o devido valor que é o treinamento do
modelo
aqui pessoal a gente tem aqui a parte de
treinar
modelo para que vocês consigam entender
como fazer isso né como fazer essa essa
maneira de você mod a importância de
cada detalhe que você vai
colocar tudo importa eu já vi eu já vi
no curso que eu fiz o flow lá atrás a
gente tava na pré-história do dos
modelos né eu peguei um PDF cara subiu
um PDF lá entendeu Não só com o tempo
você vai vendo pessoal e você vai
acompanhando os papers da galera que
trabalha dos das pesquisas né Tem duas
situações hoje pessoal que é importante
a primeira é você limpar o máximo
possível os dados que você vai importar
Quem já usou o Defy para poder importar
e a base de conhecimento ele tem uma
opção Zinha legal lá que fala assim ó
você quer transformar em pergunta e
resposta [ __ ] é maravilhoso cara e também
pessoal a gente tem e eu não lembro o
termo o nome exato do termo é o colapso
do modelo é assim o GPT Emerson 1 2 3
ele foi criado com 100% de informação
humana pessoas escreveram e ele importou
essa escrita já o cinco já o quatro o
cinco e o seis ele já vai ser treinado
com muita informação que a própria I
quebrou e gerou e tem e tem um um a
revista Nature pessoal fez um modelo fez
uma até num uma das minoset eu coloquei
isso daí ele fez uma série de
experimentos que depois de seis ciclos
de treinamento o modelo colapsa ele ele
ele ele tá tão cheio de de dados que o
próprio modelo gerou que atrapalha a
pesquisa semântica dele então ó Que
loucura cara daqui daqui alguns anos nós
vamos ter que perdido com isso aí cara
só que ele eles também repararam Emerson
que até em modelos customizados na Open
aí a o o o colapso acontece bem antes
por isso que é importante pessoal
entender isso daqui ó que colocou aqui ó
e uma parte do treinamento você põe em
base de conhecimento outra parte você
põe no próprio prompt para já guiar o
sistema então aqui pessoal é eu eu
gostei demais disso aqui porque
justamente Quando você vai pesquisar
isso aqui Erson você vai ver livro cara
é isso daqui é isso aqui ó só que de
novo né pessoal é muito bagunçado esse
conteúdo aqui a gente colocou na
sequência certinho para vocês ó pra
gente poder falar né essa fase até na
ordem a gente colocou até na ordem de
importância isso na ordem que você
precisa
na ordem de importância
e por que que eu por que que isso aí tá
na base porque para para poder chegar lá
no no no n8n nas partes mais avançada eu
não vou ter que est voltando ensinando
porque que tem que ser daquele jeito
promt da
ferramenta então assim você vai entender
como é que monta um prompt para uma
ferramenta como é que uma ferramenta tem
que se comportar como é que um agente
que vai conversar com o cliente tem que
se comportar que é completamente
diferente porque um responde texto outro
Responde Jon um responde de uma maneira
outro Responde de outra não é só formato
de
saída formato de saída de
entrada cara tudo tudo que você vai ver
aí é importante para para você conseguir
extrair o melhor então assim é um básico
que nem vocês estão vendo aí mas é um
básico um basicão mesmo tipo tem tem tem
de tudo aí que que que você não não vai
ver num lugar só né você não vai ter
acesso num lugar só então é é realmente
um sim e aqui hermerson a gente tem
pessoal essa essa questão do juntando
ali o promp né já o prompt o treinamento
entra esse daqui que eu acho que é um
dos uma das das do dos pilares também da
ia É a lógica do fluxo que é uma coisa
que é uma coisa pessoal que o eu eu já
tinha visto os gringos fazendo mas em
português você acha muito pouco conteúdo
falando isso daqui enquando eu tava
conversando com o Ed o Eron citou eu
falei Putz cara legal isso aqui porque
isso aqui é uma coisa que o povo tá
batendo muito em cima disso daqui
Emerson que é é você
eh literalmente pessoal projetar o seu o
seu o seu o seu modelo o seu bote
projetar como é que as coisas O que que
a etapa um vai fazer a dois vai fazer a
três vai fazer como como quando que ele
volta quando que ele avança eh isso aqui
é importante né quando é que pensa fazer
um loop tem fazer que vai ter que quando
é que vai rec concatenar aquela
informação ontem perguntara né na Live
pro pro nosso amigo lá pro Como é que
chama ele o Gabriel né Eh
eh não sei se foi o Gabriel ou se foi o
Diego como que ele fazia para instalar
um sistema de um cliente para outro e
ele deu uma boa explicação eu já monto
ele flexível então é mais ou menos isso
daqui pessoal e você quem for montar um
produto um serviço vai ter que lidar
muito com isso né Erson de montar ele de
uma maneira que seja até fácil
manutenção
né e que seja fácil de daqui a se meses
você olhar ele você ainda entendeu o que
que você fez ali aquela brincadeira né
jer quando quando eu escrevi o código só
eu e Deus sabia Agora só Deus sabe né
agora só Deus
sabe isso isso isso isso acontece a a
vantagem da a vantagem de você entender
o conteúdo básico é porque diferente
sabe que eu acho diferente da questão de
de de código é porque tipo assim tu
entendendo o básico tu consegue é igual
andar de bicicleta tu vai olhando ali e
tu vai começando a entender de novo Como
é que tu fez sim né e eh vendo como foi
aqu como foi aquele prompt tu olha vê um
negócio ali que tu já melhorou tu já
aprendeu tu tu já corrige já melhora
aquela situação então assim você vai é
como é que tu burla algumas situações tu
vai ver coisas por exemplo no ntn Ah vou
usar um node de agente que é o caso da
memória O pessoal Tem muita dúvida
relacionada a memória lá porque eu criei
uma forma lá no no fluxo da comunidade e
dá uma burlada nessa memória porque
quando você utiliza a mesma memória ela
fica sujando ali né Eh a memória um do
outro se for a mesma então encontrei uma
forma ali de usar o chat Memory
injetando o a memória dentro do promp de
maneira que o agente consegue ler a
memória sem sujar ela então tipo assim
você tem que ter um uma uma lógica ali
de estratégia dentro dos prompts para
fazer funcionar o que você quer que
funcione e aqui a gente colocou né Heron
de mostrar como é que seria no Defy como
é que você estaria no nhn porque muda a
dinâmica muda mais assim A Regra geral é
a mesma mas a dinâmica de fazer muda né
o e acredito que se lançar outra
plataforma essa lógica ainda vai ser a
mesma sim então assim pessoal a a aí Aí
termina aqui com o workshop né que a
gente vai poder mostrar ali os fluxos da
comunidade daí aqui a gente avançou mais
no curso e eu falei para vocês aqui já
começa a ficar mais complexo também os
workflows dá pra gente poder pegar
assuntos mais legais para poder montar
os fluxos ali né E no final pessoal no
final de Novembro começo de dezembro a
gente chega na fase três do básico que é
a gente começar a mexer mais no NN
porque daí Aí é legal porque nesse ponto
né Erson a gente já a gente já usou o NN
com de junto mas desse ponto pra frente
eu preciso preparar vocês com o leng
Chen do neen pra gente poder tacar o
terror no avançado depois quando virar o
ano nessa questão de de de vocês
entenderem o nhn porque aqui pessoal até
o Edson o Edson fez foi um uma
observação interessante hoje muito
importante hoje e eu vou ter que fazer
esse serviço pessoal que é um serviço
difícil para mim porque assim ó o en TN
pessoal a gente teve uma conversa com
eles eh Há questão de um mês um mês e
pouco atrás o en Chen de etl que é o que
vocês usam para pegar um lead aqui e
salvar o Lead lá no Ma sabe isso chama
de etl isso aí pessoal existem regras
para ele vocês conhecem que é usar fila
usar o Rabbit que é você ter um workflow
só com um Trigger tudo aquilo que eu
falo para vocês tudo que eu falo pessoal
ó vamos quebrar em sub workflows Vamos
separar vamos dividir para
conquistar no mundo do etl pessoal é
assim que funciona só que o nnd o n para
leng chain ele é uma outra pegada então
ah eu ten Eu vou ter esse trabalho
pessoal ao longo desses próximos meses
de de dividir o pensamento de vocês
estou trabalhando com o ia algumas
regras que o Luiz ensina não se aplicam
então na ia pessoal você você pode ver
até pelo próprio node os workflows do
Emerson que na ia tudo bem você ter dois
três Trigger ali para I pessoal é assim
é mais fácil para você modelar ele no
etl normal eu vou buscar l 5000 lin do
banco de dados que que eu falo para
vocês gente põe na fila eu falo para
vocês põe na fila chega no i pessoal o
que que eu vou falar para vocês pessoal
vamos instalar o Nate sem fila Luiz tá
ficando louco não cara que eu preciso
que ele responda rápido a fila vai
entrar ali vai ter um tempo para poder
responder e o liit tá lá esperando a
resposta Então pessoal é importante
porque
eh eu vou aproveitar esse gancho do
curso dessa dessa nossa abordagem maior
em cima do n para já ir a gente
conversando sobre isso também e falar
assim [ __ ] pessoal ó Eu Sei Que Eu Tô há
3 anos falando para vocês usar a fila
mas nesse caso com o Emerson aqui ó
vamos vamos rodar sem fila vocês fingem
que vocês não ouviram por TRS anos que
era para usar hein Edson vamos fingir
que eu não falei para vocês que não
existia nhn sem fila e realmente pessoal
o nhn no modo normal dele só com fila
mas chega na ia pessoal não dá e no no
normal vamos usar sub work Flow vamos
vamos dividir vamos reduzir o tamanho
dele na Iá pessoal [ __ ] montar um fluxo
com vários workflows você tem você tem
que ter Jeziel a cabeça muito boa cara
você não tem que brigar com sogra a moer
não tem que encher o saco seu o cachorro
não pode latir porque senão cara você
você as crianças entrou no quarto aqui ó
eu já não sei M work Flow que fal o quê
entendeu não então algumas regrinhas
pessoal vão mudar né então aqui nesse
modo naessa fase três é a gente falar
mais sobre isso né a gente a gente eh
inverteu um pouco a cabeça de vocês
entender assim ó o que que você tá
fazendo com seu NN esse workflow aqui é
de A é então é assim assim assim esse
workflow aqui é de Não esse aqui é
normal Luiz Então é assim assim assim pr
pra gente tentar não misturar tá bom
pessoal então essa essa fase três aqui a
gente vai fazer ela em dezembro que é
pra gente poder terminar o ano né que eu
sei que vocês vão viajar eu sei que
vocês vão tirar férias então a gente
planejou outubro novembro e uma parte de
Dezembro para finalizar essa parte essa
parte básica aqui liberando conteúdo
para vocês toda semana e fazendo esse
bate-papo na quinta-feira que a a gente
vai gravar também vai ficar disponível
para vocês as aulas o bate-papo vocês
vão ter uma área da promov pessoal ali
para quem já assinou já apareceu para
vocês lá né a formação e a makers é um
bate-papo só sobre a então eu tirei esse
bate-papo do geral que é para não
bagunçar ali é um fórum só sobre a para
vocês também não usarem o outro suporte
para não ficar
um cara perguntando do docker outro
perguntando do ya outro perguntando do
template prompt então é uma área
totalmente exclusiva pessoal pra gente
centralizar ali as perguntas o
conhecimento os bate-papo vocês vão
poder publicar também itens lá algum
artigo que você achou algum workflow que
você quer Quer compartilhar é uma área
que a gente vai montar só sobre A Iá
para vocês ao longo dessa semana eu vou
terminar de fazer esse esse trabalhinho
de casa ali pra gente já começar a
conversar ali né a a gente já começar a
alinhar com vocês ali esse esse conteúdo
né pessoal e e a previsão né Emerson é
que no dia 15 desse mês a gente já
Public para você uma uma grande leva de
aulas ali para que vocês possam já
começar a fazer o curso tá gente e a
gente só Estendeu o pessoal pro dia 15
porque tem eleição né então eu não
queria atrapalhar muito que eu falei
para vocês eu quero que vocês acompanhem
e e também pessoal toda terça-feira tem
a Live da comunidade né Emerson então a
gente quer pegar esse da Live da
comunidade para poder trazer mais
conteúdo para vocês também então a Live
da comunidade na Live nesse dia a gente
publica as aulas na quinta-feira a gente
tem um encontro e quem e quem assistiu a
Live da comunidade quem assistiu a Live
da comunidade pela primeira vez ontem
assustou não guardem aquela impressão do
YouTube pras próximas porque aquela foi
a a única a última
e realmente houve ali um gzin na na
missão mas a gente já já se programou
aqui paraas nas próximas não acontecer
ISO de novo porque porque é legal
pessoal porque é uma chance né da Galera
poder mostrar o trabalho né e e a gente
pensa até se dependendo da quantidade de
pessoas que que vão se propor l a
mostrar a gente até montar mais Live
também né mas entend pess até vocês
mesmo mais tarde né Vocês montaram as
lives também para poder mostrar né então
porque ali pessoal ali é um ganha ganha
né a gente quer Eh e eu tenho essa
proposta de fazer com que vocês montem o
negócio de vocês montem a empresa de
vocês que vocês Prof profissionalizam o
trabalho de vocês porque eu fui eu
sempre fui o nerd eu sempre fui o cara
que que trabalhava no CPD o programador
que mexia com qualquer coisa eu só fui
eu só fui mear bem na vida Emerson
quando quando eu larguei dis ser isso aí
cara e eu fui o cara que montou um CNPJ
ali minha vida mudou entendeu Não porque
ali eu pude negociar de uma maneira
diferente as coisas beleza você quer
bastante coisa Beleza eu vou te mandar
uma furinha das das coisas que eu faço e
e e a ideia pessoal é é é mostrar é
trazer para vocês trazer vocês também
pro YouTube né Vocês poderem também
mostrar o trabalho de vocês vocês viram
lá né que o hermerson faz a comunidade
dele esse trabalho de a galera vim
mostrar o trabalho Quem se interessar
depois chama os caras para poder
negociar poder conversar A ideia é
também pegar da promovo web e fazer isso
tá gente então Eh são duas comunidades
diferentes mas a gente tem esse Elo de
ligação entre as duas comunidades né e e
a gente também néon o máximo possível
promover essa galera aqui né promover
essa galera também que tá aqui no yamer
para poder quem sabe lá e fazer uma
apresentação né eu sei que vocês montam
produto e e e fazer essa fazer essa a
comunidade girar pessoal porque não é só
o curso tá gente é importante entender
isso néon não é só o curso é a
comunidade é o lugar para você perguntar
é um lugar para você tá ali ali pessoal
vamos dizer que tem um b de um filtro eh
não é o grupo do WhatsApp pessoal eu
gosto de grupo do WhatsApp mas o grupo
do WhatsApp tem um cara interesse tem um
cara não interessado tem um cara curioso
tem um cara briguento e ali não ali já
passou por todos ali é o café ali é o pó
né Aqui já é o café já já passou por
todos os filtros passou por tudo ali
cara fal assim ó aqui só tá mesmo quem
tá montando quem tá interessado o cara
que pagou o cara que entrou é o cara que
tá a fim entendu então ali é uma
conversa melhor é um lugar melhor para
poder conversar né então a gente quer
trazer PR vocês pessoal porque de novo
né i é um negócio louco né Cada tem
novidade cada dia parece uma coisa
diferente bomba atrás de bomba
explodindo na cabeça da gente aí os Car
lanç modelo na mesma semana CCO empresas
lança modelo diferent então tem que ter
a comunidade tem que ter esse lugar pra
gente compartilhar até notícia
informação né Então essa nos nossa
proposta geral al que que vocês estão
achando da da grade até
agora deu para pegar uma geral pessu
entend ação nessa primeira fase que é
feito pro iniciante mas também pro
avançado é que a gente quis pegar também
o iniciante então mas é um vocês viram
ali que tem coisa para caramba então
assim tem tem bastante coisa legal para
vocês aprenderem né E esse desafio nesse
final de ano pessoal apesar de demorar
luí mas são três meses luí pessoal não é
primeiro que passa assim né mas a ideia
pessoal aí para vocês é para vocês
aprenderem mesmo aprender assistir o
vídeo uma duas vezes tem aula pessoal no
curso doc eu falo pessoal assiste de
novo o curso para não sair fazendo e já
ir sair errando né então a ideia pessoal
é a gente a gente passar para vocês
gradativamente esse material a gente ir
conversando sobre esse material que vai
ser passado a gente gravar essas
conversa bem com você a gente ter ali o
bate-papo a gente ter as lives de de
terça-feira para vocês poderem ver
também como é que funciona vocês poder
falar também nessas lives tem a Live de
sexta que é o o podcast como com edon
para trazer notícia para vocês né e a
ideia é publicar na comunidade antes da
notícia né então a gente tá montando
pessoal Essa eu já tô montando o 2025 já
eu tenho que começar agora se eu começar
2025 eu tenho que começar um ou dois
meses antes né ano passado eu comecei um
mês antes não deu tempo então ho eu tô
começando três meses antes que é para
sobrar tempo né da gente poder atualizar
tudo né gente e tem e tem muito mais
coisas por vir aí né e e e a tendência
cada vez melhorar oferecer mais conteúdo
trazer né o Emerson o Emerson Para mim é
a maior vitória do ano nesse sentido
porque vocês viram pessoal acho que eu
acho que hoje foi bom ermon porque hoje
como não foi live live pessoal é é
Tiroteio né você tem que olhar aqui
olhar aqui você tá falando uma coisa
você esquece porque o outro tá aqui no
chat perguntando e eu eu tenho que
pensar e falar e ler outra coisa ao
mesmo tempo hoje que vocês viram o
hermerson mais calmo né hoje você hoje
vocês viram a mesma coisa que eu vi que
ele é um cara que sabe hoje falei assim
deixa o Emerson falar para você ver como
é que ele sabe hoje eu acho que não tem
tirou qualquer dúvida que tirou qualquer
dúvida o pessoal fala ass pô luí é igual
você f cara é igualzinho meu irmão a
diferença é só m só muro sotaque não
é ai ai mas assim a a ideia pessoal A
ideia é muito a gente juntar essa força
né junto com com esse senhor que tá na
outra ponta aqui ó esse senhor tá na bem
na ponta lá s um dos poucos santistas
que sobraram no mundo aí e e e também
pessoal unir essa força tá gente o nosso
objetivo maior é unir essa força eh eu
sei que vocês quem tá há mais tempo
confia mais né já sabe como é que
funciona quem tá entrando entra cheio de
dúvida mas a ideia é fazer esses
bate-papo aqui para poder colocar os
pingos no zir porque ontem a gente
falhou muito na comunicação porque da
Live foi um Deus nos acuda mas a gente
tá aqui hoje para poder esclarecer para
vocês as dúvidas tirar aqui pessoal ô
deixa eu só ler aqui a o comentário da
galera que eu acabei não olhando muito
aqui na no chat pode falar Emerson po eu
respondi alguns aí Luiz Ah é eh eu
respondi alguns aí o Guilherme ficou com
uma dúvida sobre se Defy é melhor que
flo ou vice-versa eu respondi para ele
ali
Eh aí ele perguntou também sobre as
questões do que vai ser apresentado eu
falei que estaria na grade mas se quiser
complementar com alguma coisa ali fica à
vontade
aí ó lá o Luiz Henrique falou que é s
antista também então temos dois no mundo
ó lá né não tá mais sozinho tem alguém
assisti o jogo tomar uma cerveja
é porque coisa coisa boa é difícil de
achar né Luiz coisa ruim a gente
encontra os montes por
aí pessoal alguém F com alguma dúvida
alguém tem algum comentário alguém tem
alguma alguma dica sugestão pode falar
aí porque eu falei para vocês pessoal
andorinho sozinho faz verão né a gente
monta a gente planeja Mas é para vocês
né então é legal ver Manda aí Micael
Manda
aí você comentou acho que foi ontem você
fez um comentário breve
sobre ensinar a gente
comercializar se você puder falar alguma
coisinha aí se porque eu tô nesse
projeto assim de de tentar comercializar
mas eu não sei eu fal toda quarta-feira
Micael a gente vai começar eu já já tava
H muito sempre engavetado isso daí
Porque eu tentei fazer duas vezes cara
mas não deu não deu não é que não deu
certo é que assim é que a gente não
tinha um um um bom assunto para poder
tratar desse assunto entendeu Não agora
com o ia cara Eu até comentei na Live
ontem que eu eu eu particularmente acho
Micael que a ia ela permite que a gente
crie produtos muito mais fáceis e não
não só ajudar a construir o produto mas
aí a também seu produto então por
exemplo o Fabrício Fabrício é muito bom
de cop Fabrício o cara 10 de COP talvez
cara ele pode montar umaa para
automatizar muito o processo de quem faz
cop ou ele pode montar o maa para ajudar
ele a ganhar produtividade nas cópias
dele dos clientes dele treinar o modelo
com com cópias antigas de lançamentos
antigos treinar o modelo com o maior
número de informação possível e de
estilo de escrita maneira El ele pode
montar uma ia que gera cinco versões de
um c Action diferente então assim é um
negócio Micael que é muito no code então
assim qual que é o maior problema que eu
vejo no SAS A não ser que você fosse um
cara muito dedicado mesmo cara para
aprender o nocode era muito difícil você
montar um SAS cara a gente vê lá no os
meus amigos lá do noc de Startup do neto
do do Mateus lá sim lá lá sai mas cara
você tem que ficar um ano dois anos
montando o negócio pro negócio sair
entendo e quando o negócio sai já tá
velho já então eu acho que o i cara ele
consegue a gente consegue montar um
negócio muito mais prático que eu falei
assim numa reunião com o cliente cara
uma hora que você fica com cliente você
já escaneia o QR Code dele já liga
Evolution nele já pluga no nhn já pluga
no Defy num Type bot Seja lá o que for e
E você já consegue pelo menos dar um bom
dia ali cara o cara sai da reunião com c
dando bom dia pros outros né então assim
é o tipo de coisa Ô Micael que e aí e aí
entra Esse é o assunto esse vai ser a
nossa a nosso pano de fundo para essa
mentoria de criar de criar empresa de
criar produto criar serviço né eu tenho
a Power teck já há muitos anos eu tenho
promovo web também já há alguns anos e
antes disso eu tinha a a a oranj web né
que é o que eu mon foi a minha a meu
primeiro meu primeiro meio que eu montei
né era meio na época Ainda então assim
ali cara eu peguei um pouco de
experiência de como é que você vende né
e eu sempre trabalhei prestei
consultoria para para empresas que
também tinham produto então não só o que
eu tive de experiência mas o que eu pude
aprender também com com os outros ali
sabe então a ideia vamos supor hoje por
exemplo toda quarta-feira começa hoje
hoje a gente vai vai bater um papo sobre
audiência e Aida porque antes de vender
eu tenho que ter para quem vender não é
assim Fabrício se eu não tiver o público
eu não não vendo cara então a gente vai
conversar sobre isso como para quem que
você vende Quem que é o potencial
cliente seu Como que você trabalha como
é que você chega nele Como que você
conhece ele para daí o Micael ao longo
dos encontros a gente poder falar sobre
precificação sobre Persona montar
produto é um produto ou é um serviço o
que que é um produto que que é um
serviço é um é um produto customizado ou
é um produto de prateleira é um serviço
que você vai fazer igual para todo mundo
ou o cara vai poder ficar dando upsell e
customizar isso aí cara ajuda porque eu
vejo Micael que muita gente sabe fazer
eu tenho muito aluno aqui cara que sabe
montar um servidor docker mas assim
desculpa falar pessoal vende por 200
conto entendeu não então assim não tem
problema 200 conto o problema cara é
vender por 200 conos uma fintech que
poderia pagar um dinheirão para você
entendeu Não e às vezes a gente não tem
a assim a gente a gente tem que entender
cara que não é que o preço vai da cara
do cliente mas é que existem realidades
diferentes Então esse esse grupo de
empresas ess esse essas empresas
iniciante esse cara autônomo eu posso
montar um produtinho mais simples e mais
acessível para ele mas eu não vou vender
o simples acessível para uma empresa
consolidada com 10 funcionário não vou
cara eu não vou criar um um bote de
atendimento que vai substituir duas três
pessoas da empresa do cara e cobrar R
100 por mês dele entendeu não então é é
meio que a gente bater esse papo Micael
para aprender junto trocar ideia junto
Porque eu sei que eu sei que fazer vocês
muito de vocês sabem mas assim a gente
poder conversar também ter um lugar para
vocês conversarem até entre vocês também
sobre como vender o que que o Antônio
faz o Antônio é um belo exemplo de um
cara que tá montando um SAS de chat ut
ele tem ali O Edson também monta
consultoria faz consultoria hoje de
quase tudo o Fabrício aqui ó trabalha
com copo então juntar essa galera o jez
eldor Né que é programador tem o Marcos
também é programador e que presta
serviço também até para entender Ô
Micael Aonde dá pra gente entrar para
trabalhar cara porque mercado tem cara o
mercado tem muito e às vezes a gente
fica montando e é um é uma crítica
minha muita muita muita gente assim às
vezes eu crio eu crio produto para para
iguais então por exemplo Micael eu eu eu
eu eu posso montar um sistema de bot
para você pensando em você a Persona é
você a Persona é o Duarte a Persona é o
Antônio eu eu tenho cara um nicho muito
pequeno para trabalhar ou eu posso
montar um sistema pro cliente final e aí
eu tenho milhões e milhares milhares e
milhões de pessoas para poder trabalhar
então às vezes cara o conhecimento do
cara ele fica só circulando entre as
entre pessoas iguais e o cara não vende
o cara não escala sendo que às vezes
cara ele consegue montar algo por causa
da padaria comprar E aí cara 1 padaria
compra se for um preço legal um negócio
um negócio interessante o cara compra
cara é só ele fazer a aa a aula de hoje
né que é audiência e Aida é só entender
como é que é esse processo então assim
esse esse esse treinamento é esse é esse
bate-papo sobre isso entendeu Não porque
daí a gente consegue fazer essa
sequência de ficar toda quarta-feira
conversando batendo um papo sobre isso
daí Ah Umas Du hor conversar ali em
relação ao preço né Luiz gravado
gravado em relação ao preço eu sofri com
isso também um bom tempo eu achava que o
preço que eu tinha que cobrar
eh era proporcional ou ao tempo que eu
levaria para
executar ou ao trabalho que aquilo me
daria né Não não só o tempo mas o
trabalho que aquilo me
daria e na verdade é o contrário né o
preço do produto ele tá mais relacionado
a dor que você resolve do cliente o
tamanho da dor ou o tamanho da vantagem
que ele vai ter depois que o produto
tiver pronto que ele tiver executando é
o que você falou você cria um bote pro
cara o cara consegue com aquele bote ter
um ento melhor do que ele tinha com três
atendentes e ele não vai mais precisar
dessas três atendentes o justo é que
você cobre pelo menos o que seria o
salário de uma delas né pelo menos
isso
gente eu queria eu queria ouvir de vocês
o que que vocês acham da grade aí
relacionada ao preço que eu vi muita
gente comentando lá no grupo né e e e
assim agora que vocês estão vendo a
grade aí ão
eh estão vendo o conteúdo que a gente
vai fazer o que que
tem lembrar lembrar que é o básico né a
gente falou do básico tá gente o a gente
a gente ainda vai mais pra frente do
avançado Esse é só o básico tá que são
esses são esses dois próximos mêses tá
gente então outubro novembro uma
partezinha de Dezembro porque a gente tá
no meio a gente começa no meio de
outubro acaba no meio de Dezembro né
então são na prática dois meses eh a
gente vai ficar nesse nesse nessa tudo
isso aqui que vocês viram que é o básico
fundamento para daí quando chegar em
janeiro a gente começar no avançad já
até lá todo mundo já se alinhou o o que
nunca viu já vai entender o que já viu
já se atualizou né Tá todo mundo no
mesmo isso todo mundo no no mesmo nível
no mesmo barco ali PR a gente poder aí
aí o Eron aí a criança vai chorar a mãe
a mãe não vai ver né Eron eh e e assim
vocês que estão aqui tem quase 90
pessoas né na na sala e eu tenho que
dizer para vocês eh não perdam tempo
comprem porque o seguinte esse preço não
existe curso desse preç comentário do
chat no chat aqui ó tá barato pessoal a
gente falou na Live ontem né é um preço
de lançamento é um preço de lançamento
até acabar essa fase aqui de Eu Acredito
que até o final de novembro a gente vai
manter esse preço de lançamento depois a
gente vai ter que subir pessoal porque e
vocês é bastante coisa mesmo entendeu
não bastante coisa mesmo e e é um e é um
preço pessoal PR vocês a gente a gente
focou né Emerson em em em montar montar
uma galera mais ponta firme e geralmente
pessoal essa fase inicial vai vir muita
gente que já segue a gente então também
é um preço legal para vocês poderem
começar que já conhec a gente tudo né
mas vai subir tá gente né até porque os
cursos do mercado néon são muito mais
caros que isso daí mas a gente a gente
tá começando bom para todo mundo bom pra
gente bom para vocês mas né fica esperto
fica esperto
vai fazer o oposto do mercado né Luiz
gente vai fazer oposto do mercado é não
é sendo mal é porque não faz sentido
você ter um curso com esse tanto de
conteúdo Por esse preço então vocês
estão pegando no começo não não é
estratégia de marketing isso aqui é
porque é um fato vocês podem medir podem
comparar com qualquer um outro curso de
de qualquer um que tiver do Zé da
esquina que não que não vai ter desse
conteúdo então tipo assim não não não
não faz sentido manter esse preço pro
conteúdo que a gente vai ter então e eh
eu eu já vi gente chorando lá no grupo
então quero você então
compre porque não vai manter esse preço
e fora que que vocês eh vão ser eh
divulgadores para outras pessoas que
também vão comprar o curso né tá com
dúvida né ajuda ajuda bastante gente
também com isso daí né emon sim a até
que vocês divulguem né esse curso Olha
gente o curso tá Tá legal né então vocês
vocês vão pegar a nata da da do suporte
a nata do do do do começo ali do vocês
vão ajudar a gente a definir como vai
ser os próximos anos desse curso Ô Lu
fazer parte da história da promov web e
de novo né assim entender pessoal que o
Emerson Ele veio para poder e realmente
fazer parte O Edson também tá aí né para
fazer parte dessa área de cursos né pra
gente poder tocar isso aí junto Porque
por isso para vocês assim o pessoal acha
Emerson e e eles estão certo de achar
que assim tipo peguei um freelancer sabe
peguei um freelancer para poder fazer o
negócio ali não é assim tá gente não é
essa a ideia tá eh é uma coisa que a
gente conversou bastante né conversou
bastante sobre isso daí então assim o
hermerson tem todo interesse do mundo em
fazer curso eu falei Erson já tem a
comunidade ali cara vamos juntar força
PR acho que junto a gente consegue
atender muito mais pessoas do que cada
um sozinho o mesmo falo pro Edson out
outras pessoas também que eu vou
convidar para poder fazer outros tipos
de conteúdo que eu eu acho que assim
Emerson a gente soma mesmo ento soma
mesmo porque é uma maneira que a gente
tem cara de um um um auxiliar o outro
também até nós nós eu você o Edson mesmo
assim mas a comunidade ganha demais com
isso cara porque os caras teria que
comprar três cursos ia comprar o meu
comprar o seu ia comprar o do Edson né
então assim a gente tá ajudando nisso né
pessoal e outra tá pessoal um outro
ponto que é importantíssimo de falar com
vocês aqui esse da promov web é um
produto tá o que o Emerson tem no pro
dele é um outro produto e eu acredito
que seja até complementar tá gente então
e não é porque você tem um que você não
vai querer o outro tá E e são são duas
abordagens diferentes esta promov web é
uma coisa entre eu e ele tá o da o pro
dele é uma coisa do do Emerson com a
comunidade dele então lá não tem curso
né Emerson lá ele faz uma mentoria eh em
cima do os fluxos que estão ali não é
isso isso então assim complementa demais
que vão fazer aqui não misturem as
situações o pro isso é é muito bom
porque você vai ter acesso a mim né você
vai poder me chamar atenção lá eu tô com
essa e essa situação aqui então o pro
ele é algo mais pessoal né Ele é mais
resolução de de de problema resolução de
situação ele é eh eh ele não é um um um
estudo né ele vai te ajudar então assim
são duas situações diferentes Tá sim e e
se quiser contratar as duas eu fico
grato né né Luiz um complemento é pegar
os dois é qu falei para vocês pessoal lá
é legal porque assim lá tem uma outra
comunidade tem uma é uma outra rotina
pessoal então eu eu eu acredito Emerson
e eu já Conversei bastante com a galera
sobre isso daí o um curso não viabiliza
o outro eu acho que a gente tem que
fazer cara o maior não possível de coisa
mesmo porque o mercado ele gira muito
rápido el anda muito rápido então é
legal pessoal assim eu acho que eu acho
que compensa por isso que é a gente a
gente combinou de não existir um
monopólio em cima disso né falar ah não
o o Erson é exclusivo agora não ele toca
lá ele o Erson faz consultoria pessoal
oon tem uma empresa que vende bot que
vende implementação de bot tudo isso
continua rodando eu também tenho aqui a
promov web né para quem não pegar esse
módulo com ia para quem tem o nosso
módulo clássico continua normal eu vou
continuar colocando curso também ali né
A diferença é que esse esse cliente
acessa mais coisas mas o acessa o ia a
mais Mas eu continuo mantendo outro
produto esse curso de empreendedorismo é
desse promovo web que o cara de a ganha
acesso mas o cara que não é da ia também
tem acesso então assim é é é fazer isso
para facilitar vídeo vocês e dar opções
para vocês tá de Ah luí não quero
aprender ia ou já aprendo I Beleza cara
você tem você pode ir pro Emerson você
pode ir para outra comunidade Mas aqui
tem docker aqui tem n aent um monte de
coisa para vocês poder aprender ainda né
e eu acho que é isso né Emerson não sei
se alguém mais tem algum comentário
pessoal o Elton ia falar pode falar
Elton é eu acho que é isso se ninguém
tiver mais nenhuma
pergunta além de mim tem o outro Bruno
também mas queria falar é que eu acho
que eles erraram na comunicação porque o
luí é é vítima dele mesmo os seus cursos
aí não tem ninguém na internet que faça
algo parecido com o que você faz sim a
gente consegue consumir ali o grátis e
dali você tem certeza que você vai
comprar o curso e vai ser muito bom
então quando vocês começaram a divulgar
lá aí veio com uma qualidade de áudio
ruim na primeira Live aí depois veio
apresentando uns workflows que depois no
curso na no grupo falou que workflow não
faz parte e vai ser construído no meio
do caminho então a comunicação ali ficou
embolada E isso gerou esse tanto de
dúvida esse tanto de questões agora hoje
aqui desse bate-papo que você falou você
vai fazer parte dessas aulas com ele vão
ser gravadas juntos então a gente sabe
que vai ter a mesma qualidade da promov
web porque especial lista começou a
aparecer de de caixo falando que é
especialista em a mas o Luiz é
Embaixador de um monte de marca de de
empresa grande e não fica gritando isso
a cada segundo para tentar provar que é
o melhor então o Luiz acho que ele virou
o problema dele foi ele ser tão bom E aí
algumas coisas apareceram e Acabaram
dando esse medo pra gente Eu por exemplo
eu tava esperando curso de a do luí não
comprei de nenhum outro lugar que
apareceu porque eu queria aprender com o
melhor e queria aprender com a didática
do luí aí me deixou muito na dúvida
porque passou a insegurança ali nas
apresentações que vocês fizeram agora
com essa Live de hoje ficou muito mais
esclarecido e muito mais fácil de
entender e ter certeza que vai ser um
produto de ótima qualidade e e ali ali
também tem tem uma questão também el que
assim a gente a gente quando quando eu
eu que procurei o Emerson né assim eu
que fui atrás do Emerson porque eu
conversei com algumas pessoas gente me
indica alguém porque o nhn já tem o nhn
tem uma a gente tem um notion lá que tem
o radar de de influen
aí eu peguei e olhei assim falei cara
mas eu não quero influenciador porque
influenciador eu é que você falou eu sou
vítima de mim muita coisa o Fabrício tá
rind ele briga muito comigo isso aí
porque o meu maior defeito é esse mesmo
de não falar as coisas o Fabrício hoje T
me enchendo o saco cara por causa disso
daí Aí eu peguei não eu quero alguém
cara que trabalhe com isso porque sabe
por porque assim a gente que vive mais
no conteúdo eu trabalhei muitos e muitos
e muitos anos com isso e eu ainda
trabalho com isso mas vamos sup tem
algumas áreas que cara você vai ter
pessoas que se destacam mais e o cara
vive aquilo que é o caso do Emerson o
Emerson ele vive a eu sei bastante coisa
de a mas e existe essa diferença de eu
saber o i a e o cara que vive a ia
entendeu não então eu acho que juntar os
dois juntar eu que tenho uma cabeça mais
professoral eu que tenho uma cabeça mais
de de querer entender as coisas e os
caras o Emerson que tem a cabeça do dia
a di da vivência dos truques ele sabe de
coisas que eu não sei cara porque ele
vive nesse dia a dia do mesmo modo que
eu vou vou trazer coisas para ele que
talvez não faça parte do dia a dia dele
então foi juntar esses dois para poder
fazer isso daí né e o Edson né O Edson é
o nosso é o nosso mentor Geral de tudo
aqui ele que fala assim ó gente tá
viajando muito gente tá ruim gente tem
que melhorar esse é o é é o pai da gente
aqui sabe a gente só fica dando uns
toques a oreia fica fervendo Às vezes
aqui assim sabe e realmente né Welton
ontem a gente eh a gente queria falar
mais na Live mas a Live foi muito legal
cara daí aí foi deixando foi deixando
deu problema no YouTube e Aí eu f fiquei
meio [ __ ] cara e agora né que que eu faço
né eu tô com mais gente no YouTube do
que do que no meet aí esqueci mesmo cara
a gente a gente começou a falar e E no
meio da Live caiu o WhatsApp do Emerson
e a gente F falou e agora cara até para
responder vai ser ruim então falei assim
B vamos marcar uma live amanhã que daí a
gente fala só sobre isso a gente põe uns
pingos nos vi ali né a gente tá anunciou
na Live ontem que ia ter essa Live de
hoje né mas daí Aí eu falei assim ó
vamos conversar daí a gente faz certinho
faz só isso porque daí a gente conversa
direto com eles e explica certinho esse
que era para est na Live de ontem não
entrou mas hoje a gente fez
endeu eu
qued eu apreendi que não dá para
misturar o assunto né ass o dia que for
Live é Live cara não tem como aar aqui
nós estamos aqui ó a uma hora e quanto
aqui nós estamos aqui ó Hora 2 hor não
tinha como fazer Live ontem não e eu
quero pedir para vocês que ouviram tem
tem aqui quase 90 pessoas né que
divulguem nos outros grupos né que fique
alguém que diga alguma coisa diferente
do que a gente explicou aqui dentro tá
eh que esclareça PR as pessoas porque a
gente não consegue chegar até todos mas
a gente sabe que vocês estão em vários
lugares né então passa informação de
manentes sim
é então passa essa informação certinho
né explique pro pras outras pessoas né
não o pessoal falou alguma coisa
diferente não não é bem assim não mas
vai ficar mais caro se comprar não não
vai porque Foi explicado assim na na
chamada tal né então Ó o curso vai vai
valer a pena alguém como falar de algum
curso lá olha o Luiz tá lançando um
curso de a ajuda a gente Ness uma olhada
sabe por Emerson até uma até importante
é outra puxada de orelia que eu recebo
diariamente de todo mundo mas é eu estou
trabalhando isso daí pessoal o meu
marketing ele não é ativo então não fico
gente entra no grupo aqui ó gente vamos
comprar para MOV web gente vamos assinar
a gente não faz assim a gente o cara
entra faz o gratuito ele conhece ele
assiste bastante Live faç muita coisa no
YouTube eu respondo na medida do
possível a galera então é muito no
orgânico sabe Emerson então a gente
precisa que vocês ajudem vocês indiquem
também pessoal Porque quanto mais
pessoas tiver melhor não não pra gente
mas para vocês mesmo porque é mais
pessoas no bate-papo é mais pessoa
envolvida e e na promovo we pessoal até
vou vou mostrar para vocês que a
comunidade a ideia ideia mesmo é é
montar esse Hub de pessoas interessadas
né porque aqui ó muita gente faz negócio
muita gente vira sócio muita gente vira
parceiro de de compartilhar serviço
troca serviço então a gente tem essa
essa essa é a ideia né não é só mais o
conteúdo é também o o lugar né aqui
pessoal é a nossa comunidade a gente usa
o circo né que é muito famoso aí nas
comunidades né e aqui tem né A parte de
bate-papo a parte de vídeos aos
pouquinhos eu tô migrando todo o
conteúdo do site para cá então eh a
gente vai trocar o site também ao longo
desse mês então vai ficar tudo aqui
mesmo e no site vai ficar só o
instalador o instalador fica lá a gente
vai criar um instalador vai eu vou criar
mais ferramentas na na na plataforma
também mas assim a parte de curso de
vídeo vai vir tudinho para cá tá aqui
tem a parte de suporte de ajuda que
vocês e vocês podem abrir ticket sobre
sobre docker sobre NN né tem ess essas
formações que são as as formações
tradicionais nossas né a gente lançou t
o de MK hoje de manhã então tá aqui o de
MK ó tá aqui os o curso de mtic também e
tem a área de downloads né Ó que você
vai poder fazer o download de stec isso
aqui pessoal é do curso tradicional do
curso geral e aí tem aqui o do Y makers
que eu tô montando ainda aqui tá já tem
um bate-papo com o pessoal entrou aqui
at vai ter o fórum para vocês poder
postar dúvida também tudo vai ter uma
área para vocês publicarem e vai ter
área de download também e a de curto n
Então são serão CCO falando em download
eu prometi pro pessoal que eu ia eh
passar alguma coisa para eles aqui né na
na na na chamada né e mand o cara pediu
lá umas pediu alguns flor né disse olha
Manda uns cinco flor lá pra gente ficar
feliz sim aí ó já tem aí seis Flow para
vocês aí tá já tem seis Flow básico aí
para vocês ir testando lá no publicar
ali os Flow tentando vendo como que é e
tal que V vão ser alguns que a gente vai
construir lá né então
Eh Vocês já vão poder ir vendo e são
fluxos de estudo mas que também
funcionam não é Erson a ideia Nossa
pessoal é assim o que tá ali é para
ajudar vocês a a entenderem o conteúdo
né e e mas coisas úteis que funcionam tá
não é só um exemplo lá que não vai
servir para nada né é trazer um
hermerson ele compartilhou ali eu vou eu
devo publicar entre hoje e amanhã tá
pessoal que hoje tem mais uma live à
noite ainda mas eu vou publicar mas são
fluxos reais que ele usa né não é um
negócio não é um teste não é um A ideia
é trazer mesmo esses esses itens ainda é
na categoria básica mas mas assim eu eu
eu gostei achei legal assim dá para
fazer bastante coisa básico mais
funcional né já teve uma dúvida aí de de
um aluno no no chat aí Luiz que ele
perguntou como seria a dinâmica do do
curso né Eu falei que seria um aulas na
pegada que tá a promov web agora aulas
curtas objetivas direto ao ponto
E que provavelmente ao final do primeiro
módulo já eh quando for ter ali a a
parte mostrar na prática né a pessoa já
vai sair conseguindo fazer alguma coisa
então se ele quiser antecipar isso até
antes do curso pega esse workflow que
vai ser liberado aí e já tenta fazer
alguma coisa e se por acaso tiver dúvida
use o o chat ali com o pessoal que a
gente acaba respondendo ali eu respondo
o Erson responde aí acaba ajudando se
tiver alguma dúvida a gente já antecipa
não que servir Como suporte porque o
curso ainda não começou mas se for
alguma dica a gente já consegue ajudar
ali o o ru o Ruben falou que ele quer
falar com alguém que não seja o Edson o
ru ali pessoal outra coisa também
pessoal não sei se ela tá aqui ainda
Cadê eu vi ela aqui mas acho que ela
saiu hein Deixa eu ver a Sarinha pessoal
não é um botte tá depois tem o Bruno aí
viu Luiz que ele tá um tempinho com a
mão levantada Ah tá a a Sarinha pessoal
não é um bote tá a Sarinha ela travar a
gente que já tem 3 anos já
bot tá debutando hoje isso fazendo
aniversário hoje depois contar parabéns
para ela o o Bruno Manda aí Bruno é
Bruno getter isso Bruno guter o Bruno na
programação a gente tem os getters né
que são os leitores de de método o meu
nome é meu nome é Eduardo Oliveira
Fonseca é eu é o fim do arquivo então
nós
tem dá a dupla aí é na na na faculdade
tinha me chamava de de getset lá dos
Gets das
programação mas a a a dúvida mesmo Luiz
é a parte que você comentou da extensão
da assinatura para quem já é aluno como
que fica essa parte porque já fica
recorrente para todo ano já descontar na
assinatura do cartão eu vou ver no
hotmart lá ô ô ô Bruno para poder dar
uma estendida nisso daí mas quando for
chegar próximo do período a comunicação
Nossa vai falar com você se não der No
pior dos casos cancela entendeu não a
renovação automática aí já mas assim o
nosso sistema aqui ele vai controlar
Entendeu essa só a dúvida mesmo é porque
assim a gente tem tem muito mais pessoas
entrando sem a o desconto do que com o
desconto entendeu não então acabou que
eu optei pelo pela renovação padrão E aí
eu controlo internamente aqui a
renovação de vocês não não sim fica mais
prático a outra dúvida eu tô com um
conhecido que tá querendo entrar agora
também na comunidade daí ele tava vendo
se ele pegava a assinatura padrão e
depois fazer o upgrade ou se ele já pega
a assinatura completa p aluma diferença
nas
duaso tá esse esse esse upgrade Bruno
ele vai valer por um tempo só uhum
porque até por diversas questões aqui
até de de de do hotmart lá mesmo sabe
Então assim eh quem quiser fazer o ia
cara seria melhor já pegar o ia já
entendeu não até porque pro Futuro Bruno
mais para frente a ideia é a gente a
gente ter já esse plano esse plano seu
principal né o com ia porque eu acho que
a gente vai que falei para você é
anuidade que a gente vai ficar um annic
falando isso aí cara a gente vai gravar
o curso ali por 2 3 meses por 4 meses
mas depois cara é é não não para porque
vai vir update vai vir novos modelos
novas maneiras novas ferramentas né
então então assim Acho que já já
compensa pegar já o ele e Avisa ele que
é preço de lançamento tá a gente vai
mudar esse preço aí até porque a gente
quer mesmo
eh fomentar a comunidade agora no início
ent ou não e sim é eu passei o contato
ali do do do chat que eu tava
conversando
para eu vou mudar o site hoje de novo tá
eu mudei o site ontem coloquei lá um
banner Zinho no final vou pôr um banner
Zinho No começo vai ficar mais fácil
porque tem que scrollar muito ali para
poder achar o é que assim é que mudou o
público sabe Bruno Hoje a gente vai
falar sobre isso também né mudou muito
um pouco mais mudou o público porque o
cara o o o fluxo padrão Pessoal vocês
estão entrando já querendo assinar isso
aí já é muito fora do meu do meu fluxo
normal o meu fluxo normal o que que é o
cara entrar ele ele ficar ali ele vai na
comunidade e algum momento ele vai
receber um e-mail pedindo para ele
assinar então assim eu e esse público
novo Bruno ele já quer Eh tá com a faca
queij na mão já entendeu não então eu
vou ter que atualizar o site para poder
ficar de acordo com
essa esse novo público né que eu tô
alcançando agora o cara que já tá muito
mais interessado mesmo em fechar já
assim sabe lá o Eric falou que já veio
para comprar isso aí Eric é um pouco
diferente do meu do meu modelo
tradicional de fazer então eu vou ter
que vocês me dão um tempo tá pessoal que
eu vou eu vou mexer no site tá mas é é
dando na mão e é muda muita coisa viu é
a brincadeira que eu fiz aqui eu recebi
uns quatro ou cinco print de cartão de
crédito aqui ó tá aí meu cartão já faz
minha assinatura eu falei Calma não tá
nem vendendo ainda o
fabr Fabrício o o o Fabrício entre entre
Cops e ubers ele fica pensando assim ó
[ __ ] cara o luí não mexeu na página
inicial dele ainda é pode deixar gente
eu vou chamar esse homem no canto nós
vamos resolver problema vamos nós vamos
resolver isso
daí pessoal mas é isso pessoal eh
agradeço vocês aí acho não sei se o
Emerson caiu ali que o Emerson Emerson
tá lá na casa do Del V Eu só não vou
perder a piada viu Luiz não vou perder a
piada antes de acabar a Live eu vou vou
falar aqui porque eh o pessoal tá
acostumado com a pegada da da black
friday né não vamos aguardar Novembro
porque vai ter desconto quem aguardar
novembro vai perder o
desconto ou compra esse mês que o luí já
deu aí a letra que ele acha que vai
estender o desconto até o final do mês
agora já não tem mais volta tá gravado
eh só que é até o final do mês Novembro
já não vai ter mais aí não adianta não
mas e a black friday não vai ter black
friday não eu achei engraçado o pessoal
até surpreso é Luiz Que estranho luí
dando desconto é uma coisa excepcional
mesmo ó TR anos de empresa hein pessoal
a gente fez a gente fez TR anos no meio
do ano é a primeira vez dar um desconto
cara
Aproveite primeira e talvez seja a
última S só uma questão aqui que eh o
Emerson não ficar triste apesar que eu
não fui uma das pessoas que perguntei a
respeito do do do conteúdo do curso né e
ele perguntou aí algumas vezes o pessoal
não comentou né Mas o conteúdo do curso
aí a gente olhou aí a a a ementa né do
curso né que foi apresentada aí e e
realmente esse início aí já me
surpreendeu Em vários pontos viu Luiz eh
dessa mesmo sendo um conteúdo Inicial
então deixa aí para o Emerson pode ver
depois aí que a ema do curso tá bacana
que é para ele não ficar triste que ele
foi bombardeado por causa disso lá dos
grupos né Beleza sim é que a ideia era a
gente fazer na Live ontem aí acabou que
não deu cara daí não vamos vamos vamos
arrumar isso daí para ficar o buraco da
comunicação lá entendeu não que sabe por
que gada é ruim cara que é um workflow
tão legal cara tão legal de ver ali que
você eu me perdi mesmo cara eu fiquei
vendo eu me perdi Olha que eu faço tem
tem o quê 7 anos cara que eu faço Live
que eu jcis as coisas eu fiquei ontem
que nem o menino sabe você fica só
olhando os cara falando assim ó aí eu
falei aí Aí perde perdeu tudo ô jais aí
aí monta monta uma ementa dessa pro
curso básico imagina o que que vem no
avançado no avançado né cara eu acho que
o povo lá do nhn lá se eles fic se eles
olhar um fluxo desses aqui eles vão
ficar
[Risadas]
doido é muita coisa é muita coisa é uma
é muito trabalho né para desenvolver né
cara e hoje vai avançado vai pegar a
garrafa de café e vai deixar do lado do
computador já é hoje vai ter aula do do
do empreendedorismo né Aí eu vou mandar
o link para vocês que hoje vai ser uma
aula aberta hoje semana que vem eu vou
fazer aberta eu ia fazer fechada mas vou
fazer aberto até para quem quer ver como
é que é uma aula mesmo assim tudo poder
entrar participar Mas vai ser um negócio
legal cara porque é um é um fundamento
de marketing né Fabrício falar de Aida e
eu vi lá o vídeo do IOR Adorei o vídeo
do IOR cara falando de Aida falei P cara
vou falar isso com os caras porque ali
ali tá o ponto chave desses caras diá
dess desse assunto assim dá para casar
muito bem uma coisa com a outra
assim pessoal Obrigadão viu Obrigado
mesmo por vocês Luiz tem uma mão
levantada do Luc paciência duas horas e
pouco conversando tem o Luca aí Oi tem o
Luca com a mão levantada aí Lu Opa Manda
aí Lucas Pode mandar Opa tão me ouvindo
Tô ouvindo Boa tarde pessoal só uma
dúvida aqui do pagamento Quem assinou
nesse preço na renovação continua esse
preço ou vai mudar vai ter a autorização
do próximo na renovação a gente vai
fazer por esse preço da entrada ainda
entendeu não ah fechou a gente vai fazer
eu sempre faço isso na próxima a gente
aumenta o mercado faz muito isso né Ô
Luiz só para não só para não gerar ruído
aí na comunicação eh se ele se ele já
era aluno e comprou com desconto na
renovação não é só o valor que ele pagou
agora isso iso é é do valor cheio é
valor cheio é não tranquilo
tranquilo valeu manda aí
Eric cara é o seguinte
eh eu sou gestor de tráfego tenho uma
sociedade aqui com com um amigo meu já
faz uns três anos
e eu tô entrando aí nessa área de
Inteligência Artificial beleza Eh já tem
algum conhecimento
de de integrações entre sistemas api
essas coisas até bumble também já
desenvolv alguns aplicativos e a
pergunta como se fosse para uma pessoa
tá começando do zero eu tô vendo aqui
que tem muita gente que já é
desenvolvedor tal eu começando do zero
agora eh imaginando isso em quanto tempo
daria para deixar uma uma sistema desse
no i poder vendê-lo isso aí éca é legal
porque assim ó é que vai depender muito
do que que você tá montando então por
exemplo assim ó vamos pegar é o que que
as que que as empresas precisam elas
precisam primeiro do básico
eh nesse nessas aulas de quarta-feira a
gente vai falar sobre isso né então por
exemplo Eric Vamos pegar uma um dentista
que que o dentista precisa na prática
escanear um QR Code no celular dele para
conectar na Evolution para poder ativar
ali uma secretária virtual que vai dar
bom dia boa tarde boa noite pra galera
no domingão vai vai conseguir fazer um
agendamento um menuzinho ali ó Ah eu
queria tô com dor no dente que eu quero
marcar um horário Olha tem disponível na
segunda-feira geralmente isso daí é que
você monta não é um negócio vamos vamos
colocar um prazo assim de duas semanas
vai em duas semanas você implementa essa
base tem aqui uma galera que monta até
em menos tempo entendeu não assim depois
que você pegar os fundamentos você
entender como é que funciona você esse
essa é uma média que eu gosto porque tem
muito teste eh não é uma coisa que você
mexe o dia inteiro cara porque eh você
tem outras coisas para você fazer então
eu calculo sempre umas duas semanas para
poder montar um
botzin isso aí Eric é bem replicável
entend não e isso que é o legal que eu
até comentei com eles né que eu acho que
o ia é bem é bem escalável no sentio de
SAS porque o ia é muito replicável então
um bot que você monta para uma empresa
cara se você montar ele bem bem modular
at viu na Live isso daí ontem você
replica em um dia você replica para três
quatro empresas igual o mesmo bote e faz
uma assinatura faz uma recorrência então
assim eu acho éca eu acho que assim
vamos colocar aí esse período agora de 2
TR meses de aprendizado porque ali a
gente a gente vai partir do zero mesmo
né então isso a minha pergunta era essa
partindo do zero a gente considera e a
gente a gente quer fazer isso daí cara
porque que que é o ruim de eu prometer
pro cara que ele vai conseguir fazer é
que eu ten eu preciso que ele parta do
zero com a gente entendeu não então eu
fico mais à vontade em falar assim ó
você monta em duas semanas depois que
você partir do zero e fizer o básico
porque ali não sei se chegou a
acompanhar aqui é um básico bem grande
entendeu não então assim e para esse
cara vamos supor lá em janeiro você
monta em duas semanas agora agora ainda
não e a ideia de oferecer para vocês
também os workflows prontos e ainda
nesse ainda sendo workflows básicos e do
Defy chain é para vocês poderem é que eu
vou dizer para vocês Assim vocês vocês
poderem já implementar alguma coisa e
testando isso aqui Eric é muito teste
muito teste cara porque você você vai
perceber que às vezes a palavra que você
usa o bot não entende aí você tem que
lar no promp melhorar o promp tratar
aquilo por exemplo ô Eric é nhn em
transcrição nenhuma funciona chat ut e
nenhuma funciona entendeu não então cara
é a já promp de bicho para poder
corrigir as palavrinhas para já vir
bonitinho para mim a transcrição isso aí
cara é fazendo cada vídeo que eu subo
Cara eu tenho que melhorar meu prompt
entendeu então assim é é a ideia a ideia
nessa nessa fase é mostrar também isso
daí você parte do básico vamos avançando
mas a ideia assim em em média ô ô Eric
um cara mais assim habituado a trabalhar
com isso daí cara duas semanas é um
tempo muito bom cara para ele montar um
negócio legal luí eu vou resolver um
problema para um cara só em uma semana
você monta mas assim aí fica menos
replicável entende entende a jogada eh
se eu fosse montar para mim Eu montaria
em dias mas eu mas assim se monto para
mim cara eu só vou eu vou gastar todo
esse tempo fazer para atender uma pessoa
só eu não cara eu vou eu vou gastar duas
semanas para montar um negócio que se o
eds gostar eu falo assim ó ô eds chove o
pix aí para poder eu eu passo para você
o workflow eu instalo para você o o o
sistema então assim é
é essa aqui vai ser uma uma das pegadas
que a gente vai passar er de tentar
fazer o mais replicável possível
entendeu para poder esse esses esses 15
dias investido vire Vire mais vendas
para você entendeu Não eu enxergo e
assim sabe é eu sou meio meio meio
bitolado nessa coisa de SAS de de
replicar de reaproveitar entendeu então
eu enxergo muito o i assim
cara entendi entendi valeu e realmente
tem que ser né tudo o que a gente faz
aqui também na agência tem que ser
replicável para agilizar o tempo ou ou
você vende muito caro para valer o tempo
ou você vende mais em conta e vende seis
vezes 10 vezes entendendo não essa é a
regra
né pessoal é
isso show de bola Luiz show de bola
obrigado viu pessoal obrigado mesmo eu
vou eu vou enviar o link para vocês da
aula de da aula da noite aí também todo
mundo entra de novo pra gente poder
conversar aqui vai ser o negócio uma
aula bem legal aí é mais de marketing
mas mas vai se aplicar com um monte de
coisa vocês vão gostar de da gente
conversar aí vou brigar muito com vocês
hojeo Hã Eu recomendo Luiz recomendo
você fazer o seguinte eu sei que você
vai já subir esse vídeo lá né Vai
publicar o vídeo isso vou pô no YouTube
é depois que você colocar no YouTube
tiver o link do vídeo você libera o
grupo o grupo para para pergun para
mensagens de novo porque senão o pessoal
vai continuar quem não assistiu a Live
ISO isso eu vou fazer perguntando as
mesmas coisas porque aí depois quando
tiver lá quem perguntar a gente manda
assistir o vídeo sim daí a gente elimina
o ruído lá
também valeu valeu gente obrigado viu P
Valeu um abraço hein l