#!/usr/bin/env python3
"""
Sistema de Extração TJSP - Script Principal v2.0
Executa a extração completa de precatórios do TJSP com estrutura profissional

Uso:
    python src/sistema_principal.py
"""

import os
import sys
import logging
import shutil
from pathlib import Path

# Adicionar o diretório pai ao path para importações
sys.path.append(str(Path(__file__).parent.parent))

from src.extrator_tjsp import ExtratorTJSP

def setup_logging():
    """Configurar logging para o sistema"""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_dir / "sistema_tjsp.log", encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def verificar_estrutura():
    """Verificar se a estrutura de diretórios está correta"""
    dirs_necessarios = [
        "data/input",
        "data/output", 
        "logs",
        "src"
    ]
    
    for dir_path in dirs_necessarios:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
    
    # Verificar se há PDFs para processar
    input_dir = Path("data/input")
    pdfs = list(input_dir.glob("*.pdf"))
    
    if not pdfs:
        logging.warning(f"Nenhum PDF encontrado em {input_dir}")
        logging.info("Copiando PDFs de downloads_completos se existir...")
        
        # Tentar copiar de downloads_completos se existir
        old_dir = Path("downloads_completos")
        if old_dir.exists():
            for pdf in old_dir.glob("*.pdf"):
                shutil.copy2(pdf, input_dir)
            logging.info(f"Copiados {len(list(old_dir.glob('*.pdf')))} PDFs")
    
    return len(list(input_dir.glob("*.pdf")))

def main():
    """Função principal do sistema"""
    print("🚀 Sistema de Extração TJSP v2.0")
    print("=" * 50)
    
    # Configurar logging
    setup_logging()
    
    # Verificar estrutura
    num_pdfs = verificar_estrutura()
    logging.info(f"📊 PDFs disponíveis para processamento: {num_pdfs}")
    
    if num_pdfs == 0:
        logging.error("❌ Nenhum PDF encontrado para processar!")
        logging.info("💡 Coloque os PDFs na pasta data/input/")
        return False
    
    # Configurações
    PASTA_PDFS = "data/input"
    ARQUIVO_SAIDA = "data/output/TJSP_PRECATORIOS_EXTRAIDOS.xlsx"
    
    try:
        # Criar extrator
        logging.info("🔧 Inicializando extrator TJSP...")
        extrator = ExtratorTJSP(PASTA_PDFS, ARQUIVO_SAIDA)
        
        # Processar PDFs
        logging.info("📋 Iniciando processamento de PDFs...")
        sucesso_processamento = extrator.processar_pdfs(
            limite=None,  # Processar todos
            debug=False,
            modo_incremental=True  # Usar modo incremental
        )
        
        if sucesso_processamento:
            # Gerar planilha Excel
            logging.info("📊 Gerando planilha Excel...")
            sucesso_excel = extrator.gerar_planilha_excel()
            
            if sucesso_excel:
                logging.info("✅ Sistema executado com sucesso!")
                logging.info(f"📄 Resultado salvo em: {ARQUIVO_SAIDA}")
                return True
            else:
                logging.error("❌ Erro ao gerar planilha Excel")
                return False
        else:
            logging.error("❌ Erro no processamento de PDFs")
            return False
            
    except Exception as e:
        logging.error(f"❌ Erro crítico: {e}")
        return False

if __name__ == "__main__":
    sucesso = main()
    if not sucesso:
        sys.exit(1)
    
    print("\n🎉 Processamento concluído!")
    print("📊 Verifique os resultados em data/output/")
    print("📋 Logs disponíveis em logs/")
