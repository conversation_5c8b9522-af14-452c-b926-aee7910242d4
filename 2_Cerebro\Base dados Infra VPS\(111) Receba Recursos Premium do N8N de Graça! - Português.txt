﻿Olá pessoal tudo joia aqui o Luiz nesse vídeo  quero bater um papo com vocês sobre um recurso  
chamado Community Plus que acabou de chegar no  N8N tem em torno de umas duas semanas e agora que  
eles lançaram oficialmente uma Landing page uma  página eu posso vir aqui e fazer vídeo e contar  
para vocês sobre esse recurso tá pessoal community  Plus ele vai desbloquear para vocês três recursos  
para vocês utilizarem da versão enterprise  no seu n8n self hosted só que aqui pessoal  
eu tenho que já tirar uma confusão na cabeça de  vocês a versão enterprise do n é uma versão paga  
caríssima você pode utilizar tanto no modelo SAAS  quanto no modelo enterprise no modelo self hosted  
tá bom essa licença pessoal do community Plus  ela não vai te dar a licença enterprise ela vai  
desbloquear somente por enquanto na gravação aqui  desse vídeo estamos aqui em novembro de 2024 três  
recursos da versão enterprise desses três recursos  pessoal dois são extremamente legais um recurso eu  
não vejo muita aplicação Eu juro que eu tentei  achar até para poder gravar o vídeo aqui achar  
algum tipo de aplicabilidade ali para ele eu não  Realmente eu não encontrei tá gente mas dois são  
extremamente legais para você poder aplicar  no seu dia a dia um outro ponto pessoal que  
eu tenho que trazer para vocês como Embaixador  minha função é trazer clareza para vocês sobre o  
n8n sobre a licença sobre os lançamentos que eles  fazem né um ponto importante pessoal é que esse  
programa O community Plus  não tem ainda  o nome definido Alguns chamam de community  
Plus outros chamam de community Edition ainda não  ficou muito claro esse programa pessoal ele Visa  
registrar o seu N8N self hosted para poder te dar  acesso a esses três recursos tá E também pessoal é  
uma maneira do n8n poder conhecer melhor um pouco  as suas ações a licença pessoal é enviada para  
você por e-mail em tempo real você vai abrir  a interface eu vou fazer com vocês aqui esse  
passo a passo você pede a licença ele encaminha  no seu e-mail na hora você pega a licença copia  
e cola N8N ativou o recurso essa licença pessoal  ela só vale para uma instalação então você que  
tem aí vários clientes você vai ter que gerar uma  licença para cada uma das da suas instalações tá  
esse é um ponto importante até o momento pessoal  eu não encontrei nenhuma limitação nenhuma qud  
ade de Quantas licenças uma pessoa pode ter é  claro que você pode utilizar um e-mail al um  
e-mail diferente para cada licença sem problema  também tá Outro ponto importante pessoal é que  
a qualquer momento eles podem descontinuar Esse  programa é ainda é um programa em fase Beta vamos  
dizer assim eles estão avaliando estão testando  é praticamente um MVP ali na mão deles tá só que  
tem um ponto importante que daqui a pouquinho  eu vou mostrar para vocês se o nhn encerrar  
esse esse programa community Plus você que tem a  sua licença continua utilizando Então pessoal se  
apressa e pega a sua licença eu não vejo motivo  pro nhn eh descontinuar esse programa Mas eles  
que são donos eles que decidem eu só tenho aqui  acesso a algumas informações que eu posso trazer  
para vocês tá gente Então vale a pena pega aí as  instalações que vocês têm configura esse community  
Plus vocês vão ver aqui que é são duas três etapas  é rapidinho para fazer isso daí tá E vale muito a  
pena tá gente pessoal aqui no site do neen eles  lançaram né agora na há dois dias atrás essa  
página aqui até peço desculpa pessoal por não ter  gravado esse vídeo antes que eu tô gripado acho  
que estão reparando que eu tô com a voz um pouco  rouco ainda mas basicamente aqui pessoal eles  
explicam a a licença então nós temos aqui a versão  community Edition a versão community Edition  
pessoal é que vocês usam que eu uso também aí  no o famoso self hosted né então e também existe  
a versão enterprise que é uma versão paga ele  pega esse mesmo N8N que a gente usa e desbloquei  
um montão de recurso legal ali nele tá esse  community Plus pessoal eles estão chamando aqui  
de edição de community registrada ele vai ele vai  desbloquear para vocês o debug em editor isso aqui  
é muito legal porque vocês vão poder pegar uma  execução alguma execução do histórico e carregar  
ela no editor a execução inteira como se fosse  informações pinadas então quando você for executar  
tá pronto né Outro ponto pessoal é você vai poder  ter um work um histórico de workflow aqui pessoal  
ó ele fala que a gente vai ter um dia de histórico  de workflow já é alguma coisa pelo menos no que  
você tá trabalhando ali já vai quebrar muito  seu galho aqui na promov pessoal no nosso curso  
lá na nossa comunidade tem um módulo aonde eu  ensino você a fazer o backup no Git tá então  
você vai fazer um backup de hora em hora de  todos os workflows que foram modificados cada  
modificação vira um comit lá no num repositório  se do Git fica perfeito você pode configurar para  
seu para todos os seus clientes é mais legal do  que esse porque esse daqui são só 24 horas de de  
de histórico nesse nesse do nosso treinamento  não ali no nosso curso eu mostro esse módulo  
eu mostro vai ficar fixo lá e eu tenho pessoal  mais de um ano de histórico dos meus workflows  
é interessante para você poder acompanhar aí a a  evolução e vai ter esse Custom data execution aqui  
pessoal que esse que eu falei para vocês que eu  não vi muita aplicabilidade nele né então você vai  
poder buscar tem um node né que chama Custom data  você vai poder buscar do histórico de execuções  
informações para poder colocar ali com certeza  alguém vai achar algum uso aqui mas eu vou focar  
com vocês nos dois que são os mais legais que é o  debug em editor e o workflow History né assiste o  
vídeo até o final que eu vou mostrar para vocês  mais informações sobre esses dois componentes  
pessoal aqui ele fala para vocês né então é muito  bom a gente deixar bem claro isso né que uma vez  
que você ativou a sua licença do community Plus  né ela nunca Vai expirar né e e eles podem e  
modificar quais são os recursos que estarão  disponíveis ali e não vai impactar licenças  
anteriores então se você hoje for lá e configurar  vamos supor que semana que vem eles vêm e acaba  
com community Plus você continua com o seu a Chin  aí tranquilinho rodando ele tá gente então é bom  
deixar claro isso daqui para que não Gere confusão  Então vamos vamos recapitular aqui pessoal o ideal  
é você gerar uma uma licença por instalação Outro  ponto ideal é você gerar uma licença por e-mail tá  
não tem a informação pessoal quem quiser testar  depois comenta aqui caso você consiga gerar mais  
licenças por e-mail Tá bom acredito com o tempo  eles possam colocar um limite de licenças que um  
e-mail pode ter então cria aí um um um licença  de e-mail diferente acho que é mais prático se  
o NN parar de brincar com isso daí você não perde  os recursos que você habilitou tá E isso não vai  
ser uma versão enterprise continua sendo a versão  community com esses três recursos da enterprise  
habilitado para vocês tá então aqui pessoal o que  que vocês vão fazer olha como que é extremamente  
simples habilitar isso daí você tem que ter o Nate  n pess pelo menos da versão 1 P 63 para cima tá  
163 para cima é o ideal esse meu aqui pessoal  como eu uso para teste então eu tô aqui com a  
versão é 1.66 né que é o último o último release  aí deles Pessoal vocês vão vir aqui Ó nesses três  
pontinhos vocês vão vir aqui em settings lá em  cima o primeiro menu uso e plano vai aparecer  
para você pessoal esse ícone aqui ó se isso aqui  não aparecer para vocês Vocês precisam atualizar  
o seu nhn para que possa aparecer isso daí tá bom  eu vou clicar aqui no unlock Então vou vir aqui ó  
unlock Luiz cliquei no unlock vai aparecer aqui  ó adquira e recursos da versão paga para sempre  
tá entre parente forever al para vocês verem  né Ó então workflow History Advanced debugging  
e execution search tagging aqui você vai poder  colocar o e-mail você pode ver pessoal que ele  
vai pegar o e-mail lá do admin da sua instalação  né então o eu estou com o usuário admin aqui  
o usuário é esse meu C digital @gmail aqui tá  bom luí posso vir aqui mudar o e-mail tranquilo  
pessoal pode mudar o e-mail sim sem problema  nenhum caso você tenha ali aliases caso você use  
alguma regrinha de algum outro e-mail que você  tem para quem usa e-mail do clou flir também é  
legal né aí pessoal você vai clicar aqui ó envie  a licença para mim então quando você clicar aqui  
ele vai pegar ele vai enviar para você a licença  tá então essa licença pessoal vai pingar lá no  
no seu e-mail demora pessoal menos de um minuto  pode ver ali ó Acabou de pingar o e-mail no meu  
no meu no meu aqui em cima eu vou clicar aqui  no meu e-mail tá lá ó aqui está a sua licença  
então aqui ele dá a licença tá bom pessoal essa  Licença aqui tá eu vou eu vou poder vir aqui  
e eu vou poder clicar nesse botão activ activate  licens Key tá se eu clicar nele ele cai lá no meu  
no meu NN ó lá e tá ativado aqui ó minha o meu  NN agora está registrado tá bom então agora eu  
tenho essa esse community Plus registrado tá  bom Aqui no meu NN então simplesinho pessoal  
você poderia também clicar aqui nesse copiar a  sua licença tá E aqui na tela você poderia colar  
Tá bom então você poderia vir aqui ó enter  activation Key e você poderia colar aqui a  
licença ele também iria habilitar para você então  tanto faz você colar esse copiar e colar naquele  
Campo você clicar nesse botão aqui você  vai ativar do mesmo jeito Por isso que é  
uma licença individual por por instalação tá  não tem como ativar essa licença em uma outra  
instalação pessoal é só isso tá já estou com os  recursos aqui em mãos tá então eu vou criar aqui  
um workflow bem simplesinho bem didático só para  vocês verem o recurso do do History né então pode  
ver pessoal que eu já eu eu vou salvar aqui meu  workflow ó então eu vou mostrar aqui ó community  
Plus então mudei o nome dele eu vou vir aqui ó  vou colocar um customer dat T Store Eu uso esse  
esse eu gosto desse node né para poder usar como  treinamento pode ver pessoal que habilitou aqui  
para mim um Icone Zinho Dea só recarregar aqui  porque o meu eu utilizei uma outra tela então ele  
é que eu eu cliquei no botão e eu não recarreguei  o meu n Então vai vir aqui ó vai aparecer aqui lá  
em cima para vocês verem ó se eu clicar aqui  pessoal eu vou ter Ó o meu o meu workflow na  
versão zero eu vou ter o meu workflow sem nada a  primeira vez que eu salvei ele eu já vou ter aqui  
a versão latest já com o customer data Store Então  por 24 horas é bom que fique claro isso né por 24  
horas você vai poder vir aqui e colocar tá gente  ah luí eu quero e eu quero salvar esse histórico  
por mais tempo não dá pessoal aí eu recomendo que  você faça lá o nosso módulo na nossa comunidade  
tem lá o curso o último módulo que fica do nosso  curso NN é esse backup aí no Git é o modo que eu  
acho mais seguro tá gente totalmente gratuito  O Git Hub é gratuito você vai criar um workflow  
no seu en que vai fazer tudo fica certinho tá Ah  luí tem 200 workflows tranquilo faço pela linha de  
comando assiste lá pessoal assiste lá aquele curso  inteiro né o nosso curso NN vai desde o básico até  
o avançado e passa por essa parte de devops para  poder ajudar vocês tá quem quem se interessar você  
que não é aluno ainda tá perdendo tempo aí e  não é aluno ainda aqui na descrição do vídeo  
e também no comentário fixado tem o link para você  poder assinar a promov web e fazer parte da nossa  
comunidade tá gente então vem aqui ó eu vou pôr  aqui um no set tá vim aqui coloquei um set deixa  
eu mapear um campo um campo simples aqui deixa  eu rodar ele aqui ah vamos criar aqui um um uma  
um boleano boleano vou arrastar aqui o e-mail vou  usar aquela função né lá no nosso curso tem lá o  
módulo de expressões né para você poder expressões  e extensão de expressão para você poder aprender  
Esses códigos aqui do nateen Então beleza pessoal  ó vim aqui ó rodei aqui tá gente rodei coloquei  
aqui deixa eu incluir todos os campos vou dar um  play de novo aqui testar o workflow Olha só o meu  
histórico ó Conforme eu vou apertando salvar ele  vai girando para mim pessoal isso aqui é muito mão  
na massa pro dia a dia de uso da nhn lembrando  dura 24 horas mas beleza pessoal às vezes hoje  
aqui eu tô trabalhando agora aqui isso aqui é  muito útil para mim você segue um caminho Putz  
não gostei você vem aqui ó e volta para aquele  anterior você pode clicar aqui nesse botãozinho  
lateral você vai ter a opção restaurar então ele  volta o seu workflow para esse ponto você tem a  
opção ó de clonar para um novo workflow então você  pode pegar o workflow nesse ponto clona ele então  
você fica com esse que você já mexeu bastante e  pode clonar um novo workflow anterior pessoal para  
resolver o nosso problema aqui de de uso do NN né  eu posso abrir ele numa nova aba né então ele vai  
abrir uma nova aba desse work Flow para m ou eu  posso vir aqui e fazer um download para fazer um  
backup não sei o que for mais prático para vocês  mas pessoal ó no dia a dia de US NN isso aqui é  
maravilhoso pessoal maravilhoso ter isso daqui  eu acho que é um dos melhores um dos melhores  
recursos da NN é esse gerenciamento de versão aqui  tá então só fica claro para vocês que ele dura 24  
horas tá gente e aqui pessoal quero dar um outro  recado para vocês tá aqui na promov web você  
tem acesso à nossa comunidade tá uma comunidade  fantástica lá no Circle você tem acesso aos nossos  
treinamentos tá bom e também agora nós temos uma  segunda formação que é o ia makers então ali você  
também vai poder fazer um curso de a começando do  zero vamos abordar o Defy vamos abordar o nhn tá  
então você que quer conhecer um pouco mais sobre  a formação aqui na descrição tem um link para  
vocês conhecerem mais da formação do Y makers  tá gente pessoal então agora que vocês viram  
ess essa maravilha aqui do gerenciamento de  versão vamos pra outra Maravilha que eu falei  
para vocês aqui na aba execução pessoal eu posso  vir aqui agora eu tenho três execuções aqui ó eu  
poderia ter uma execução que gerou algum tipo de  erro enfim qualquer execução do histórico tanto  
em produção quanto desenvolvimento Olha que legal  pessoal eu posso vir aqui ó pegar essa execução  
apertar esse botão aqui ó copiar pro editor quando  eu apertar ele Ó lá que que o anan fez pessoal ele  
veio aqui ó e eu eu tô com esses dados agora no  node eu posso vir aqui e olhar node a node para  
poder corrigir alguma coisa isso aqui pessoal  é maravilhoso isso aqui é um recurso realmente  
Fantástico você poder pegar do um histórico uma  execução né e fala assim ah luí esse node aqui por  
exemplo meu Edit Fields tá com erro então agora  eu posso vir aqui ó com os dados do histórico e  
pinar ele se eu quiser posso vir aqui e pinar  ele então agora eu consigo pessoal imagina pro  
debug como que é prático isso porque você você  vai poder fazer esse debug super simples tá  
então qualquer informação do seu histórico aquele  errinho de ontem aquele errinho da semana passada  
que ainda tá no seu log aqui você fala nossa cara  como é que eu vou conseguir reproduzir esse erro  
de novo não precisa mais pessoal é só você clicar  no na execução que tá com erro alguma alguma algum  
problema ali copia pro editor ele vai falar que  já tem informações pinadas né eu vou despinar os  
nodes e ele vai lá e preenche os nodes para mim  E aí eu venho aqui e pino os nodes que eu quero  
né eu vou salvar a informação no node para poder  trabalhar com meu workflow pessoal isso aqui ó  
o versão e esse cara de você poder fazer esse  debug no editor isso aqui já vale muito a pena  
pessoal vale muito muito muito a pena tá de vocês  virem aqui e utilizar testa aí pessoal é de graça  
você vai lá entra você faz faz o desbloqueio você  vai receber o seu e-mail com a sua licença e vai  
ativar ela sem nenhum problema tá gente Pessoal  é isso tá então como Embaixador Tenho que trazer  
para vocês essas novidades essa daqui eu gostei  demais tá gente instala aí configura se tiver  
alguma dúvida pessoal comenta aqui que eu respondo  você se você é membro da promov web publica no  
fórum ou publica ali no nosso grupo do WhatsApp  aqui na descrição também vai ter o link do nosso  
grupo do WhatsApp para você poder entrar pessoal  o grupo ali só de profissionais pessoas que querem  
trabalhar ali tem iniciante ali tem gente avançada  o foco que que é pessoal não tem papo furado não  
tem briga nosso um grupo de um nível muito bom ali  para você poder entrar e fazer uma pergunta poder  
compartilhar poder ver a discussão que acontece aí  tá pessoal dá um like no vídeo comenta se inscreve  
no canal compartilha esse vídeo nas comunidades  que Vocês participam e até a próxima valeu pessoal