[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "webagent-social-extraction"
version = "2.0.0"
description = "Enterprise-Grade AI Solution for Social Media Analytics & Viral Prediction"
readme = "README.md"
license = {file = "LICENSE"}
authors = [
    {name = "WebAgent Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "WebAgent Team", email = "<EMAIL>"}
]
keywords = ["ai", "social-media", "analytics", "viral-prediction", "fastapi", "celery"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Internet :: WWW/HTTP :: Dynamic Content",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
requires-python = ">=3.11"
dependencies = [
    "fastapi>=0.104.1",
    "uvicorn[standard]>=0.24.0",
    "celery[redis]>=5.3.4",
    "redis>=5.0.1",
    "supabase>=2.3.0",
    "psycopg2-binary>=2.9.9",
    "sqlalchemy>=2.0.23",
    "google-generativeai>=0.3.2",
    "yt-dlp>=2023.12.30",
    "pydantic>=2.5.2",
    "pydantic-settings>=2.1.0",
    "python-dotenv>=1.0.0",
    "structlog>=23.2.0",
    "httpx>=0.25.2",
    "aiofiles>=23.2.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.12.0",
    "black>=23.11.0",
    "isort>=5.12.0",
    "flake8>=6.1.0",
    "mypy>=1.7.1",
    "pre-commit>=3.6.0",
]
test = [
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.12.0",
    "factory-boy>=3.3.0",
    "httpx>=0.25.2",
]
docs = [
    "mkdocs>=1.5.3",
    "mkdocs-material>=9.4.8",
    "mkdocs-mermaid2-plugin>=1.1.1",
]
monitoring = [
    "prometheus-client>=0.19.0",
    "sentry-sdk[fastapi]>=1.39.2",
]
media = [
    "opencv-python>=********",
    "pillow>=10.1.0",
    "ffmpeg-python>=0.2.0",
    "moviepy>=1.0.3",
    "librosa>=0.10.1",
]

[project.urls]
Homepage = "https://github.com/webagent/social-extraction"
Documentation = "https://webagent.github.io/social-extraction"
Repository = "https://github.com/webagent/social-extraction"
Issues = "https://github.com/webagent/social-extraction/issues"
Changelog = "https://github.com/webagent/social-extraction/blob/main/CHANGELOG.md"

[project.scripts]
webagent = "src.cli:main"
webagent-api = "src.api.main:start_server"
webagent-worker = "src.workers.main:start_worker"

[tool.setuptools.packages.find]
where = ["src"]
include = ["src*"]
exclude = ["tests*"]

[tool.setuptools.package-data]
"*" = ["*.yaml", "*.yml", "*.json", "*.toml"]

# ===== BLACK CONFIGURATION =====
[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

# ===== ISORT CONFIGURATION =====
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["src", "tests"]
known_third_party = ["fastapi", "celery", "redis", "supabase", "pydantic"]

# ===== MYPY CONFIGURATION =====
[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "yt_dlp.*",
    "instaloader.*",
    "tweepy.*",
    "cv2.*",
    "moviepy.*",
    "librosa.*",
]
ignore_missing_imports = true

# ===== PYTEST CONFIGURATION =====
[tool.pytest.ini_options]
minversion = "7.0"
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=src",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
    "--cov-fail-under=80",
]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "e2e: marks tests as end-to-end tests",
    "performance: marks tests as performance tests",
]

# ===== COVERAGE CONFIGURATION =====
[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/migrations/*",
    "*/venv/*",
    "*/.venv/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

# ===== FLAKE8 CONFIGURATION =====
[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503", "E501"]
exclude = [
    ".git",
    "__pycache__",
    "build",
    "dist",
    ".venv",
    "venv",
    ".eggs",
    "*.egg",
]
