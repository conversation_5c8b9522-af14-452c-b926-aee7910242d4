{"inicio_processamento": "2025-07-28T15:32:21.270377", "ultima_atualizacao": "2025-07-28T17:49:36.964426", "ultimo_consultado": "0001100-79.2010.8.26.0053", "consultados": {"0000968-44.1980.8.26.0224": {"status_consulta": "Iniciando consulta", "observacao": "Processo iniciado", "status_processo": "N/A", "numero_lista": 1, "timestamp": "2025-07-28T15:33:04.512724"}, "0000021-79.1986.8.26.0191": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 2, "timestamp": "2025-07-28T15:38:03.526637"}, "0001083-74.1987.8.26.0562": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 3, "timestamp": "2025-07-28T15:38:22.690331"}, "0000004-34.1982.8.26.0595": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 4, "timestamp": "2025-07-28T15:38:41.670982"}, "0401760-28.1998.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 5, "timestamp": "2025-07-28T15:38:57.715787"}, "0000004-78.1973.8.26.0068": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 6, "timestamp": "2025-07-28T15:39:13.751084"}, "0000018-05.1977.8.26.0462": {"status_consulta": "Consultado", "observacao": "Processo consultado - Status: final<PERSON><PERSON>, Requerente: <PERSON>, Partes: Sem partes proibidas, Palavras: Sem palavras proibidas", "status_processo": "finalizado", "numero_lista": 7, "timestamp": "2025-07-28T15:39:27.482143"}, "0019530-04.1998.8.26.0602": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 8, "timestamp": "2025-07-28T15:40:41.402620"}, "0418985-71.1992.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 9, "timestamp": "2025-07-28T15:40:58.407196"}, "0027870-48.1995.8.26.0114": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 10, "timestamp": "2025-07-28T15:41:14.932773"}, "0003395-60.2004.8.26.0066": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 11, "timestamp": "2025-07-28T15:41:30.848497"}, "0402355-90.1999.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 12, "timestamp": "2025-07-28T15:41:48.415530"}, "0000014-50.1978.8.26.0294": {"status_consulta": "Consultado", "observacao": "Processo consultado - Status: finalizado, Requerente: Departamento de Estradas e Rodagens de São Paulo, Partes: Com partes proibidas, Palavras: Com palavras proibidas", "status_processo": "finalizado", "numero_lista": 13, "timestamp": "2025-07-28T15:42:00.415124"}, "0000734-14.2001.8.26.0099": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 14, "timestamp": "2025-07-28T15:42:35.621793"}, "0076206-34.1979.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 15, "timestamp": "2025-07-28T15:42:53.390420"}, "0136729-64.2006.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 16, "timestamp": "2025-07-28T15:43:09.775129"}, "0609276-66.2008.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 17, "timestamp": "2025-07-28T15:43:26.512798"}, "0415518-45.1996.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 18, "timestamp": "2025-07-28T15:43:42.999408"}, "0119962-48.2006.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 19, "timestamp": "2025-07-28T15:43:59.446720"}, "0023293-17.1997.8.26.0224": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 20, "timestamp": "2025-07-28T15:44:15.203055"}, "0033746-89.2003.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 21, "timestamp": "2025-07-28T15:44:31.782682"}, "0422624-24.1997.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 22, "timestamp": "2025-07-28T15:44:48.089536"}, "0831888-82.2006.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 23, "timestamp": "2025-07-28T15:45:04.417161"}, "0420269-70.1999.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 24, "timestamp": "2025-07-28T15:45:21.085396"}, "0003534-92.1982.8.26.0224": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 25, "timestamp": "2025-07-28T15:45:36.068335"}, "0004010-89.2000.8.26.0066": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 26, "timestamp": "2025-07-28T15:45:51.425162"}, "0066725-42.1982.8.26.0053": {"status_consulta": "Consultado", "observacao": "Processo consultado - Status: finalizado, Requerente: Fazenda Estadual, Partes: Com partes proibidas, Palavras: Com palavras proibidas", "status_processo": "finalizado", "numero_lista": 27, "timestamp": "2025-07-28T15:46:07.123015"}, "0423028-41.1998.8.26.0053": {"status_consulta": "Consultado", "observacao": "Processo consultado - Status: final<PERSON><PERSON>, Requerente: <PERSON>da <PERSON>ec<PERSON>, Partes: Sem partes proibidas, Palavras: Com palavras proibidas", "status_processo": "finalizado", "numero_lista": 28, "timestamp": "2025-07-28T15:47:23.211725"}, "0420169-52.1998.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 29, "timestamp": "2025-07-28T15:48:35.797031"}, "0001884-17.1997.8.26.0278": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 30, "timestamp": "2025-07-28T15:48:56.167880"}, "0403596-70.1997.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 31, "timestamp": "2025-07-28T15:49:16.545979"}, "0407330-58.1999.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 32, "timestamp": "2025-07-28T15:49:33.113457"}, "0024687-82.2000.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 33, "timestamp": "2025-07-28T15:49:49.561277"}, "0403255-73.1999.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 34, "timestamp": "2025-07-28T15:50:06.505669"}, "0010593-80.2010.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 35, "timestamp": "2025-07-28T15:50:22.814287"}, "0121213-04.2006.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 36, "timestamp": "2025-07-28T15:50:39.871284"}, "0407581-18.1995.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 37, "timestamp": "2025-07-28T15:50:56.690083"}, "0006455-80.2004.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 38, "timestamp": "2025-07-28T15:51:13.358485"}, "0000002-10.1975.8.26.0660": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 39, "timestamp": "2025-07-28T15:51:30.140281"}, "0134582-94.2008.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 40, "timestamp": "2025-07-28T15:51:48.505473"}, "0401185-83.1999.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 41, "timestamp": "2025-07-28T15:52:05.439559"}, "0057163-51.2007.8.26.0564": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 42, "timestamp": "2025-07-28T15:52:21.873650"}, "0089511-69.2005.8.26.0281": {"status_consulta": "Consultado", "observacao": "Processo consultado - Status: final<PERSON><PERSON>, Requerente: <PERSON>, Partes: Sem partes proibidas, Palavras: Sem palavras proibidas", "status_processo": "finalizado", "numero_lista": 43, "timestamp": "2025-07-28T15:52:35.121997"}, "0108775-43.2006.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 44, "timestamp": "2025-07-28T16:02:10.183495"}, "5000310-88.2014.8.26.0014": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 45, "timestamp": "2025-07-28T16:02:26.141330"}, "0013612-41.2003.8.26.0053": {"status_consulta": "Consultado", "observacao": "Processo consultado - Status: finalizado, Requerente: Concremat Engenharia S/A, Partes: Com partes proibidas, Palavras: Com palavras proibidas", "status_processo": "finalizado", "numero_lista": 46, "timestamp": "2025-07-28T16:02:38.950517"}, "0413621-50.1994.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 47, "timestamp": "2025-07-28T16:03:06.193868"}, "0914146-10.1973.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 48, "timestamp": "2025-07-28T16:03:23.432044"}, "0414679-59.1992.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 49, "timestamp": "2025-07-28T16:03:39.718869"}, "0002741-70.2001.8.26.0198": {"status_consulta": "Consultado", "observacao": "Processo consultado - Status: final<PERSON><PERSON>, Requerente: <PERSON>, Partes: Sem partes proibidas, Palavras: Sem palavras proibidas", "status_processo": "finalizado", "numero_lista": 50, "timestamp": "2025-07-28T16:04:12.399822"}, "0010613-48.2002.8.26.0604": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 51, "timestamp": "2025-07-28T16:04:40.407090"}, "0000436-98.2002.8.26.0224": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 52, "timestamp": "2025-07-28T16:04:57.659146"}, "0020971-42.2003.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 53, "timestamp": "2025-07-28T16:05:14.386917"}, "0019213-28.2003.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 54, "timestamp": "2025-07-28T16:05:31.448696"}, "0014692-27.2010.8.26.0269": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 55, "timestamp": "2025-07-28T16:05:46.514170"}, "0029268-67.2005.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 56, "timestamp": "2025-07-28T16:06:03.052600"}, "0021572-53.2000.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 57, "timestamp": "2025-07-28T16:06:28.965898"}, "0009037-71.2005.8.26.0650": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 58, "timestamp": "2025-07-28T16:06:46.037447"}, "0002429-44.2001.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 59, "timestamp": "2025-07-28T16:07:20.601287"}, "0031021-59.2005.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 60, "timestamp": "2025-07-28T16:07:40.998393"}, "0001905-44.2004.8.26.0602": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 61, "timestamp": "2025-07-28T16:07:58.951812"}, "0006919-67.2008.8.26.0602": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 62, "timestamp": "2025-07-28T16:08:17.301709"}, "0011156-42.2011.8.26.0602": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 63, "timestamp": "2025-07-28T16:08:35.275533"}, "0012943-09.2011.8.26.0602": {"status_consulta": "Consultado", "observacao": "Processo consultado - Status: final<PERSON>do, Requerente: Solang<PERSON>ho, Partes: Sem partes proibidas, Palavras: Com palavras proibidas", "status_processo": "finalizado", "numero_lista": 64, "timestamp": "2025-07-28T16:08:48.820883"}, "0000670-35.2008.8.26.0268": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 65, "timestamp": "2025-07-28T16:09:13.619389"}, "0130267-57.2007.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 66, "timestamp": "2025-07-28T16:09:37.288654"}, "0121642-97.2008.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 67, "timestamp": "2025-07-28T16:10:41.052298"}, "0106251-05.2008.8.26.0053": {"status_consulta": "Consultado", "observacao": "Processo consultado - Status: final<PERSON><PERSON>, Requerente: <PERSON><PERSON><PERSON>, Partes: Sem partes proibidas, Palavras: Sem palavras proibidas", "status_processo": "finalizado", "numero_lista": 68, "timestamp": "2025-07-28T16:11:22.293468"}, "0135271-12.2006.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 69, "timestamp": "2025-07-28T16:11:47.786379"}, "0050325-11.2008.8.26.0224": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 70, "timestamp": "2025-07-28T16:12:38.590427"}, "0000924-13.2011.8.26.0588": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 71, "timestamp": "2025-07-28T16:13:09.681807"}, "0003216-62.2004.8.26.0637": {"status_consulta": "Não encontrado", "observacao": "Processo não encontrado no TJSP", "status_processo": "Não encontrado", "numero_lista": 72, "timestamp": "2025-07-28T16:17:17.467014"}, "0002985-41.2010.8.26.0470": {"status_consulta": "Erro", "observacao": "Falha na verificação de sessão", "status_processo": "Erro", "numero_lista": 73, "timestamp": "2025-07-28T16:17:18.259179"}, "0025039-64.2005.8.26.0053": {"status_consulta": "Erro", "observacao": "Falha na verificação de sessão", "status_processo": "Erro", "numero_lista": 74, "timestamp": "2025-07-28T16:17:18.261702"}, "0018877-35.2008.8.26.0510": {"status_consulta": "Erro", "observacao": "Falha na verificação de sessão", "status_processo": "Erro", "numero_lista": 75, "timestamp": "2025-07-28T16:17:18.263927"}, "0007316-65.2007.8.26.0472": {"status_consulta": "Erro", "observacao": "Falha na verificação de sessão", "status_processo": "Erro", "numero_lista": 76, "timestamp": "2025-07-28T16:17:18.336243"}, "0007460-30.2006.8.26.0066": {"status_consulta": "Erro", "observacao": "Falha na verificação de sessão", "status_processo": "Erro", "numero_lista": 77, "timestamp": "2025-07-28T16:17:18.338716"}, "0001386-58.2005.8.26.0368": {"status_consulta": "Erro", "observacao": "Falha na verificação de sessão", "status_processo": "Erro", "numero_lista": 78, "timestamp": "2025-07-28T16:17:18.340666"}, "0003712-34.2003.8.26.0053": {"status_consulta": "Erro", "observacao": "Falha na verificação de sessão", "status_processo": "Erro", "numero_lista": 79, "timestamp": "2025-07-28T16:17:18.416625"}, "0014131-54.2004.8.26.0223": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 80, "timestamp": "2025-07-28T16:20:49.553420"}, "0003910-12.2008.8.26.0695": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 81, "timestamp": "2025-07-28T16:21:35.682714"}, "0006424-60.2004.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 82, "timestamp": "2025-07-28T16:21:53.295096"}, "0007381-66.2001.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 83, "timestamp": "2025-07-28T16:22:14.902240"}, "0008783-22.2000.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 84, "timestamp": "2025-07-28T16:23:12.233265"}, "0001297-73.2006.8.26.0344": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 85, "timestamp": "2025-07-28T16:23:26.769719"}, "0012297-75.2003.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 86, "timestamp": "2025-07-28T16:23:45.078728"}, "0004020-70.2003.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 87, "timestamp": "2025-07-28T16:24:01.612615"}, "0002772-64.2008.8.26.0483": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 88, "timestamp": "2025-07-28T16:24:15.783139"}, "0030606-76.2005.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 89, "timestamp": "2025-07-28T16:24:31.783198"}, "0113242-65.2006.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 90, "timestamp": "2025-07-28T16:24:48.845735"}, "0001631-36.2011.8.26.0311": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 91, "timestamp": "2025-07-28T16:25:03.198634"}, "0139577-87.2007.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 92, "timestamp": "2025-07-28T16:25:21.138260"}, "0023970-77.2010.8.26.0196": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 93, "timestamp": "2025-07-28T16:25:37.076294"}, "0007229-76.2005.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 94, "timestamp": "2025-07-28T16:25:53.216268"}, "0007640-46.2010.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 95, "timestamp": "2025-07-28T16:26:09.158384"}, "0134921-87.2007.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 96, "timestamp": "2025-07-28T16:26:25.590188"}, "0028843-11.2003.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 97, "timestamp": "2025-07-28T16:26:42.034714"}, "0604659-63.2008.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 98, "timestamp": "2025-07-28T16:26:57.969362"}, "0002413-94.2012.8.26.0218": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 99, "timestamp": "2025-07-28T16:27:12.579196"}, "0003169-20.2010.8.26.0430": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 100, "timestamp": "2025-07-28T16:27:27.829888"}, "0007374-35.2005.8.26.0053": {"status_consulta": "Consultado", "observacao": "Processo consultado - Status: final<PERSON><PERSON>, Requerente: <PERSON>, Partes: Sem partes proibidas, Palavras: Com palavras proibidas", "status_processo": "finalizado", "numero_lista": 101, "timestamp": "2025-07-28T16:27:40.274714"}, "0024720-57.2009.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 102, "timestamp": "2025-07-28T16:28:07.235452"}, "0003231-35.2009.8.26.0288": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 103, "timestamp": "2025-07-28T16:28:22.653460"}, "0025959-77.2001.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 104, "timestamp": "2025-07-28T16:28:41.989397"}, "0006945-05.2004.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 105, "timestamp": "2025-07-28T16:29:00.030255"}, "0011789-07.2006.8.26.0189": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 106, "timestamp": "2025-07-28T16:29:14.744656"}, "0001401-66.2008.8.26.0030": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 107, "timestamp": "2025-07-28T16:29:31.698246"}, "0125633-81.2008.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 108, "timestamp": "2025-07-28T16:29:47.886389"}, "0030771-67.2009.8.26.0576": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 109, "timestamp": "2025-07-28T16:30:04.650355"}, "0001332-59.2011.8.26.0602": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 110, "timestamp": "2025-07-28T16:30:23.189864"}, "0032259-84.2003.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 111, "timestamp": "2025-07-28T16:30:39.876438"}, "0108409-04.2006.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 112, "timestamp": "2025-07-28T16:30:56.308398"}, "0003341-54.2002.8.26.0587": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 113, "timestamp": "2025-07-28T16:31:12.486369"}, "0117973-70.2007.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 114, "timestamp": "2025-07-28T16:31:29.287751"}, "0004980-26.2003.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 115, "timestamp": "2025-07-28T16:31:45.181488"}, "0020272-42.2007.8.26.0625": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 116, "timestamp": "2025-07-28T16:32:01.242606"}, "0012463-15.2000.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 117, "timestamp": "2025-07-28T16:32:17.184327"}, "0014614-17.2001.8.26.0053": {"status_consulta": "Consultado", "observacao": "Processo consultado - Status: final<PERSON><PERSON>, Requerente: Sônia Maria <PERSON>uza Mac<PERSON>o, Partes: Sem partes proibidas, Palavras: Com palavras proibidas", "status_processo": "finalizado", "numero_lista": 118, "timestamp": "2025-07-28T16:32:30.160114"}, "0016295-80.2005.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 119, "timestamp": "2025-07-28T16:32:55.486113"}, "0017929-19.2002.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 120, "timestamp": "2025-07-28T16:33:13.843728"}, "0000983-85.2011.8.26.0269": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 121, "timestamp": "2025-07-28T16:34:09.408959"}, "0000348-19.2012.8.26.0480": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 122, "timestamp": "2025-07-28T16:34:32.424751"}, "0033033-24.2008.8.26.0576": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 123, "timestamp": "2025-07-28T16:34:49.921288"}, "0013614-15.2010.8.26.0037": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 124, "timestamp": "2025-07-28T16:35:10.104274"}, "0026357-53.2003.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 125, "timestamp": "2025-07-28T16:35:27.718685"}, "0004178-76.2010.8.26.0281": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 126, "timestamp": "2025-07-28T16:35:44.585150"}, "0018339-71.2005.8.26.0506": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 127, "timestamp": "2025-07-28T16:36:01.920420"}, "0022546-41.2010.8.26.0344": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 128, "timestamp": "2025-07-28T16:36:17.410366"}, "0007201-29.2011.8.26.0270": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 129, "timestamp": "2025-07-28T16:36:33.875142"}, "0048987-70.2006.8.26.0224": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 130, "timestamp": "2025-07-28T16:36:50.321672"}, "0002605-86.2001.8.26.0323": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 131, "timestamp": "2025-07-28T16:37:06.948690"}, "0922670-71.1997.8.26.0014": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 132, "timestamp": "2025-07-28T16:37:23.625527"}, "0009220-13.2012.8.26.0063": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 133, "timestamp": "2025-07-28T16:37:40.075901"}, "0040767-09.2009.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 134, "timestamp": "2025-07-28T16:37:57.102029"}, "0001075-82.2005.8.26.0654": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 135, "timestamp": "2025-07-28T16:38:13.655296"}, "0002078-31.2005.8.26.0506": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 136, "timestamp": "2025-07-28T16:38:30.580051"}, "0001151-58.2002.8.26.0219": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 137, "timestamp": "2025-07-28T16:38:48.708247"}, "0004424-09.2012.8.26.0344": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 138, "timestamp": "2025-07-28T16:39:03.887219"}, "0013269-78.2010.8.26.0286": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 139, "timestamp": "2025-07-28T16:39:20.475365"}, "0013486-73.2000.8.26.0286": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 140, "timestamp": "2025-07-28T16:39:36.767235"}, "0020726-31.2003.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 141, "timestamp": "2025-07-28T16:39:53.502735"}, "3034498-69.2013.8.26.0405": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 142, "timestamp": "2025-07-28T16:40:10.931706"}, "0021677-55.2011.8.26.0405": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 143, "timestamp": "2025-07-28T16:40:26.552331"}, "0050628-30.2009.8.26.0405": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 144, "timestamp": "2025-07-28T16:40:42.777590"}, "0005564-58.2001.8.26.0152": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 145, "timestamp": "2025-07-28T16:41:02.262020"}, "0006754-93.2011.8.26.0576": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 146, "timestamp": "2025-07-28T16:41:19.212365"}, "0006651-50.2004.8.26.0053": {"status_consulta": "Consultado", "observacao": "Processo consultado - Status: final<PERSON><PERSON>, Requerente: <PERSON>, Partes: Sem partes proibidas, Palavras: Sem palavras proibidas", "status_processo": "finalizado", "numero_lista": 147, "timestamp": "2025-07-28T16:41:34.213952"}, "0078099-16.2008.8.26.0224": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 148, "timestamp": "2025-07-28T16:41:55.749310"}, "0001649-41.2007.8.26.0491": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 149, "timestamp": "2025-07-28T16:42:11.307430"}, "0005120-10.2004.8.26.0220": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 150, "timestamp": "2025-07-28T16:42:29.724282"}, "0000237-79.2012.8.26.0142": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 151, "timestamp": "2025-07-28T16:42:45.848847"}, "0001621-27.2011.8.26.0073": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 152, "timestamp": "2025-07-28T16:43:01.291388"}, "0003937-87.2007.8.26.0417": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 153, "timestamp": "2025-07-28T16:43:17.325123"}, "0010808-39.2002.8.26.0602": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 154, "timestamp": "2025-07-28T16:43:35.267729"}, "0002113-60.2010.8.26.0588": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 155, "timestamp": "2025-07-28T16:43:52.835773"}, "0005410-62.1996.8.26.0363": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 156, "timestamp": "2025-07-28T16:44:07.940540"}, "0029523-93.2003.8.26.0053": {"status_consulta": "Consultado", "observacao": "Processo consultado - Status: final<PERSON><PERSON>, Requerente: <PERSON><PERSON><PERSON>, Partes: Sem partes proibidas, Palavras: Com palavras proibidas", "status_processo": "finalizado", "numero_lista": 157, "timestamp": "2025-07-28T16:44:20.989025"}, "0000169-24.1997.8.26.0153": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 158, "timestamp": "2025-07-28T16:44:52.752975"}, "0105475-05.2008.8.26.0053": {"status_consulta": "Consultado", "observacao": "Processo consultado - Status: final<PERSON><PERSON>, Requerente: <PERSON><PERSON><PERSON>, Partes: Sem partes proibidas, Palavras: Com palavras proibidas", "status_processo": "finalizado", "numero_lista": 159, "timestamp": "2025-07-28T16:45:06.460215"}, "0014668-48.2009.8.26.0361": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 160, "timestamp": "2025-07-28T16:45:29.076299"}, "0002497-07.2003.8.26.0220": {"status_consulta": "Consultado", "observacao": "Processo consultado - Status: finalizado, Requerente: Herdeiros ou Sucessores de ALAIR DE ALMEIDA CASSULA, Partes: Sem partes proibidas, Palavras: Com palavras proibidas", "status_processo": "finalizado", "numero_lista": 161, "timestamp": "2025-07-28T16:45:42.768742"}, "1003259-70.2013.8.26.0053": {"status_consulta": "Consultado", "observacao": "Processo consultado - Status: finalizado, Requerente: WILSON DE OLIVEIRA LEITE, Partes: Sem partes proibidas, Palavras: Com palavras proibidas", "status_processo": "finalizado", "numero_lista": 162, "timestamp": "2025-07-28T16:46:01.765386"}, "3007210-12.2013.8.26.0482": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 163, "timestamp": "2025-07-28T16:46:22.865389"}, "0007134-11.2008.8.26.0063": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 164, "timestamp": "2025-07-28T16:46:37.664898"}, "0019670-17.2008.8.26.0625": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 165, "timestamp": "2025-07-28T16:46:53.829196"}, "0031328-71.2009.8.26.0053": {"status_consulta": "Consultado", "observacao": "Processo consultado - Status: final<PERSON><PERSON>, Requerente: <PERSON><PERSON><PERSON><PERSON>, Partes: Sem partes proibidas, Palavras: Com palavras proibidas", "status_processo": "finalizado", "numero_lista": 166, "timestamp": "2025-07-28T16:47:06.342240"}, "0029719-50.1998.8.26.0114": {"status_consulta": "Consultado", "observacao": "Processo consultado - Status: final<PERSON><PERSON>, Requerente: Eide Aparecida <PERSON>, Partes: Sem partes proibidas, Palavras: Com palavras proibidas", "status_processo": "finalizado", "numero_lista": 167, "timestamp": "2025-07-28T16:47:37.239548"}, "0000913-78.2007.8.26.0408": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 168, "timestamp": "2025-07-28T16:47:56.333512"}, "0001207-32.2002.8.26.0077": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 169, "timestamp": "2025-07-28T16:48:11.609131"}, "1001115-26.2013.8.26.0053": {"status_consulta": "Consultado", "observacao": "Processo consultado - Status: finalizado, Requerente: LAERCIO GUARNETTI DOS SANTOS, Partes: Sem partes proibidas, Palavras: Com palavras proibidas", "status_processo": "finalizado", "numero_lista": 170, "timestamp": "2025-07-28T16:48:25.322943"}, "0001005-06.2001.8.26.0428": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 171, "timestamp": "2025-07-28T16:48:44.225623"}, "0003433-72.2004.8.26.0066": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 172, "timestamp": "2025-07-28T16:48:58.537247"}, "0035122-76.2004.8.26.0053": {"status_consulta": "Consultado", "observacao": "Processo consultado - Status: final<PERSON><PERSON>, Requerente: <PERSON><PERSON><PERSON>, Partes: Sem partes proibidas, Palavras: Com palavras proibidas", "status_processo": "finalizado", "numero_lista": 173, "timestamp": "2025-07-28T17:35:23.154448"}, "0002917-91.2006.8.26.0483": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 174, "timestamp": "2025-07-28T17:35:52.394328"}, "0039384-93.2009.8.26.0053": {"status_consulta": "Consultado", "observacao": "Processo consultado - Status: final<PERSON><PERSON>, Requerente: Edina Lucia Gonçalves de Maio, Partes: Sem partes proibidas, Palavras: Com palavras proibidas", "status_processo": "finalizado", "numero_lista": 175, "timestamp": "2025-07-28T17:36:04.966001"}, "0029399-61.2013.8.26.0053": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 176, "timestamp": "2025-07-28T17:36:24.461848"}, "0039860-29.2012.8.26.0053": {"status_consulta": "Consultado", "observacao": "Processo consultado - Status: final<PERSON><PERSON>, Requerente: <PERSON><PERSON>ueno de Toledo Junior, Partes: Sem partes proibidas, Palavras: Sem palavras proibidas", "status_processo": "finalizado", "numero_lista": 177, "timestamp": "2025-07-28T17:36:36.704350"}, "0029153-11.2013.8.26.0071": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 178, "timestamp": "2025-07-28T17:36:54.732654"}, "0008353-06.2003.8.26.0590": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 179, "timestamp": "2025-07-28T17:37:12.125531"}, "1000308-06.2013.8.26.0053": {"status_consulta": "Consultado", "observacao": "Processo consultado - Status: final<PERSON><PERSON>, Requerente: <PERSON>, Partes: Sem partes proibidas, Palavras: Sem palavras proibidas", "status_processo": "finalizado", "numero_lista": 180, "timestamp": "2025-07-28T17:37:25.665793"}, "0122838-73.2006.8.26.0053": {"status_consulta": "Consultado", "observacao": "Processo consultado - Status: final<PERSON><PERSON>, Requerente: <PERSON>, Partes: Sem partes proibidas, Palavras: Com palavras proibidas", "status_processo": "finalizado", "numero_lista": 181, "timestamp": "2025-07-28T17:37:53.069058"}, "0137872-54.2007.8.26.0053": {"status_consulta": "Consultado", "observacao": "Processo consultado - Status: final<PERSON><PERSON>, Requerente: <PERSON><PERSON>, Partes: Sem partes proibidas, Palavras: Sem palavras proibidas", "status_processo": "finalizado", "numero_lista": 182, "timestamp": "2025-07-28T17:38:24.448037"}, "0116318-97.2006.8.26.0053": {"status_consulta": "Consultado", "observacao": "Processo consultado - Status: final<PERSON><PERSON>, Requerente: <PERSON>, Partes: Sem partes proibidas, Palavras: Com palavras proibidas", "status_processo": "finalizado", "numero_lista": 183, "timestamp": "2025-07-28T17:38:40.522950"}, "0003324-87.2010.8.26.0053": {"status_consulta": "Consultado", "observacao": "Processo consultado - Status: final<PERSON><PERSON>, Requerente: <PERSON><PERSON><PERSON>, Partes: Sem partes proibidas, Palavras: Com palavras proibidas", "status_processo": "finalizado", "numero_lista": 184, "timestamp": "2025-07-28T17:39:07.854528"}, "0002903-73.2005.8.26.0053": {"status_consulta": "Consultado", "observacao": "Processo consultado - Status: final<PERSON><PERSON>, Requerente: <PERSON>, Partes: Sem partes proibidas, Palavras: Com palavras proibidas", "status_processo": "finalizado", "numero_lista": 185, "timestamp": "2025-07-28T17:39:29.860002"}, "0107274-54.2006.8.26.0053": {"status_consulta": "Consultado", "observacao": "Processo consultado - Status: final<PERSON><PERSON>, Requerente: <PERSON><PERSON>, Partes: Sem partes proibidas, Palavras: Com palavras proibidas", "status_processo": "finalizado", "numero_lista": 186, "timestamp": "2025-07-28T17:39:49.989284"}, "0413174-91.1996.8.26.0053": {"status_consulta": "Consultado", "observacao": "Processo consultado - Status: finalizado, Requerente: Urbano Cavalcante de Almeida - Antecipacao de Tutela, Partes: Sem partes proibidas, Palavras: Com palavras proibidas", "status_processo": "finalizado", "numero_lista": 187, "timestamp": "2025-07-28T17:40:17.478398"}, "0040641-56.2009.8.26.0053": {"status_consulta": "Consultado", "observacao": "Processo consultado - Status: final<PERSON><PERSON>, Requerente: <PERSON>, Partes: Sem partes proibidas, Palavras: Com palavras proibidas", "status_processo": "finalizado", "numero_lista": 188, "timestamp": "2025-07-28T17:40:44.844052"}, "0013200-47.2002.8.26.0053": {"status_consulta": "Consultado", "observacao": "Processo consultado - Status: final<PERSON><PERSON>, Requerente: <PERSON>, Partes: Sem partes proibidas, Palavras: Com palavras proibidas", "status_processo": "finalizado", "numero_lista": 189, "timestamp": "2025-07-28T17:41:00.970015"}, "0408929-32.1999.8.26.0053": {"status_consulta": "Consultado", "observacao": "Processo consultado - Status: final<PERSON><PERSON>, Requerente: Dalva Cassia de Araujo <PERSON>, Partes: Sem partes proibidas, Palavras: Sem palavras proibidas", "status_processo": "finalizado", "numero_lista": 190, "timestamp": "2025-07-28T17:41:17.798941"}, "0019108-41.2009.8.26.0053": {"status_consulta": "Consultado", "observacao": "Processo consultado - Status: final<PERSON><PERSON>, Requerente: <PERSON><PERSON>, Partes: Sem partes proibidas, Palavras: Com palavras proibidas", "status_processo": "finalizado", "numero_lista": 191, "timestamp": "2025-07-28T17:41:48.887317"}, "0023130-11.2010.8.26.0053": {"status_consulta": "Consultado", "observacao": "Processo consultado - Status: final<PERSON><PERSON>, Requerente: Violante <PERSON> e Silva - espólio, Partes: Sem partes proibidas, Palavras: Com palavras proibidas", "status_processo": "finalizado", "numero_lista": 192, "timestamp": "2025-07-28T17:42:16.248216"}, "0030422-81.2009.8.26.0053": {"status_consulta": "Consultado", "observacao": "Processo consultado - Status: final<PERSON><PERSON>, Requerente: <PERSON>, Partes: Sem partes proibidas, Palavras: Com palavras proibidas", "status_processo": "finalizado", "numero_lista": 193, "timestamp": "2025-07-28T17:42:46.921717"}, "0019706-33.2005.8.26.0506": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 194, "timestamp": "2025-07-28T17:43:17.812790"}, "0124919-92.2006.8.26.0053": {"status_consulta": "Consultado", "observacao": "Processo consultado - Status: final<PERSON><PERSON>, Re<PERSON><PERSON>: <PERSON>, Partes: Sem partes proibidas, Palavras: Com palavras proibidas", "status_processo": "finalizado", "numero_lista": 195, "timestamp": "2025-07-28T17:43:30.014605"}, "0010385-72.2005.8.26.0053": {"status_consulta": "Consultado", "observacao": "Processo consultado - Status: final<PERSON><PERSON>, Requerente: <PERSON>rreira, Partes: Sem partes proibidas, Palavras: Com palavras proibidas", "status_processo": "finalizado", "numero_lista": 196, "timestamp": "2025-07-28T17:43:47.860718"}, "0114172-83.2006.8.26.0053": {"status_consulta": "Consultado", "observacao": "Processo consultado - Status: final<PERSON><PERSON>, Requerente: <PERSON><PERSON><PERSON>, Partes: Sem partes proibidas, Palavras: Sem palavras proibidas", "status_processo": "finalizado", "numero_lista": 197, "timestamp": "2025-07-28T17:44:05.656195"}, "0002540-74.2003.8.26.0597": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 198, "timestamp": "2025-07-28T17:44:25.135272"}, "0000259-97.2010.8.26.0372": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 199, "timestamp": "2025-07-28T17:44:39.471556"}, "0022488-48.2004.8.26.0053": {"status_consulta": "Consultado", "observacao": "Processo consultado - Status: final<PERSON><PERSON>, Requerente: <PERSON><PERSON><PERSON><PERSON>, Partes: Sem partes proibidas, Palavras: Com palavras proibidas", "status_processo": "finalizado", "numero_lista": 200, "timestamp": "2025-07-28T17:44:52.592301"}, "0012523-80.2003.8.26.0053": {"status_consulta": "Consultado", "observacao": "Processo consultado - Status: final<PERSON><PERSON>, Requerente: <PERSON><PERSON><PERSON>, Partes: Sem partes proibidas, Palavras: Com palavras proibidas", "status_processo": "finalizado", "numero_lista": 201, "timestamp": "2025-07-28T17:45:19.676526"}, "0024087-22.2004.8.26.0053": {"status_consulta": "Consultado", "observacao": "Processo consultado - Status: final<PERSON><PERSON>, Requerente: Innocencia <PERSON><PERSON>, Partes: Sem partes proibidas, Palavras: Com palavras proibidas", "status_processo": "finalizado", "numero_lista": 202, "timestamp": "2025-07-28T17:45:46.922925"}, "0015610-44.2003.8.26.0053": {"status_consulta": "Consultado", "observacao": "Processo consultado - Status: final<PERSON><PERSON>, Requerente: <PERSON> (falecido), Partes: Sem partes proibidas, Palavras: Com palavras proibidas", "status_processo": "finalizado", "numero_lista": 203, "timestamp": "2025-07-28T17:46:02.710058"}, "0000739-73.2005.8.26.0106": {"status_consulta": "Sem precatórios", "observacao": "Processo sem precatórios", "status_processo": "Sem precatórios", "numero_lista": 204, "timestamp": "2025-07-28T17:46:33.474846"}, "0001784-48.2003.8.26.0053": {"status_consulta": "Consultado", "observacao": "Processo consultado - Status: final<PERSON><PERSON>, Requerente: <PERSON><PERSON><PERSON> Lima, Partes: Sem partes proibidas, Palavras: Com palavras proibidas", "status_processo": "finalizado", "numero_lista": 205, "timestamp": "2025-07-28T17:46:45.761204"}, "0008619-08.2010.8.26.0053": {"status_consulta": "Consultado", "observacao": "Processo consultado - Status: final<PERSON><PERSON>, Requerente: <PERSON><PERSON>, Partes: Sem partes proibidas, Palavras: Sem palavras proibidas", "status_processo": "finalizado", "numero_lista": 206, "timestamp": "2025-07-28T17:47:03.415443"}, "0033959-61.2004.8.26.0053": {"status_consulta": "Consultado", "observacao": "Processo consultado - Status: final<PERSON><PERSON>, Requerente: <PERSON>eiva <PERSON>, Partes: Sem partes proibidas, Palavras: Com palavras proibidas", "status_processo": "finalizado", "numero_lista": 207, "timestamp": "2025-07-28T17:47:19.213805"}, "0019186-16.2001.8.26.0053": {"status_consulta": "Consultado", "observacao": "Processo consultado - Status: final<PERSON><PERSON>, Requerente: <PERSON>, Partes: Sem partes proibidas, Palavras: Com palavras proibidas", "status_processo": "finalizado", "numero_lista": 208, "timestamp": "2025-07-28T17:47:38.654868"}, "0012769-42.2004.8.26.0053": {"status_consulta": "Consultado", "observacao": "Processo consultado - Status: final<PERSON><PERSON>, Requerente: <PERSON>, Partes: Sem partes proibidas, Palavras: Sem palavras proibidas", "status_processo": "finalizado", "numero_lista": 209, "timestamp": "2025-07-28T17:47:54.309431"}, "0415506-65.1995.8.26.0053": {"status_consulta": "Consultado", "observacao": "Processo consultado - Status: final<PERSON><PERSON>, Requerente: <PERSON><PERSON><PERSON>, Partes: Sem partes proibidas, Palavras: Com palavras proibidas", "status_processo": "finalizado", "numero_lista": 210, "timestamp": "2025-07-28T17:48:13.036537"}, "0302562-72.1985.8.26.0053": {"status_consulta": "Consultado", "observacao": "Processo consultado - Status: <PERSON><PERSON><PERSON>, Requer<PERSON>: <PERSON>, Partes: Sem partes proibidas, Palavras: Com palavras proibidas", "status_processo": "finalizado", "numero_lista": 211, "timestamp": "2025-07-28T17:48:31.303917"}, "0407086-08.1994.8.26.0053": {"status_consulta": "Consultado", "observacao": "Processo consultado - Status: finalizado, Requerente: Petrocoque S/A Indústria e Comércio, Partes: Com partes proibidas, Palavras: Com palavras proibidas", "status_processo": "finalizado", "numero_lista": 212, "timestamp": "2025-07-28T17:48:56.305988"}, "0007410-77.2005.8.26.0053": {"status_consulta": "Consultado", "observacao": "Processo consultado - Status: final<PERSON><PERSON>, Requerente: <PERSON>, Partes: Sem partes proibidas, Palavras: Com palavras proibidas", "status_processo": "finalizado", "numero_lista": 213, "timestamp": "2025-07-28T17:49:12.010048"}, "0411305-64.1994.8.26.0053": {"status_consulta": "Consultado", "observacao": "Processo consultado - Status: final<PERSON><PERSON>, Requerente: <PERSON>, Partes: Sem partes proibidas, Palavras: Com palavras proibidas", "status_processo": "finalizado", "numero_lista": 214, "timestamp": "2025-07-28T17:49:28.125504"}, "0027430-31.2001.8.26.0053": {"status_consulta": "Erro", "observacao": "Falha na verificação de sessão", "status_processo": "Erro", "numero_lista": 215, "timestamp": "2025-07-28T17:49:36.557986"}, "0017040-31.2003.8.26.0053": {"status_consulta": "Erro", "observacao": "Falha na verificação de sessão", "status_processo": "Erro", "numero_lista": 216, "timestamp": "2025-07-28T17:49:36.725012"}, "0424396-51.1999.8.26.0053": {"status_consulta": "Erro", "observacao": "Falha na verificação de sessão", "status_processo": "Erro", "numero_lista": 217, "timestamp": "2025-07-28T17:49:36.727457"}, "0100945-55.2008.8.26.0053": {"status_consulta": "Erro", "observacao": "Falha na verificação de sessão", "status_processo": "Erro", "numero_lista": 218, "timestamp": "2025-07-28T17:49:36.729537"}, "0001100-79.2010.8.26.0053": {"status_consulta": "Erro", "observacao": "Falha na verificação de sessão", "status_processo": "Erro", "numero_lista": 219, "timestamp": "2025-07-28T17:49:36.843236"}}, "processados": {"0000018-05.1977.8.26.0462": {"resultado": {"NumeroLista": 7, "NumeroAutosPrincipal": "0000018-05.1977.8.26.0462", "StatusProcessoPrincipal": "<PERSON><PERSON><PERSON><PERSON> (TJSP)", "NumeroPrecatorioCompletoTJSP": "0000018-05.1977.8.26.0462 (17)", "NomeClientePrecatorio": "<PERSON><PERSON><PERSON>", "StatusValidacaoPrecatorio": "<PERSON><PERSON><PERSON><PERSON>", "StatusDownloadOficio": "Sucesso", "NomeArquivoBaixado": "doc_39328331.pdf", "DataHoraProcessamento": "2025-07-28 15:40:15", "AmbienteProcessamento": "monte"}, "numero_lista": 7, "timestamp": "2025-07-28T15:40:24.448920"}, "0000014-50.1978.8.26.0294": {"resultado": {"NumeroLista": 13, "NumeroAutosPrincipal": "0000014-50.1978.8.26.0294", "StatusProcessoPrincipal": "<PERSON><PERSON><PERSON><PERSON> (TJSP)", "NumeroPrecatorioCompletoTJSP": "0000014-50.1978.8.26.0294 (01)", "NomeClientePrecatorio": "<PERSON>", "StatusValidacaoPrecatorio": "<PERSON><PERSON><PERSON><PERSON>", "StatusDownloadOficio": "Sucesso", "NomeArquivoBaixado": "doc_42586694.pdf", "DataHoraProcessamento": "2025-07-28 15:42:06", "AmbienteProcessamento": "monte"}, "numero_lista": 13, "timestamp": "2025-07-28T15:42:15.757767"}, "0066725-42.1982.8.26.0053": {"resultado": {"NumeroLista": 27, "NumeroAutosPrincipal": "0066725-42.1982.8.26.0053", "StatusProcessoPrincipal": "<PERSON><PERSON><PERSON><PERSON> (TJSP)", "NumeroPrecatorioCompletoTJSP": "0066725-42.1982.8.26.0053 (03)", "NomeClientePrecatorio": "<PERSON>", "StatusValidacaoPrecatorio": "<PERSON><PERSON><PERSON><PERSON>", "StatusDownloadOficio": "Sucesso (Nome Não Identificado)", "NomeArquivoBaixado": "VERIFICAR PASTA MANUALMENTE", "DataHoraProcessamento": "2025-07-28 15:46:32", "AmbienteProcessamento": "monte"}, "numero_lista": 27, "timestamp": "2025-07-28T15:46:45.794512"}, "0423028-41.1998.8.26.0053": {"resultado": {"NumeroLista": 28, "NumeroAutosPrincipal": "0423028-41.1998.8.26.0053", "StatusProcessoPrincipal": "<PERSON><PERSON><PERSON><PERSON> (TJSP)", "NumeroPrecatorioCompletoTJSP": "0423028-41.1998.8.26.0053 (07)", "NomeClientePrecatorio": "<PERSON>", "StatusValidacaoPrecatorio": "<PERSON><PERSON><PERSON><PERSON>", "StatusDownloadOficio": "Sucesso", "NomeArquivoBaixado": "doc_95225785.pdf", "DataHoraProcessamento": "2025-07-28 15:48:08", "AmbienteProcessamento": "monte"}, "numero_lista": 28, "timestamp": "2025-07-28T15:48:18.982456"}, "0089511-69.2005.8.26.0281": {"resultado": {"NumeroLista": 43, "NumeroAutosPrincipal": "0089511-69.2005.8.26.0281", "StatusProcessoPrincipal": "<PERSON><PERSON><PERSON><PERSON> (TJSP)", "NumeroPrecatorioCompletoTJSP": "0089511-69.2005.8.26.0281 (03)", "NomeClientePrecatorio": "<PERSON><PERSON><PERSON>", "StatusValidacaoPrecatorio": "<PERSON><PERSON><PERSON><PERSON>", "StatusDownloadOficio": "Sucesso", "NomeArquivoBaixado": "doc_61787381.pdf", "DataHoraProcessamento": "2025-07-28 15:52:57", "AmbienteProcessamento": "monte"}, "numero_lista": 43, "timestamp": "2025-07-28T15:53:06.552404"}, "0002741-70.2001.8.26.0198": {"resultado": {"NumeroLista": 50, "NumeroAutosPrincipal": "0002741-70.2001.8.26.0198", "StatusProcessoPrincipal": "<PERSON><PERSON><PERSON><PERSON> (TJSP)", "NumeroPrecatorioCompletoTJSP": "0002741-70.2001.8.26.0198 (01)", "NomeClientePrecatorio": "CLIENTE NÃO LOCALIZADO", "StatusValidacaoPrecatorio": "<PERSON><PERSON><PERSON><PERSON>", "StatusDownloadOficio": "<PERSON><PERSON><PERSON>", "NomeArquivoBaixado": "", "DataHoraProcessamento": "2025-07-28 16:04:18", "AmbienteProcessamento": "monte"}, "numero_lista": 50, "timestamp": "2025-07-28T16:04:23.962849"}, "0029523-93.2003.8.26.0053": {"resultado": {"NumeroLista": 157, "NumeroAutosPrincipal": "0029523-93.2003.8.26.0053", "StatusProcessoPrincipal": "<PERSON><PERSON><PERSON><PERSON> (TJSP)", "NumeroPrecatorioCompletoTJSP": "0029523-93.2003.8.26.0053 (02)", "NomeClientePrecatorio": "<PERSON><PERSON><PERSON>", "StatusValidacaoPrecatorio": "<PERSON><PERSON><PERSON><PERSON>", "StatusDownloadOficio": "Sucesso", "NomeArquivoBaixado": "doc_26503838.pdf", "DataHoraProcessamento": "2025-07-28 16:44:27", "AmbienteProcessamento": "monte"}, "numero_lista": 157, "timestamp": "2025-07-28T16:44:36.998892"}, "0031328-71.2009.8.26.0053": {"resultado": {"NumeroLista": 166, "NumeroAutosPrincipal": "0031328-71.2009.8.26.0053", "StatusProcessoPrincipal": "<PERSON><PERSON><PERSON><PERSON> (TJSP)", "NumeroPrecatorioCompletoTJSP": "0031328-71.2009.8.26.0053 (03)", "NomeClientePrecatorio": "<PERSON>", "StatusValidacaoPrecatorio": "<PERSON><PERSON><PERSON><PERSON>", "StatusDownloadOficio": "Sucesso", "NomeArquivoBaixado": "doc_31951611.pdf", "DataHoraProcessamento": "2025-07-28 16:47:12", "AmbienteProcessamento": "monte"}, "numero_lista": 166, "timestamp": "2025-07-28T16:47:21.318233"}, "0035122-76.2004.8.26.0053": {"resultado": {"NumeroLista": 173, "NumeroAutosPrincipal": "0035122-76.2004.8.26.0053", "StatusProcessoPrincipal": "<PERSON><PERSON><PERSON><PERSON> (TJSP)", "NumeroPrecatorioCompletoTJSP": "0035122-76.2004.8.26.0053 (01)", "NomeClientePrecatorio": "Caio <PERSON>", "StatusValidacaoPrecatorio": "<PERSON><PERSON><PERSON><PERSON>", "StatusDownloadOficio": "Sucesso", "NomeArquivoBaixado": "doc_27919482.pdf", "DataHoraProcessamento": "2025-07-28 17:35:28", "AmbienteProcessamento": "monte"}, "numero_lista": 173, "timestamp": "2025-07-28T17:35:37.616663"}, "1000308-06.2013.8.26.0053": {"resultado": {"NumeroLista": 180, "NumeroAutosPrincipal": "1000308-06.2013.8.26.0053", "StatusProcessoPrincipal": "<PERSON><PERSON><PERSON><PERSON> (TJSP)", "NumeroPrecatorioCompletoTJSP": "1000308-06.2013.8.26.0053 (01)", "NomeClientePrecatorio": "<PERSON>", "StatusValidacaoPrecatorio": "<PERSON><PERSON><PERSON><PERSON>", "StatusDownloadOficio": "Sucesso", "NomeArquivoBaixado": "doc_38343082.pdf", "DataHoraProcessamento": "2025-07-28 17:37:31", "AmbienteProcessamento": "monte"}, "numero_lista": 180, "timestamp": "2025-07-28T17:37:40.353833"}, "0122838-73.2006.8.26.0053": {"resultado": {"NumeroLista": 181, "NumeroAutosPrincipal": "0122838-73.2006.8.26.0053", "StatusProcessoPrincipal": "<PERSON><PERSON><PERSON><PERSON> (TJSP)", "NumeroPrecatorioCompletoTJSP": "0122838-73.2006.8.26.0053 (03)", "NomeClientePrecatorio": "<PERSON> Aparecida <PERSON>", "StatusValidacaoPrecatorio": "<PERSON><PERSON><PERSON><PERSON>", "StatusDownloadOficio": "Sucesso", "NomeArquivoBaixado": "doc_36757161.pdf", "DataHoraProcessamento": "2025-07-28 17:38:02", "AmbienteProcessamento": "monte"}, "numero_lista": 181, "timestamp": "2025-07-28T17:38:11.391305"}, "0116318-97.2006.8.26.0053": {"resultado": {"NumeroLista": 183, "NumeroAutosPrincipal": "0116318-97.2006.8.26.0053", "StatusProcessoPrincipal": "<PERSON><PERSON><PERSON><PERSON> (TJSP)", "NumeroPrecatorioCompletoTJSP": "0116318-97.2006.8.26.0053 (01)", "NomeClientePrecatorio": "Maria da Costa Ferreira", "StatusValidacaoPrecatorio": "<PERSON><PERSON><PERSON><PERSON>", "StatusDownloadOficio": "Sucesso", "NomeArquivoBaixado": "doc_36853297.pdf", "DataHoraProcessamento": "2025-07-28 17:38:46", "AmbienteProcessamento": "monte"}, "numero_lista": 183, "timestamp": "2025-07-28T17:38:54.954821"}, "0107274-54.2006.8.26.0053": {"resultado": {"NumeroLista": 186, "NumeroAutosPrincipal": "0107274-54.2006.8.26.0053", "StatusProcessoPrincipal": "<PERSON><PERSON><PERSON><PERSON> (TJSP)", "NumeroPrecatorioCompletoTJSP": "0107274-54.2006.8.26.0053 (01)", "NomeClientePrecatorio": "<PERSON><PERSON>", "StatusValidacaoPrecatorio": "<PERSON><PERSON><PERSON><PERSON>", "StatusDownloadOficio": "Sucesso", "NomeArquivoBaixado": "doc_37741649.pdf", "DataHoraProcessamento": "2025-07-28 17:39:55", "AmbienteProcessamento": "monte"}, "numero_lista": 186, "timestamp": "2025-07-28T17:40:04.558834"}, "0413174-91.1996.8.26.0053": {"resultado": {"NumeroLista": 187, "NumeroAutosPrincipal": "0413174-91.1996.8.26.0053", "StatusProcessoPrincipal": "<PERSON><PERSON><PERSON><PERSON> (TJSP)", "NumeroPrecatorioCompletoTJSP": "0413174-91.1996.8.26.0053 (01)", "NomeClientePrecatorio": "Urbano Cavalcante de Almeida - Antecipacao de Tutela", "StatusValidacaoPrecatorio": "<PERSON><PERSON><PERSON><PERSON>", "StatusDownloadOficio": "Sucesso", "NomeArquivoBaixado": "doc_38277764.pdf", "DataHoraProcessamento": "2025-07-28 17:40:22", "AmbienteProcessamento": "monte"}, "numero_lista": 187, "timestamp": "2025-07-28T17:40:32.058703"}, "0408929-32.1999.8.26.0053": {"resultado": {"NumeroLista": 190, "NumeroAutosPrincipal": "0408929-32.1999.8.26.0053", "StatusProcessoPrincipal": "<PERSON><PERSON><PERSON><PERSON> (TJSP)", "NumeroPrecatorioCompletoTJSP": "0408929-32.1999.8.26.0053 (02)", "NomeClientePrecatorio": "CLIENTE NÃO LOCALIZADO", "StatusValidacaoPrecatorio": "<PERSON><PERSON><PERSON><PERSON>", "StatusDownloadOficio": "Sucesso", "NomeArquivoBaixado": "doc_30714309.pdf", "DataHoraProcessamento": "2025-07-28 17:41:27", "AmbienteProcessamento": "monte"}, "numero_lista": 190, "timestamp": "2025-07-28T17:41:36.270103"}, "0019108-41.2009.8.26.0053": {"resultado": {"NumeroLista": 191, "NumeroAutosPrincipal": "0019108-41.2009.8.26.0053", "StatusProcessoPrincipal": "<PERSON><PERSON><PERSON><PERSON> (TJSP)", "NumeroPrecatorioCompletoTJSP": "0019108-41.2009.8.26.0053 (01)", "NomeClientePrecatorio": "<PERSON><PERSON>", "StatusValidacaoPrecatorio": "<PERSON><PERSON><PERSON><PERSON>", "StatusDownloadOficio": "Sucesso", "NomeArquivoBaixado": "doc_38322011.pdf", "DataHoraProcessamento": "2025-07-28 17:41:54", "AmbienteProcessamento": "monte"}, "numero_lista": 191, "timestamp": "2025-07-28T17:42:03.474095"}, "0030422-81.2009.8.26.0053": {"resultado": {"NumeroLista": 193, "NumeroAutosPrincipal": "0030422-81.2009.8.26.0053", "StatusProcessoPrincipal": "<PERSON><PERSON><PERSON><PERSON> (TJSP)", "NumeroPrecatorioCompletoTJSP": "0030422-81.2009.8.26.0053 (01)", "NomeClientePrecatorio": "Amelia Sanches Daniel", "StatusValidacaoPrecatorio": "<PERSON><PERSON><PERSON><PERSON>", "StatusDownloadOficio": "Sucesso", "NomeArquivoBaixado": "doc_38454731.pdf", "DataHoraProcessamento": "2025-07-28 17:42:52", "AmbienteProcessamento": "monte"}, "numero_lista": 193, "timestamp": "2025-07-28T17:43:01.572206"}, "0022488-48.2004.8.26.0053": {"resultado": {"NumeroLista": 200, "NumeroAutosPrincipal": "0022488-48.2004.8.26.0053", "StatusProcessoPrincipal": "<PERSON><PERSON><PERSON><PERSON> (TJSP)", "NumeroPrecatorioCompletoTJSP": "0022488-48.2004.8.26.0053 (01)", "NomeClientePrecatorio": "<PERSON><PERSON>", "StatusValidacaoPrecatorio": "<PERSON><PERSON><PERSON><PERSON>", "StatusDownloadOficio": "Sucesso", "NomeArquivoBaixado": "doc_38492636.pdf", "DataHoraProcessamento": "2025-07-28 17:44:58", "AmbienteProcessamento": "monte"}, "numero_lista": 200, "timestamp": "2025-07-28T17:45:07.223887"}, "0012523-80.2003.8.26.0053": {"resultado": {"NumeroLista": 201, "NumeroAutosPrincipal": "0012523-80.2003.8.26.0053", "StatusProcessoPrincipal": "<PERSON><PERSON><PERSON><PERSON> (TJSP)", "NumeroPrecatorioCompletoTJSP": "0012523-80.2003.8.26.0053 (02)", "NomeClientePrecatorio": "<PERSON><PERSON><PERSON>", "StatusValidacaoPrecatorio": "<PERSON><PERSON><PERSON><PERSON>", "StatusDownloadOficio": "Sucesso", "NomeArquivoBaixado": "doc_38491203.pdf", "DataHoraProcessamento": "2025-07-28 17:45:25", "AmbienteProcessamento": "monte"}, "numero_lista": 201, "timestamp": "2025-07-28T17:45:34.330539"}, "0015610-44.2003.8.26.0053": {"resultado": {"NumeroLista": 203, "NumeroAutosPrincipal": "0015610-44.2003.8.26.0053", "StatusProcessoPrincipal": "<PERSON><PERSON><PERSON><PERSON> (TJSP)", "NumeroPrecatorioCompletoTJSP": "0015610-44.2003.8.26.0053 (01)", "NomeClientePrecatorio": "<PERSON>", "StatusValidacaoPrecatorio": "<PERSON><PERSON><PERSON><PERSON>", "StatusDownloadOficio": "Sucesso", "NomeArquivoBaixado": "doc_38626325.pdf", "DataHoraProcessamento": "2025-07-28 17:46:08", "AmbienteProcessamento": "monte"}, "numero_lista": 203, "timestamp": "2025-07-28T17:46:17.416872"}}, "falhas": {}, "estatisticas": {"total_consultados": 577, "total_processados": 23, "sucessos": 21, "falhas": 0, "downloads_realizados": 21, "recuperacoes_sessao": 0}}