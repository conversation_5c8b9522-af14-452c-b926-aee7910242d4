#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Teste específico para validar a Opção 3 do orquestrador
Simula o comportamento da opção 3 para debug
"""

import os
import sys
from pathlib import Path

def testar_logica_opcao3():
    """Testa a lógica da opção 3 com dados reais"""
    print("🧪 TESTE DA LÓGICA DA OPÇÃO 3")
    print("=" * 50)
    
    # Importar orquestrador
    try:
        from orquestrador_tjsp_e2e import OrquestradorTJSP, INPUT_EXTRACAO_DIR
        print("✅ Orquestrador importado com sucesso")
    except Exception as e:
        print(f"❌ Erro ao importar: {e}")
        return False
    
    # Criar instância
    orquestrador = OrquestradorTJSP()
    
    # Verificar estado atual
    print("\n📊 ESTADO ATUAL:")
    print(f"   Etapas concluídas: {orquestrador.checkpoint_data['etapas_concluidas']}")
    print(f"   Downloads sincronizados: {len(orquestrador.checkpoint_data['downloads_sincronizados'])}")
    
    # Contar arquivos reais
    arquivos_input = len(list(INPUT_EXTRACAO_DIR.glob("*.pdf"))) if INPUT_EXTRACAO_DIR.exists() else 0
    print(f"   Arquivos em input: {arquivos_input}")
    
    # Simular sincronização (retorna 0 porque já foram sincronizados)
    arquivos_sincronizados = 0  # Simulando resultado real
    print(f"   Novos sincronizados: {arquivos_sincronizados}")
    
    # Testar condições
    print("\n🔍 TESTE DAS CONDIÇÕES:")
    
    condicao1 = arquivos_sincronizados > 0
    print(f"   arquivos_sincronizados > 0: {condicao1}")
    
    condicao2 = "extracao" not in orquestrador.checkpoint_data["etapas_concluidas"]
    print(f"   'extracao' not in etapas_concluidas: {condicao2}")
    
    condicao_final = condicao1 or condicao2
    print(f"   Condição final (OR): {condicao_final}")
    
    condicao_arquivos = arquivos_input > 0
    print(f"   Há arquivos para processar: {condicao_arquivos}")
    
    # Resultado
    print("\n🎯 RESULTADO:")
    if condicao_final and condicao_arquivos:
        print("✅ DEVERIA EXECUTAR EXTRAÇÃO")
        return True
    elif condicao_final and not condicao_arquivos:
        print("⚠️ Condição atendida mas sem arquivos")
        return False
    else:
        print("❌ Condição não atendida - não executaria extração")
        return False

def testar_execucao_extracao():
    """Testa se a extração pode ser executada"""
    print("\n🔄 TESTE DE EXECUÇÃO DA EXTRAÇÃO")
    print("-" * 40)
    
    try:
        from orquestrador_tjsp_e2e import OrquestradorTJSP
        orquestrador = OrquestradorTJSP()
        
        print("🚀 Tentando executar extração...")
        resultado = orquestrador.executar_extracao()
        
        if resultado:
            print("✅ Extração executada com sucesso!")
        else:
            print("❌ Extração falhou")
            
        return resultado
        
    except Exception as e:
        print(f"❌ Erro na execução: {e}")
        return False

def main():
    """Função principal de teste"""
    print("🔧 DIAGNÓSTICO COMPLETO DA OPÇÃO 3")
    print("=" * 60)
    
    # Teste 1: Lógica
    logica_ok = testar_logica_opcao3()
    
    # Teste 2: Execução (apenas se lógica estiver ok)
    if logica_ok:
        execucao_ok = testar_execucao_extracao()
    else:
        execucao_ok = False
        print("\n⏭️ Pulando teste de execução (lógica falhou)")
    
    # Resumo
    print("\n" + "=" * 60)
    print("📋 RESUMO DOS TESTES:")
    print(f"   Lógica da Opção 3: {'✅ OK' if logica_ok else '❌ FALHA'}")
    print(f"   Execução da Extração: {'✅ OK' if execucao_ok else '❌ FALHA'}")
    
    if logica_ok and execucao_ok:
        print("\n🎉 TODOS OS TESTES PASSARAM!")
        print("💡 A Opção 3 deveria funcionar corretamente agora")
    else:
        print("\n⚠️ Alguns testes falharam")
        print("💡 Verifique os erros acima para mais detalhes")
    
    return logica_ok and execucao_ok

if __name__ == "__main__":
    sucesso = main()
    sys.exit(0 if sucesso else 1)
