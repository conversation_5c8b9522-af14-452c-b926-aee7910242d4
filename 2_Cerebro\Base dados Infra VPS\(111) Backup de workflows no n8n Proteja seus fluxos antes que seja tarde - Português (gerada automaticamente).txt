﻿E aí aut tomador tudo beleza seja
bem-vindo a mais uma aula aqui do nosso
intensivão ntn e hoje vou trazer aqui
mais uma aula pro nosso módulo de boas
práticas né já tem um tempinho a gente
não faz novas aulas né nesse módulo E
hoje é para falarmos sobre backups de
workflow né Isso aí é considerado uma
boa prática inclusive você né que e
trabalha aí com muitas automações né
você tem automações de tudo quanto é
tipo automações para isso para aquilo
até mesmo automações para clientes né É
muito importante que você tenha uma
rotina de backups né do dos workflows tá
e eu acompanho às vezes em grupos e vejo
muita gente às vezes falando ali às
vezes dá uma pena né Às vezes a pessoa
chega e fala ah poxa Fui atualizar aqui
o ntn e Poxa apagou todos os meus
workflows né ou então sei lá às vezes a
pessoa tem algum problema também ali na
hospedagem o servidor some ou então é
corrompido a pessoa não consegue mais
acessar o ntn Enfim então são vários
fatores né de risco que pode eh causar
aí a perda dos seus workflows né porém
Quem segue as boas práticas né
Principalmente ensinada aqui no
intensivão ntn jamais vai passar por
esses estresses né ou seja sabe que ali
tem uma rotina fazendo um backup e
eventualmente ali dos seus workflows tá
até porque se acontecer qualquer coisa
ali com o seu ntn você simplesmente pode
subir ali Uma Nova Instância tá uma Nova
Instância do zero mesmo com banco de
dados ali Zerado e simplesmente importar
os seus workflows e seguir a vida
normalmente tá então o que que a gente
vai fazer aqui eh eu vou ensinar como
fazer o backup já na nova versão do NN
né se você que acompanha aí né o nosso
canal no YouTube se eu não me engano
acho que tem dois tutoriais que a gente
fala sobre backups J orif
mas na versão antiga do Ann né quando
era na versão zero também na versão que
não existia ainda o convert to file né
ele antigamente era o famoso MOV binary
data né a gente usava muito esse node
para poder ali transitar né de Jon para
binário então de binário para Jon mas
não tem mais esse node Então hoje agora
é o convert to file Então a gente vai
ensinar como você fazer backup já na
versão atualizada do ntm
principalmente usando ali o novo node
que é o convert to file Tá mas o
procedimento digamos assim a lógica é a
mesma dos workflows antigos tá então a
gente vai começar primeiramente aqui
eh com o node de agendamento né que é o
Schedule tá geralmente a gente define
aqui um intervalo de um dia para backup
tá é claro que você pode colocar ali de
seis em 6 horas de três em 3 horas de
uma em uma hora né vai depender ali da
sua tolerância né Eh de perda de dados
né Se eu chegar para você e falar olha
se você perder um dia de trabalho ali
impacta muito na sua vida não não
impacta porque eu não faço muitas
automações assim com frequência Então
coloca um dia tá mas se você é uma
pessoa que constantemente né você
eh está criando automações você tá
ajustando melhorando automações talvez
vale a pena colocar um intervalo menor
tipo sei lá a cada 3 horas a cada 6
horas tá e mas aqui nesse exemplo eu vou
colocar a cada um dia tá Ou seja a cada
24 horas então eu posso colocar aqui ó
todos os dias
a À meia-noite
tá então aqui ó eu já estou com a
unidade de tempo Days né que é dia com
intervalo de um ou seja cada um dia
né Que horas às meia-noite né que tá
aqui Midnight né Eu queria fazer esse
backup todos os dias 9 da manhã então
você pode colocar aqui 9 A tá que aí ele
faria todos os dias 9 da manhã Hugo não
eu quero fazer e no finalzinho ali do
dia às 10 da noite então você pode vir
aqui e colocar 10 PM tá que aí ele faria
todo dia ali de noite às 10 da noite tá
mas vou deixar aqui Midnight mesmo né
que seria todo dia meia-noite né que eu
sei que certamente não vou estar ali
fazendo automação Olha que às vezes eu
estou né mas assim geralmente as pessoas
meia-noite ali não estão fazendo
automações e tal então À vezes é o
melhor momento para acontecer um backup
né Para justamente não não ter backup
ali enquanto você tá editando um
workflow né OK beleza depois que a gente
define o intervalo que que a gente vai
fazer agora a gente vai colocar um node
chamado ntn tá que é um node que permite
a gente coletar algumas informações do
banco de dados do prop NN o prop NN ele
tem um banco de dados onde ele armazena
aqui informações de workflows de
credenciais de logs de execuções né
então você pode usar esse node aqui
chamado ntn tá E aí você pode
escolher essa S aqui ó chamado get many
workflows ou seja ele vai retornar todos
os seus workflows salvos ali no seu ntn
tá E aí você tem que apontar uma
credencial aqui tá então quando você
vier aqui ó Provavelmente o seu vai est
vazio Você vai clicar aqui em Create New
credential
tá E aí ele vai te pedir para você
colocar duas informações aqui a sua P tá
e vai pedir também para você colocar é a
URL base do seu ntn tá a URL base é
basicamente composta por isso aqui ó
então por exemplo
Ó você vai chegar aqui ó você vai tentar
colocar nesse formato aqui ó tá vendo ó
você vai colocar lá https né que
provavelmente é o protocolo do seu ntn
tá lembrando que é muito importante você
usar o ntn com conexão segura tá já que
envolve ali suas automações automações
de clientes tá bom E aí você vai colocar
por exemplo né geralmente a gente
instala o ntn em um subdomínio ntn Então
a gente vai botar aqui ntn ponto aí
Claro viria ali o nome da sua empresa né
Por exemplo
empresa pcom.br Então vamos dizer que
essa aqui seria o endereço do seu ntn tá
mas aí como a gente tá usando a própria
api do ntn né a gente vai complementar
com barra api bar v1 tá então vou botar
aqui ó barra API bar v1 Ok sim até o
presente momento a api do NN continua na
versão 1 e eles ainda não tiveram assim
uma melhoria significativa para virar
uma versão do ou uma versão 3 Tá mas
para o que a gente vai fazer aqui que é
um backup ela atende muito bem tá então
basicamente você colocaria esse valor
aqui ó tá então isso aqui de fato seria
a URL base da chamada api do seu ntn tá
bom e a p você
colocaria a
chave que você obtém Onde Aqui ó quando
você vem aqui pertinho da do seu nome
aqui de usuário do NN você vem em
settings tá se vem que ele vai deixa eu
salvar aqui senão posso perder
essa isso aqui que eu já fiz tá E aí que
acontece quando você vem aqui ó em nnapi
você vai poder criar uma chave api tá eu
não posso criar aqui agora porque eu já
tenho uma criada então por isso que o
botão aqui no meu caso ele tá
desabilitado Tá mas quando você não
tiver nenhuma você vai poder criar uma
tá E aí você vai ter acesso à chave
Lembrando que já é bom você copiar a
chave porque se você não copiar ali no
momento você não consegue copiar mais tá
aí você só consegue ter acesso assim ó
tá tá vendo ó fica tipo mascarado então
criou a chave já deu um nome né eu deu
um nome aqui de ntn apq já copia o
código e já vai lá no workflow apontar
na sua credencial tá então essa chave
aqui basicamente né Eu já informei aqui
na minha credencial
tá ó eu já tenho uma credencial aqui
então basicamente eu informaria a chave
aqui ó eu copiei e aí eu informaria ela
aqui né eu colaria ela aqui tá bom então
ficaria aqui a minha chave apq e a URL
base da api do meu ntn tá fazendo isso
ao clicar em salvar vai dar sucesso aqui
tá E aí sim você já vai conseguir fazer
eh algumas requisições aqui na P do ntn
no caso aqui a gente estaria puxando
todos os workflows então por exemplo se
eu der um teste step aqui olha o que que
vai
acontecer ele vai retornar todos os meus
workflows então você vê aqui ó que ele
retornou
258 itens Ou seja eu tenho hoje aqui no
nosso ntn
258 e workflows tá E aí como você pode
ver todos eles tem ali informações né de
quando foi criado a última vez que foi
atualizado o id o nome se se está ativo
ou não Quais são os nodes que fazem
parte desse workflow e informações de
conexão eh
configurações enfim ó
versão tags né Se tiver alguma tags ali
associado workflow tá então você tem
todas as informações do seu workflow Por
meio dessa P aqui do ntn tá E aí o
próximo passo é de fato a
gente gerar o Jon desses workflows
porque aqui eu estou recebendo o Jon
código Tá mas eu preciso gerar o os
arquivos do workflow com base nesse
código de json E aí é onde a gente vai
usar
o o node convert to file tá ó que é esse
aqui inclusive ele substitui o move
binary data né que é o node que ficou
obsoleto tá E aí qual a opção a a gente
vai escolher aqui convert to Jon Ou seja
eu quero que pegue o o o código Jon né
porque ó se você for reparar eu tenho
aqui o código tá vendo ó Eu tenho o
código aqui ó Jon eu eu tô determinando
o seguinte eu quero que pegue esse
código aqui tá e transforme em arquivo
Jon Ok sendo que por padrão ele vai
considerar todos os itens em um único
arquivo
tá nesse exemplo aqui de backup a gente
vai ter que criar é um Jon para cada
item se você escolhe essa opção aqui
default é como ele tentasse pegar todos
os seus
258 workflows e tentasse e agrupar em
único arquivo Jon tá se você fizer isso
primeiro que vai dar erro tá E mesmo que
você conseguisse talvez quando você
fosse importar ia dar erro também tá
então o certo é você de fato gerar um
Jon para cada item um Jon para cada
workflow ou seja nesse caso aqui teria
que gerado do
258 arquivos de Jon Tá bom então eu vou
aqui simplesmente alterar o modo
para item to separate file n ou seja um
item por arquivo tá E aqui eu vou manter
o campo data mesmo tá quando ele for
gerar o binário aquele binário já vai
receber o nome de data mesmo tá então
não preciso colocar um outro nome aqui
tá Então você só seleciona essa opção
clica aqui aí claro eu posso botar aqui
ó
gera arquivos Jon 2
workflows
ó gera tá gera aqui eu já posso renomear
para
obté lista de
workflows
Tá e agora para finalizar né inclusive
deixa eu até executar aqui ó vou
executar não sei se vai demorar um pouco
deixa eu dar um teste STP aqui
ó
ah ele deu um erro
Ah tá falando que é muita informação
Talvez eu tenha que executado zero né
Vamos fazer o seguinte vamos dar um test
workflow para ele executar do
zero pronto agora foi tá então gerou
aqui ó
258 arquivos Jon tá Inclusive eu posso
clicar aqui em viw né ó aí você vê que
no View ele tem tipo o mesmo
código dos itens aqui de workflow tá
é E aí você pode Claro fazer o download
aqui individualmente de cada um se você
quiser tá mas no caso aqui nós vamos
apontar para uma pasta dentro do próprio
servidor do ntn e para poder salvar
esses workflows tá da mesma forma também
que você poderia salvar em um Google
Drive tá então assim aonde você vai
salvar fica ao seu critério tá então
assim nesse exemplo aqui Claro vou te
mostrar como você salva né em
um dentro de uma pasta do seu próprio
servidor ntn como também Posso
aproveitar aqui a oportunidade para
mostrar como salvar também dentro de um
Google Drive tá então vou pegar
aqui vou puxar aqui um node chamado
read write files to Disk né ou seja quer
salvar um arquivo no no disco né no HD
do Servidor então eu posso escolher essa
opção aqui ó write né que é escrever tá
o read seria consultar se eu quisesse
pegar um arquivo lá do servidor do n
como eu vou salvar então vou escolher o
write né que é escrever Ok e aqui em
fipf eu basicamente tenho que colocar o
caminho onde esses backups né eles
estariam sendo salvos então por exemplo
eu já estou logado aqui no servidor do
meu ntn tá como você pode ver aqui ó eu
tenho uma pasta chamado workflows né que
está todos os workflows
eh que foram feito backups Ok é claro eu
poderia muito bem aqui ó criar uma nova
pasta tá no seu caso aí Claro você vai
ter que criar uma nova pasta né para
poder salvar esses backup então eu
poderia criar aqui ó workflows 2
tá ó workflows 2 beleza ó e aí eu posso
simplesmente ó copiar esse caminho aqui
tá ó repara que está vazia a pasta Ok
eh
cadê E aí eu posso vir aqui ó
e colocar esse caminho tá sendo que aqui
eu tenho que colocar não só o caminho
mas também o nome do arquivo porque
quando ele for escever ele vai escrever
aquele arquivo naquele caminho então por
isso que aqui tá falando phip and name
ou seja o caminho que vai ser sal o
arquivo mais o nome do próprio arquivo
tá E aí como é que eu pego aqui o nome
né
do próprio nome aqui
do do arquivo né eu posso vir aqui né A
Claro eu colocaria aqui no modo
expressão
abro
aqui e aí que acontece eu não vou
conseguir ver os outputs aqui por quê
Porque eles estão em binários tá mas se
você aprendeu
eh o editor de expressão né se você
assistiu ali as nossas aulas sobre Edit
Field né o antigo set também você
assistiu as aulas de intens eh do como
posso dizer não é intensivão não aulão
né aulão de ntn lá no nosso canal né do
YouTube você provavelmente já tá
dominando o editor de expressão Então
você sabe que existe uma variável né eu
posso dar um barra aqui e em seguida eu
vou tentar referenciar o nome do do
arquivo tá então eu posso muito bem ó
usar aqui uma expressão que ao invés de
ser Jon vai ser binary tá Por quê Porque
os arquivos Jon são binários então eu tô
referenciando no binary Para quê Para
que eu consiga usar um desses Campos
aqui ó tá E aí qual Campo eu usaria o
file name tá para pegar o quê O nome do
meu arquivo Jon ok que no caso é o nome
daquele workflow ou seja o arquivo do
workflow vai ter o título do workflow tá
Inclusive eu já tô batendo o olho aqui e
já vi que eu esqueci uma coisinha que eu
também vou te ensinar a ajeitar no no
node
da da no node anterior né Para quê Para
que a gente possa pegar o nome ali do
workflow eh correto né Para que cada
binário aqui para que cada arquivo tenha
o próprio nome ali do workflow né senão
todos aqui ó vão sair com o mesmo nome
tá vendo ó file Jon file Jone ou seja
aqui eu não consigo saber que workflow é
esse tá se você fizer isso só vai salvar
um um workflow no seu servidor com o
nome de o Jason Por quê ele vai estar
sobrescrevendo um aou outro tá mas antes
de gente fazer essa correção o que eu
quero te dizer é o seguinte ó uma vez
que eu tenho a variável binária eu posso
dar um ponto e aí eu vou ter ali o campo
chamado data tá e depois eu posso dar um
ponto e aí sim quando eu informo data
porque é o nome do binário né eu posso
ter acesso aqui ó a extensão do arquivo
o nome do arquivo o tamanho do arquivo o
tipo do arquivo e o mimi Type do arquivo
tá então posso escolher mimi Type Ok
mimi Type não perdão file name então
quando eu escolho o file name vem o nome
do arquivo precedido da extensão dele
que é Jon né que é ponto Jon
Ok só isso aqui eu já salvaria
automaticamente o meu workflow lá no no
meu HD né no meu servidor Onde tá o ntn
mas antes de fazer isso vamos só fazer
aqui uma pequena correção tá inclusive
aqui ó já posso e colocar rename tá e
salva
eh wwk
flows no servidor do do n8n
Tá Ok mas aí vamos só fazer um pequeno
ajuste aqui no nosso convert to file que
eu esqueci que é o quê a gente colocar e
o file name dele para quê para que possa
cada arquivo Jon ter o seu próprio nome
então repare que aqui ó quando retorna
lista de workflows eu tenho o campo name
tá então eu posso simplesmente pegar
aqui
ó e jogar PR cá Ok ó então repare que
aqui agora eu tenho o nome do meu do meu
workflow E aí eu posso complementar com
Jon
tá ficar assim ó transcrição e tradução
de áudio P Jon né Por exemplo se se for
um outro workflow né Por exemplo Deixa
eu ver se eu consigo pegar aqui ó eu ten
um outro workflow aqui
ó ó eu tem um workflow chamado obter
campo de um Array de itens ou seja aqui
iria gerar um segundo arquivo Jon com o
nome obter campo de arre item Deu para
entender então cada Jon teria ali o nome
correspondente do workflow que que está
sendo gerado
tá agora sim agora eu posso simplesmente
vir aqui né Vou salvar
E aí eu posso rodar essa automação e
basicamente essa minha pasta aqui vai
ser populada com o backup dos workflows
né então venho aqui dou um teste
workflow tá ele foi lá pegou a lista
gerou os 250 arquivos Deon dos workflows
tá ó repare que cada workflow agora está
com seu próprio nome tá vendo ó
ó ok ó e cada workflow foi salvo com seu
próprio nome né lá na minha pasta
workflow 2 que fica no diretório barrot
bar ntn tá isso aqui é o o diretório
raiz do ntn tá E aí eu não aconselho
você também já salvar os backup junto
com os demais arquivos ali da pasta raiz
do ntn porque pode embolar então sempre
cria uma pasta parte chamado workflows
backups backups dos workflows enfim
sempre crio uma pasta para poder enviar
esses backups tá então se eu vi aqui
agora ó e
atualizar pronto ó está todos os meus
backups aqui
tá agora claro né Eh a gente sabe que
salvar as coisas no servidor ainda assim
também tem um risco porque vamos dizer
que o seu servidor sei lá suma é
corrompido e você não consegue mais
acessar ou então sei lá a empresa de
hospedagem bloqueou sua conta Excluiu a
sua conta pagou seus servidores por
algum motivo um engano né E aí você
perderia todo se flows mesmo fazendo
esse backup por quê Porque você tá
salvando em uma infra que não é sua né
Você só tá usando lá como cliente mas é
bom né que você tenha ali Talvez um
apontando também para
um um armazenamento em nuvem né que não
seja uma hospedagem tá E aí a gente pode
simplesmente aqui ó criar mais um uma
ramificação para o quê para o Google
Drive tá ó e aí a gente pode escolher o
procedimento
chamado upload file é esse aqui ó ó
upload file tá Para quê Para subir
arquivos em uma pasta do Google Drive tá
E aí eu posso
simplesmente
eh acessar aqui ó o Google
Drive in eu estou até aqui no google
drive da Auto né eu vou tentar criar uma
pasta aqui mesmo ó na pasta principal
tá aliás no meu mydrive né E aí eu posso
botar aqui ó
eh novo
backup de workflows tá ó criar criei a
pasta Ok eu já consigo acessar ela aqui
repara que ela está vazia tá E aí eu
posso voltar lá na minha automação e aí
eu posso né colocar aqui o mesmo nome né
ó posso pegar aqui
sendo que em vez de ser no servidor é no
Google Drive
tá então simplesmente coloco salva salva
os workflows
né porque aqui já dá entender que é
Google Drive que ele vai salvar tá então
salva os workflows tá E aí eu posso
seguir o mesmo esquema aqui né posso
pegar a expressão aqui né Ó que é o nome
do meu arquivo
Ok E aí eu basicamente aqui eu só
apontaria tá não preciso mexer em nada
aqui ó você deixa do jeito que está a
única coisa que você vai fazer é aqui em
fename de fato você vai colocar aquela
expressão né para ele e trazer o nome do
arquivo com a extensão dele tá e aqui
você apontaria a
né claro por padrão ele já vai est
apontando my drive né que é o Dri padrão
ali né E aqui na pasta você pode clicar
aqui e
tentar achar tua
pasta É claro aqui a gente também não
pode esquecer de apontar para credencial
certo
então eu vou apontar aqui PR credencial
da Auto Tá Hugo como é que cria
credencial de Google Drive tem módulos
de Google Drive aqui no curso que você
pode olhar lá e ver como faz tá bom
E então já apontei aqui pra minha
credencial aqui agora eu vou tentar
apontar pra pasta que eu criei então eu
botei lá ó novo
backup D vamos ver se já aparece aqui
pra gente ó já aparece tá vendo ó novo
backup de workflows tá pronto então aqui
ó só informei o nome do arquivo e a
pasta que eu criei lá agora a pouco no
Google Drive onde vai receber o backup
dos meus workflows tá então eu posso vir
aqui posso pausar isso aqui
temporariamente ou então eu posso deixar
ele aqui junto né e rodar tudo de novo
tá
eh quando você salva no servidor ele
básicamente sobrescreve tá então esse
que é o legal de salvar no servidor você
pode fazer quantos backups quiser que
ele sempre vai manter apenas a última
versão do workflow ou seja o último
backup que foi feito ele não ele não
fica acrescentando
no Google Drive eu eu acredito que ele
acrescenta então se você já tem lá a os
250 workflows quando for rodar de novo
no outro dia é como ele acrescentasse
mais
258 workflows né o Google Drive ele a
princípio não sobrescreve eu não sei se
é uma configuração que é padrão e que dá
para alterar mas pelos testes que eu fiz
o Google Drive ele ele ele fica
acumulando né aí o que você pode fazer
é antes de salvar você pode colocar aqui
um como é que fala uma operação de
delete do Google Drive para poder
primeiro limpar aquela pasta tirar tudo
que tem lá para então salvar novamente
os workflows Tá bom mas vamos lá vou
executar do zero aqui né vamos dizer que
vai dar meite né E aí vai rodar a rotina
e a rotina vai lá vai pegar a lista de
workflows vai gerar o Jon de cada um vai
salvar no servidor tá E também vai
salvar lá no google drive tá é claro que
no Google Drive Ele pode demorar um
pouquinho mais né mas no servidor é bem
rápido tá não é que se eu vi aqui agora
no servidor vou dar um refresh tá
ó pronto deu um refresh repare que ele
não ele não duplicou tá vendo ó ele
continuou mantendo ali os 200
58 arquivos né aqui no meu caso ele tá
listando 254 que
provavelmente talvez tinha node
workflows ali com o mesmo nome e acabou
sobrescrevendo os que tem o mesmo nome
por isso que apareceu só 24 Tá mas a
princípio ele ele salva exatamente o
número correto de workflow que você tem
tá bom E tá lá a gente tá aguardando né
tá até agora rodando mas ó se você for
ver ó ele tá salvando tá vendo ó
ó tá salvando
ó deixa eu ver se eu consigo selecionar
tudo não acho que eu não consigo
selecionar
tudo deixa eu pegar do último aqui que
eu vou ter mais ou menos uma noção ó 63
itens né Por quê Porque ele tá salvando
um por um É claro que eu não vou esperar
terminar porque senão vai dar aqui quase
30 minutos de aula isso se já não passou
né ó já estamos ah deu 30 minutos de
aula já se eu continuar aqui vai dar 40
minutos tá mas foi justamente só para
você ver né que está funcionando né ó a
automação tá rodando lá salvando tanto
no servidor do ntn quanto também no
Google Drive daqui a pouquinho ele vai
terminar Tá bom mas eu não vou esperar
concluir porque o mais importante é o
que eu já te mostrei agora que dá para
fazer né você pode salvar os workflows
Aonde você quiser tá bom então isso aí
Espero que você tenha gostado dessa aula
não deixe de fazer backup Ok assistindo
essa aula já aplique aí uma rotina do do
do seu backup Ah eu já ia esquecer Ative
o seu workflow tá para que ele possa
ficar aqui no automático você tem que
vir aqui e ativar o seu workflow tá bom
Para quê Para que quando der aquele dia
aquele horário essa rotina aqui possa
executar e fazer os backups automáticos
do seu workflow Tá bom então como eu
falei assistiu essa aula não deixe já de
implementar essa automação porque é
muito importante qualquer imprevisto que
ocorrer você vai estar assegurado ali
com seus workflows ali salvos né digamos
assim tá bom obrigado pração um grande
abraço e até uma próxima aula