#!/bin/bash

# ===================================================================
# RESTORE COMPLETO N8N - TODOS OS DADOS E CONFIGURAÇÕES
# ===================================================================

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Configurações
BACKUP_DIR="${BACKUP_DIR:-./backups}"
BACKUP_NAME="$1"

# Funções de log
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}[RESTORE]${NC} $1"
}

# Banner
show_banner() {
    echo -e "${BLUE}"
    echo "====================================================================="
    echo "    🔄 RESTORE COMPLETO N8N - TODOS OS DADOS E CONFIGURAÇÕES"
    echo "====================================================================="
    echo "    Backup: $BACKUP_NAME"
    echo "    Origem: $BACKUP_DIR"
    echo "====================================================================="
    echo -e "${NC}"
}

# Verificar parâmetros
check_parameters() {
    if [ -z "$BACKUP_NAME" ]; then
        log_error "Nome do backup não fornecido!"
        echo ""
        echo "Uso: $0 <nome_do_backup>"
        echo ""
        echo "Backups disponíveis:"
        ls -la "$BACKUP_DIR"/*.tar.gz 2>/dev/null | awk '{print "  " $9}' || echo "  Nenhum backup encontrado"
        exit 1
    fi
    
    # Verificar se arquivo de backup existe
    if [ ! -f "${BACKUP_DIR}/${BACKUP_NAME}.tar.gz" ]; then
        log_error "Arquivo de backup não encontrado: ${BACKUP_DIR}/${BACKUP_NAME}.tar.gz"
        exit 1
    fi
}

# Verificar pré-requisitos
check_prerequisites() {
    log_info "Verificando pré-requisitos..."
    
    # Verificar se Docker está rodando
    if ! docker info &> /dev/null; then
        log_error "Docker não está rodando!"
        exit 1
    fi
    
    log_success "Pré-requisitos verificados!"
}

# Confirmar restore
confirm_restore() {
    log_warning "⚠️  ATENÇÃO: Este processo irá SOBRESCREVER todos os dados atuais do N8N!"
    echo ""
    echo "Dados que serão substituídos:"
    echo "  • Todos os workflows"
    echo "  • Todas as credenciais"
    echo "  • Todas as execuções"
    echo "  • Configurações do sistema"
    echo "  • Cache e filas"
    echo ""
    
    read -p "Tem certeza que deseja continuar? (digite 'CONFIRMO' para prosseguir): " confirmation
    
    if [ "$confirmation" != "CONFIRMO" ]; then
        log_info "Restore cancelado pelo usuário."
        exit 0
    fi
    
    log_info "Confirmação recebida. Iniciando restore..."
}

# Parar serviços
stop_services() {
    log_header "1. PARANDO SERVIÇOS"
    
    log_info "Parando containers N8N..."
    docker-compose down
    
    log_success "Serviços parados"
}

# Extrair backup
extract_backup() {
    log_header "2. EXTRAINDO BACKUP"
    
    log_info "Extraindo backup: ${BACKUP_NAME}.tar.gz"
    
    cd "$BACKUP_DIR"
    tar xzf "${BACKUP_NAME}.tar.gz"
    
    if [ $? -eq 0 ]; then
        log_success "Backup extraído com sucesso"
    else
        log_error "Falha na extração do backup"
        exit 1
    fi
    
    cd - > /dev/null
}

# Restaurar configurações
restore_configurations() {
    log_header "3. RESTAURANDO CONFIGURAÇÕES"
    
    BACKUP_PATH="${BACKUP_DIR}/${BACKUP_NAME}"
    
    log_info "Restaurando arquivos de configuração..."
    
    # Backup das configurações atuais
    if [ -f "docker-compose.yml" ]; then
        cp "docker-compose.yml" "docker-compose.yml.backup.$(date +%Y%m%d_%H%M%S)"
        log_info "Backup da configuração atual criado"
    fi
    
    if [ -f ".env" ]; then
        cp ".env" ".env.backup.$(date +%Y%m%d_%H%M%S)"
        log_info "Backup do .env atual criado"
    fi
    
    # Restaurar configurações
    if [ -f "${BACKUP_PATH}/configs/docker-compose.yml" ]; then
        cp "${BACKUP_PATH}/configs/docker-compose.yml" .
        log_info "docker-compose.yml restaurado"
    fi
    
    if [ -f "${BACKUP_PATH}/configs/.env" ]; then
        cp "${BACKUP_PATH}/configs/.env" .
        log_info ".env restaurado"
    fi
    
    # Restaurar diretório configs se existir
    if [ -d "${BACKUP_PATH}/configs" ]; then
        cp -r "${BACKUP_PATH}/configs" . 2>/dev/null || true
        log_info "Diretório configs restaurado"
    fi
    
    log_success "Configurações restauradas"
}

# Restaurar volumes
restore_volumes() {
    log_header "4. RESTAURANDO VOLUMES"
    
    BACKUP_PATH="${BACKUP_DIR}/${BACKUP_NAME}"
    
    log_info "Removendo volumes existentes..."
    docker volume rm n8n-production_n8n_data 2>/dev/null || true
    docker volume rm n8n-production_postgres_data 2>/dev/null || true
    docker volume rm n8n-production_redis_data 2>/dev/null || true
    
    log_info "Criando novos volumes..."
    docker volume create n8n-production_n8n_data
    docker volume create n8n-production_postgres_data
    docker volume create n8n-production_redis_data
    
    # Restaurar volume n8n_data
    if [ -f "${BACKUP_PATH}/volumes/n8n_data.tar.gz" ]; then
        log_info "Restaurando volume n8n_data..."
        docker run --rm \
            -v n8n-production_n8n_data:/target \
            -v "${PWD}/${BACKUP_PATH}/volumes":/backup \
            alpine:latest \
            tar xzf /backup/n8n_data.tar.gz -C /target
        log_success "Volume n8n_data restaurado"
    fi
    
    # Restaurar volume postgres_data
    if [ -f "${BACKUP_PATH}/volumes/postgres_data.tar.gz" ]; then
        log_info "Restaurando volume postgres_data..."
        docker run --rm \
            -v n8n-production_postgres_data:/target \
            -v "${PWD}/${BACKUP_PATH}/volumes":/backup \
            alpine:latest \
            tar xzf /backup/postgres_data.tar.gz -C /target
        log_success "Volume postgres_data restaurado"
    fi
    
    # Restaurar volume redis_data
    if [ -f "${BACKUP_PATH}/volumes/redis_data.tar.gz" ]; then
        log_info "Restaurando volume redis_data..."
        docker run --rm \
            -v n8n-production_redis_data:/target \
            -v "${PWD}/${BACKUP_PATH}/volumes":/backup \
            alpine:latest \
            tar xzf /backup/redis_data.tar.gz -C /target
        log_success "Volume redis_data restaurado"
    fi
    
    log_success "Todos os volumes restaurados"
}

# Iniciar serviços
start_services() {
    log_header "5. INICIANDO SERVIÇOS"
    
    log_info "Iniciando containers..."
    docker-compose up -d
    
    log_info "Aguardando inicialização dos serviços..."
    sleep 30
    
    # Verificar se serviços estão rodando
    if docker-compose ps | grep -q "Up"; then
        log_success "Serviços iniciados com sucesso"
    else
        log_error "Falha na inicialização dos serviços"
        log_info "Verifique os logs: docker-compose logs"
        exit 1
    fi
}

# Restaurar banco PostgreSQL
restore_postgresql() {
    log_header "6. RESTAURANDO BANCO POSTGRESQL"
    
    BACKUP_PATH="${BACKUP_DIR}/${BACKUP_NAME}"
    
    if [ -f "${BACKUP_PATH}/postgresql_dump.sql.gz" ]; then
        log_info "Aguardando PostgreSQL ficar disponível..."
        
        # Aguardar PostgreSQL ficar pronto
        for i in {1..30}; do
            if docker-compose exec -T postgres pg_isready -U "${POSTGRES_USER:-n8n_user}" -d "${POSTGRES_DB:-n8n}" &> /dev/null; then
                break
            fi
            sleep 2
        done
        
        log_info "Restaurando banco PostgreSQL..."
        
        # Restaurar banco
        gunzip -c "${BACKUP_PATH}/postgresql_dump.sql.gz" | \
        docker-compose exec -T postgres psql \
            -U "${POSTGRES_USER:-n8n_user}" \
            -d "${POSTGRES_DB:-n8n}"
        
        if [ $? -eq 0 ]; then
            log_success "Banco PostgreSQL restaurado"
        else
            log_error "Falha na restauração do PostgreSQL"
            exit 1
        fi
    else
        log_warning "Backup do PostgreSQL não encontrado"
    fi
}

# Restaurar Redis
restore_redis() {
    log_header "7. RESTAURANDO REDIS"
    
    BACKUP_PATH="${BACKUP_DIR}/${BACKUP_NAME}"
    
    if [ -f "${BACKUP_PATH}/redis_dump.rdb" ]; then
        log_info "Restaurando Redis..."
        
        # Parar Redis temporariamente
        docker-compose stop redis
        
        # Restaurar arquivo RDB
        docker run --rm \
            -v n8n-production_redis_data:/target \
            -v "${PWD}/${BACKUP_PATH}":/backup \
            alpine:latest \
            cp /backup/redis_dump.rdb /target/dump.rdb
        
        # Reiniciar Redis
        docker-compose start redis
        
        # Aguardar Redis ficar pronto
        sleep 10
        
        if docker-compose exec -T redis redis-cli ping &> /dev/null; then
            log_success "Redis restaurado"
        else
            log_error "Falha na restauração do Redis"
            exit 1
        fi
    else
        log_warning "Backup do Redis não encontrado"
    fi
}

# Verificar restore
verify_restore() {
    log_header "8. VERIFICANDO RESTORE"
    
    log_info "Verificando serviços..."
    
    # Verificar PostgreSQL
    if docker-compose exec -T postgres pg_isready -U "${POSTGRES_USER:-n8n_user}" -d "${POSTGRES_DB:-n8n}" &> /dev/null; then
        log_success "PostgreSQL está funcionando"
    else
        log_error "PostgreSQL não está respondendo"
    fi
    
    # Verificar Redis
    if docker-compose exec -T redis redis-cli ping &> /dev/null; then
        log_success "Redis está funcionando"
    else
        log_error "Redis não está respondendo"
    fi
    
    # Verificar N8N
    sleep 15
    if curl -s http://localhost:5678 > /dev/null; then
        log_success "N8N está respondendo"
    else
        log_warning "N8N pode ainda estar inicializando"
    fi
}

# Limpeza
cleanup() {
    log_header "9. LIMPEZA"
    
    log_info "Removendo arquivos temporários..."
    rm -rf "${BACKUP_DIR}/${BACKUP_NAME}"
    
    log_success "Limpeza concluída"
}

# Relatório final
show_final_report() {
    echo ""
    echo -e "${GREEN}"
    echo "====================================================================="
    echo "                    RESTORE CONCLUÍDO COM SUCESSO!"
    echo "====================================================================="
    echo -e "${NC}"
    
    log_info "Restore Details:"
    echo "  📁 Backup: $BACKUP_NAME"
    echo "  🕒 Data: $(date)"
    echo "  📍 N8N: http://localhost:5678"
    echo ""
    
    log_info "Dados Restaurados:"
    echo "  ✅ PostgreSQL (workflows, credenciais, execuções)"
    echo "  ✅ Redis (cache, filas, sessões)"
    echo "  ✅ Volumes N8N (arquivos, configurações)"
    echo "  ✅ Configurações (docker-compose, .env)"
    echo ""
    
    log_info "Próximos Passos:"
    echo "  1. Acesse http://localhost:5678"
    echo "  2. Verifique se todos os workflows estão funcionando"
    echo "  3. Teste as credenciais"
    echo "  4. Execute alguns workflows para validar"
    echo ""
    
    log_success "Restore N8N completo finalizado!"
}

# Função principal
main() {
    show_banner
    check_parameters
    check_prerequisites
    confirm_restore
    stop_services
    extract_backup
    restore_configurations
    restore_volumes
    start_services
    restore_postgresql
    restore_redis
    verify_restore
    cleanup
    show_final_report
}

# Executar se for script principal
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
