@echo off
chcp 65001 >nul
title TJSP Orquestrador End-to-End v1.0

echo.
echo ================================================================
echo 🚀 TJSP ORQUESTRADOR END-TO-END v1.0
echo ================================================================
echo.
echo 📋 Sistema que conecta automaticamente:
echo    • Download de ofícios do TJSP (ESAJ)
echo    • Extração de informações dos PDFs
echo.
echo 🔧 Verificando ambiente...

:: Verificar se Python está instalado
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python não encontrado!
    echo 💡 Instale Python 3.8+ e tente novamente
    pause
    exit /b 1
)

:: Verificar se os diretórios existem
if not exist "tjsp\" (
    echo ❌ Diretório 'tjsp' não encontrado!
    echo 💡 Verifique se está no diretório correto
    pause
    exit /b 1
)

if not exist "extracao\" (
    echo ❌ Diretório 'extracao' não encontrado!
    echo 💡 Verifique se está no diretório correto
    pause
    exit /b 1
)

echo ✅ Ambiente verificado com sucesso!
echo.
echo 🚀 Iniciando Orquestrador TJSP...
echo.

:: Executar o orquestrador
python orquestrador_tjsp_e2e.py

echo.
echo 📊 Execução finalizada!
echo 📁 Logs disponíveis em: logs_orquestrador\
echo.
pause
