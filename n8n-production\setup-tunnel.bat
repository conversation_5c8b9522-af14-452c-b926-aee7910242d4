@echo off
echo ========================================
echo CONFIGURACAO DE TUNEL NGROK PARA N8N
echo ========================================

echo.
echo Verificando se ngrok esta instalado...
where ngrok >nul 2>&1
if %errorlevel% neq 0 (
    echo ERRO: ngrok nao encontrado!
    echo.
    echo Para instalar ngrok:
    echo 1. Va para https://ngrok.com/download
    echo 2. Baixe e instale o ngrok
    echo 3. Execute: ngrok authtoken SEU_TOKEN
    echo.
    pause
    exit /b 1
)

echo ngrok encontrado!
echo.

echo Iniciando tunel para N8N na porta 5678...
echo.
echo IMPORTANTE: Mantenha esta janela aberta!
echo O tunel ficara ativo enquanto esta janela estiver aberta.
echo.
echo Pressione Ctrl+C para parar o tunel.
echo.

ngrok http 5678 --domain=your-domain.ngrok.io

echo.
echo Tunel encerrado.
pause
