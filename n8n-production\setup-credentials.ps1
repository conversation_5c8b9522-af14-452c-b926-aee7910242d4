# ==========================================
# SCRIPT DE GERACAO DE CREDENCIAIS N8N
# ==========================================

Write-Host "🔐 Gerando credenciais seguras para n8n Production..." -ForegroundColor Green

# Funcao para gerar string aleatoria base64
function Generate-RandomBase64 {
    param([int]$Length)
    $bytes = New-Object byte[] $Length
    [System.Security.Cryptography.RNGCryptoServiceProvider]::Create().GetBytes($bytes)
    return [Convert]::ToBase64String($bytes)
}

# Gerar credenciais
$N8N_ENCRYPTION_KEY = Generate-RandomBase64 32
$POSTGRES_PASSWORD = Generate-RandomBase64 16
$N8N_PASSWORD = Generate-RandomBase64 12
$GRAFANA_PASSWORD = Generate-RandomBase64 12

Write-Host ""
Write-Host "📋 CREDENCIAIS GERADAS:" -ForegroundColor Cyan
Write-Host "N8N_ENCRYPTION_KEY: $N8N_ENCRYPTION_KEY" -ForegroundColor Yellow
Write-Host "POSTGRES_PASSWORD: $POSTGRES_PASSWORD" -ForegroundColor Yellow
Write-Host "N8N_PASSWORD: $N8N_PASSWORD" -ForegroundColor Yellow
Write-Host "GRAFANA_PASSWORD: $GRAFANA_PASSWORD" -ForegroundColor Yellow

# Salvar em arquivo de credenciais
$credentialsContent = @"
# ==========================================
# CREDENCIAIS N8N PRODUCTION
# Data: $(Get-Date)
# ==========================================

N8N_ENCRYPTION_KEY=$N8N_ENCRYPTION_KEY
POSTGRES_PASSWORD=$POSTGRES_PASSWORD
N8N_PASSWORD=$N8N_PASSWORD
GRAFANA_PASSWORD=$GRAFANA_PASSWORD

# ==========================================
# IMPORTANTE: 
# - Mantenha este arquivo seguro
# - A chave de criptografia eh CRITICA
# - Faca backup das credenciais
# ==========================================
"@

$credentialsContent | Out-File -FilePath "credentials.txt" -Encoding UTF8

# Criar arquivo .env com as credenciais
$envContent = @"
# ==========================================
# CONFIGURACOES GERAIS N8N PRODUCTION
# ==========================================
COMPOSE_PROJECT_NAME=n8n-production

# ==========================================
# DOMINIO E SSL (ALTERE CONFORME SEU DOMINIO)
# ==========================================
DOMAIN_NAME=localhost
SUBDOMAIN=n8n
SSL_EMAIL=admin@localhost

# ==========================================
# N8N CONFIGURACOES
# ==========================================
N8N_ENCRYPTION_KEY=$N8N_ENCRYPTION_KEY
N8N_USER=admin
N8N_PASSWORD=$N8N_PASSWORD
N8N_HOST=localhost
N8N_PORT=5678
N8N_PROTOCOL=http

# ==========================================
# POSTGRESQL CONFIGURACOES
# ==========================================
POSTGRES_DB=n8n
POSTGRES_USER=n8n_user
POSTGRES_PASSWORD=$POSTGRES_PASSWORD
POSTGRES_NON_ROOT_USER=n8n_user
POSTGRES_NON_ROOT_PASSWORD=$POSTGRES_PASSWORD

# ==========================================
# TIMEZONE
# ==========================================
GENERIC_TIMEZONE=America/Sao_Paulo

# ==========================================
# MONITORAMENTO
# ==========================================
GRAFANA_ADMIN_PASSWORD=$GRAFANA_PASSWORD
"@

$envContent | Out-File -FilePath ".env" -Encoding UTF8

Write-Host ""
Write-Host "✅ Credenciais salvas em:" -ForegroundColor Green
Write-Host "   - credentials.txt (backup das credenciais)" -ForegroundColor White
Write-Host "   - .env (arquivo de configuracao)" -ForegroundColor White
Write-Host ""
Write-Host "⚠️  IMPORTANTE: Faca backup destes arquivos!" -ForegroundColor Red
