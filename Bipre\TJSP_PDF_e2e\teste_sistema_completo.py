#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de Teste do Sistema TJSP End-to-End
Valida funcionamento completo do orquestrador e módulos

Funcionalidades:
- Teste de estrutura de diretórios
- Validação de dependências
- Teste de sincronização
- Simulação de processo completo
- Relatório de validação

Autor: Sistema Augment
Data: 2025-01-28
"""

import os
import sys
import json
import shutil
import tempfile
from pathlib import Path
from datetime import datetime

# Adicionar diretório atual ao path
sys.path.append(str(Path(__file__).parent))

def criar_estrutura_teste():
    """Cria estrutura de teste temporária"""
    print("🔧 Criando estrutura de teste...")
    
    # Criar diretório temporário
    temp_dir = Path(tempfile.mkdtemp(prefix="tjsp_teste_"))
    
    # Criar estrutura de diretórios
    dirs_teste = [
        temp_dir / "tjsp" / "downloads_completos",
        temp_dir / "extracao" / "data" / "input",
        temp_dir / "extracao" / "data" / "output",
        temp_dir / "extracao" / "src",
        temp_dir / "logs_orquestrador"
    ]
    
    for dir_path in dirs_teste:
        dir_path.mkdir(parents=True, exist_ok=True)
        
    # Criar arquivos de teste
    arquivos_teste = {
        temp_dir / "tjsp" / "ProcessadorTJSPUnificado_final.py": "# Script de teste TJSP\nprint('TJSP Test')",
        temp_dir / "extracao" / "src" / "sistema_principal.py": "# Script de teste Extração\nprint('Extração Test')",
        temp_dir / "tjsp" / "downloads_completos" / "teste1.pdf": b"PDF teste 1",
        temp_dir / "tjsp" / "downloads_completos" / "teste2.pdf": b"PDF teste 2",
    }
    
    for arquivo, conteudo in arquivos_teste.items():
        if isinstance(conteudo, str):
            arquivo.write_text(conteudo, encoding='utf-8')
        else:
            arquivo.write_bytes(conteudo)
            
    print(f"✅ Estrutura criada em: {temp_dir}")
    return temp_dir

def testar_importacoes():
    """Testa importação dos módulos"""
    print("\n🔍 Testando importações...")
    
    resultados = {}
    
    # Testar orquestrador
    try:
        from orquestrador_tjsp_e2e import OrquestradorTJSP
        resultados["orquestrador"] = "✅ OK"
    except ImportError as e:
        resultados["orquestrador"] = f"❌ Erro: {e}"
        
    # Testar sincronizador
    try:
        from sincronizador_arquivos import SincronizadorArquivos
        resultados["sincronizador"] = "✅ OK"
    except ImportError as e:
        resultados["sincronizador"] = f"❌ Erro: {e}"
        
    for modulo, status in resultados.items():
        print(f"   {modulo}: {status}")
        
    return all("✅" in status for status in resultados.values())

def testar_sincronizacao(temp_dir):
    """Testa funcionalidade de sincronização"""
    print("\n🔄 Testando sincronização...")
    
    try:
        from sincronizador_arquivos import SincronizadorArquivos
        
        origem = temp_dir / "tjsp" / "downloads_completos"
        destino = temp_dir / "extracao" / "data" / "input"
        backup = temp_dir / "backup_teste"
        
        sincronizador = SincronizadorArquivos(origem, destino, backup)
        resultados = sincronizador.sincronizar_todos()
        
        print(f"   📊 Arquivos processados: {resultados['total_arquivos']}")
        print(f"   ✅ Sucessos: {resultados['sucessos']}")
        print(f"   📄 Copiados: {resultados['copiados']}")
        print(f"   ⏱️ Tempo: {resultados['tempo_execucao']:.2f}s")
        
        # Verificar se arquivos foram copiados
        arquivos_destino = list(destino.glob("*.pdf"))
        if len(arquivos_destino) == 2:
            print("   ✅ Sincronização bem-sucedida")
            return True
        else:
            print(f"   ❌ Esperado 2 arquivos, encontrado {len(arquivos_destino)}")
            return False
            
    except Exception as e:
        print(f"   ❌ Erro na sincronização: {e}")
        return False

def testar_orquestrador(temp_dir):
    """Testa funcionalidades básicas do orquestrador"""
    print("\n🚀 Testando orquestrador...")
    
    try:
        # Mudar temporariamente para diretório de teste
        original_dir = os.getcwd()
        os.chdir(temp_dir)
        
        from orquestrador_tjsp_e2e import OrquestradorTJSP
        
        # Criar orquestrador
        orquestrador = OrquestradorTJSP()
        
        # Testar verificação de sistemas
        tjsp_ok, extracao_ok = orquestrador.verificar_sistemas()
        print(f"   📊 Sistema TJSP: {'✅' if tjsp_ok else '❌'}")
        print(f"   📊 Sistema Extração: {'✅' if extracao_ok else '❌'}")
        
        # Testar sincronização
        arquivos_sync = orquestrador.sincronizar_arquivos()
        print(f"   🔄 Arquivos sincronizados: {arquivos_sync}")
        
        # Testar relatório
        relatorio = orquestrador.gerar_relatorio_consolidado()
        print(f"   📋 Relatório gerado: {relatorio.exists() if relatorio else False}")
        
        os.chdir(original_dir)
        return True
        
    except Exception as e:
        print(f"   ❌ Erro no orquestrador: {e}")
        os.chdir(original_dir)
        return False

def testar_estrutura_atual():
    """Testa estrutura do projeto atual"""
    print("\n📁 Testando estrutura atual...")
    
    estrutura_esperada = [
        "tjsp/ProcessadorTJSPUnificado_final.py",
        "extracao/src/sistema_principal.py",
        "orquestrador_tjsp_e2e.py",
        "sincronizador_arquivos.py",
        "EXECUTAR_ORQUESTRADOR.bat",
        "README_ORQUESTRADOR.md"
    ]
    
    resultados = {}
    for item in estrutura_esperada:
        path = Path(item)
        existe = path.exists()
        resultados[item] = "✅ OK" if existe else "❌ Não encontrado"
        print(f"   {item}: {resultados[item]}")
        
    return all("✅" in status for status in resultados.values())

def gerar_relatorio_teste(resultados):
    """Gera relatório final dos testes"""
    print("\n📊 Gerando relatório de testes...")
    
    relatorio = {
        "timestamp": datetime.now().isoformat(),
        "testes_executados": resultados,
        "resumo": {
            "total_testes": len(resultados),
            "sucessos": sum(1 for r in resultados.values() if r),
            "falhas": sum(1 for r in resultados.values() if not r)
        }
    }
    
    arquivo_relatorio = Path(f"relatorio_testes_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
    
    with open(arquivo_relatorio, 'w', encoding='utf-8') as f:
        json.dump(relatorio, f, ensure_ascii=False, indent=2)
        
    print(f"📋 Relatório salvo: {arquivo_relatorio}")
    return relatorio

def main():
    """Função principal de teste"""
    print("🧪 TESTE COMPLETO DO SISTEMA TJSP END-TO-END")
    print("=" * 60)
    
    resultados = {}
    
    # 1. Testar estrutura atual
    resultados["estrutura_atual"] = testar_estrutura_atual()
    
    # 2. Testar importações
    resultados["importacoes"] = testar_importacoes()
    
    # 3. Criar estrutura de teste
    temp_dir = criar_estrutura_teste()
    
    try:
        # 4. Testar sincronização
        resultados["sincronizacao"] = testar_sincronizacao(temp_dir)
        
        # 5. Testar orquestrador
        resultados["orquestrador"] = testar_orquestrador(temp_dir)
        
    finally:
        # Limpar estrutura de teste
        try:
            shutil.rmtree(temp_dir)
            print(f"\n🧹 Estrutura de teste removida: {temp_dir}")
        except Exception as e:
            print(f"\n⚠️ Erro ao remover estrutura de teste: {e}")
    
    # 6. Gerar relatório
    relatorio = gerar_relatorio_teste(resultados)
    
    # 7. Resumo final
    print("\n" + "=" * 60)
    print("📊 RESUMO DOS TESTES")
    print("=" * 60)
    
    for teste, resultado in resultados.items():
        status = "✅ PASSOU" if resultado else "❌ FALHOU"
        print(f"{teste}: {status}")
        
    sucessos = relatorio["resumo"]["sucessos"]
    total = relatorio["resumo"]["total_testes"]
    
    print(f"\n🎯 RESULTADO FINAL: {sucessos}/{total} testes passaram")
    
    if sucessos == total:
        print("🎉 TODOS OS TESTES PASSARAM! Sistema pronto para uso.")
        return True
    else:
        print("⚠️ Alguns testes falharam. Verifique os erros acima.")
        return False

if __name__ == "__main__":
    sucesso = main()
    sys.exit(0 if sucesso else 1)
