# 🎬 FERRAMENTAS DE MÍDIA E PROCESSAMENTO - MAPEAMENTO COMPLETO

**Data:** 2025-01-24  
**Versão:** v1.0 - Ferramentas Especializadas para Agentes IA  
**Autor:** Augment Code Orchestrator V5.0  
**Projeto:** WebAgent Social Extraction  
**Escopo:** Ferramentas de processamento, edição e criação de mídia viral  

---

## 🎯 EXECUTIVE SUMMARY

Este documento mapeia **ferramentas essenciais** para processamento, edição e criação de mídia que são **críticas** para agentes de IA especializados em conteúdo viral. Cobre desde processamento básico até criação programática de vídeos.

### CATEGORIAS DE FERRAMENTAS:

**1. PROCESSAMENTO DE VÍDEO/ÁUDIO:**
- **FFmpeg** - Swiss army knife para mídia
- **OpenCV** - Computer vision e análise
- **Whisper** - Transcrição e análise de áudio
- **librosa** - Análise a<PERSON> de áudio

**2. CRIAÇÃO PROGRAMÁTICA:**
- **Remotion** - Vídeos com React/TypeScript
- **MoviePy** - Edição de vídeo em Python
- **Manim** - Animações matemáticas
- **Lottie** - Animações web

**3. PROCESSAMENTO DE IMAGEM:**
- **PIL/Pillow** - Manipulação básica
- **ImageMagick** - Processamento avançado
- **Sharp** - Processamento rápido (Node.js)
- **Wand** - ImageMagick para Python

**4. ANÁLISE E IA:**
- **MediaPipe** - ML para mídia
- **YOLO** - Detecção de objetos
- **Face Recognition** - Reconhecimento facial
- **DeepFace** - Análise facial avançada

---

## 🔧 FFMPEG - PROCESSAMENTO UNIVERSAL

### CAPACIDADES PRINCIPAIS:

```bash
# Conversão de formatos
ffmpeg -i input.mp4 -c:v libx264 -c:a aac output.mp4

# Extração de áudio
ffmpeg -i video.mp4 -vn -acodec copy audio.aac

# Criação de thumbnails
ffmpeg -i video.mp4 -ss 00:00:05 -vframes 1 thumbnail.jpg

# Corte de vídeo
ffmpeg -i input.mp4 -ss 00:01:00 -t 00:00:30 -c copy output.mp4

# Redimensionamento
ffmpeg -i input.mp4 -vf scale=1280:720 output.mp4

# Compressão
ffmpeg -i input.mp4 -crf 23 -preset medium compressed.mp4

# Concatenação de vídeos
ffmpeg -f concat -safe 0 -i filelist.txt -c copy output.mp4

# Adição de legendas
ffmpeg -i video.mp4 -i subtitles.srt -c copy -c:s mov_text output.mp4

# Extração de frames
ffmpeg -i video.mp4 -vf fps=1/10 frame_%04d.png

# Criação de GIF
ffmpeg -i video.mp4 -vf "fps=10,scale=320:-1" output.gif
```

### INTEGRAÇÃO COM PYTHON:

```python
import ffmpeg
import subprocess
from pathlib import Path

class FFmpegProcessor:
    def __init__(self):
        self.temp_dir = Path("temp_media")
        self.temp_dir.mkdir(exist_ok=True)
    
    def extract_viral_clips(self, video_path: str, segments: list) -> list:
        """Extrair clipes virais de vídeo longo"""
        clips = []
        
        for i, segment in enumerate(segments):
            start_time = segment['start']
            duration = segment['duration']
            output_path = self.temp_dir / f"clip_{i}.mp4"
            
            try:
                (
                    ffmpeg
                    .input(video_path, ss=start_time, t=duration)
                    .output(str(output_path), vcodec='libx264', acodec='aac')
                    .overwrite_output()
                    .run(quiet=True)
                )
                clips.append(str(output_path))
            except ffmpeg.Error as e:
                print(f"Erro ao processar clipe {i}: {e}")
        
        return clips
    
    def create_compilation(self, clips: list, output_path: str) -> str:
        """Criar compilação de clipes"""
        # Criar arquivo de lista
        filelist_path = self.temp_dir / "filelist.txt"
        with open(filelist_path, 'w') as f:
            for clip in clips:
                f.write(f"file '{clip}'\n")
        
        try:
            (
                ffmpeg
                .input(str(filelist_path), format='concat', safe=0)
                .output(output_path, c='copy')
                .overwrite_output()
                .run(quiet=True)
            )
            return output_path
        except ffmpeg.Error as e:
            print(f"Erro ao criar compilação: {e}")
            return None
    
    def add_viral_effects(self, video_path: str, output_path: str) -> str:
        """Adicionar efeitos virais ao vídeo"""
        try:
            (
                ffmpeg
                .input(video_path)
                .filter('scale', 1080, 1920)  # Formato vertical
                .filter('pad', 1080, 1920, '(ow-iw)/2', '(oh-ih)/2', 'black')
                .filter('drawtext', 
                       text='VIRAL CONTENT', 
                       fontsize=60, 
                       fontcolor='yellow',
                       x='(w-text_w)/2', 
                       y='50')
                .output(output_path, vcodec='libx264', acodec='aac')
                .overwrite_output()
                .run(quiet=True)
            )
            return output_path
        except ffmpeg.Error as e:
            print(f"Erro ao adicionar efeitos: {e}")
            return None
    
    def analyze_video_metrics(self, video_path: str) -> dict:
        """Analisar métricas técnicas do vídeo"""
        try:
            probe = ffmpeg.probe(video_path)
            video_stream = next((stream for stream in probe['streams'] 
                               if stream['codec_type'] == 'video'), None)
            audio_stream = next((stream for stream in probe['streams'] 
                               if stream['codec_type'] == 'audio'), None)
            
            return {
                'duration': float(probe['format']['duration']),
                'size_bytes': int(probe['format']['size']),
                'bitrate': int(probe['format']['bit_rate']),
                'video': {
                    'codec': video_stream['codec_name'],
                    'width': int(video_stream['width']),
                    'height': int(video_stream['height']),
                    'fps': eval(video_stream['r_frame_rate']),
                    'bitrate': int(video_stream.get('bit_rate', 0))
                } if video_stream else None,
                'audio': {
                    'codec': audio_stream['codec_name'],
                    'sample_rate': int(audio_stream['sample_rate']),
                    'channels': int(audio_stream['channels']),
                    'bitrate': int(audio_stream.get('bit_rate', 0))
                } if audio_stream else None
            }
        except Exception as e:
            print(f"Erro ao analisar vídeo: {e}")
            return {}
```

---

## ⚛️ REMOTION - CRIAÇÃO PROGRAMÁTICA DE VÍDEOS

### SETUP E CONFIGURAÇÃO:

```bash
# Instalação
npm install remotion

# Criar projeto
npx create-video --template=blank

# Preview
npm run dev

# Render
npm run build
```

### COMPONENTES PARA CONTEÚDO VIRAL:

```typescript
// src/ViralVideo.tsx
import { AbsoluteFill, useCurrentFrame, useVideoConfig, interpolate, Img, Audio } from 'remotion';

interface ViralContentProps {
  title: string;
  viral_score: number;
  thumbnail_url: string;
  audio_url?: string;
  stats: {
    views: number;
    likes: number;
    shares: number;
  };
}

export const ViralVideo: React.FC<ViralContentProps> = ({
  title,
  viral_score,
  thumbnail_url,
  audio_url,
  stats
}) => {
  const frame = useCurrentFrame();
  const { fps, durationInFrames } = useVideoConfig();
  
  // Animações
  const titleOpacity = interpolate(frame, [0, 30], [0, 1], { extrapolateRight: 'clamp' });
  const scoreScale = interpolate(frame, [30, 60], [0, 1], { extrapolateRight: 'clamp' });
  const statsOpacity = interpolate(frame, [60, 90], [0, 1], { extrapolateRight: 'clamp' });
  
  return (
    <AbsoluteFill style={{ backgroundColor: '#000' }}>
      {/* Audio de fundo */}
      {audio_url && <Audio src={audio_url} />}
      
      {/* Thumbnail de fundo */}
      <Img
        src={thumbnail_url}
        style={{
          width: '100%',
          height: '100%',
          objectFit: 'cover',
          opacity: 0.7
        }}
      />
      
      {/* Overlay escuro */}
      <AbsoluteFill style={{ backgroundColor: 'rgba(0,0,0,0.5)' }} />
      
      {/* Título */}
      <div
        style={{
          position: 'absolute',
          top: '20%',
          left: '10%',
          right: '10%',
          opacity: titleOpacity,
          color: 'white',
          fontSize: '48px',
          fontWeight: 'bold',
          textAlign: 'center',
          textShadow: '2px 2px 4px rgba(0,0,0,0.8)'
        }}
      >
        {title}
      </div>
      
      {/* Viral Score */}
      <div
        style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: `translate(-50%, -50%) scale(${scoreScale})`,
          color: '#FFD700',
          fontSize: '72px',
          fontWeight: 'bold',
          textAlign: 'center',
          textShadow: '3px 3px 6px rgba(0,0,0,0.8)'
        }}
      >
        🔥 {viral_score}
      </div>
      
      {/* Estatísticas */}
      <div
        style={{
          position: 'absolute',
          bottom: '20%',
          left: '10%',
          right: '10%',
          opacity: statsOpacity,
          display: 'flex',
          justifyContent: 'space-around',
          color: 'white',
          fontSize: '24px'
        }}
      >
        <div>👀 {stats.views.toLocaleString()}</div>
        <div>❤️ {stats.likes.toLocaleString()}</div>
        <div>🔄 {stats.shares.toLocaleString()}</div>
      </div>
    </AbsoluteFill>
  );
};

// src/Root.tsx
import { Composition } from 'remotion';
import { ViralVideo } from './ViralVideo';

export const RemotionRoot: React.FC = () => {
  return (
    <>
      <Composition
        id="viral-content"
        component={ViralVideo}
        durationInFrames={300} // 10 segundos a 30fps
        fps={30}
        width={1080}
        height={1920} // Formato vertical para mobile
        defaultProps={{
          title: "Conteúdo Viral Detectado!",
          viral_score: 85,
          thumbnail_url: "https://example.com/thumbnail.jpg",
          stats: {
            views: 1500000,
            likes: 250000,
            shares: 50000
          }
        }}
      />
    </>
  );
};
```

### GERAÇÃO AUTOMÁTICA DE VÍDEOS:

```python
# viral_video_generator.py
import subprocess
import json
from pathlib import Path

class RemotionVideoGenerator:
    def __init__(self, project_path: str):
        self.project_path = Path(project_path)
    
    def generate_viral_video(self, content_data: dict, output_path: str) -> str:
        """Gerar vídeo viral usando Remotion"""
        
        # Preparar props
        props = {
            "title": content_data.get("title", "Viral Content"),
            "viral_score": content_data.get("viral_score", 0),
            "thumbnail_url": content_data.get("thumbnail_url", ""),
            "stats": {
                "views": content_data.get("views", 0),
                "likes": content_data.get("likes", 0),
                "shares": content_data.get("shares", 0)
            }
        }
        
        # Comando Remotion
        cmd = [
            "npx", "remotion", "render",
            "viral-content",
            output_path,
            "--props", json.dumps(props),
            "--overwrite"
        ]
        
        try:
            result = subprocess.run(
                cmd,
                cwd=self.project_path,
                capture_output=True,
                text=True,
                check=True
            )
            return output_path
        except subprocess.CalledProcessError as e:
            print(f"Erro ao gerar vídeo: {e.stderr}")
            return None
    
    def create_thumbnail_video(self, thumbnail_url: str, duration: int = 5) -> str:
        """Criar vídeo simples a partir de thumbnail"""
        props = {
            "thumbnail_url": thumbnail_url,
            "duration": duration
        }
        
        output_path = f"thumbnail_video_{hash(thumbnail_url)}.mp4"
        return self.generate_viral_video(props, output_path)
```

---

## 👁️ OPENCV - ANÁLISE DE VÍDEO E IMAGEM

### ANÁLISE DE CONTEÚDO VIRAL:

```python
import cv2
import numpy as np
from typing import Dict, List, Tuple

class ViralContentAnalyzer:
    def __init__(self):
        # Carregar modelos pré-treinados
        self.face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
        self.eye_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_eye.xml')
        
        # Detector de movimento
        self.bg_subtractor = cv2.createBackgroundSubtractorMOG2()
    
    def analyze_viral_potential(self, video_path: str) -> Dict:
        """Analisar potencial viral de vídeo"""
        cap = cv2.VideoCapture(video_path)
        
        # Métricas
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        duration = total_frames / fps
        
        face_frames = 0
        motion_intensity = []
        color_variance = []
        brightness_levels = []
        
        frame_count = 0
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_count += 1
            
            # Análise de faces
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            faces = self.face_cascade.detectMultiScale(gray, 1.1, 4)
            if len(faces) > 0:
                face_frames += 1
            
            # Análise de movimento
            fg_mask = self.bg_subtractor.apply(frame)
            motion = np.sum(fg_mask) / (fg_mask.shape[0] * fg_mask.shape[1])
            motion_intensity.append(motion)
            
            # Análise de cor
            color_var = np.var(frame)
            color_variance.append(color_var)
            
            # Análise de brilho
            brightness = np.mean(cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY))
            brightness_levels.append(brightness)
        
        cap.release()
        
        # Calcular métricas virais
        face_percentage = face_frames / frame_count
        avg_motion = np.mean(motion_intensity)
        motion_variance = np.var(motion_intensity)
        avg_color_variance = np.mean(color_variance)
        avg_brightness = np.mean(brightness_levels)
        brightness_variance = np.var(brightness_levels)
        
        # Score viral baseado em métricas
        viral_score = self.calculate_viral_score({
            'face_percentage': face_percentage,
            'motion_intensity': avg_motion,
            'motion_variance': motion_variance,
            'color_variance': avg_color_variance,
            'brightness_variance': brightness_variance,
            'duration': duration
        })
        
        return {
            'duration': duration,
            'total_frames': total_frames,
            'fps': fps,
            'face_percentage': face_percentage,
            'avg_motion_intensity': avg_motion,
            'motion_variance': motion_variance,
            'avg_color_variance': avg_color_variance,
            'avg_brightness': avg_brightness,
            'brightness_variance': brightness_variance,
            'viral_score': viral_score,
            'has_faces': face_percentage > 0.1,
            'is_dynamic': avg_motion > 0.05,
            'is_colorful': avg_color_variance > 1000,
            'optimal_duration': 15 <= duration <= 60
        }
    
    def calculate_viral_score(self, metrics: Dict) -> float:
        """Calcular score viral baseado em métricas"""
        score = 0
        
        # Presença de faces (importante para engajamento)
        if metrics['face_percentage'] > 0.1:
            score += 20
        
        # Movimento dinâmico (mantém atenção)
        if 0.02 < metrics['motion_intensity'] < 0.1:
            score += 15
        
        # Variação de movimento (não monótono)
        if metrics['motion_variance'] > 0.001:
            score += 10
        
        # Cores vibrantes
        if metrics['color_variance'] > 1000:
            score += 15
        
        # Duração otimizada
        if 15 <= metrics['duration'] <= 60:
            score += 20
        elif 5 <= metrics['duration'] <= 90:
            score += 10
        
        # Variação de brilho (mudanças de cena)
        if metrics['brightness_variance'] > 100:
            score += 10
        
        # Normalizar para 0-100
        return min(score, 100)
    
    def extract_key_frames(self, video_path: str, num_frames: int = 5) -> List[np.ndarray]:
        """Extrair frames-chave do vídeo"""
        cap = cv2.VideoCapture(video_path)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        # Calcular intervalos
        interval = total_frames // (num_frames + 1)
        key_frames = []
        
        for i in range(1, num_frames + 1):
            frame_number = i * interval
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
            ret, frame = cap.read()
            if ret:
                key_frames.append(frame)
        
        cap.release()
        return key_frames
    
    def detect_text_in_video(self, video_path: str) -> List[Dict]:
        """Detectar texto em vídeo usando OCR"""
        import pytesseract
        
        cap = cv2.VideoCapture(video_path)
        text_detections = []
        frame_count = 0
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_count += 1
            
            # Processar apenas alguns frames
            if frame_count % 30 == 0:  # A cada segundo (assumindo 30fps)
                # Pré-processamento para OCR
                gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                
                # Detectar texto
                text = pytesseract.image_to_string(gray)
                if text.strip():
                    text_detections.append({
                        'frame': frame_count,
                        'timestamp': frame_count / 30,  # Assumindo 30fps
                        'text': text.strip()
                    })
        
        cap.release()
        return text_detections
```

---

## 🎵 WHISPER - ANÁLISE DE ÁUDIO

### TRANSCRIÇÃO E ANÁLISE:

```python
import whisper
import librosa
import numpy as np
from typing import Dict, List

class AudioAnalyzer:
    def __init__(self):
        self.whisper_model = whisper.load_model("base")
    
    def analyze_audio_content(self, audio_path: str) -> Dict:
        """Análise completa de áudio"""
        
        # Transcrição com Whisper
        result = self.whisper_model.transcribe(audio_path)
        
        # Análise com librosa
        y, sr = librosa.load(audio_path)
        
        # Métricas de áudio
        duration = librosa.get_duration(y=y, sr=sr)
        tempo, beats = librosa.beat.beat_track(y=y, sr=sr)
        spectral_centroids = librosa.feature.spectral_centroid(y=y, sr=sr)[0]
        spectral_rolloff = librosa.feature.spectral_rolloff(y=y, sr=sr)[0]
        mfccs = librosa.feature.mfcc(y=y, sr=sr)
        
        # Detectar música vs fala
        is_music = self.detect_music_vs_speech(y, sr)
        
        # Análise de energia
        energy = librosa.feature.rms(y=y)[0]
        avg_energy = np.mean(energy)
        energy_variance = np.var(energy)
        
        return {
            'transcription': result['text'],
            'language': result['language'],
            'segments': result['segments'],
            'duration': duration,
            'tempo': tempo,
            'avg_spectral_centroid': np.mean(spectral_centroids),
            'avg_spectral_rolloff': np.mean(spectral_rolloff),
            'avg_energy': avg_energy,
            'energy_variance': energy_variance,
            'is_music': is_music,
            'has_speech': len(result['text'].strip()) > 0,
            'speech_rate': len(result['text'].split()) / duration if duration > 0 else 0,
            'mfcc_features': np.mean(mfccs, axis=1).tolist()
        }
    
    def detect_music_vs_speech(self, y: np.ndarray, sr: int) -> bool:
        """Detectar se áudio é música ou fala"""
        # Análise de características
        spectral_centroid = np.mean(librosa.feature.spectral_centroid(y=y, sr=sr))
        spectral_bandwidth = np.mean(librosa.feature.spectral_bandwidth(y=y, sr=sr))
        rolloff = np.mean(librosa.feature.spectral_rolloff(y=y, sr=sr))
        
        # Heurística simples (pode ser melhorada com ML)
        if spectral_centroid > 2000 and spectral_bandwidth > 1500:
            return True  # Provavelmente música
        return False  # Provavelmente fala
    
    def extract_audio_highlights(self, audio_path: str, num_segments: int = 3) -> List[Dict]:
        """Extrair segmentos de destaque do áudio"""
        y, sr = librosa.load(audio_path)
        
        # Calcular energia por segmento
        segment_length = len(y) // (num_segments * 2)  # Mais segmentos para escolher os melhores
        segments = []
        
        for i in range(0, len(y) - segment_length, segment_length):
            segment = y[i:i + segment_length]
            energy = np.mean(librosa.feature.rms(y=segment))
            tempo, _ = librosa.beat.beat_track(y=segment, sr=sr)
            
            segments.append({
                'start_time': i / sr,
                'end_time': (i + segment_length) / sr,
                'energy': energy,
                'tempo': tempo,
                'score': energy * (tempo / 120)  # Score combinado
            })
        
        # Selecionar os melhores segmentos
        segments.sort(key=lambda x: x['score'], reverse=True)
        return segments[:num_segments]
```
