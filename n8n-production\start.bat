@echo off
echo ==========================================
echo    N8N PRODUCTION ENVIRONMENT COMPLETO
echo ==========================================
echo.

echo Verificando se o Docker está rodando...
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERRO: Docker não está instalado ou não está rodando!
    echo Por favor, instale o Docker Desktop e certifique-se de que está rodando.
    pause
    exit /b 1
)

echo Docker encontrado! Iniciando ambiente completo...
echo.

echo 1. Iniciando PostgreSQL e Redis...
docker-compose up -d postgres redis

echo.
echo 2. Aguardando health checks...
timeout /t 15 /nobreak >nul

echo.
echo 3. Iniciando N8N + Worker...
docker-compose up -d n8n n8n-worker

echo.
echo 4. Iniciando Monitoramento (Prometheus + Grafana)...
docker-compose up -d prometheus grafana

echo.
echo 5. Iniciando Ferramentas de Administração...
docker-compose up -d pgadmin redisinsight

echo.
echo 6. Iniciando Bull Board (Monitoramento de Filas)...
docker-compose up -d bull-board

echo.
echo 7. Verificando status de todos os serviços...
docker-compose ps

echo.
echo ==========================================
echo    AMBIENTE COMPLETO RODANDO!
echo ==========================================
echo.
echo 🎯 N8N:           http://localhost:5678 (admin/admin123)
echo 📊 Grafana:       http://localhost:3000 (admin/grafana123)
echo 🔍 Prometheus:    http://localhost:9090
echo 🐘 PgAdmin:       http://localhost:5050 (<EMAIL>/pgadmin123)
echo 🔴 RedisInsight:  http://localhost:8001
echo 📋 Bull Board:    http://localhost:3002
echo.
echo Para parar: stop.bat ou docker-compose down
echo Para logs: docker-compose logs -f [serviço]
echo.

echo Abrindo interfaces principais...
start http://localhost:5678
timeout /t 2 /nobreak >nul
start http://localhost:3000
timeout /t 2 /nobreak >nul
start http://localhost:5050

echo.
echo Pressione qualquer tecla para sair...
pause >nul
