# ==========================================
# VOLUMES PARA PERSISTENCIA DE DADOS
# ==========================================
volumes:
  postgres_data:
  n8n_data:
  redis_data:
  grafana_data:
  prometheus_data:
  postgres_evolution_data:
  redis_evolution_data:
  evolution_instances:
  traefik_data:

# ==========================================
# REDES
# ==========================================
networks:
  n8n_network:
    driver: bridge

# ==========================================
# CONFIGURACAO COMPARTILHADA N8N
# ==========================================
x-shared-n8n: &shared-n8n
  image: docker.n8n.io/n8nio/n8n:latest
  restart: always
  environment:
    # Configuracoes de banco de dados
    - DB_TYPE=postgresdb
    - DB_POSTGRESDB_HOST=postgres
    - DB_POSTGRESDB_PORT=5432
    - DB_POSTGRESDB_DATABASE=${POSTGRES_DB}
    - DB_POSTGRESDB_USER=${POSTGRES_NON_ROOT_USER}
    - DB_POSTGRESDB_PASSWORD=${POSTGRES_NON_ROOT_PASSWORD}
    
    # Configuracoes Redis (Queue Mode)
    - EXECUTIONS_MODE=queue
    - QUEUE_MODE=redis
    - QUEUE_BULL_REDIS_HOST=redis
    - QUEUE_BULL_REDIS_PORT=6379
    - QUEUE_HEALTH_CHECK_ACTIVE=true
    
    # Configuracoes n8n
    - N8N_ENCRYPTION_KEY=${N8N_ENCRYPTION_KEY}
    - N8N_HOST=${N8N_HOST}
    - N8N_PORT=${N8N_PORT}
    - N8N_PROTOCOL=${N8N_PROTOCOL}
    - N8N_BASIC_AUTH_ACTIVE=true
    - N8N_BASIC_AUTH_USER=${N8N_USER}
    - N8N_BASIC_AUTH_PASSWORD=${N8N_PASSWORD}
    
    # Configuracoes de dados
    - N8N_DEFAULT_BINARY_DATA_MODE=filesystem
    - N8N_ENFORCE_SETTINGS_FILE_PERMISSIONS=true
    - N8N_BINARY_DATA_MODE=${N8N_BINARY_DATA_MODE}
    - N8N_FILE_STORAGE_PATH=${N8N_FILE_STORAGE_PATH}

    # Community Nodes
    - N8N_COMMUNITY_PACKAGES_ENABLED=${N8N_COMMUNITY_PACKAGES_ENABLED}
    - N8N_COMMUNITY_PACKAGES_ALLOW_TOOL_USAGE=${N8N_COMMUNITY_PACKAGES_ALLOW_TOOL_USAGE}
    - NODES_INCLUDE=${NODES_INCLUDE}
    - N8N_DISABLE_PRODUCTION_MAIN_PROCESS=${N8N_DISABLE_PRODUCTION_MAIN_PROCESS}
    - N8N_SKIP_WEBHOOK_DEREGISTRATION_SHUTDOWN=${N8N_SKIP_WEBHOOK_DEREGISTRATION_SHUTDOWN}

    # Configuracoes de logs
    - N8N_LOG_OUTPUT=file,console
    - N8N_LOG_LEVEL=info
    - N8N_LOG_FILE_LOCATION=/home/<USER>/.n8n/logs/n8n.log

    # Metricas e monitoramento
    - N8N_METRICS=true
    - N8N_RUNNERS_ENABLED=true

    # Timezone
    - GENERIC_TIMEZONE=${GENERIC_TIMEZONE}
    
  volumes:
    - n8n_data:/home/<USER>/.n8n
    - ./local-files:/files
    - ./logs/n8n:/home/<USER>/.n8n/logs
    - c:/Users/<USER>/Documents:/host-documents:ro
    - c:/Users/<USER>/Downloads:/host-downloads
    - c:/temp:/host-temp
  networks:
    - n8n_network
  depends_on:
    postgres:
      condition: service_healthy
    redis:
      condition: service_healthy

# ==========================================
# SERVICOS
# ==========================================
services:
  # PostgreSQL Database
  postgres:
    image: postgres:16-alpine
    restart: always
    environment:
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_NON_ROOT_USER=${POSTGRES_NON_ROOT_USER}
      - POSTGRES_NON_ROOT_PASSWORD=${POSTGRES_NON_ROOT_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-data.sh:/docker-entrypoint-initdb.d/init-data.sh
    networks:
      - n8n_network
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -h localhost -U ${POSTGRES_USER} -d ${POSTGRES_DB}']
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    ports:
      - "5432:5432"

  # Redis Cache/Queue
  redis:
    image: redis:7-alpine
    restart: always
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - n8n_network
    healthcheck:
      test: ['CMD', 'redis-cli', 'ping']
      interval: 10s
      timeout: 5s
      retries: 5
    ports:
      - "6379:6379"

  # n8n Main Application
  n8n:
    <<: *shared-n8n
    ports:
      - "5678:5678"
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.n8n.rule=Host(`${N8N_HOST}`)"
      - "traefik.http.routers.n8n.tls=true"
      - "traefik.http.routers.n8n.entrypoints=websecure"
      - "traefik.http.services.n8n.loadbalancer.server.port=5678"

  # n8n Worker (Queue Processing)
  n8n-worker:
    <<: *shared-n8n
    command: worker
    ports: []
    depends_on:
      - n8n

  # Traefik Reverse Proxy
  traefik:
    image: traefik:v3.0
    restart: always
    command:
      - "--api.dashboard=true"
      - "--api.insecure=true"
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--entrypoints.web.address=:80"
      - "--entrypoints.websecure.address=:443"
      - "--certificatesresolvers.letsencrypt.acme.tlschallenge=true"
      - "--certificatesresolvers.letsencrypt.acme.email=${SSL_EMAIL}"
      - "--certificatesresolvers.letsencrypt.acme.storage=/letsencrypt/acme.json"
      - "--log.level=INFO"
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"  # Dashboard
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - traefik_data:/letsencrypt
      - ./logs/traefik:/var/log/traefik
    networks:
      - n8n_network

  # pgAdmin - PostgreSQL Management
  pgadmin:
    image: dpage/pgadmin4:latest
    restart: always
    environment:
      - PGADMIN_DEFAULT_EMAIL=${PGADMIN_DEFAULT_EMAIL}
      - PGADMIN_DEFAULT_PASSWORD=${PGADMIN_DEFAULT_PASSWORD}
      - PGADMIN_CONFIG_SERVER_MODE=False
    volumes:
      - ./data/pgadmin:/var/lib/pgadmin
    networks:
      - n8n_network
    ports:
      - "5050:80"
    depends_on:
      - postgres

  # Redis Commander - Redis Management (Substitui RedisInsight)
  redis-commander:
    image: rediscommander/redis-commander:latest
    restart: always
    environment:
      - REDIS_HOSTS=local:redis:6379
      - HTTP_USER=admin
      - HTTP_PASSWORD=redis123
    ports:
      - "8002:8081"
    networks:
      - n8n_network
    depends_on:
      - redis

  # PostgreSQL para Evolution API
  postgres-evolution:
    image: postgres:15-alpine
    container_name: postgres-evolution
    restart: always
    environment:
      - POSTGRES_DB=evolution
      - POSTGRES_USER=evolution_user
      - POSTGRES_PASSWORD=evolution123
      - PGDATA=/var/lib/postgresql/data/pgdata
    volumes:
      - postgres_evolution_data:/var/lib/postgresql/data
    networks:
      - n8n_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U evolution_user -d evolution"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Redis para Evolution API
  redis-evolution:
    image: redis:7-alpine
    container_name: redis-evolution
    restart: always
    command: ["redis-server", "--appendonly", "yes", "--port", "6379"]
    volumes:
      - redis_evolution_data:/data
    networks:
      - n8n_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Evolution API - WhatsApp Integration
  evolution-api:
    image: atendai/evolution-api:v2.2.0
    container_name: evolution-api
    restart: always
    ports:
      - "8001:8080"
    volumes:
      - evolution_instances:/evolution/instances
    networks:
      - n8n_network
    depends_on:
      postgres-evolution:
        condition: service_healthy
      redis-evolution:
        condition: service_healthy
    environment:
      # Server Configuration
      - SERVER_TYPE=http
      - SERVER_PORT=8080
      - SERVER_URL=http://localhost:8001
      - CORS_ORIGIN=*
      - CORS_METHODS=GET,POST,PUT,DELETE
      - CORS_CREDENTIALS=true

      # Authentication
      - AUTHENTICATION_TYPE=apikey
      - AUTHENTICATION_API_KEY=evolution_api_key_123
      - AUTHENTICATION_EXPOSE_IN_FETCH_INSTANCES=true

      # Database (usando PostgreSQL dedicado)
      - DATABASE_ENABLED=true
      - DATABASE_PROVIDER=postgresql
      - DATABASE_CONNECTION_URI=****************************************************************/evolution?schema=public
      - DATABASE_CONNECTION_CLIENT_NAME=evolution_api
      - DATABASE_SAVE_DATA_INSTANCE=true
      - DATABASE_SAVE_DATA_NEW_MESSAGE=true
      - DATABASE_SAVE_MESSAGE_UPDATE=true
      - DATABASE_SAVE_DATA_CONTACTS=true
      - DATABASE_SAVE_DATA_CHATS=true
      - DATABASE_SAVE_DATA_LABELS=true
      - DATABASE_SAVE_DATA_HISTORIC=true

      # Redis Cache (usando Redis dedicado)
      - CACHE_REDIS_ENABLED=true
      - CACHE_REDIS_URI=redis://redis-evolution:6379/0
      - CACHE_REDIS_PREFIX_KEY=evolution_api
      - CACHE_REDIS_SAVE_INSTANCES=false

      # Local Cache
      - CACHE_LOCAL_ENABLED=false

      # Instance Settings
      - DEL_INSTANCE=false
      - DEL_STORE_CLEANED=false

      # Webhook
      - WEBHOOK_GLOBAL_ENABLED=false
      - WEBHOOK_GLOBAL_URL=
      - WEBHOOK_GLOBAL_WEBHOOK_BY_EVENTS=false

      # Chatwoot Integration
      - CHATWOOT_MESSAGE_READ=true
      - CHATWOOT_MESSAGE_DELETE=true
      - CHATWOOT_BOT_CONTACT=true
      - CHATWOOT_ALWAYS_ONLINE=false
      - CHATWOOT_DAYS_LIMIT_IMPORT_MESSAGES=0

      # Log Settings
      - LOG_LEVEL=ERROR
      - LOG_COLOR=true
      - LOG_BAILEYS=error

  # Prometheus - Metrics Collection
  prometheus:
    image: prom/prometheus:latest
    restart: always
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./config/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - n8n_network
    ports:
      - "9090:9090"

  # Grafana - Monitoring Dashboard
  grafana:
    image: grafana/grafana:latest
    restart: always
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/grafana:/etc/grafana/provisioning
    networks:
      - n8n_network
    ports:
      - "3000:3000"
    depends_on:
      - prometheus

  # Bull Board - Queue Monitoring
  bull-board:
    build:
      context: .
      dockerfile: Dockerfile.bullboard
    restart: always
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    networks:
      - n8n_network
    ports:
      - "3002:3002"
    depends_on:
      - redis
