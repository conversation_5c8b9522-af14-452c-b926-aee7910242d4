#!/bin/bash

# ===================================================================
# BACKUP COMPLETO N8N - TODOS OS DADOS E CONFIGURAÇÕES
# ===================================================================

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Configurações
BACKUP_DIR="${BACKUP_DIR:-./backups}"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_NAME="n8n_backup_${TIMESTAMP}"
BACKUP_PATH="${BACKUP_DIR}/${BACKUP_NAME}"
RETENTION_DAYS="${RETENTION_DAYS:-30}"

# Funções de log
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}[BACKUP]${NC} $1"
}

# Banner
show_banner() {
    echo -e "${BLUE}"
    echo "====================================================================="
    echo "    🔄 BACKUP COMPLETO N8N - TODOS OS DADOS E CONFIGURAÇÕES"
    echo "====================================================================="
    echo "    Backup: $BACKUP_NAME"
    echo "    Destino: $BACKUP_PATH"
    echo "====================================================================="
    echo -e "${NC}"
}

# Verificar pré-requisitos
check_prerequisites() {
    log_info "Verificando pré-requisitos..."
    
    # Verificar se Docker está rodando
    if ! docker info &> /dev/null; then
        log_error "Docker não está rodando!"
        exit 1
    fi
    
    # Verificar se containers estão rodando
    if ! docker-compose ps | grep -q "Up"; then
        log_error "Containers N8N não estão rodando!"
        log_info "Execute: docker-compose up -d"
        exit 1
    fi
    
    # Criar diretório de backup
    mkdir -p "$BACKUP_PATH"
    
    log_success "Pré-requisitos verificados!"
}

# Backup PostgreSQL
backup_postgresql() {
    log_header "1. BACKUP POSTGRESQL"
    
    log_info "Fazendo backup do banco PostgreSQL..."
    
    # Backup completo do banco
    docker-compose exec -T postgres pg_dump \
        -U "${POSTGRES_USER:-n8n_user}" \
        -d "${POSTGRES_DB:-n8n}" \
        --verbose \
        --no-owner \
        --no-privileges \
        --clean \
        --if-exists \
        > "${BACKUP_PATH}/postgresql_dump.sql"
    
    if [ $? -eq 0 ]; then
        log_success "Backup PostgreSQL concluído"
        
        # Compactar backup SQL
        gzip "${BACKUP_PATH}/postgresql_dump.sql"
        log_info "Backup PostgreSQL compactado"
    else
        log_error "Falha no backup PostgreSQL"
        exit 1
    fi
    
    # Backup de metadados do banco
    docker-compose exec -T postgres psql \
        -U "${POSTGRES_USER:-n8n_user}" \
        -d "${POSTGRES_DB:-n8n}" \
        -c "\l" > "${BACKUP_PATH}/postgresql_databases.txt"
    
    docker-compose exec -T postgres psql \
        -U "${POSTGRES_USER:-n8n_user}" \
        -d "${POSTGRES_DB:-n8n}" \
        -c "\dt" > "${BACKUP_PATH}/postgresql_tables.txt"
    
    log_info "Metadados PostgreSQL salvos"
}

# Backup Redis
backup_redis() {
    log_header "2. BACKUP REDIS"
    
    log_info "Fazendo backup do Redis..."
    
    # Forçar save do Redis
    docker-compose exec -T redis redis-cli BGSAVE
    
    # Aguardar conclusão do save
    while [ "$(docker-compose exec -T redis redis-cli LASTSAVE)" = "$(docker-compose exec -T redis redis-cli LASTSAVE)" ]; do
        sleep 1
    done
    
    # Copiar arquivo RDB
    docker-compose exec -T redis cat /data/dump.rdb > "${BACKUP_PATH}/redis_dump.rdb"
    
    if [ $? -eq 0 ]; then
        log_success "Backup Redis concluído"
    else
        log_error "Falha no backup Redis"
        exit 1
    fi
    
    # Backup de informações do Redis
    docker-compose exec -T redis redis-cli INFO > "${BACKUP_PATH}/redis_info.txt"
    docker-compose exec -T redis redis-cli CONFIG GET "*" > "${BACKUP_PATH}/redis_config.txt"
    
    log_info "Informações Redis salvas"
}

# Backup volumes N8N
backup_n8n_volumes() {
    log_header "3. BACKUP VOLUMES N8N"
    
    log_info "Fazendo backup dos volumes N8N..."
    
    # Criar diretório para volumes
    mkdir -p "${BACKUP_PATH}/volumes"
    
    # Backup do volume n8n_data
    docker run --rm \
        -v n8n-production_n8n_data:/source:ro \
        -v "${PWD}/${BACKUP_PATH}/volumes":/backup \
        alpine:latest \
        tar czf /backup/n8n_data.tar.gz -C /source .
    
    if [ $? -eq 0 ]; then
        log_success "Backup volume n8n_data concluído"
    else
        log_error "Falha no backup volume n8n_data"
        exit 1
    fi
    
    # Backup do volume postgres_data
    docker run --rm \
        -v n8n-production_postgres_data:/source:ro \
        -v "${PWD}/${BACKUP_PATH}/volumes":/backup \
        alpine:latest \
        tar czf /backup/postgres_data.tar.gz -C /source .
    
    if [ $? -eq 0 ]; then
        log_success "Backup volume postgres_data concluído"
    else
        log_error "Falha no backup volume postgres_data"
        exit 1
    fi
    
    # Backup do volume redis_data
    docker run --rm \
        -v n8n-production_redis_data:/source:ro \
        -v "${PWD}/${BACKUP_PATH}/volumes":/backup \
        alpine:latest \
        tar czf /backup/redis_data.tar.gz -C /source .
    
    if [ $? -eq 0 ]; then
        log_success "Backup volume redis_data concluído"
    else
        log_error "Falha no backup volume redis_data"
        exit 1
    fi
    
    log_info "Todos os volumes N8N foram salvos"
}

# Backup configurações
backup_configurations() {
    log_header "4. BACKUP CONFIGURAÇÕES"
    
    log_info "Fazendo backup das configurações..."
    
    # Criar diretório para configurações
    mkdir -p "${BACKUP_PATH}/configs"
    
    # Backup docker-compose.yml
    if [ -f "docker-compose.yml" ]; then
        cp "docker-compose.yml" "${BACKUP_PATH}/configs/"
        log_info "docker-compose.yml salvo"
    fi
    
    # Backup .env
    if [ -f ".env" ]; then
        cp ".env" "${BACKUP_PATH}/configs/"
        log_info ".env salvo"
    fi
    
    # Backup de outros arquivos de configuração
    for config_file in "*.yml" "*.yaml" "*.json" "*.conf"; do
        if ls $config_file 1> /dev/null 2>&1; then
            cp $config_file "${BACKUP_PATH}/configs/" 2>/dev/null || true
        fi
    done
    
    # Backup do diretório configs se existir
    if [ -d "configs" ]; then
        cp -r "configs" "${BACKUP_PATH}/"
        log_info "Diretório configs salvo"
    fi
    
    log_success "Configurações salvas"
}

# Backup metadados do sistema
backup_system_metadata() {
    log_header "5. BACKUP METADADOS DO SISTEMA"
    
    log_info "Coletando metadados do sistema..."
    
    # Criar diretório para metadados
    mkdir -p "${BACKUP_PATH}/metadata"
    
    # Informações do Docker
    docker --version > "${BACKUP_PATH}/metadata/docker_version.txt"
    docker-compose --version > "${BACKUP_PATH}/metadata/docker_compose_version.txt"
    docker-compose ps > "${BACKUP_PATH}/metadata/containers_status.txt"
    docker images > "${BACKUP_PATH}/metadata/docker_images.txt"
    docker volume ls > "${BACKUP_PATH}/metadata/docker_volumes.txt"
    docker network ls > "${BACKUP_PATH}/metadata/docker_networks.txt"
    
    # Informações do sistema
    date > "${BACKUP_PATH}/metadata/backup_timestamp.txt"
    uname -a > "${BACKUP_PATH}/metadata/system_info.txt" 2>/dev/null || true
    df -h > "${BACKUP_PATH}/metadata/disk_usage.txt" 2>/dev/null || true
    free -h > "${BACKUP_PATH}/metadata/memory_usage.txt" 2>/dev/null || true
    
    # Informações específicas do N8N
    docker-compose exec -T n8n n8n --version > "${BACKUP_PATH}/metadata/n8n_version.txt" 2>/dev/null || echo "N8N version not available" > "${BACKUP_PATH}/metadata/n8n_version.txt"
    
    log_success "Metadados coletados"
}

# Criar arquivo de manifesto
create_manifest() {
    log_header "6. CRIANDO MANIFESTO DO BACKUP"
    
    cat > "${BACKUP_PATH}/BACKUP_MANIFEST.md" << EOF
# BACKUP N8N COMPLETO

## Informações do Backup
- **Data/Hora**: $(date)
- **Backup ID**: $BACKUP_NAME
- **Versão do Script**: 1.0

## Conteúdo do Backup

### 1. Banco de Dados PostgreSQL
- \`postgresql_dump.sql.gz\` - Dump completo do banco N8N
- \`postgresql_databases.txt\` - Lista de databases
- \`postgresql_tables.txt\` - Lista de tabelas

### 2. Cache Redis
- \`redis_dump.rdb\` - Dump do Redis
- \`redis_info.txt\` - Informações do Redis
- \`redis_config.txt\` - Configurações do Redis

### 3. Volumes Docker
- \`volumes/n8n_data.tar.gz\` - Volume de dados do N8N
- \`volumes/postgres_data.tar.gz\` - Volume de dados do PostgreSQL
- \`volumes/redis_data.tar.gz\` - Volume de dados do Redis

### 4. Configurações
- \`configs/docker-compose.yml\` - Configuração Docker Compose
- \`configs/.env\` - Variáveis de ambiente
- \`configs/\` - Outros arquivos de configuração

### 5. Metadados
- \`metadata/\` - Informações do sistema e versões

## Como Restaurar
\`\`\`bash
./scripts/restore-n8n-complete.sh $BACKUP_NAME
\`\`\`

## Verificação de Integridade
- PostgreSQL: $(du -h "${BACKUP_PATH}/postgresql_dump.sql.gz" 2>/dev/null | cut -f1 || echo "N/A")
- Redis: $(du -h "${BACKUP_PATH}/redis_dump.rdb" 2>/dev/null | cut -f1 || echo "N/A")
- Volumes: $(du -h "${BACKUP_PATH}/volumes/" 2>/dev/null | tail -1 | cut -f1 || echo "N/A")
- Total: $(du -h "${BACKUP_PATH}/" 2>/dev/null | tail -1 | cut -f1 || echo "N/A")
EOF

    log_success "Manifesto criado"
}

# Compactar backup final
compress_backup() {
    log_header "7. COMPACTANDO BACKUP FINAL"
    
    log_info "Compactando backup completo..."
    
    cd "$BACKUP_DIR"
    tar czf "${BACKUP_NAME}.tar.gz" "$BACKUP_NAME"
    
    if [ $? -eq 0 ]; then
        # Remover diretório não compactado
        rm -rf "$BACKUP_NAME"
        log_success "Backup compactado: ${BACKUP_NAME}.tar.gz"
    else
        log_error "Falha na compactação do backup"
        exit 1
    fi
    
    cd - > /dev/null
}

# Limpeza de backups antigos
cleanup_old_backups() {
    log_header "8. LIMPEZA DE BACKUPS ANTIGOS"
    
    log_info "Removendo backups com mais de $RETENTION_DAYS dias..."
    
    find "$BACKUP_DIR" -name "n8n_backup_*.tar.gz" -type f -mtime +$RETENTION_DAYS -delete
    
    REMAINING_BACKUPS=$(find "$BACKUP_DIR" -name "n8n_backup_*.tar.gz" -type f | wc -l)
    log_info "Backups restantes: $REMAINING_BACKUPS"
}

# Relatório final
show_final_report() {
    echo ""
    echo -e "${GREEN}"
    echo "====================================================================="
    echo "                    BACKUP CONCLUÍDO COM SUCESSO!"
    echo "====================================================================="
    echo -e "${NC}"
    
    BACKUP_SIZE=$(du -h "${BACKUP_DIR}/${BACKUP_NAME}.tar.gz" 2>/dev/null | cut -f1 || echo "N/A")
    
    log_info "Detalhes do Backup:"
    echo "  📁 Arquivo: ${BACKUP_NAME}.tar.gz"
    echo "  📊 Tamanho: $BACKUP_SIZE"
    echo "  📍 Local: ${BACKUP_DIR}/${BACKUP_NAME}.tar.gz"
    echo "  🕒 Data: $(date)"
    echo ""
    
    log_info "Conteúdo do Backup:"
    echo "  ✅ PostgreSQL (workflows, credenciais, execuções)"
    echo "  ✅ Redis (cache, filas, sessões)"
    echo "  ✅ Volumes N8N (arquivos, configurações)"
    echo "  ✅ Configurações (docker-compose, .env)"
    echo "  ✅ Metadados do sistema"
    echo ""
    
    log_info "Para restaurar:"
    echo "  ./scripts/restore-n8n-complete.sh $BACKUP_NAME"
    echo ""
    
    log_success "Backup N8N completo finalizado!"
}

# Função principal
main() {
    show_banner
    check_prerequisites
    backup_postgresql
    backup_redis
    backup_n8n_volumes
    backup_configurations
    backup_system_metadata
    create_manifest
    compress_backup
    cleanup_old_backups
    show_final_report
}

# Executar se for script principal
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
