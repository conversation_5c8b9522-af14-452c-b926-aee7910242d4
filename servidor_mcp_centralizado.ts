/**
 * SERVIDOR MCP CENTRALIZADO - Augment Code Orchestrator V5.0
 * 
 * Arquitetura: 1 servidor único que atende todos os clientes
 * - Augment Code, <PERSON>SC<PERSON>, <PERSON>, <PERSON> CLI, <PERSON>
 * - Elimina 100% da duplicação de processos
 * - Baseado no example-remote-server oficial do MCP
 */

import express from 'express';
import cors from 'cors';
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { CallToolRequestSchema, ListToolsRequestSchema } from '@modelcontextprotocol/sdk/types.js';

// Importar todos os servidores MCP existentes
import { createMemoryServer } from '@modelcontextprotocol/server-memory';
import { createEverythingServer } from '@modelcontextprotocol/server-everything';
import { createGitHubServer } from '@modelcontextprotocol/server-github';
import { createSequentialThinkingServer } from '@modelcontextprotocol/server-sequential-thinking';
// Adicionar outros imports conforme necessário

const app = express();
const PORT = 3232;

interface MCPServerInstance {
  name: string;
  server: Server;
  tools: any[];
  resources: any[];
  prompts: any[];
}

class CentralizedMCPServer {
  private servers: Map<string, MCPServerInstance> = new Map();
  private app: express.Application;

  constructor() {
    this.app = express();
    this.setupMiddleware();
    this.initializeServers();
    this.setupRoutes();
  }

  private setupMiddleware() {
    // CORS para permitir conexões de qualquer origem
    this.app.use(cors({
      origin: true,
      methods: ['GET', 'POST', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'Mcp-Protocol-Version', 'Mcp-Session-Id'],
      exposedHeaders: ['Mcp-Protocol-Version', 'Mcp-Session-Id'],
      credentials: true
    }));

    this.app.use(express.json());

    // Headers de segurança
    this.app.use((req, res, next) => {
      res.setHeader('X-Content-Type-Options', 'nosniff');
      res.setHeader('X-Frame-Options', 'SAMEORIGIN');
      res.setHeader('Cache-Control', 'no-store, max-age=0');
      next();
    });

    // Logging
    this.app.use((req, res, next) => {
      console.log(`[${new Date().toISOString()}] ${req.method} ${req.url}`);
      next();
    });
  }

  private async initializeServers() {
    console.log('🚀 Inicializando servidores MCP...');

    // Configurar cada servidor MCP
    const serverConfigs = [
      {
        name: 'memory',
        factory: () => createMemoryServer(),
        env: {}
      },
      {
        name: 'everything', 
        factory: () => createEverythingServer(),
        env: {}
      },
      {
        name: 'github',
        factory: () => createGitHubServer(),
        env: {
          GITHUB_PERSONAL_ACCESS_TOKEN: process.env.GITHUB_PERSONAL_ACCESS_TOKEN || '****************************************'
        }
      },
      {
        name: 'sequential-thinking',
        factory: () => createSequentialThinkingServer(),
        env: {}
      },
      {
        name: 'context7',
        factory: () => this.createContext7Server(),
        env: {}
      },
      {
        name: '21st-dev-magic',
        factory: () => this.create21stDevServer(),
        env: {
          API_KEY: process.env.API_KEY_21ST || '3efe2d0659b1c6e95b2140da7376afe07cd3e51ae695c6a3e9f246e150f5e1f8'
        }
      },
      {
        name: 'supabase',
        factory: () => this.createSupabaseServer(),
        env: {
          SUPABASE_ACCESS_TOKEN: process.env.SUPABASE_ACCESS_TOKEN || '********************************************'
        }
      },
      {
        name: 'playwright',
        factory: () => this.createPlaywrightServer(),
        env: {}
      },
      {
        name: 'google-maps',
        factory: () => this.createGoogleMapsServer(),
        env: {
          GOOGLE_MAPS_API_KEY: process.env.GOOGLE_MAPS_API_KEY || 'AIzaSyDOiKX9cj85FZOFB3WyP2hUm6qoXUtj6bk'
        }
      },
      {
        name: 'netlify',
        factory: () => this.createNetlifyServer(),
        env: {}
      },
      {
        name: 'blowback',
        factory: () => this.createBlowbackServer(),
        env: {}
      },
      {
        name: 'windows-mcp',
        factory: () => this.createWindowsMCPServer(),
        env: {}
      }
    ];

    for (const config of serverConfigs) {
      try {
        // Configurar variáveis de ambiente
        Object.assign(process.env, config.env);

        // Criar servidor
        const server = await config.factory();
        
        // Obter capacidades do servidor
        const tools = await this.getServerTools(server);
        const resources = await this.getServerResources(server);
        const prompts = await this.getServerPrompts(server);

        this.servers.set(config.name, {
          name: config.name,
          server,
          tools,
          resources,
          prompts
        });

        console.log(`✅ Servidor ${config.name} inicializado com ${tools.length} tools`);
      } catch (error) {
        console.error(`❌ Erro ao inicializar servidor ${config.name}:`, error);
      }
    }

    console.log(`🎯 ${this.servers.size} servidores MCP centralizados ativos`);
  }

  private async getServerTools(server: Server): Promise<any[]> {
    try {
      const response = await server.request(
        { method: 'tools/list' },
        ListToolsRequestSchema
      );
      return response.tools || [];
    } catch {
      return [];
    }
  }

  private async getServerResources(server: Server): Promise<any[]> {
    try {
      const response = await server.request(
        { method: 'resources/list' },
        { type: 'object', properties: {} }
      );
      return response.resources || [];
    } catch {
      return [];
    }
  }

  private async getServerPrompts(server: Server): Promise<any[]> {
    try {
      const response = await server.request(
        { method: 'prompts/list' },
        { type: 'object', properties: {} }
      );
      return response.prompts || [];
    } catch {
      return [];
    }
  }

  private setupRoutes() {
    // Endpoint principal MCP (Streamable HTTP)
    this.app.all('/mcp', this.handleMCPRequest.bind(this));
    
    // Endpoint SSE (compatibilidade)
    this.app.get('/sse', this.handleSSEConnection.bind(this));
    this.app.post('/message', this.handleMessage.bind(this));

    // Endpoint de status
    this.app.get('/status', this.handleStatus.bind(this));

    // Endpoint de capacidades consolidadas
    this.app.get('/capabilities', this.handleCapabilities.bind(this));

    // Health check
    this.app.get('/health', (req, res) => {
      res.json({ 
        status: 'healthy', 
        servers: this.servers.size,
        timestamp: new Date().toISOString()
      });
    });
  }

  private async handleMCPRequest(req: express.Request, res: express.Response) {
    try {
      const { method, params } = req.body;

      // Roteamento baseado no método
      switch (method) {
        case 'initialize':
          return this.handleInitialize(req, res);
        case 'tools/list':
          return this.handleListTools(req, res);
        case 'tools/call':
          return this.handleCallTool(req, res, params);
        case 'resources/list':
          return this.handleListResources(req, res);
        case 'prompts/list':
          return this.handleListPrompts(req, res);
        default:
          res.status(400).json({ error: `Método não suportado: ${method}` });
      }
    } catch (error) {
      console.error('Erro no handleMCPRequest:', error);
      res.status(500).json({ error: 'Erro interno do servidor' });
    }
  }

  private handleInitialize(req: express.Request, res: express.Response) {
    res.json({
      protocolVersion: '2024-11-05',
      capabilities: {
        tools: {},
        resources: {},
        prompts: {},
        logging: {}
      },
      serverInfo: {
        name: 'Centralized MCP Server',
        version: '1.0.0'
      }
    });
  }

  private handleListTools(req: express.Request, res: express.Response) {
    const allTools: any[] = [];
    
    for (const [serverName, serverInstance] of this.servers) {
      for (const tool of serverInstance.tools) {
        allTools.push({
          ...tool,
          name: `${serverName}/${tool.name}`,
          description: `[${serverName}] ${tool.description}`
        });
      }
    }

    res.json({ tools: allTools });
  }

  private async handleCallTool(req: express.Request, res: express.Response, params: any) {
    try {
      const { name, arguments: args } = params;
      const [serverName, toolName] = name.split('/', 2);

      const serverInstance = this.servers.get(serverName);
      if (!serverInstance) {
        return res.status(404).json({ error: `Servidor não encontrado: ${serverName}` });
      }

      const response = await serverInstance.server.request(
        {
          method: 'tools/call',
          params: {
            name: toolName,
            arguments: args
          }
        },
        CallToolRequestSchema
      );

      res.json(response);
    } catch (error) {
      console.error('Erro ao chamar tool:', error);
      res.status(500).json({ error: 'Erro ao executar tool' });
    }
  }

  private handleListResources(req: express.Request, res: express.Response) {
    const allResources: any[] = [];
    
    for (const [serverName, serverInstance] of this.servers) {
      for (const resource of serverInstance.resources) {
        allResources.push({
          ...resource,
          uri: `${serverName}://${resource.uri}`,
          name: `[${serverName}] ${resource.name}`
        });
      }
    }

    res.json({ resources: allResources });
  }

  private handleListPrompts(req: express.Request, res: express.Response) {
    const allPrompts: any[] = [];
    
    for (const [serverName, serverInstance] of this.servers) {
      for (const prompt of serverInstance.prompts) {
        allPrompts.push({
          ...prompt,
          name: `${serverName}/${prompt.name}`,
          description: `[${serverName}] ${prompt.description}`
        });
      }
    }

    res.json({ prompts: allPrompts });
  }

  private handleSSEConnection(req: express.Request, res: express.Response) {
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    
    res.write('data: {"type":"connection","status":"connected"}\n\n');
    
    // Manter conexão viva
    const keepAlive = setInterval(() => {
      res.write('data: {"type":"ping"}\n\n');
    }, 30000);

    req.on('close', () => {
      clearInterval(keepAlive);
    });
  }

  private handleMessage(req: express.Request, res: express.Response) {
    // Implementar lógica de mensagem SSE
    res.json({ status: 'message received' });
  }

  private handleStatus(req: express.Request, res: express.Response) {
    const status = {
      server: 'Centralized MCP Server',
      version: '1.0.0',
      uptime: process.uptime(),
      servers: Array.from(this.servers.entries()).map(([name, instance]) => ({
        name,
        tools: instance.tools.length,
        resources: instance.resources.length,
        prompts: instance.prompts.length,
        status: 'active'
      })),
      totalTools: Array.from(this.servers.values()).reduce((sum, s) => sum + s.tools.length, 0),
      totalResources: Array.from(this.servers.values()).reduce((sum, s) => sum + s.resources.length, 0),
      totalPrompts: Array.from(this.servers.values()).reduce((sum, s) => sum + s.prompts.length, 0)
    };

    res.json(status);
  }

  private handleCapabilities(req: express.Request, res: express.Response) {
    const capabilities = {
      servers: Array.from(this.servers.keys()),
      endpoints: [
        'GET /health - Health check',
        'GET /status - Status detalhado',
        'GET /capabilities - Esta lista',
        'POST /mcp - Endpoint principal MCP',
        'GET /sse - Conexão SSE',
        'POST /message - Mensagens SSE'
      ],
      protocols: ['Streamable HTTP', 'Server-Sent Events'],
      authentication: 'None (local server)'
    };

    res.json(capabilities);
  }

  // Métodos para criar servidores específicos (implementar conforme necessário)
  private createContext7Server() {
    // Implementar servidor Context7
    return new Server({ name: 'context7', version: '1.0.0' }, {});
  }

  private create21stDevServer() {
    // Implementar servidor 21st-dev-magic
    return new Server({ name: '21st-dev-magic', version: '1.0.0' }, {});
  }

  private createSupabaseServer() {
    // Implementar servidor Supabase
    return new Server({ name: 'supabase', version: '1.0.0' }, {});
  }

  private createPlaywrightServer() {
    // Implementar servidor Playwright
    return new Server({ name: 'playwright', version: '1.0.0' }, {});
  }

  private createGoogleMapsServer() {
    // Implementar servidor Google Maps
    return new Server({ name: 'google-maps', version: '1.0.0' }, {});
  }

  private createNetlifyServer() {
    // Implementar servidor Netlify
    return new Server({ name: 'netlify', version: '1.0.0' }, {});
  }

  private createBlowbackServer() {
    // Implementar servidor Blowback
    return new Server({ name: 'blowback', version: '1.0.0' }, {});
  }

  private createWindowsMCPServer() {
    // Implementar servidor Windows MCP
    return new Server({ name: 'windows-mcp', version: '1.0.0' }, {});
  }

  public start() {
    this.app.listen(PORT, () => {
      console.log(`🎯 SERVIDOR MCP CENTRALIZADO ATIVO`);
      console.log(`📍 URL: http://localhost:${PORT}`);
      console.log(`🔧 Servidores: ${this.servers.size}`);
      console.log(`📊 Status: http://localhost:${PORT}/status`);
      console.log(`🏥 Health: http://localhost:${PORT}/health`);
      console.log(`⚡ Capabilities: http://localhost:${PORT}/capabilities`);
    });
  }
}

// Inicializar servidor
const centralServer = new CentralizedMCPServer();
centralServer.start();

export default CentralizedMCPServer;
