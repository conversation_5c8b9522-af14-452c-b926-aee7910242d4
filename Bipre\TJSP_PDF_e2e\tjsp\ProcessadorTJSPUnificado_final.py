#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
TJSP Processador - Versão SEM FILTRO
Processa números de autos de arquivo Excel, valida no TJSP, e baixa ofícios requisitórios.
Inclui sistema de checkpoint para reinicialização e auto-detecção de ambiente.

Características:
- Auto-detecção do arquivo Excel no diretório
- Sistema de checkpoint em Excel + JSON para retomada
- Auto-detecção de ambiente
- Integração com módulos de download especializados
- Logging avançado e tratamento robusto de erros
- Número da lista no Excel para facilitar reinício

MODIFICAÇÃO IMPORTANTE (2025-07-08):
- TODOS OS FILTROS removidos do PROCESSO PRINCIPAL
- Filtros de status, partes proibidas e palavras proibidas removidos
- Processos "extintos", com "partes proibidas" ou "palavras proibidas" CONTINUAM processamento
- Filtros aplicados APENAS nos PRECATÓRIOS INDIVIDUAIS
- Registro completo de todos os dados para análise
"""

import os
import sys
import time
import json
import logging
import re
import warnings

# Suprimir todos os warnings
warnings.filterwarnings("ignore")
os.environ['PYTHONWARNINGS'] = 'ignore'
import pandas as pd
import glob
from datetime import datetime
from pathlib import Path

# Suprimir logs desnecessários do TensorFlow e outros
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'
import warnings
warnings.filterwarnings('ignore')

try:
    from tqdm import tqdm
except ImportError:
    print("Biblioteca 'tqdm' não encontrada. Necessária para a barra de progresso.")
    print("Para instalar, execute: pip install tqdm")
    sys.exit(1)

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, ElementNotInteractableException, InvalidSelectorException

# --- CONFIGURAÇÕES GLOBAIS ---
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
CHECKPOINT_FILE = "checkpoint_processamento.json"
CHECKPOINT_EXCEL_FILE = "checkpoint_processamento.xlsx"

# URLs do TJSP
URL_TJSP_CONSULTA = 'https://esaj.tjsp.jus.br/cpopg/open.do'
URL_TJSP_LOGIN = 'https://esaj.tjsp.jus.br/sajcas/login?service=https%3A%2F%2Fesaj.tjsp.jus.br%2Fesaj%2Fj_spring_cas_security_check'

# Configurações de timeout
TIMEOUT_PADRAO = 30
TIMEOUT_DOWNLOAD = 60
TIMEOUT_NAVEGACAO = 15

# Indicadores de sessão expirada - baseado no HTML específico do TJSP
INDICADORES_SESSAO_EXPIRADA = ["identificar-se", "Identificar-se", "IDENTIFICAR-SE"]

# Nomes de usuários válidos conhecidos
USUARIOS_VALIDOS_CONHECIDOS = ["DENIS HENRIQUE SOUSA OLIVEIRA"]

# Adicionar diretório do script ao path para importações
if SCRIPT_DIR not in sys.path:
    sys.path.append(SCRIPT_DIR)

logger = None

def detectar_arquivo_excel_automatico():
    """
    Detecta automaticamente o arquivo Excel no diretório do script
    """
    nomes_possiveis = [
        "final_numeros_autos_extraidos",
        "final_numeros_autos_extraidos.xlsx",
        "final_numeros_autos",
        "final_numeros",
        "numeros_autos_extraidos",
        "numeros_autos",
        "numeros_autos.xlsx"
    ]
    
    print("🔍 Detectando arquivo Excel automaticamente...")
    
    for nome in nomes_possiveis:
        caminho_completo = os.path.join(SCRIPT_DIR, nome)
        if os.path.exists(caminho_completo):
            return caminho_completo
    
    # Buscar por padrão mais amplo
    padroes_busca = [
        "final_numeros_autos_extraidos*.xlsx",
        "final_numeros_autos_extraidos*.xls",
        "*numeros_autos*.xlsx",
        "*numeros_autos*.xls"
    ]
    
    for padrao in padroes_busca:
        arquivos_encontrados = glob.glob(os.path.join(SCRIPT_DIR, padrao))
        if arquivos_encontrados:
            arquivo_escolhido = arquivos_encontrados[0]
            return arquivo_escolhido
    
    return None

class ConfiguradorAmbiente:
    """
    Classe responsável por detectar e configurar o ambiente automaticamente
    """
    
    def __init__(self):
        self.usuario_detectado = None
        self.perfil_chrome = None
        self.chromedriver_path = None
        self.detectar_ambiente()
    
    def detectar_ambiente(self):
        """Detecta automaticamente o ambiente de forma genérica"""
        usuario_atual = os.getenv('USERNAME', 'Usuario')
        self.usuario_detectado = usuario_atual

        # Configuração genérica do perfil Chrome
        self.perfil_chrome = f"C:/Users/<USER>/Profile_TJSP"

        # Criar perfil se não existir
        self.criar_perfil_chrome_automatico()

        # Configuração de autenticação genérica
        self.nomes_autenticacao = ["Autenticado", usuario_atual]

        # Localizar ChromeDriver (será usado apenas como fallback)
        self.chromedriver_path = self.localizar_chromedriver()

        # Logger pode não estar inicializado ainda
        try:
            logger.info(f"Ambiente detectado: {self.usuario_detectado}")
            logger.info(f"Perfil Chrome: {self.perfil_chrome}")
        except:
            pass

    def criar_perfil_chrome_automatico(self):
        """Cria o perfil Chrome automaticamente se não existir"""
        try:
            if not os.path.exists(self.perfil_chrome):
                os.makedirs(self.perfil_chrome, exist_ok=True)
                try:
                    logger.info(f"Perfil Chrome criado automaticamente: {self.perfil_chrome}")
                except:
                    pass
                print(f"📁 Perfil Chrome criado: {self.perfil_chrome}")
            else:
                try:
                    logger.info(f"Perfil Chrome existente encontrado: {self.perfil_chrome}")
                except:
                    pass
                print(f"✅ Perfil Chrome encontrado: {self.perfil_chrome}")
        except Exception as e:
            try:
                logger.error(f"Erro ao criar perfil Chrome: {e}")
            except:
                pass
            print(f"⚠️ Erro ao criar perfil Chrome: {e}")
        
        if logger:
            logger.info(f"Ambiente detectado: {self.usuario_detectado}")
            logger.info(f"Perfil Chrome: {self.perfil_chrome}")
            logger.info(f"ChromeDriver: {self.chromedriver_path or 'Gerenciamento automático'}")
    
    def localizar_chromedriver(self):
        """Localiza o ChromeDriver na ordem de prioridade"""
        caminhos_chromedriver = [
            os.path.join(SCRIPT_DIR, "drivers", "chromedriver.exe"),
            f"C:/Users/<USER>/.wdm/drivers/chromedriver/win64/*/chromedriver.exe",
            f"C:/Users/<USER>/.cache/selenium/chromedriver/win64/*/chromedriver.exe",
        ]
        
        for caminho_pattern in caminhos_chromedriver:
            if '*' in caminho_pattern:
                caminhos_encontrados = glob.glob(caminho_pattern)
                if caminhos_encontrados:
                    caminho = sorted(caminhos_encontrados)[-1]
                    if os.path.exists(caminho):
                        return caminho
            else:
                if os.path.exists(caminho_pattern):
                    return caminho_pattern
        
        return None

class GerenciadorCheckpoint:
    """
    Classe responsável por gerenciar o mapeamento de progresso com suporte a Excel
    """
    
    def __init__(self, arquivo_checkpoint=CHECKPOINT_FILE, arquivo_checkpoint_excel=CHECKPOINT_EXCEL_FILE):
        self.arquivo_checkpoint = os.path.join(SCRIPT_DIR, arquivo_checkpoint)
        self.arquivo_checkpoint_excel = os.path.join(SCRIPT_DIR, arquivo_checkpoint_excel)
        self.dados_checkpoint = self.carregar_checkpoint()
    
    def carregar_checkpoint(self):
        """Carrega o checkpoint existente ou cria um novo"""
        if os.path.exists(self.arquivo_checkpoint):
            try:
                with open(self.arquivo_checkpoint, 'r', encoding='utf-8') as f:
                    dados = json.load(f)
                if logger:
                    logger.info(f"Checkpoint carregado: {len(dados.get('consultados', {}))} registros consultados")
                return dados
            except Exception as e:
                if logger:
                    logger.warning(f"Erro ao carregar checkpoint: {e}")
        
        # Estrutura inicial do checkpoint
        return {
            "inicio_processamento": datetime.now().isoformat(),
            "ultima_atualizacao": datetime.now().isoformat(),
            "ultimo_consultado": "",  # Último número de autos consultado
            "consultados": {},  # numero_autos -> dados_consulta
            "processados": {},  # numero_autos -> dados_resultado
            "falhas": {},       # numero_autos -> dados_erro
            "estatisticas": {
                "total_consultados": 0,
                "total_processados": 0,
                "sucessos": 0,
                "falhas": 0,
                "downloads_realizados": 0,
                "recuperacoes_sessao": 0
            }
        }
    
    def salvar_checkpoint(self):
        """Salva o checkpoint atual em JSON e Excel"""
        self.dados_checkpoint["ultima_atualizacao"] = datetime.now().isoformat()
        
        # Salvar JSON
        try:
            with open(self.arquivo_checkpoint, 'w', encoding='utf-8') as f:
                json.dump(self.dados_checkpoint, f, ensure_ascii=False, indent=2)
            if logger:
                logger.debug("Checkpoint JSON salvo com sucesso")
        except Exception as e:
            if logger:
                logger.error(f"Erro ao salvar checkpoint JSON: {e}")
        
        # Salvar Excel
        self.salvar_checkpoint_excel()
    
    def salvar_checkpoint_excel(self):
        """Salva o checkpoint em formato Excel"""
        try:
            dados_para_excel = []
            
            # Processar dados dos consultados (incluindo não processados)
            for numero_autos, info in self.dados_checkpoint["consultados"].items():
                dados_consulta = {
                    "NumeroLista": info.get("numero_lista", "N/A"),
                    "NumeroAutosPrincipal": numero_autos,
                    "StatusGeral": "Consultado",
                    "StatusConsulta": info.get("status_consulta", "Consultado"),
                    "DataHoraConsulta": info.get("timestamp", ""),
                    "StatusProcessoPrincipal": info.get("status_processo", "N/A"),
                    "NumeroPrecatorioCompletoTJSP": "N/A",
                    "NomeClientePrecatorio": "N/A",
                    "StatusValidacaoPrecatorio": "N/A",
                    "StatusDownloadOficio": "N/A",
                    "NomeArquivoBaixado": "N/A",
                    "DataHoraProcessamento": info.get("timestamp", ""),
                    "AmbienteProcessamento": "Sistema",
                    "ObservacaoConsulta": info.get("observacao", "")
                }
                dados_para_excel.append(dados_consulta)
            
            # Processar dados dos processados (sobrescrever se existir)
            for numero_autos, info in self.dados_checkpoint["processados"].items():
                # Remover entrada anterior se existir
                dados_para_excel = [d for d in dados_para_excel if d["NumeroAutosPrincipal"] != numero_autos]
                
                resultado = info["resultado"].copy()
                resultado["NumeroLista"] = info.get("numero_lista", resultado.get("NumeroLista", "N/A"))
                resultado["NumeroAutosPrincipal"] = numero_autos
                resultado["StatusGeral"] = "Processado"
                resultado["StatusConsulta"] = "Sucesso"
                resultado["DataHoraConsulta"] = info["timestamp"]
                resultado["TimestampProcessamento"] = info["timestamp"]
                resultado["ObservacaoConsulta"] = "Processamento completo"
                dados_para_excel.append(resultado)
            
            # Processar dados das falhas (sobrescrever se existir)
            for numero_autos, info in self.dados_checkpoint["falhas"].items():
                # Remover entrada anterior se existir
                dados_para_excel = [d for d in dados_para_excel if d["NumeroAutosPrincipal"] != numero_autos]
                
                dados_falha = {
                    "NumeroLista": info.get("numero_lista", "N/A"),
                    "NumeroAutosPrincipal": numero_autos,
                    "StatusGeral": "Falha",
                    "StatusConsulta": "Erro",
                    "DataHoraConsulta": info["timestamp"],
                    "StatusProcessoPrincipal": "Erro",
                    "NumeroPrecatorioCompletoTJSP": "N/A",
                    "NomeClientePrecatorio": "N/A",
                    "StatusValidacaoPrecatorio": "N/A",
                    "StatusDownloadOficio": "N/A",
                    "NomeArquivoBaixado": "N/A",
                    "DataHoraProcessamento": info["timestamp"],
                    "AmbienteProcessamento": "Sistema",
                    "TimestampProcessamento": info["timestamp"],
                    "ErroDetalhado": info["erro"],
                    "ObservacaoConsulta": f"Erro: {info['erro']}"
                }
                dados_para_excel.append(dados_falha)
            
            if dados_para_excel:
                df = pd.DataFrame(dados_para_excel)
                
                # Reorganizar colunas para melhor visualização
                colunas_ordenadas = [
                    "NumeroLista", "NumeroAutosPrincipal", "StatusGeral", "StatusConsulta", "DataHoraConsulta",
                    "StatusProcessoPrincipal", "NumeroPrecatorioCompletoTJSP", "NomeClientePrecatorio",
                    "StatusValidacaoPrecatorio", "StatusDownloadOficio", "NomeArquivoBaixado", 
                    "DataHoraProcessamento", "AmbienteProcessamento", "ObservacaoConsulta"
                ]
                
                # Adicionar coluna de erro se existir
                if "ErroDetalhado" in df.columns:
                    colunas_ordenadas.append("ErroDetalhado")
                
                # Reordenar colunas
                colunas_existentes = [col for col in colunas_ordenadas if col in df.columns]
                df = df[colunas_existentes]
                
                # Ordenar por NumeroLista se possível
                try:
                    # Converter NumeroLista para numérico para ordenação correta
                    df_temp = df.copy()
                    df_temp['NumeroListaSort'] = pd.to_numeric(df_temp['NumeroLista'], errors='coerce')
                    df = df_temp.sort_values('NumeroListaSort').drop('NumeroListaSort', axis=1)
                except:
                    # Se falhar, manter ordem original
                    pass
                
                # Salvar com formatação
                with pd.ExcelWriter(self.arquivo_checkpoint_excel, engine='openpyxl') as writer:
                    df.to_excel(writer, sheet_name='Checkpoint_Processamento', index=False)
                    
                    # Adicionar planilha com estatísticas
                    stats_data = {
                        "Métrica": [
                            "Total Consultados",
                            "Total Processados",
                            "Sucessos",
                            "Downloads Realizados", 
                            "Falhas",
                            "Último Consultado",
                            "Início Processamento",
                            "Última Atualização"
                        ],
                        "Valor": [
                            self.dados_checkpoint["estatisticas"]["total_consultados"],
                            self.dados_checkpoint["estatisticas"]["total_processados"],
                            self.dados_checkpoint["estatisticas"]["sucessos"],
                            self.dados_checkpoint["estatisticas"]["downloads_realizados"],
                            self.dados_checkpoint["estatisticas"]["falhas"],
                            self.dados_checkpoint.get("ultimo_consultado", "Nenhum"),
                            self.dados_checkpoint["inicio_processamento"],
                            self.dados_checkpoint["ultima_atualizacao"]
                        ]
                    }
                    
                    stats_df = pd.DataFrame(stats_data)
                    stats_df.to_excel(writer, sheet_name='Estatisticas', index=False)
                
                if logger:
                    logger.debug(f"Checkpoint Excel salvo: {self.arquivo_checkpoint_excel}")
            
        except Exception as e:
            if logger:
                logger.error(f"Erro ao salvar checkpoint Excel: {e}")
            print(f"⚠️ Erro ao salvar checkpoint Excel: {e}")
    
    def marcar_consultado(self, numero_autos, status_consulta="Consultado", observacao="", status_processo="N/A", numero_lista=None):
        """Marca um número como consultado"""
        self.dados_checkpoint["consultados"][numero_autos] = {
            "status_consulta": status_consulta,
            "observacao": observacao,
            "status_processo": status_processo,
            "numero_lista": numero_lista,
            "timestamp": datetime.now().isoformat()
        }
        self.dados_checkpoint["ultimo_consultado"] = numero_autos
        self.dados_checkpoint["estatisticas"]["total_consultados"] += 1
        
        # Salvar a cada 3 consultas para não perder progresso
        if self.dados_checkpoint["estatisticas"]["total_consultados"] % 3 == 0:
            self.salvar_checkpoint()
    
    def marcar_processado(self, numero_autos, resultado, numero_lista=None):
        """Marca um número como processado"""
        self.dados_checkpoint["processados"][numero_autos] = {
            "resultado": resultado,
            "numero_lista": numero_lista,
            "timestamp": datetime.now().isoformat()
        }
        self.dados_checkpoint["estatisticas"]["total_processados"] += 1
        if resultado.get("StatusDownloadOficio") == "Sucesso":
            self.dados_checkpoint["estatisticas"]["downloads_realizados"] += 1
            self.dados_checkpoint["estatisticas"]["sucessos"] += 1
        
        # Salvar a cada processamento bem-sucedido
        self.salvar_checkpoint()
    
    def marcar_falha(self, numero_autos, erro, numero_lista=None):
        """Marca um número como falha"""
        self.dados_checkpoint["falhas"][numero_autos] = {
            "erro": str(erro),
            "numero_lista": numero_lista,
            "timestamp": datetime.now().isoformat()
        }
        self.dados_checkpoint["estatisticas"]["falhas"] += 1
        self.salvar_checkpoint()
    
    def ja_consultado(self, numero_autos):
        """Verifica se um número já foi consultado"""
        return (numero_autos in self.dados_checkpoint["consultados"] or 
                numero_autos in self.dados_checkpoint["processados"] or
                numero_autos in self.dados_checkpoint["falhas"])
    
    def ja_processado(self, numero_autos):
        """Verifica se um número já foi processado com sucesso"""
        return numero_autos in self.dados_checkpoint["processados"]
    
    def obter_estatisticas(self):
        """Retorna estatísticas do processamento"""
        # Garantir que a estatística de recuperações existe
        if "recuperacoes_sessao" not in self.dados_checkpoint["estatisticas"]:
            self.dados_checkpoint["estatisticas"]["recuperacoes_sessao"] = 0
        return self.dados_checkpoint["estatisticas"]

    def incrementar_recuperacoes_sessao(self):
        """Incrementa o contador de recuperações de sessão"""
        if "recuperacoes_sessao" not in self.dados_checkpoint["estatisticas"]:
            self.dados_checkpoint["estatisticas"]["recuperacoes_sessao"] = 0
        self.dados_checkpoint["estatisticas"]["recuperacoes_sessao"] += 1
        self.dados_checkpoint["ultima_atualizacao"] = datetime.now().isoformat()
        if logger:
            logger.info(f"Recuperação de sessão registrada. Total: {self.dados_checkpoint['estatisticas']['recuperacoes_sessao']}")

    def obter_ultimo_consultado(self):
        """Retorna o último número consultado"""
        return self.dados_checkpoint.get("ultimo_consultado", "")

class ProcessadorTJSPUnificado:
    """
    Classe principal para processamento unificado do TJSP
    """
    
    def __init__(self):
        self.configurar_logging()
        self.ambiente = ConfiguradorAmbiente()
        self.checkpoint = GerenciadorCheckpoint()
        self.driver = None
        self.main_window_handle = None
        
        # Diretórios
        self.download_dir = os.path.join(SCRIPT_DIR, "downloads_completos")
        self.log_dir = os.path.join(SCRIPT_DIR, "logs_completos")
        
        # Criar diretórios necessários
        os.makedirs(self.download_dir, exist_ok=True)
        os.makedirs(self.log_dir, exist_ok=True)
        
        # Importar módulos de download
        self.importar_modulos_download()
    
    def configurar_logging(self):
        """Configura o sistema de logging"""
        global logger
        
        log_dir = os.path.join(SCRIPT_DIR, "logs_completos")
        os.makedirs(log_dir, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_filename = f"processador_unificado_tjsp_{timestamp}.log"
        log_path = os.path.join(log_dir, log_filename)
        
        if logging.getLogger().hasHandlers():
            logging.getLogger().handlers.clear()
        
        # Handler para arquivo
        file_handler = logging.FileHandler(log_path, encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        file_formatter = logging.Formatter('%(asctime)s [%(levelname)s] - %(funcName)s - %(message)s', datefmt='%Y-%m-%d %H:%M:%S')
        file_handler.setFormatter(file_formatter)
        
        # Handler para console (apenas WARNING e acima)
        stream_handler = logging.StreamHandler(sys.stdout)
        stream_handler.setLevel(logging.ERROR)  # Reduzir ainda mais os logs no console
        stream_formatter = logging.Formatter('[LOG-%(levelname)s] %(message)s')
        stream_handler.setFormatter(stream_formatter)
        
        # Configurar logger
        logger_obj = logging.getLogger()
        logger_obj.setLevel(logging.INFO)
        logger_obj.addHandler(file_handler)
        logger_obj.addHandler(stream_handler)
        
        logger = logger_obj
        
        # Reduzir verbosidade do Selenium e outras bibliotecas
        from selenium.webdriver.remote.remote_connection import LOGGER as selenium_logger
        selenium_logger.setLevel(logging.ERROR)
        
        # Suprimir logs específicos
        logging.getLogger("urllib3").setLevel(logging.ERROR)
        logging.getLogger("tensorflow").setLevel(logging.ERROR)
        logging.getLogger("absl").setLevel(logging.ERROR)
    
    def importar_modulos_download(self):
        """Importa os módulos especializados de download"""
        try:
            from tjsp_download import atualizar_baixar_documento as tjsp_atualizar_baixar_documento
            self.tjsp_download_func = tjsp_atualizar_baixar_documento
            self.tjsp_download_available = True
            logger.info("Módulo tjsp_download.py importado com sucesso")
        except ImportError as e:
            logger.error(f"Falha ao importar tjsp_download.py: {e}")
            self.tjsp_download_available = False
            # Função fallback
            self.tjsp_download_func = lambda driver, nome, diretorio: False
    
    def carregar_numeros_excel(self, caminho_excel):
        """Carrega números de autos do arquivo Excel"""
        print(f"📋 Carregando números de autos de: {os.path.basename(caminho_excel)}")
        logger.info(f"Carregando números de: {caminho_excel}")
        
        if not os.path.exists(caminho_excel):
            print(f"❌ Arquivo Excel não encontrado: {caminho_excel}")
            logger.error(f"Arquivo Excel não encontrado: {caminho_excel}")
            return []
        
        try:
            # Ler Excel
            df = pd.read_excel(caminho_excel)
            
            # Verificar se tem a coluna correta
            if 'Número_Autos' not in df.columns:
                print(f"❌ Coluna 'Número_Autos' não encontrada no Excel")
                print(f"Colunas disponíveis: {list(df.columns)}")
                logger.error(f"Coluna 'Número_Autos' não encontrada. Colunas: {list(df.columns)}")
                return []
            
            # Extrair números válidos
            numeros_validos = []
            numeros_invalidos = []
            
            for idx, row in df.iterrows():
                numero = str(row['Número_Autos']).strip()
                
                if not numero or numero == 'nan':
                    continue
                
                # Validar formato
                if re.match(r'\d{7}-\d{2}\.\d{4}\.\d\.\d{2}\.\d{4}', numero):
                    numeros_validos.append(numero)
                else:
                    numeros_invalidos.append((idx + 2, numero))  # +2 por causa do header
                    logger.warning(f"Linha {idx + 2}: formato inválido '{numero}'")
            
            print(f"✅ Carregados {len(numeros_validos)} números válidos do Excel")
            if numeros_invalidos:
                print(f"⚠️ {len(numeros_invalidos)} números com formato inválido ignorados")
                logger.warning(f"{len(numeros_invalidos)} números inválidos ignorados")
            
            logger.info(f"Total números válidos carregados: {len(numeros_validos)}")
            return numeros_validos

        except Exception as e:
            print(f"❌ Erro ao ler arquivo Excel: {e}")
            logger.error(f"Erro ao ler arquivo Excel: {e}", exc_info=True)
            return []

    def _determinar_numero_inicial_checkpoint(self, numeros_lista, ultimo_consultado, numero_inicial_usuario):
        """
        Determina o número inicial baseado no checkpoint inteligente
        Encontra o primeiro número NÃO consultado na lista

        Args:
            numeros_lista (list): Lista de números a processar
            ultimo_consultado (str): Último número consultado do checkpoint
            numero_inicial_usuario (int): Número inicial fornecido pelo usuário

        Returns:
            int: Número inicial determinado (1-based)
        """
        try:
            # Obter estatísticas do checkpoint
            stats = self.checkpoint.obter_estatisticas()
            total_consultados = stats.get("total_consultados", 0)

            # Se não há checkpoint, usar entrada do usuário
            if total_consultados == 0 or not ultimo_consultado:
                print(f"📝 Nenhum checkpoint encontrado - Iniciando do número {numero_inicial_usuario}")
                return numero_inicial_usuario

            # Encontrar o primeiro número NÃO consultado
            primeiro_nao_consultado = None
            for idx, numero in enumerate(numeros_lista):
                if not self.checkpoint.ja_consultado(numero):
                    primeiro_nao_consultado = idx + 1  # 1-based
                    break

            if primeiro_nao_consultado is None:
                print(f"✅ Todos os números já foram consultados!")
                return len(numeros_lista) + 1  # Indicar que terminou

            # Lógica inteligente do checkpoint
            if numero_inicial_usuario == 1 and primeiro_nao_consultado > 1:
                # Se usuário usou padrão (1) e há checkpoint, sempre usar checkpoint
                print(f"🎯 CHECKPOINT INTELIGENTE ATIVADO!")
                print(f"   • Usuário usou padrão (1), mas há {primeiro_nao_consultado - 1} números já consultados")
                print(f"   • Iniciando automaticamente do número {primeiro_nao_consultado}")
                logger.info(f"Checkpoint inteligente: usuário usou padrão, iniciando do número {primeiro_nao_consultado}")
                return primeiro_nao_consultado
            elif primeiro_nao_consultado > numero_inicial_usuario:
                # Se checkpoint é maior que entrada específica do usuário
                print(f"🎯 Usando checkpoint: iniciando do número {primeiro_nao_consultado}")
                print(f"   • Pulando {primeiro_nao_consultado - 1} números já consultados")
                logger.info(f"Checkpoint aplicado: iniciando do número {primeiro_nao_consultado}, pulando {primeiro_nao_consultado - 1} já consultados")
                return primeiro_nao_consultado
            else:
                print(f"👤 Usando entrada do usuário: iniciando do número {numero_inicial_usuario}")
                if primeiro_nao_consultado > numero_inicial_usuario:
                    print(f"   • Atenção: alguns números podem já ter sido consultados")
                return numero_inicial_usuario

        except Exception as e:
            print(f"⚠️ Erro ao determinar número inicial: {e}")
            logger.error(f"Erro ao determinar número inicial: {e}")
            return numero_inicial_usuario

    def configurar_chrome(self):
        """Configura as opções do Chrome com configurações testadas e funcionais"""
        chrome_options = Options()

        # Configuração do perfil Chrome (TESTADO E FUNCIONANDO)
        chrome_options.add_argument(f"--user-data-dir={self.ambiente.perfil_chrome}")
        chrome_options.add_argument("--profile-directory=Default")

        # Configurações básicas
        chrome_options.add_argument("--start-maximized")
        chrome_options.add_argument("--disable-popup-blocking")

        # Suprimir TODOS os logs e warnings do Chrome
        chrome_options.add_argument("--log-level=3")
        chrome_options.add_argument("--silent")
        chrome_options.add_argument("--disable-logging")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-gpu-sandbox")
        chrome_options.add_argument("--disable-software-rasterizer")
        chrome_options.add_argument("--disable-background-timer-throttling")
        chrome_options.add_argument("--disable-backgrounding-occluded-windows")
        chrome_options.add_argument("--disable-renderer-backgrounding")
        chrome_options.add_argument("--disable-features=TranslateUI,VizDisplayCompositor")
        chrome_options.add_argument("--disable-ipc-flooding-protection")

        # Configurações para automação (TESTADAS)
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)

        # Configurações para WebSigner (TESTADAS)
        chrome_options.add_argument("--disable-web-security")
        chrome_options.add_argument("--allow-running-insecure-content")
        chrome_options.add_argument("--disable-features=VizDisplayCompositor")

        # Configurações de download
        prefs = {
            "download.prompt_for_download": False,
            "download.default_directory": self.download_dir,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True,
            "plugins.always_open_pdf_externally": True
        }
        chrome_options.add_experimental_option("prefs", prefs)

        return chrome_options
    
    def iniciar_navegador(self):
        """Inicia o navegador Chrome com o método otimizado que funcionou"""
        try:
            logger.info("Iniciando navegador Chrome com perfil específico")

            # Usar apenas o método que funcionou - gerenciamento automático
            chrome_options = self.configurar_chrome()
            self.driver = webdriver.Chrome(options=chrome_options)

            logger.info("Navegador iniciado com gerenciamento automático (método otimizado)")
            return True

        except Exception as e:
            print(f"❌ Erro ao iniciar o navegador: {e}")
            logger.error(f"Erro ao iniciar o navegador: {e}", exc_info=True)
            return False
    
    def autenticar_usuario(self):
        """Realiza autenticação automatizada no TJSP com certificado digital"""
        try:
            logger.info("Iniciando autenticação automatizada com certificado digital.")

            self.driver.get(URL_TJSP_LOGIN)
            time.sleep(2)

            # Primeiro, verificar se já está autenticado
            if self._verificar_autenticacao_existente():
                return True

            # Tentar autenticação automatizada com certificado
            if self._autenticar_certificado_automatico():
                return True

            # Se falhou, usar método manual como fallback
            logger.warning("Autenticação automática falhou, usando método manual")
            return self._autenticacao_manual_fallback()

        except Exception as e:
            logger.error(f"Erro na autenticação: {e}", exc_info=True)
            return self._autenticacao_manual_fallback()

    def _verificar_autenticacao_existente(self):
        """Verifica se o usuário já está autenticado"""
        try:
            logger.info("Verificando se já está autenticado")
            time.sleep(1)

            for nome in self.ambiente.nomes_autenticacao:
                if nome in self.driver.page_source.lower():
                    logger.info(f"Usuário já autenticado: {nome}")
                    print("✅ Usuário já autenticado")
                    return True
            return False
        except Exception as e:
            logger.error(f"Erro ao verificar autenticação existente: {e}")
            return False

    def _autenticar_certificado_automatico(self):
        """Realiza autenticação automática com certificado digital"""
        try:
            logger.info("Iniciando autenticação automática com certificado digital")

            # Aguardar a página carregar completamente
            time.sleep(2)

            # Verificar se a aba de certificado digital está ativa
            try:
                aba_certificado = self._aguardar_elemento_presente((By.ID, "linkAbaCertificado"), timeout=5)
                if aba_certificado:
                    parent_li = aba_certificado.find_element(By.XPATH, "..")
                    if "ui-tabs-selected" not in parent_li.get_attribute("class"):
                        logger.info("Clicando na aba Certificado Digital")
                        aba_certificado.click()
                        time.sleep(1)
                    else:
                        logger.info("Aba Certificado Digital já está ativa")
                else:
                    logger.warning("Aba de certificado digital não encontrada")
                    return False
            except Exception as e:
                logger.error(f"Erro ao acessar aba certificado: {e}")
                return False

            # Verificar se há certificado disponível
            if not self._verificar_certificado_disponivel():
                logger.warning("Nenhum certificado válido encontrado")
                return False

            # Aguardar um pouco para garantir que o certificado está carregado
            time.sleep(2)

            # Clicar no botão Entrar do certificado
            try:
                botao_entrar = self._aguardar_elemento_clicavel((By.ID, "submitCertificado"), timeout=10)
                # Se o seletor principal falhou, tentar seletores alternativos
                if not botao_entrar:
                    logger.info("Botão Entrar principal não encontrado, tentando seletores alternativos")
                    botao_entrar = self._tentar_seletores_alternativos_botao_entrar()

                if botao_entrar:
                    logger.info("Clicando no botão Entrar")

                    # Scroll para o elemento se necessário
                    self.driver.execute_script("arguments[0].scrollIntoView(true);", botao_entrar)
                    time.sleep(0.5)

                    # Tentar clicar usando JavaScript se o clique normal falhar
                    try:
                        botao_entrar.click()
                    except Exception:
                        print("� Clique normal falhou, tentando com JavaScript...")
                        self.driver.execute_script("arguments[0].click();", botao_entrar)

                    # Aguardar processamento da autenticação
                    print("⏳ Aguardando processamento da autenticação...")
                    time.sleep(5)

                    # Verificar se a autenticação foi bem-sucedida
                    return self._verificar_sucesso_autenticacao()
                else:
                    print("⚠️ Botão Entrar não encontrado com nenhum seletor")
                    # Executar debug para entender a estrutura da página
                    self._debug_elementos_pagina()
                    return False

            except Exception as e:
                print(f"⚠️ Erro ao clicar no botão Entrar: {e}")
                logger.error(f"Erro ao clicar no botão Entrar: {e}")
                return False

        except Exception as e:
            print(f"⚠️ Erro na autenticação automática: {e}")
            logger.error(f"Erro na autenticação automática: {e}")
            return False

    def _verificar_sucesso_autenticacao(self):
        """Verifica se a autenticação foi bem-sucedida"""
        try:
            # Aguardar redirecionamento inicial
            max_tentativas = 10

            for tentativa in range(max_tentativas):
                time.sleep(2)

                # Verificar se ainda está na página de login
                if "sajcas/login" in self.driver.current_url:
                    print(f"⏳ Tentativa {tentativa + 1}/{max_tentativas} - Ainda na página de login...")
                    continue
                else:
                    break

            # Navegar para a página de consulta para verificar autenticação
            print("🔄 Navegando para página de consulta para verificar autenticação...")
            self.driver.get(URL_TJSP_CONSULTA)
            time.sleep(3)

            # Verificar se o elemento de nome do usuário está presente
            try:
                elemento_nome = self.driver.find_element(By.ID, "headerNmUsuarioLogado")
                nome_usuario = elemento_nome.text.strip()

                if nome_usuario and len(nome_usuario) > 3:
                    print(f"✅ Autenticação automática confirmada")
                    logger.info(f"Autenticação automática bem-sucedida - Usuário: {nome_usuario}")
                    return True
                else:
                    print("⚠️ Elemento de nome encontrado mas vazio")

            except NoSuchElementException:
                print("⚠️ Elemento de nome do usuário não encontrado")

            # Verificação alternativa usando métodos de fallback
            print("🔄 Tentando verificação alternativa...")
            if self.verificar_sessao_por_indicadores_alternativos():
                return True

            # Verificação final usando nomes de autenticação configurados
            page_source_lower = self.driver.page_source.lower()
            for nome in self.ambiente.nomes_autenticacao:
                if nome in page_source_lower:
                    logger.info(f"Autenticação confirmada por nome configurado: {nome}")
                    print(f"✅ Autenticação confirmada por nome: {nome}")
                    return True

            print("❌ Autenticação automática não confirmada")

            # Debug detalhado quando a autenticação falha
            print("🔍 Executando debug detalhado...")
            self.debug_estado_autenticacao()

            return False

        except Exception as e:
            print(f"❌ Erro ao verificar sucesso da autenticação: {e}")
            logger.error(f"Erro ao verificar sucesso da autenticação: {e}")
            return False

    def _autenticacao_manual_fallback(self):
        """Método de fallback para autenticação manual"""
        try:
            print("\n" + "="*80)
            print(f"🔑 AUTENTICAÇÃO MANUAL NECESSÁRIA - {self.ambiente.usuario_detectado}".center(80))
            print("="*80)
            print("\nSiga estas etapas para autenticação:")
            print("\n1. Verifique se o certificado está selecionado")
            print("2. Clique no botão 'Entrar'")
            print("3. Aguarde o redirecionamento")
            print("\nIMPORTANTE: Nome deve aparecer no canto superior direito do portal.")
            print("\n>> Pressione Enter SOMENTE APÓS concluir a autenticação completa...")
            input()
            time.sleep(3)

            # Verificar autenticação
            usuario_encontrado = False
            for nome in self.ambiente.nomes_autenticacao:
                if nome in self.driver.page_source.lower():
                    logger.info(f"Autenticação manual OK (nome '{nome}' encontrado).")
                    print(f"✅ Autenticação manual confirmada (nome '{nome}' encontrado)!")
                    usuario_encontrado = True
                    break

            if usuario_encontrado:
                return True
            else:
                logger.warning("Nome de usuário não confirmado automaticamente.")
                print("⚠️ Nome não confirmado automaticamente.")
                confirmacao = input("Confirma que a autenticação está completa e seu nome aparece no portal? (s/n): ").strip().lower()
                if confirmacao == 's':
                    logger.info("Autenticação manual confirmada pelo usuário.")
                    print("✅ Autenticação manual confirmada!")
                    return True
                else:
                    logger.error("Autenticação manual não confirmada.")
                    print("❌ Autenticação manual não confirmada.")
                    return False

        except Exception as e:
            print(f"❌ Erro na autenticação manual: {e}")
            logger.error(f"Erro na autenticação manual: {e}", exc_info=True)
            return False

    def _aguardar_elemento_clicavel(self, locator, timeout=10):
        """Aguarda um elemento ficar clicável"""
        try:
            wait = WebDriverWait(self.driver, timeout)
            elemento = wait.until(EC.element_to_be_clickable(locator))
            return elemento
        except TimeoutException:
            return None

    def _aguardar_elemento_presente(self, locator, timeout=10):
        """Aguarda um elemento estar presente na página"""
        try:
            wait = WebDriverWait(self.driver, timeout)
            elemento = wait.until(EC.presence_of_element_located(locator))
            return elemento
        except TimeoutException:
            return None

    def _verificar_certificado_disponivel(self):
        """Verifica se há certificados disponíveis para autenticação"""
        try:
            # Aguardar o select de certificados aparecer
            select_certificado = self._aguardar_elemento_presente((By.ID, "certificados"), timeout=5)
            if not select_certificado:
                print("⚠️ Dropdown de certificados não encontrado")
                return False

            # Verificar se há opções no select
            opcoes = select_certificado.find_elements(By.TAG_NAME, "option")
            if len(opcoes) == 0:
                print("⚠️ Nenhuma opção de certificado encontrada")
                return False

            # Verificar se a primeira opção tem valor (certificado válido)
            primeira_opcao = opcoes[0]
            valor_certificado = primeira_opcao.get_attribute("value")
            texto_certificado = primeira_opcao.text

            if valor_certificado and valor_certificado.strip():
                print(f"✅ Certificado válido encontrado")
                return True
            else:
                print("⚠️ Certificado encontrado mas sem valor válido")
                return False

        except Exception as e:
            print(f"⚠️ Erro ao verificar certificado: {e}")
            return False

    def _tentar_seletores_alternativos_botao_entrar(self):
        """Tenta encontrar o botão Entrar usando seletores alternativos"""
        seletores_alternativos = [
            (By.ID, "submitCertificado"),
            (By.CSS_SELECTOR, "input[value='Entrar'].actionCertificado"),
            (By.CSS_SELECTOR, "input[type='button'][value='Entrar']"),
            (By.CSS_SELECTOR, ".actionCertificado"),
            (By.XPATH, "//input[@value='Entrar' and contains(@class, 'actionCertificado')]"),
            (By.XPATH, "//input[@type='button' and @value='Entrar']"),
            (By.XPATH, "//button[text()='Entrar']"),
            (By.CSS_SELECTOR, "input[name='pbEntrar'][value='Entrar']")
        ]

        for i, seletor in enumerate(seletores_alternativos):
            try:
                print(f"🔍 Tentando seletor {i+1}: {seletor[1]}")
                elemento = self._aguardar_elemento_clicavel(seletor, timeout=3)
                if elemento and elemento.is_displayed():
                    print(f"✅ Seletor {i+1} funcionou!")
                    return elemento
            except Exception as e:
                print(f"⚠️ Seletor {i+1} falhou: {e}")
                continue

        return None

    def _debug_elementos_pagina(self):
        """Método de debug para listar elementos relevantes na página"""
        try:
            print("🔍 DEBUG: Analisando elementos da página...")

            # Verificar se estamos na página correta
            print(f"URL atual: {self.driver.current_url}")

            # Procurar por elementos relacionados a certificado
            elementos_certificado = self.driver.find_elements(By.CSS_SELECTOR, "*[id*='certificado'], *[class*='certificado']")
            print(f"Elementos com 'certificado': {len(elementos_certificado)}")

            # Procurar por botões
            botoes = self.driver.find_elements(By.CSS_SELECTOR, "input[type='button'], input[type='submit'], button")
            print(f"Botões encontrados: {len(botoes)}")
            for i, botao in enumerate(botoes[:5]):  # Mostrar apenas os primeiros 5
                try:
                    valor = botao.get_attribute("value") or botao.text
                    id_elem = botao.get_attribute("id")
                    classe = botao.get_attribute("class")
                    print(f"  Botão {i+1}: valor='{valor}', id='{id_elem}', class='{classe}'")
                except:
                    pass

            # Verificar se há erros JavaScript
            try:
                logs = self.driver.get_log('browser')
                if logs:
                    print(f"Logs do navegador: {len(logs)} entradas")
                    for log in logs[-3:]:  # Mostrar últimas 3 entradas
                        print(f"  {log['level']}: {log['message']}")
            except:
                print("⚠️ Não foi possível obter logs do navegador")

        except Exception as e:
            print(f"⚠️ Erro no debug: {e}")

    def verificar_sessao_ativa(self):
        """Verifica se a sessão ainda está ativa no TJSP"""
        try:
            # Log completo para arquivo, mensagem simples para usuário
            logger.info("Verificando se a sessão ainda está ativa")

            # Garantir que estamos na página correta para verificação
            url_atual = self.driver.current_url
            if URL_TJSP_CONSULTA not in url_atual:
                logger.info(f"Navegando para página de consulta: {URL_TJSP_CONSULTA}")
                self.driver.get(URL_TJSP_CONSULTA)
                time.sleep(3)
            else:
                logger.info("Já na página de consulta")
                time.sleep(1)

            # Aguardar a página carregar completamente
            try:
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.TAG_NAME, "body"))
                )
            except TimeoutException:
                logger.warning("Timeout ao aguardar carregamento da página")
                return False

            # Verificar se o nome do usuário aparece no header (elemento específico do TJSP)
            try:
                elemento_nome = self.driver.find_element(By.ID, "headerNmUsuarioLogado")

                # Verificar se o elemento está visível
                if not elemento_nome.is_displayed():
                    logger.warning("Elemento headerNmUsuarioLogado encontrado mas não está visível")
                    return False

                nome_usuario = elemento_nome.text.strip()
                logger.info(f"Texto do elemento headerNmUsuarioLogado: '{nome_usuario}'")

                # Verificar se é uma sessão expirada usando lógica melhorada
                if self._detectar_sessao_expirada(nome_usuario):
                    print("❌ Sessão expirada - realizando novo login")
                    logger.warning(f"Sessão expirada detectada - Texto encontrado: '{nome_usuario}'")
                    return False

                # Verificar se é um nome válido (mais de 3 caracteres e contém letras)
                if nome_usuario and len(nome_usuario) > 3 and any(c.isalpha() for c in nome_usuario):
                    # Adicionar usuário à lista de válidos se não estiver
                    if nome_usuario not in USUARIOS_VALIDOS_CONHECIDOS:
                        USUARIOS_VALIDOS_CONHECIDOS.append(nome_usuario)
                        logger.info(f"Novo usuário válido adicionado: {nome_usuario}")

                    # Sessão ativa confirmada
                    logger.info(f"Sessão ativa confirmada - Usuário: {nome_usuario}")
                    return True
                else:
                    logger.warning(f"Nome de usuário inválido ou muito curto: '{nome_usuario}'")
                    return False

            except NoSuchElementException:
                logger.warning("Elemento headerNmUsuarioLogado não encontrado na página")
                print("⚠️ Elemento de nome do usuário não encontrado")

                # Verificar se há indicadores de que precisa fazer login
                if self._detectar_necessidade_login():
                    print("❌ Detectada necessidade de login")
                    return False

                # Verificação adicional: procurar por elementos que indicam login
                if self._verificar_elementos_login_alternativos():
                    print("✅ Login confirmado por verificação alternativa")
                    return True

                # Se não há indicadores de login necessário, assumir que NÃO está logado
                print("❌ Nome não encontrado e sem confirmação alternativa - Assumindo sessão expirada")
                return False

        except Exception as e:
            print(f"⚠️ Erro ao verificar sessão: {e}")
            logger.error(f"Erro ao verificar sessão: {e}")
            return False

    def _detectar_sessao_expirada(self, nome_usuario):
        """
        Detecta se a sessão expirou baseado no nome do usuário retornado
        Baseado no HTML específico do TJSP onde:
        - Logado: <span id="headerNmUsuarioLogado">DENIS HENRIQUE SOUSA OLIVEIRA</span>
        - Não logado: <span id="headerNmUsuarioLogado">Identificar-se </span>

        Args:
            nome_usuario (str): Nome do usuário obtido do elemento headerNmUsuarioLogado

        Returns:
            bool: True se sessão expirada, False caso contrário
        """
        if not nome_usuario:
            logger.warning("Nome do usuário vazio - sessão expirada")
            return True

        nome_limpo = nome_usuario.strip()
        nome_lower = nome_limpo.lower()

        # Verificar indicadores ESPECÍFICOS de sessão expirada do TJSP
        for indicador in INDICADORES_SESSAO_EXPIRADA:
            if indicador.lower() in nome_lower:
                logger.info(f"Indicador de sessão expirada detectado: '{nome_limpo}' contém '{indicador}'")
                return True

        # Verificar se é exatamente "Identificar-se" (caso específico do TJSP)
        if nome_limpo.lower() == "identificar-se":
            logger.info(f"Texto exato 'Identificar-se' detectado - sessão expirada")
            return True

        # Verificar se é muito curto para ser um nome real (menos de 5 caracteres)
        if len(nome_limpo) < 5:
            logger.info(f"Nome muito curto detectado: '{nome_limpo}' - possível sessão expirada")
            return True

        # Verificar se contém apenas caracteres especiais ou números
        if not any(c.isalpha() for c in nome_limpo):
            logger.info(f"Nome sem letras detectado: '{nome_limpo}' - sessão expirada")
            return True

        # Se chegou até aqui, é provavelmente um nome válido
        logger.info(f"Nome de usuário válido detectado: '{nome_limpo}' - sessão ativa")
        return False

    def verificar_login_por_html_especifico(self):
        """
        Verifica login usando o HTML específico do TJSP
        Baseado na estrutura:
        - Não logado: <span id="headerNmUsuarioLogado">Identificar-se </span>
        - Logado: <span id="headerNmUsuarioLogado">DENIS HENRIQUE SOUSA OLIVEIRA</span>
        """
        try:
            # Buscar o elemento específico
            elemento = self.driver.find_element(By.ID, "headerNmUsuarioLogado")

            if not elemento.is_displayed():
                logger.warning("Elemento headerNmUsuarioLogado não está visível")
                return False

            texto = elemento.text.strip()
            logger.info(f"Texto do elemento de login: '{texto}'")

            # Verificação específica para "Identificar-se"
            if texto.lower() == "identificar-se":
                logger.info("Detectado texto 'Identificar-se' - usuário NÃO está logado")
                return False

            # Verificação para nomes válidos (mais de 5 caracteres, contém letras)
            if len(texto) > 5 and any(c.isalpha() for c in texto):
                logger.info(f"Detectado nome de usuário válido: '{texto}' - usuário ESTÁ logado")
                return True

            # Casos intermediários
            logger.warning(f"Texto ambíguo detectado: '{texto}' - assumindo não logado")
            return False

        except NoSuchElementException:
            logger.warning("Elemento headerNmUsuarioLogado não encontrado")
            return False
        except Exception as e:
            logger.error(f"Erro na verificação específica de login: {e}")
            return False

    def _verificar_elementos_login_alternativos(self):
        """
        Verificação alternativa de login quando o elemento principal não é encontrado
        Procura por outros indicadores de que o usuário está logado
        """
        try:
            logger.info("Executando verificação alternativa de login")

            # 1. Verificar se há elementos que só aparecem quando logado
            elementos_logado = [
                "//a[contains(@href, 'logout')]",  # Link de logout
                "//span[contains(@class, 'header__navbar__name')]",  # Classe do nome do usuário
                "//div[contains(@class, 'header-user')]",  # Div do usuário
                "//span[contains(text(), 'Sair')]",  # Texto "Sair"
            ]

            for xpath in elementos_logado:
                try:
                    elemento = self.driver.find_element(By.XPATH, xpath)
                    if elemento.is_displayed():
                        logger.info(f"Elemento de login encontrado: {xpath}")
                        return True
                except NoSuchElementException:
                    continue

            # 2. Verificar se NÃO há elementos que indicam necessidade de login
            elementos_nao_logado = [
                "//a[contains(text(), 'Identificar-se')]",
                "//button[contains(text(), 'Entrar')]",
                "//input[@type='submit' and contains(@value, 'Entrar')]",
                "//a[contains(@href, 'login')]"
            ]

            for xpath in elementos_nao_logado:
                try:
                    elemento = self.driver.find_element(By.XPATH, xpath)
                    if elemento.is_displayed():
                        logger.info(f"Elemento de não-login encontrado: {xpath}")
                        return False
                except NoSuchElementException:
                    continue

            # 3. Verificar URL atual - se não está na página de login, provavelmente está logado
            url_atual = self.driver.current_url.lower()
            if "login" not in url_atual and "sajcas" not in url_atual:
                logger.info("URL não indica página de login - assumindo logado")
                return True

            logger.warning("Verificação alternativa não conseguiu determinar status de login")
            return False

        except Exception as e:
            logger.error(f"Erro na verificação alternativa de login: {e}")
            return False

    def verificar_login_tjsp_especifico(self):
        """
        Verificação específica para o TJSP baseada no HTML fornecido
        Detecta diferença entre:
        - Logado: <span id="headerNmUsuarioLogado">DENIS HENRIQUE SOUSA OLIVEIRA</span>
        - Não logado: <span id="headerNmUsuarioLogado">Identificar-se</span>
        """
        try:
            logger.info("Executando verificação específica do TJSP")

            # Garantir que estamos na URL correta
            if not self.garantir_url_consulta():
                logger.warning("Falha ao navegar para URL de consulta")
                return False

            # Aguardar carregamento completo
            time.sleep(2)

            # Procurar pelo elemento específico do TJSP
            try:
                elemento = self.driver.find_element(By.ID, "headerNmUsuarioLogado")

                if not elemento.is_displayed():
                    logger.warning("Elemento headerNmUsuarioLogado não está visível")
                    return False

                texto = elemento.text.strip()
                logger.info(f"Texto do elemento headerNmUsuarioLogado: '{texto}'")

                # Análise específica baseada no HTML do TJSP
                if texto.lower() == "identificar-se":
                    logger.info("Detectado 'Identificar-se' - usuário NÃO está logado")
                    print("❌ Status: NÃO LOGADO (texto: 'Identificar-se')")
                    return False

                # Verificar se é um nome válido (contém letras e tem tamanho razoável)
                if len(texto) > 5 and any(c.isalpha() for c in texto) and not any(ind.lower() in texto.lower() for ind in INDICADORES_SESSAO_EXPIRADA):
                    logger.info(f"Detectado nome de usuário válido: '{texto}' - usuário ESTÁ logado")
                    return True

                # Casos ambíguos
                logger.warning(f"Texto ambíguo no elemento: '{texto}' - assumindo não logado")
                return False

            except NoSuchElementException:
                logger.warning("Elemento headerNmUsuarioLogado não encontrado")
                print("❌ Status: ELEMENTO NÃO ENCONTRADO - assumindo não logado")
                return False

        except Exception as e:
            logger.error(f"Erro na verificação específica do TJSP: {e}")
            print(f"❌ Erro na verificação: {e}")
            return False

    def _detectar_necessidade_login(self):
        """Detecta se é necessário fazer login baseado em indicadores na página"""
        try:
            indicadores_login_necessario = [
                "fazer login",
                "entrar com certificado",
                "autenticação necessária",
                "login necessário",
                "sajcas/login",
                "acesso negado",
                "sessão expirada"
            ]

            page_source_lower = self.driver.page_source.lower()
            url_lower = self.driver.current_url.lower()

            for indicador in indicadores_login_necessario:
                if indicador in page_source_lower or indicador in url_lower:
                    print(f"🚨 Indicador de login necessário encontrado: {indicador}")
                    return True

            return False

        except Exception as e:
            print(f"⚠️ Erro ao detectar necessidade de login: {e}")
            return True  # Em caso de erro, assumir que precisa de login

    def _recuperar_sessao_automaticamente(self):
        """
        Recupera a sessão automaticamente quando detectada expiração

        Returns:
            bool: True se recuperação bem-sucedida, False caso contrário
        """
        try:
            print("🔄 RECUPERAÇÃO DE SESSÃO: Iniciando renovação automática")
            logger.info("RECUPERAÇÃO DE SESSÃO: Iniciando renovação automática")

            # Salvar checkpoint antes da recuperação
            self.checkpoint.salvar_checkpoint()

            # Limpar cookies e cache
            print("🔄 RECUPERAÇÃO DE SESSÃO: Limpando cookies e cache")
            self.driver.delete_all_cookies()

            # Tentar nova autenticação
            print("🔄 RECUPERAÇÃO DE SESSÃO: Tentando nova autenticação")
            if not self.autenticar_usuario():
                print("❌ RECUPERAÇÃO DE SESSÃO: Falha na nova autenticação")
                logger.error("RECUPERAÇÃO DE SESSÃO: Falha na nova autenticação")
                return False

            # Verificar se a recuperação foi bem-sucedida
            if self._verificar_sessao_pos_recuperacao():
                print("✅ RECUPERAÇÃO DE SESSÃO: Sessão recuperada com sucesso")
                logger.info("RECUPERAÇÃO DE SESSÃO: Sessão recuperada com sucesso")

                # Incrementar contador de recuperações
                self.checkpoint.incrementar_recuperacoes_sessao()

                return True
            else:
                print("❌ RECUPERAÇÃO DE SESSÃO: Verificação pós-recuperação falhou")
                logger.error("RECUPERAÇÃO DE SESSÃO: Verificação pós-recuperação falhou")
                return False

        except Exception as e:
            print(f"❌ RECUPERAÇÃO DE SESSÃO: Erro durante recuperação: {e}")
            logger.error(f"RECUPERAÇÃO DE SESSÃO: Erro durante recuperação: {e}")
            return False

    def _verificar_sessao_pos_recuperacao(self):
        """
        Verifica se a sessão foi recuperada com sucesso

        Returns:
            bool: True se sessão válida, False caso contrário
        """
        try:
            # Aguardar um pouco para estabilizar
            time.sleep(3)

            # Navegar para página de consulta
            if not self.garantir_url_consulta():
                return False

            # Verificar sessão
            return self.verificar_sessao_ativa()

        except Exception as e:
            print(f"❌ Erro na verificação pós-recuperação: {e}")
            logger.error(f"Erro na verificação pós-recuperação: {e}")
            return False

    def garantir_url_consulta(self):
        """Garante que estamos na URL de consulta correta"""
        try:
            url_atual = self.driver.current_url

            # Se não estamos na URL de consulta, navegar para ela
            if URL_TJSP_CONSULTA not in url_atual:
                logger.info(f"Navegando para URL de consulta: {URL_TJSP_CONSULTA}")
                self.driver.get(URL_TJSP_CONSULTA)

                # Aguardar carregamento
                try:
                    WebDriverWait(self.driver, 10).until(
                        EC.presence_of_element_located((By.TAG_NAME, "body"))
                    )
                    time.sleep(2)
                    logger.info("Navegação para URL de consulta concluída")
                    return True
                except TimeoutException:
                    print("⚠️ Timeout ao navegar para URL de consulta")
                    return False
            else:
                logger.info("Já na URL de consulta correta")
                return True

        except Exception as e:
            print(f"⚠️ Erro ao garantir URL de consulta: {e}")
            logger.error(f"Erro ao garantir URL de consulta: {e}")
            return False

    def debug_estado_autenticacao(self):
        """Debug detalhado do estado de autenticação"""
        try:
            print("\n" + "="*60)
            print("🔍 DEBUG - ESTADO DE AUTENTICAÇÃO")
            print("="*60)

            # Informações básicas
            print(f"URL atual: {self.driver.current_url}")
            print(f"Título da página: {self.driver.title}")

            # Verificar elemento principal de autenticação (específico do TJSP)
            try:
                elemento_nome = self.driver.find_element(By.ID, "headerNmUsuarioLogado")
                nome_usuario = elemento_nome.text.strip()

                # Análise específica do conteúdo
                if nome_usuario.lower().strip() == "identificar-se":
                    print(f"   - ❌ SESSÃO EXPIRADA: Texto é exatamente 'Identificar-se'")
                elif any(indicador.lower() in nome_usuario.lower() for indicador in INDICADORES_SESSAO_EXPIRADA):
                    print(f"   - ❌ SESSÃO EXPIRADA: Contém indicador de sessão expirada")
                elif len(nome_usuario) > 5 and any(c.isalpha() for c in nome_usuario):
                    print(f"   - ✅ SESSÃO ATIVA: Nome de usuário válido detectado")
                else:
                    print(f"   - ⚠️ INDETERMINADO: Texto não reconhecido")

            except NoSuchElementException:
                print("❌ Elemento headerNmUsuarioLogado NÃO encontrado")

            # Verificar elementos alternativos
            seletores_alternativos = [
                (".header__navbar__name__text", "CSS - header navbar name text"),
                (".header__navbar__menu__item--user", "CSS - header navbar user item"),
                ("//span[contains(@class, 'header__navbar__name')]", "XPath - header navbar name")
            ]

            for seletor, descricao in seletores_alternativos:
                try:
                    if seletor.startswith("//"):
                        elemento = self.driver.find_element(By.XPATH, seletor)
                    else:
                        elemento = self.driver.find_element(By.CSS_SELECTOR, seletor)

                    texto = elemento.text.strip()
                    print(f"✅ {descricao}: '{texto}'")
                except NoSuchElementException:
                    print(f"❌ {descricao}: NÃO encontrado")

            # Verificar indicadores de não logado
            indicadores_nao_logado = ["login", "entrar", "certificado", "autenticação"]
            page_source_lower = self.driver.page_source.lower()

            print("\n🔍 Verificando indicadores de não logado:")
            for indicador in indicadores_nao_logado:
                if indicador in page_source_lower:
                    print(f"⚠️ Encontrado: '{indicador}'")
                else:
                    print(f"✅ Não encontrado: '{indicador}'")

            print("="*60)

        except Exception as e:
            print(f"❌ Erro no debug de autenticação: {e}")
            logger.error(f"Erro no debug de autenticação: {e}")

    def verificar_sessao_por_indicadores_alternativos(self):
        """Verifica sessão usando indicadores alternativos"""
        try:
            # Verificar se há elementos que indicam que está logado
            indicadores_logado = [
                (By.CSS_SELECTOR, ".header__navbar__name__text"),
                (By.CSS_SELECTOR, ".header__navbar__menu__item--user"),
                (By.XPATH, "//span[contains(@class, 'header__navbar__name')]"),
                (By.XPATH, "//span[@id='headerNmUsuarioLogado']")
            ]

            for seletor in indicadores_logado:
                try:
                    elemento = self.driver.find_element(*seletor)
                    if elemento.is_displayed() and elemento.text.strip():
                        nome_encontrado = elemento.text.strip()
                        print(f"✅ Sessão confirmada por indicador alternativo: {nome_encontrado}")
                        return True
                except:
                    continue

            # Verificar se há indicadores de que NÃO está logado
            indicadores_nao_logado = [
                "fazer login",
                "entrar com certificado",
                "autenticação necessária",
                "login necessário",
                "sajcas/login"
            ]

            page_source_lower = self.driver.page_source.lower()
            url_lower = self.driver.current_url.lower()

            for indicador in indicadores_nao_logado:
                if indicador in page_source_lower or indicador in url_lower:
                    print(f"❌ Indicador de não logado encontrado: {indicador}")
                    return False

            # Se chegou até aqui, assumir que está logado
            print("✅ Nenhum indicador de não logado encontrado - Assumindo sessão ativa")
            return True

        except Exception as e:
            print(f"⚠️ Erro na verificação alternativa: {e}")
            return False

    def verificar_e_renovar_sessao(self):
        """Verifica se a sessão está ativa e renova se necessário"""
        try:
            logger.info("Verificando e renovando sessão se necessário")

            # Garantir que estamos na URL correta antes de verificar
            if not self.garantir_url_consulta():
                print("❌ Falha ao navegar para URL de consulta")
                return False

            # Primeira verificação: método específico do TJSP
            if self.verificar_login_tjsp_especifico():
                return True

            print("⚠️ Verificação específica do TJSP não confirmou login, tentando método principal...")

            # Segunda verificação: método principal
            if self.verificar_sessao_ativa():
                return True

            print("⚠️ Sessão principal não confirmada, tentando verificação alternativa...")

            # Terceira verificação: métodos alternativos
            if self.verificar_sessao_por_indicadores_alternativos():
                return True

            print("❌ Sessão expirada detectada - Iniciando renovação automática...")
            logger.warning("Sessão expirada detectada - Renovando automaticamente")

            # Tentar renovar a sessão
            return self._renovar_sessao_automaticamente()

        except Exception as e:
            print(f"❌ Erro crítico na verificação/renovação de sessão: {e}")
            logger.error(f"Erro crítico na verificação/renovação de sessão: {e}")
            return False

    def _renovar_sessao_automaticamente(self):
        """Renova a sessão automaticamente"""
        try:
            print("🔄 Iniciando renovação automática da sessão...")
            logger.info("Iniciando renovação automática da sessão")

            # Limpar cookies e cache para forçar nova autenticação
            print("🧹 Limpando cookies e cache...")
            self.driver.delete_all_cookies()

            # Aguardar um pouco
            time.sleep(2)

            # Tentar autenticação automática novamente
            print("🔐 Tentando nova autenticação...")
            if self.autenticar_usuario():
                print("✅ Autenticação renovada com sucesso!")
                logger.info("Autenticação renovada automaticamente com sucesso")

                # Aguardar um pouco antes de verificar
                time.sleep(3)

                # Verificar se a renovação foi bem-sucedida navegando para a página correta
                print("🔍 Verificando se a renovação foi bem-sucedida...")
                if self.verificar_sessao_ativa():
                    print("✅ Renovação confirmada - Sessão ativa")
                    logger.info("Renovação de sessão confirmada com sucesso")
                    return True
                else:
                    print("⚠️ Renovação não confirmada após verificação")
                    logger.warning("Renovação de sessão não confirmada")

                    # Tentar uma segunda verificação
                    print("🔄 Tentando segunda verificação...")
                    time.sleep(2)
                    if self.verificar_sessao_ativa():
                        print("✅ Renovação confirmada na segunda tentativa")
                        return True
                    else:
                        print("❌ Renovação falhou na segunda verificação")
                        return False
            else:
                print("❌ Falha na renovação automática da sessão")
                logger.error("Falha na renovação automática da sessão")
                return False

        except Exception as e:
            print(f"❌ Erro na renovação automática: {e}")
            logger.error(f"Erro na renovação automática: {e}")
            return False

    def executar_com_verificacao_sessao(self, funcao_callback, *args, **kwargs):
        """Executa uma função com verificação automática de sessão"""
        max_tentativas = 3

        for tentativa in range(max_tentativas):
            try:
                # Verificar sessão antes de executar
                if not self.verificar_e_renovar_sessao():
                    print(f"❌ Tentativa {tentativa + 1}/{max_tentativas}: Falha na verificação de sessão")
                    if tentativa < max_tentativas - 1:
                        print("🔄 Aguardando antes da próxima tentativa...")
                        time.sleep(5)
                        continue
                    else:
                        print("❌ Todas as tentativas de renovação falharam")
                        return False

                # Executar a função
                resultado = funcao_callback(*args, **kwargs)

                # Se a função foi executada com sucesso, retornar o resultado
                if resultado is not False:
                    return resultado
                else:
                    print(f"⚠️ Função retornou False na tentativa {tentativa + 1}")
                    if tentativa < max_tentativas - 1:
                        time.sleep(3)
                        continue

            except Exception as e:
                print(f"❌ Erro na tentativa {tentativa + 1}/{max_tentativas}: {e}")
                logger.error(f"Erro na execução com verificação de sessão (tentativa {tentativa + 1}): {e}")

                if tentativa < max_tentativas - 1:
                    print("🔄 Tentando novamente...")
                    time.sleep(5)
                    continue

        print("❌ Todas as tentativas falharam")
        return False

    def consultar_processo_com_verificacao_sessao(self, numero_autos, numero_processo_formatado, comarca_formatada):
        """Consulta processo com verificação automática de sessão"""
        return self.executar_com_verificacao_sessao(
            self.consultar_processo_principal,
            numero_autos,
            numero_processo_formatado,
            comarca_formatada
        )

    def verificar_sessao_periodica(self, numero_processado, total_numeros):
        """Verifica a sessão periodicamente durante o processamento com recuperação automática"""
        # Verificar a cada 10 processos ou a cada 30 minutos
        if numero_processado % 10 == 0:
            print(f"🔍 Verificação periódica de sessão ({numero_processado}/{total_numeros})...")

            # Primeira tentativa: verificação normal
            if self.verificar_sessao_ativa():
                print("✅ Sessão confirmada na verificação periódica")
                return True

            # Segunda tentativa: recuperação automática
            print("⚠️ Sessão expirada detectada - Tentando recuperação automática...")
            logger.warning(f"Sessão expirada no processo {numero_processado} - Iniciando recuperação")

            if self._recuperar_sessao_automaticamente():
                print("✅ Sessão recuperada automaticamente")
                logger.info(f"Sessão recuperada automaticamente no processo {numero_processado}")
                return True
            else:
                print("❌ Falha na recuperação automática de sessão")
                logger.error(f"Falha na recuperação automática no processo {numero_processado}")
                return False

        return True
    
    def preparar_numero_para_tjsp(self, numero_autos):
        """Prepara número de autos para consulta no TJSP"""
        match = re.match(r'(\d{7}-\d{2})\.(\d{4})\.\d\.\d{2}\.(\d{4})', numero_autos)
        if not match:
            logger.error(f"Formato inesperado: {numero_autos}")
            return None, None
        return match.group(1).replace('-', '') + match.group(2), match.group(3)
    
    def extrair_ano_processo(self, numero_autos):
        """Extrai o ano do processo"""
        match = re.search(r'\d{7}-\d{2}\.(\d{4})\.\d\.\d{2}\.\d{4}', numero_autos)
        return int(match.group(1)) if match else None
    
    def consultar_processo_principal(self, numero_autos, numero_processo_formatado, comarca_formatada):
        """Consulta o processo principal no TJSP"""
        try:
            logger.info(f"Consultando {numero_autos} (TJSP: {numero_processo_formatado} / Foro: {comarca_formatada})")
            
            self.driver.get(URL_TJSP_CONSULTA)
            WebDriverWait(self.driver, 6).until(EC.presence_of_element_located((By.ID, 'numeroDigitoAnoUnificado')))
            
            # Preencher campos
            self.driver.find_element(By.ID, 'numeroDigitoAnoUnificado').clear()
            time.sleep(0.1)
            self.driver.find_element(By.ID, 'numeroDigitoAnoUnificado').send_keys(numero_processo_formatado)
            
            self.driver.find_element(By.ID, 'foroNumeroUnificado').clear()
            time.sleep(0.1)
            self.driver.find_element(By.ID, 'foroNumeroUnificado').send_keys(comarca_formatada)
            
            self.driver.find_element(By.ID, 'botaoConsultarProcessos').click()
            time.sleep(1)
            
            # Verificar resultado
            pg_src_lower = self.driver.page_source.lower()
            url_lower = self.driver.current_url.lower()
            
            if ("dados do processo" in pg_src_lower or "classe:" in pg_src_lower or "cpopg/show.do" in url_lower):
                logger.info(f"Detalhes de {numero_autos} carregados diretamente.")
                return True
            
            if "processos encontrados" in pg_src_lower:
                logger.info(f"Lista de resultados para {numero_autos}.")
                try:
                    link = WebDriverWait(self.driver, 5).until(EC.element_to_be_clickable((By.LINK_TEXT, numero_autos)))
                    logger.info(f"Link para {numero_autos} encontrado. Clicando...")
                    
                    self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", link)
                    time.sleep(0.3)
                    link.click()
                    time.sleep(1)
                    
                    pg_src_apos_clique = self.driver.page_source.lower()
                    url_apos_clique = self.driver.current_url.lower()
                    
                    if ("dados do processo" in pg_src_apos_clique or "classe:" in pg_src_apos_clique or "cpopg/show.do" in url_apos_clique):
                        logger.info(f"Detalhes de {numero_autos} carregados após clique.")
                        return True
                    else:
                        print(f"⚠️ Detalhes de {numero_autos} não confirmados após clique.")
                        logger.warning(f"Detalhes de {numero_autos} não confirmados após clique. URL: {self.driver.current_url}")
                        return False
                        
                except TimeoutException:
                    print(f"❌ Link para {numero_autos} não encontrado na lista.")
                    logger.error(f"Link para {numero_autos} não encontrado na lista.")
                    return False
                except Exception as e:
                    print(f"❌ Erro ao clicar no link da lista para {numero_autos}: {e}")
                    logger.error(f"Erro ao clicar no link da lista para {numero_autos}: {e}", exc_info=True)
                    return False
            
            if "não existem informações disponíveis" in pg_src_lower:
                print(f"❌ Processo {numero_autos} não encontrado.")
                logger.error(f"Processo {numero_autos} não encontrado.")
                return False
            
            print(f"⚠️ Consulta de {numero_autos} em página inesperada.")
            logger.warning(f"Página inesperada para {numero_autos}. URL: {self.driver.current_url}")
            return False
            
        except Exception as e:
            print(f"❌ Erro geral ao consultar {numero_autos}: {e}")
            logger.error(f"Erro geral ao consultar {numero_autos}: {e}", exc_info=True)
            return False
    
    def clicar_botao_mais_movimentacoes(self):
        """Clica no botão 'Mais' das movimentações"""
        try:
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight*0.7);")
            time.sleep(0.5)
            
            botoes_mais = self.driver.find_elements(By.ID, "linkmovimentacoes")
            for bm in reversed(botoes_mais):
                if bm.is_displayed() and bm.is_enabled():
                    logger.info("Clicando 'Mais' das movimentações.")
                    print("- Clicando no botão 'Mais'...")
                    
                    self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", bm)
                    time.sleep(0.3)
                    
                    try:
                        bm.click()
                    except:
                        self.driver.execute_script("arguments[0].click();", bm)
                    
                    time.sleep(1)
                    return True
            
            print("ℹ️ Botão 'Mais' não encontrado/habilitado.")
            logger.info("Botão 'Mais' não encontrado/habilitado.")
            
        except Exception as e:
            print(f"⚠️ Erro ao clicar 'Mais': {e}")
            logger.info(f"Erro ao clicar 'Mais': {e}")
        
        return False
    
    def verificar_palavras_proibidas_mov(self, clicar_mais=True):
        """Verifica palavras proibidas nas movimentações"""
        palavras_proibidas = ["mandado de levantamento eletrônico", "mandado de levantamento", "cessão de crédito", "pedido de penhora"]
        print("🔍 Verificando palavras proibidas nas movimentações...")
        
        try:
            if clicar_mais and not self.clicar_botao_mais_movimentacoes():
                logger.warning("Não clicou 'Mais', verificação de palavras pode ser incompleta.")
            
            texto_mov = ""
            fonte = "Nenhuma"
            
            try:
                el_tabela = self.driver.find_element(By.ID, "tabelaTodasMovimentacoes")
                texto_mov = el_tabela.text.lower()
                fonte = "ID tabela"
            except:
                try:
                    movs = self.driver.find_elements(By.XPATH, "//table[contains(@class, 'movimentacoes')]//tr/td[2]")
                    if movs:
                        texto_mov = " ".join([m.text.lower() for m in movs])
                        fonte = "XPath tabela"
                    else:
                        texto_mov = self.driver.find_element(By.TAG_NAME, "body").text.lower()
                        fonte = "body"
                except:
                    texto_mov = self.driver.page_source.lower()
                    fonte = "page_source"
            
            logger.debug(f"Texto para palavras proibidas de: {fonte}")
            
            for p in palavras_proibidas:
                if p in texto_mov:
                    print(f"🚫 Palavra proibida: {p}")
                    logger.info(f"Palavra proibida: {p}")
                    return True
            
            print("✅ Nenhuma palavra proibida.")
            logger.info("Nenhuma palavra proibida.")
            return False
            
        except Exception as e:
            print(f"⚠️ Erro ao verificar palavras: {e}")
            logger.error(f"Erro ao verificar palavras: {e}", exc_info=True)
            return False
    
    def verificar_status_processo(self):
        """Verifica o status do processo"""
        print("🔍 Verificando status do processo...")
        
        try:
            status_elements = self.driver.find_elements(By.XPATH, 
                "//span[contains(@class, 'tag') and (contains(translate(., 'ARQUIVDOEXTNCLB', 'arquivdoextnclb'), 'arquivado') or contains(translate(., 'ARQUIVDOEXTNCLB', 'arquivdoextnclb'), 'extinto') or contains(translate(., 'ARQUIVDOEXTNCLB', 'arquivdoextnclb'), 'cancelado') or contains(translate(., 'ARQUIVDOEXTNCLB', 'arquivdoextnclb'), 'baixado'))] | " +
                "//div[contains(@class, 'status-processo') and (contains(translate(., 'ARQUIVDOEXTNCLB', 'arquivdoextnclb'), 'arquivado') or contains(translate(., 'ARQUIVDOEXTNCLB', 'arquivdoextnclb'), 'extinto') or contains(translate(., 'ARQUIVDOEXTNCLB', 'arquivdoextnclb'), 'cancelado') or contains(translate(., 'ARQUIVDOEXTNCLB', 'arquivdoextnclb'), 'baixado'))] | " + 
                "//div[contains(text(),'Situação do Processo:')]/following-sibling::div[1] |" +  
                "//span[contains(@style, 'color: red') and (contains(translate(., 'ARQUIVDOEXTNCLB', 'arquivdoextnclb'), 'arquivado') or contains(translate(., 'ARQUIVDOEXTNCLB', 'arquivdoextnclb'), 'extinto') or contains(translate(., 'ARQUIVDOEXTNCLB', 'arquivdoextnclb'), 'cancelado') or contains(translate(., 'ARQUIVDOEXTNCLB', 'arquivdoextnclb'), 'baixado'))] |" + 
                "//span[contains(text(),'Extinto') or contains(text(),'Arquivado') or contains(text(),'Cancelado') or contains(text(),'Baixado')]"
            )
            
            for el in status_elements:
                if el.is_displayed():
                    texto_el = el.text.strip().lower()
                    if any(s in texto_el for s in ["extinto", "arquivado", "baixado", "cancelado"]):
                        status_print = ("Extinto" if "extinto" in texto_el else 
                                      "Arquivado" if "arquivado" in texto_el else 
                                      "Baixado" if "baixado" in texto_el else 
                                      "Cancelado" if "cancelado" in texto_el else 
                                      texto_el.capitalize())
                        print(f"🚫 Status: {status_print}")
                        logger.info(f"Status 'finalizado' detectado: {texto_el}")
                        return "finalizado"
            
            pg_lower = self.driver.page_source.lower()
            if any(s in pg_lower for s in ["processo extinto", "processo arquivado", "processo baixado", "processo cancelado"]):
                print("🚫 Status Finalizado (texto da página).")
                logger.info("Status 'finalizado' (texto da página).")
                return "finalizado"
            
            print("✅ Status: Normal/Em Andamento.")
            logger.info("Status: normal/em andamento.")
            return "normal"
            
        except Exception as e:
            print(f"⚠️ Erro ao verificar status: {e}. Assumindo 'Normal'.")
            logger.warning(f"Erro ao verificar status: {e}. Assumindo 'normal'.")
            return "normal"
    
    def _extrair_nome_reqte_da_pagina(self):
        """Extrai o nome do requerente da página"""
        reqte_nome = ""
        
        # Estratégia 1
        try:
            label_td = self.driver.find_element(By.XPATH, "//tr[contains(@class, 'fundoClaro') or contains(@class, 'fundoEscuro')]/td[.//span[normalize-space(text())='Reqte:'] or normalize-space(text())='Reqte:']")
            nome_td = label_td.find_element(By.XPATH, "./following-sibling::td[1]")
            reqte_nome = nome_td.text.strip().split('\n')[0].strip()
            if reqte_nome:
                logger.debug(f"Reqte (S1): {reqte_nome}")
                return reqte_nome
        except NoSuchElementException:
            logger.debug("Reqte (S1) não encontrado.")
        
        # Estratégia 2
        try:
            reqte_element = self.driver.find_element(By.XPATH, "//span[contains(text(),'Reqte')]/ancestor::tr/td[@class='nomeParteEAdvogado']")
            reqte_nome = reqte_element.text.strip().split('\n')[0].strip()
            if reqte_nome:
                logger.debug(f"Reqte (S2): {reqte_nome}")
                return reqte_nome
        except NoSuchElementException:
            logger.debug("Reqte (S2) não encontrado.")
        
        # Estratégia 3
        try:
            reqte_div = self.driver.find_element(By.XPATH, "//div[contains(@class,'label') and contains(text(),'Reqte:')]/following-sibling::div[contains(@class,'valor')][1]")
            reqte_nome = reqte_div.text.strip().split('\n')[0].strip()
            if reqte_nome:
                logger.debug(f"Reqte (S3): {reqte_nome}")
                return reqte_nome
        except NoSuchElementException:
            logger.debug("Reqte (S3) não encontrado.")
        
        # Estratégia 4
        try:
            elementos = self.driver.find_elements(By.XPATH, "//*[contains(text(), 'Reqte:')]")
            for el in elementos:
                txt_completo = el.find_element(By.XPATH, "./parent::*").text
                match = re.search(r"Reqte:\s*(.+)", txt_completo, re.I)
                if match:
                    cand = match.group(1).split('\n')[0].strip()
                    if cand and len(cand) > 2 and not cand.lower().startswith("advogad"):
                        reqte_nome = cand
                        logger.debug(f"Reqte (S4): {reqte_nome}")
                        return reqte_nome
        except Exception as e:
            logger.debug(f"Erro Reqte (S4): {e}")
        
        return reqte_nome
    
    def verificar_partes_processo(self):
        """Verifica se há partes proibidas no processo"""
        palavras_proibidas = ["ltda", "s/a", "s.a", "eireli", "fundo", "prefeitura", "previdência", "fazenda", "associação", "cooperativa", "consórcio", "companhia", "instituto", "estado de", "sociedade", "conselho", "universidade", "investimento", "município de", "banco", "secretaria", "departamento", "cia", "epp", "me", "usina", "itau"]
        
        print("🔍 Verificando partes do processo (Reqte)...")
        logger.info("Verificando partes (Reqte)...")
        
        reqte_nome_original = self._extrair_nome_reqte_da_pagina()
        if not reqte_nome_original:
            print("⚠️ Reqte não localizado.")
            logger.error("Reqte não localizado para verificação de partes.")
            return False
        
        reqte_nome_lower = reqte_nome_original.lower()
        print(f"ℹ Reqte: {reqte_nome_original}")
        logger.info(f"Reqte para verificação de partes: {reqte_nome_original} (lower: {reqte_nome_lower})")
        
        for p_lower in palavras_proibidas:
            if re.search(r'\b' + re.escape(p_lower) + r'\b', reqte_nome_lower):
                print(f"🚫 Parte proibida (PJ) encontrada no Reqte: '{p_lower}' (em '{reqte_nome_original}')")
                logger.info(f"Parte proibida (PJ) encontrada no Reqte: '{p_lower}' em '{reqte_nome_original}'")
                return True
        
        print("✅ Reqte válido.")
        return False
    
    def obter_nome_cliente_do_precatorio(self):
        """Obtém o nome do cliente do precatório"""
        logger.info("Buscando nome do cliente (Reqte)...")

        reqte_nome_extraido = self._extrair_nome_reqte_da_pagina()
        if not reqte_nome_extraido:
            print("  │  ├─ ⚠️ Nome do cliente não localizado.")
            logger.error("Nome do cliente não localizado.")
            return "CLIENTE NÃO LOCALIZADO"

        reqte_nome_limpo = re.sub(r'\s+e\s+outro(s)?\s*$', '', reqte_nome_extraido, flags=re.I).strip()
        print(f"  │  ├─ Nome do cliente: {reqte_nome_limpo}")
        logger.info(f"Nome do cliente: {reqte_nome_limpo}")
        return reqte_nome_limpo
    
    def extrair_numero_precatorio_completo_da_pagina(self, numero_autos_principal):
        """Extrai o número completo do precatório da página"""
        print("🆔 Extraindo número completo do precatório...")
        logger.info(f"Extraindo N° precatório para principal: {numero_autos_principal}")
        
        try:
            xpath_num_prec = f"//span[@class='unj-larger' and contains(normalize-space(.), '{numero_autos_principal}') and contains(normalize-space(.), '(') and contains(normalize-space(.), ')')]"
            
            elementos_num_prec = []
            try:
                elementos_num_prec = self.driver.find_elements(By.XPATH, xpath_num_prec)
            except InvalidSelectorException:
                logger.error(f"XPath principal para número do precatório é inválido: {xpath_num_prec}. Usando fallback.")
                return "USAR_FALLBACK_TEXTO_LINK"
            except Exception as e_find:
                logger.error(f"Erro ao buscar elementos com XPath {xpath_num_prec}: {e_find}")
                return "USAR_FALLBACK_TEXTO_LINK"
            
            for el_num_prec in elementos_num_prec:
                if el_num_prec.is_displayed():
                    texto_completo = el_num_prec.text.strip()
                    logger.debug(f"Texto candidato (span.unj-larger): '{texto_completo}'")
                    match = re.search(r"(?P<num_base>\d{7}-\d{2}\.\d{4}\.\d\.\d{2}\.\d{4})\s*\((?P<sufixo>\d+)\)", texto_completo)
                    if match:
                        num_base_extraido = match.group("num_base")
                        suf_ext_str = match.group("sufixo")
                        if num_base_extraido == numero_autos_principal:
                            try:
                                suf_final = str(int(suf_ext_str)).zfill(2)
                            except ValueError:
                                suf_final = suf_ext_str.zfill(2)
                            
                            num_formatado = f"{num_base_extraido} ({suf_final})"
                            print(f" Número do precatório (HTML span.unj-larger): {num_formatado}")
                            logger.info(f"Número do precatório extraído de span.unj-larger: {num_formatado}")
                            return num_formatado
                        else:
                            logger.warning(f"Num base extraído '{num_base_extraido}' de span.unj-larger difere do principal '{numero_autos_principal}'.")
            
            logger.info("N° precatório não encontrado via span.unj-larger ou N° base divergente. Usar fallback do texto do link.")
            return "USAR_FALLBACK_TEXTO_LINK"
            
        except Exception as e:
            print(f"❌ Erro ao extrair N° precatório da página: {e}")
            logger.error(f"Erro ao extrair N° precatório da página: {e}", exc_info=True)
            return "ERRO_EXTRACAO_DA_PAGINA"
    
    def encontrar_oficio_requisitorio(self):
        """Encontra o link do ofício requisitório"""
        print("📄 Buscando 'Ofício Requisitório-Precatório Expedido'...")
        logger.info("Procurando 'Ofício Requisitório-Precatório Expedido'...")
        
        try:
            time.sleep(1)
            xpath_exato = "//a[normalize-space()='Ofício Requisitório-Precatório Expedido']"
            
            try:
                WebDriverWait(self.driver, 4).until(EC.presence_of_element_located((By.XPATH, xpath_exato)))
                try:
                    el_temp = self.driver.find_element(By.XPATH, xpath_exato)
                    self.driver.execute_script("arguments[0].scrollIntoView({block: 'center', inline: 'nearest'});", el_temp)
                    time.sleep(0.3)
                except:
                    pass
                
                link_oficio = WebDriverWait(self.driver, 6).until(EC.element_to_be_clickable((By.XPATH, xpath_exato)))
                logger.info(f"Link '{link_oficio.text}' encontrado!")
                link_oficio.click()
                time.sleep(1.2)
                
                if len(self.driver.window_handles) > 1:
                    self.driver.switch_to.window(self.driver.window_handles[-1])
                    print("🔄 Mudou para nova janela com documento.")
                    logger.info(f"Mudou para nova janela/aba. URL: {self.driver.current_url}")
                    return True
                else:
                    print("⚠️ Link clicado, mas nova janela não abriu.")
                    logger.warning("Link clicado, mas nova janela não abriu.")
                    return False
                    
            except TimeoutException:
                print("❌ Link 'Ofício Requisitório-Precatório Expedido' não encontrado ou não clicável.")
                logger.error("Link exato não encontrado/clicável.")
                return False
                
        except Exception as e:
            print(f"❌ Erro ao buscar Ofício: {e}")
            logger.error(f"Erro ao buscar Ofício: {e}", exc_info=True)
            return False
    
    def identificar_novo_arquivo_download(self, arquivos_antes, timeout=4):
        """Identifica novos arquivos de download"""
        tempo_inicial = time.time()
        while time.time() - tempo_inicial < timeout:
            arquivos_atuais = set(os.listdir(self.download_dir))
            novos_arquivos_pdf = [f for f in (arquivos_atuais - arquivos_antes) if f.lower().endswith('.pdf')]
            if novos_arquivos_pdf:
                caminhos_completos = [os.path.join(self.download_dir, nome) for nome in novos_arquivos_pdf]
                arquivo_mais_recente = max(caminhos_completos, key=os.path.getmtime, default=None)
                if arquivo_mais_recente:
                    nome_arquivo_real = os.path.basename(arquivo_mais_recente)
                    logger.info(f"Novo arquivo de download identificado: {nome_arquivo_real}")
                    return nome_arquivo_real
            time.sleep(0.5)
        
        logger.warning("Nenhum novo arquivo PDF de download identificado dentro do timeout.")
        return None
    
    def download_documento_unificado(self, nome_arquivo_base_sugerido):
        """Realiza o download do documento usando os módulos especializados"""
        nome_arquivo_sugerido_com_ext = f"{nome_arquivo_base_sugerido}.pdf"
        print(f"📥 Fazendo download do Ofício")
        
        arquivos_antes_download = set()
        try:
            arquivos_antes_download = set(os.listdir(self.download_dir))
        except FileNotFoundError:
            logger.warning(f"Diretório de download {self.download_dir} não encontrado antes do download. Será criado.")
            os.makedirs(self.download_dir, exist_ok=True)
        
        if self.tjsp_download_available:
            sucesso_inicio_download = self.tjsp_download_func(self.driver, nome_arquivo_sugerido_com_ext, self.download_dir)
        
        if sucesso_inicio_download:
            nome_real_baixado = self.identificar_novo_arquivo_download(arquivos_antes_download)
            if nome_real_baixado:
                logger.info(f"Download bem-sucedido. Arquivo real: {nome_real_baixado}. Nome sugerido: {nome_arquivo_sugerido_com_ext}")
                return nome_real_baixado
            else:
                print(f"⚠️ Download iniciado, mas não foi possível identificar o novo arquivo na pasta {self.download_dir}.")
                logger.warning(f"Download para {nome_arquivo_sugerido_com_ext} iniciado, mas arquivo real não identificado.")
                return "ARQUIVO_NAO_IDENTIFICADO"
        else:
            print(f"❌ Falha ao iniciar o processo de download para {nome_arquivo_sugerido_com_ext}.")
            logger.error(f"Falha ao iniciar o processo de download para {nome_arquivo_sugerido_com_ext}.")
            return None
    
    def limpar_janelas_extras_e_focar_principal(self):
        """Limpa janelas extras e foca na principal"""
        try:
            if not self.driver or not self.main_window_handle:
                return
            
            handles = self.driver.window_handles
            if len(handles) > 1:
                logger.debug(f"Limpando janelas. Principal: {self.main_window_handle}. Abertas: {handles}")
                for h in handles:
                    if h != self.main_window_handle:
                        try:
                            self.driver.switch_to.window(h)
                            self.driver.close()
                            logger.debug(f"Fechada: {h}")
                        except:
                            pass
                self.driver.switch_to.window(self.main_window_handle)
            elif self.driver.current_window_handle != self.main_window_handle:
                try:
                    self.driver.switch_to.window(self.main_window_handle)
                except:
                    logger.error("Falha ao focar na janela principal após limpeza.")
            
            time.sleep(0.2)
            
        except Exception as e:
            logger.error(f"Erro ao limpar janelas: {e}")
    
    def processar_numero_autos(self, numero_display, numero_autos, total_numeros):
        """Processa um número de autos específico"""

        # Separação visual melhorada para cada processo
        print(f"\n" + "─" * 100)
        print(f"🔍 PROCESSO {numero_display:>4}/{total_numeros:<4} │ {numero_autos}")
        print("─" * 100)

        # Log completo para arquivo
        logger.info(f"Processando número {numero_display}/{total_numeros}: {numero_autos}")

        start_time_numero = time.time()
        
        try:
            # Verificar sessão antes de processar
            if not self.verificar_e_renovar_sessao():
                print(f"❌ {numero_autos}: Falha na verificação de sessão. Pulando.")
                logger.error(f"{numero_autos}: Falha na verificação de sessão.")
                self.checkpoint.marcar_consultado(numero_autos, "Erro", "Falha na verificação de sessão", "Erro", numero_lista=numero_display)
                return

            # Verificar se já foi consultado
            if self.checkpoint.ja_consultado(numero_autos):
                if self.checkpoint.ja_processado(numero_autos):
                    print(f"✅ {numero_autos} já processado anteriormente. Pulando.")
                    logger.info(f"{numero_autos} já processado anteriormente.")
                else:
                    print(f"ℹ️ {numero_autos} já consultado anteriormente. Pulando.")
                    logger.info(f"{numero_autos} já consultado anteriormente.")
                return
            
            # Marcar como consultado no início
            self.checkpoint.marcar_consultado(numero_autos, "Iniciando consulta", "Processo iniciado", numero_lista=numero_display)
            
            # Verificar terminação 0500
            if numero_autos.endswith('0500'):
                print(f"⚠️ {numero_autos}: Termina com 0500. Pulando.")
                logger.info(f"{numero_autos}: Termina com 0500.")
                self.checkpoint.marcar_consultado(numero_autos, "Ignorado", "Termina com 0500", "Filtrado", numero_lista=numero_display)
                return
            
            # Preparar número para TJSP
            num_proc_tjsp, comarca_tjsp = self.preparar_numero_para_tjsp(numero_autos)
            if not num_proc_tjsp:
                print(f"❌ {numero_autos}: Falha ao formatar número.")
                logger.error(f"{numero_autos}: Falha ao formatar número.")
                self.checkpoint.marcar_consultado(numero_autos, "Erro", "Falha ao formatar número", "Erro", numero_lista=numero_display)
                return
            
            # Consultar processo principal com verificação de sessão
            if not self.consultar_processo_com_verificacao_sessao(numero_autos, num_proc_tjsp, comarca_tjsp):
                self.checkpoint.marcar_consultado(numero_autos, "Não encontrado", "Processo não encontrado no TJSP", "Não encontrado", numero_lista=numero_display)
                self.limpar_janelas_extras_e_focar_principal()
                time.sleep(0.5)
                return
            
            # Verificar status do processo (MODIFICADO: não para mais por status "extinto")
            status_pp = self.verificar_status_processo()
            print(f"├─ Status do processo principal: {status_pp} - Continuando para verificação de precatórios")
            logger.info(f"Processo principal {numero_autos} status: {status_pp} - Continuando para precatórios (filtro removido)")

            # Verificar partes proibidas (MODIFICADO: apenas registra, não para mais)
            partes_proibidas_pp = self.verificar_partes_processo()
            if partes_proibidas_pp:
                print(f"├─ Processo principal {numero_autos} com Parte Proibida - Continuando para precatórios")
                logger.info(f"Processo principal {numero_autos} com Parte Proibida - Continuando para precatórios (filtro removido)")
            else:
                print(f"├─ Processo principal {numero_autos} sem partes proibidas")
                logger.info(f"Processo principal {numero_autos} sem partes proibidas")

            # Verificar palavras proibidas nas movimentações (MODIFICADO: apenas registra, não para mais)
            palavras_proibidas_pp = self.verificar_palavras_proibidas_mov(clicar_mais=True)
            if palavras_proibidas_pp:
                print(f"├─ Processo principal {numero_autos} com Palavra Proibida em Movimentações - Continuando para precatórios")
                logger.info(f"Processo principal {numero_autos} com Palavra Proibida em Movimentações - Continuando para precatórios (filtro removido)")
            else:
                print(f"├─ Processo principal {numero_autos} sem palavras proibidas")
                logger.info(f"Processo principal {numero_autos} sem palavras proibidas")
            
            logger.info(f"Processo principal {numero_autos} consultado (TJSP) - Todos os filtros removidos.")

            # Registrar dados completos do processo principal (MELHORADO)
            nome_requerente = self._extrair_nome_reqte_da_pagina()
            status_partes = "Com partes proibidas" if partes_proibidas_pp else "Sem partes proibidas"
            status_palavras = "Com palavras proibidas" if palavras_proibidas_pp else "Sem palavras proibidas"
            observacao_completa = f"Processo consultado - Status: {status_pp}, Requerente: {nome_requerente}, Partes: {status_partes}, Palavras: {status_palavras}"
            self.checkpoint.marcar_consultado(numero_autos, "Consultado", observacao_completa, status_pp, numero_lista=numero_display)
            url_processo_principal_com_precatorios = self.driver.current_url
            
            # Buscar links de precatórios
            xpath_links_precatorios = "//a[@class='incidente' and starts-with(normalize-space(.), 'Precatório -')]"
            xpath_links_precatorios_fallback = "//a[starts-with(normalize-space(.), 'Precatório') and contains(@href,'cpopg/show.do?processo') and string-length(normalize-space(substring-after(normalize-space(.), 'Precatório')))>0]"
            links_prec_elems = []
            
            try:
                WebDriverWait(self.driver, 2).until(EC.presence_of_all_elements_located((By.XPATH, xpath_links_precatorios)))
                links_prec_elems = self.driver.find_elements(By.XPATH, xpath_links_precatorios)
                if links_prec_elems:
                    logger.info(f"Links de precatório encontrados com seletor principal para {numero_autos}.")
            except TimeoutException:
                logger.info(f"Nenhum link de precatório (seletor principal) para {numero_autos}. Tentando fallback...")
                try:
                    WebDriverWait(self.driver, 1.5).until(EC.presence_of_all_elements_located((By.XPATH, xpath_links_precatorios_fallback)))
                    links_prec_elems = self.driver.find_elements(By.XPATH, xpath_links_precatorios_fallback)
                    if links_prec_elems:
                        logger.info(f"Links de precatório encontrados com seletor fallback para {numero_autos}.")
                except TimeoutException:
                    pass
            
            if not links_prec_elems:
                print(f"ℹ️ Nenhum link 'Precatório' clicável para {numero_autos}.")
                logger.info(f"Nenhum link 'Precatório' clicável para {numero_autos}.")
                self.checkpoint.marcar_consultado(numero_autos, "Sem precatórios", "Processo sem precatórios", "Sem precatórios", numero_lista=numero_display)
                self.limpar_janelas_extras_e_focar_principal()
                time.sleep(0.5)
                return
            
            num_total_links = len(links_prec_elems)
            print(f"🔗 {num_total_links} link(s) 'Precatório' para {numero_autos}.")
            logger.info(f"{num_total_links} link(s) 'Precatório' para {numero_autos}.")
            
            # Processar cada precatório
            for i_link in range(num_total_links):
                try:
                    self.driver.get(url_processo_principal_com_precatorios)
                    current_xpath_to_use = xpath_links_precatorios
                    
                    try:
                        WebDriverWait(self.driver, 5).until(EC.presence_of_all_elements_located((By.XPATH, current_xpath_to_use)))
                    except TimeoutException:
                        logger.debug(f"Seletor principal de precatório falhou, tentando fallback: {xpath_links_precatorios_fallback}")
                        current_xpath_to_use = xpath_links_precatorios_fallback
                        WebDriverWait(self.driver, 3).until(EC.presence_of_all_elements_located((By.XPATH, current_xpath_to_use)))
                    
                    time.sleep(1.2)
                    
                    links_atualizados = self.driver.find_elements(By.XPATH, current_xpath_to_use)
                    if i_link >= len(links_atualizados):
                        logger.warning(f"Índice {i_link} fora dos limites. Pulando.")
                        break
                    
                    link_prec_atual = links_atualizados[i_link]
                    texto_link = link_prec_atual.text.strip()
                    
                    if not texto_link or not texto_link.lower().startswith("precatório"):
                        logger.warning(f"Link {i_link+1} com texto '{texto_link}' não parece ser um precatório válido. Pulando.")
                        continue
                    
                    print(f"\n  ├─ Processando Precatório {i_link+1}/{num_total_links}: '{texto_link}'")
                    logger.info(f"Processando link Precatório {i_link+1}/{num_total_links}: '{texto_link}'")
                    
                    WebDriverWait(self.driver, 3).until(EC.element_to_be_clickable(link_prec_atual))
                    self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", link_prec_atual)
                    time.sleep(0.4)
                    link_prec_atual.click()
                    WebDriverWait(self.driver, 8).until(EC.url_changes(url_processo_principal_com_precatorios))
                    time.sleep(1.4)
                    
                    # Extrair número do precatório
                    num_prec_tjsp_extraido_pagina = self.extrair_numero_precatorio_completo_da_pagina(numero_autos)
                    
                    if ("USAR_FALLBACK_TEXTO_LINK" in num_prec_tjsp_extraido_pagina or 
                        "NÚMERO PRECATÓRIO NÃO EXTRAÍDO DA PÁGINA" in num_prec_tjsp_extraido_pagina or 
                        "ERRO_EXTRACAO_DA_PAGINA" in num_prec_tjsp_extraido_pagina):
                        
                        sufixo_do_link = re.sub(r'Precatório\s*(Nº|nº|N°|n°)?\s*-\s*', '', texto_link, flags=re.I).strip()
                        try:
                            sufixo_numerico = int(sufixo_do_link)
                            sufixo_formatado = str(sufixo_numerico).zfill(2)
                        except ValueError:
                            sufixo_formatado = sufixo_do_link
                        
                        num_prec_tjsp = f"{numero_autos} ({sufixo_formatado})"
                        print(f"  │  ├─ N° precatório: {num_prec_tjsp}")
                        logger.info(f"N° precatório formado via texto do link (fallback principal): {num_prec_tjsp}")
                    else:
                        num_prec_tjsp = num_prec_tjsp_extraido_pagina
                    
                    # Obter nome do cliente UMA ÚNICA VEZ para evitar duplicação
                    nome_cliente_prec = self.obter_nome_cliente_do_precatorio()

                    # Verificar status do precatório
                    status_prec = self.verificar_status_processo()
                    if status_prec == "finalizado":
                        logger.info(f"Precatório '{num_prec_tjsp}' status 'Finalizado' - pulando")
                        # MELHORADO: Log detalhado do precatório rejeitado por status (usando nome já obtido)
                        logger.info(f"Precatório rejeitado: {num_prec_tjsp} - Cliente: {nome_cliente_prec} - Motivo: Status {status_prec}")
                        continue

                    # Verificar partes do precatório
                    partes_prec = self.verificar_partes_processo()
                    if partes_prec:
                        logger.info(f"Precatório '{num_prec_tjsp}' com Parte Proibida - pulando")
                        # MELHORADO: Log detalhado do precatório rejeitado por partes (usando nome já obtido)
                        logger.info(f"Precatório rejeitado: {num_prec_tjsp} - Cliente: {nome_cliente_prec} - Motivo: Parte proibida (PJ)")
                        continue

                    # Verificar palavras proibidas do precatório
                    palavras_prec = self.verificar_palavras_proibidas_mov(clicar_mais=True)
                    if palavras_prec:
                        logger.info(f"Precatório '{num_prec_tjsp}' com Palavra Proibida - pulando")
                        # MELHORADO: Log detalhado do precatório rejeitado por palavras (usando nome já obtido)
                        logger.info(f"Precatório rejeitado: {num_prec_tjsp} - Cliente: {nome_cliente_prec} - Motivo: Palavras proibidas")
                        continue

                    # Precatório validado - tentar download
                    logger.info(f"Precatório '{num_prec_tjsp}' validado - tentando baixar ofício")
                    
                    # Criar resultado do precatório
                    resultado_item_precatorio = {
                        "NumeroLista": numero_display,
                        "NumeroAutosPrincipal": numero_autos,
                        "StatusProcessoPrincipal": "Válido (TJSP)",
                        "NumeroPrecatorioCompletoTJSP": num_prec_tjsp,
                        "NomeClientePrecatorio": nome_cliente_prec,
                        "StatusValidacaoPrecatorio": "Válido",
                        "StatusDownloadOficio": "Não Tentado",
                        "NomeArquivoBaixado": "",
                        "DataHoraProcessamento": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        "AmbienteProcessamento": self.ambiente.usuario_detectado
                    }
                    
                    # Tentar baixar ofício
                    if self.encontrar_oficio_requisitorio():
                        nome_base_sugerido = f"Oficio_Requisitorio_{num_prec_tjsp.replace(' ','_').replace('(','').replace(')','').replace('/','-').replace('.','_').replace(':','-')}"
                        nome_real_arquivo_baixado = self.download_documento_unificado(nome_base_sugerido)
                        
                        if nome_real_arquivo_baixado and nome_real_arquivo_baixado != "ARQUIVO_NAO_IDENTIFICADO":
                            # Destacar downloads bem-sucedidos com informações relevantes
                            print(f"\n  └─ ✅ DOWNLOAD CONCLUÍDO - Processo {numero_display}")
                            print(f"     📄 Precatório: {num_prec_tjsp}")
                            print(f"     📁 Arquivo: {nome_real_arquivo_baixado}")
                            logger.info(f"Download para '{num_prec_tjsp}' CONCLUÍDO como '{nome_real_arquivo_baixado}'")
                            resultado_item_precatorio["StatusDownloadOficio"] = "Sucesso"
                            resultado_item_precatorio["NomeArquivoBaixado"] = nome_real_arquivo_baixado
                        elif nome_real_arquivo_baixado == "ARQUIVO_NAO_IDENTIFICADO":
                            print(f"\n  └─ ⚠️ DOWNLOAD REALIZADO - Processo {numero_display}")
                            print(f"     📄 Precatório: {num_prec_tjsp}")
                            print(f"     📁 Verificar pasta de downloads")
                            logger.warning(f"Download para '{num_prec_tjsp}' talvez concluído, mas nome não identificado")
                            resultado_item_precatorio["StatusDownloadOficio"] = "Sucesso (Nome Não Identificado)"
                            resultado_item_precatorio["NomeArquivoBaixado"] = "VERIFICAR PASTA MANUALMENTE"
                        else:
                            logger.info(f"Falha no download para '{num_prec_tjsp}'")
                            resultado_item_precatorio["StatusDownloadOficio"] = "Falha Download"
                    else:
                        resultado_item_precatorio["StatusDownloadOficio"] = "Ofício Não Encontrado"
                    
                    # Marcar como processado no checkpoint
                    self.checkpoint.marcar_processado(numero_autos, resultado_item_precatorio, numero_lista=numero_display)
                
                except Exception as e:
                    print(f"❌ Erro ao processar link Precatório '{texto_link if 'texto_link' in locals() else 'desconhecido'}': {e}")
                    logger.error(f"Erro ao processar link Precatório '{texto_link if 'texto_link' in locals() else 'desconhecido'}': {e}", exc_info=True)
                    self.checkpoint.marcar_falha(numero_autos, e, numero_lista=numero_display)
                finally:
                    self.limpar_janelas_extras_e_focar_principal()
            
            end_time_numero = time.time()
            tempo_total_numero = end_time_numero - start_time_numero
            # Log completo para arquivo, sem poluir tela do usuário
            logger.info(f"FIM NÚMERO {numero_display} (Tempo: {tempo_total_numero:.2f}s)")

            # Separação visual para o próximo processo
            print("─" * 100)
            print()  # Linha em branco para separar do próximo processo
            time.sleep(0.6)
            
        except Exception as e:
            print(f"❌ Erro geral ao processar {numero_autos}: {e}")
            logger.error(f"Erro geral ao processar {numero_autos}: {e}", exc_info=True)
            self.checkpoint.marcar_falha(numero_autos, e, numero_lista=numero_display)
    
    def executar_processamento(self, numero_inicial=1):
        """Executa o processamento principal"""
        try:
            print("\n🔧  PREPARAÇÃO")
            print("-" * 30)

            # Detectar arquivo Excel automaticamente
            caminho_excel = detectar_arquivo_excel_automatico()
            if not caminho_excel:
                print("❌ Arquivo Excel não encontrado")
                logger.error(f"Arquivo Excel não encontrado em: {SCRIPT_DIR}")
                return False

            # Carregar números do Excel
            nome_arquivo = os.path.basename(caminho_excel)
            logger.info(f"Carregando números do arquivo: {caminho_excel}")
            numeros_filtrados = self.carregar_numeros_excel(caminho_excel)

            if not numeros_filtrados:
                print("❌ Nenhum processo válido encontrado")
                logger.error("Nenhum número válido carregado do Excel")
                return False

            total_numeros = len(numeros_filtrados)

            # Aplicar checkpoint inteligente
            ultimo_consultado = self.checkpoint.obter_ultimo_consultado()
            stats = self.checkpoint.obter_estatisticas()

            if ultimo_consultado and stats['total_consultados'] > 0:
                # Encontrar posição do último consultado
                try:
                    posicao_ultimo = next(i for i, num in enumerate(numeros_filtrados, 1) if num == ultimo_consultado)
                    proximo_numero = numeros_filtrados[posicao_ultimo] if posicao_ultimo < len(numeros_filtrados) else None
                    print(f"📋 Último consultado: {ultimo_consultado} (linha {posicao_ultimo})")
                    if proximo_numero:
                        print(f"📋 Próximo processo: {proximo_numero} (linha {posicao_ultimo + 1})")
                except (StopIteration, IndexError):
                    logger.warning(f"Último consultado {ultimo_consultado} não encontrado na lista")

            # Validar e aplicar checkpoint
            if not (1 <= numero_inicial <= total_numeros):
                logger.warning(f"Número inicial ({numero_inicial}) inválido. Usando 1.")
                numero_inicial = 1

            numero_inicial = self._determinar_numero_inicial_checkpoint(
                numeros_filtrados, ultimo_consultado, numero_inicial
            )

            # Verificar se já terminou
            if numero_inicial > total_numeros:
                print("\n✅ Todos os processos já foram consultados!")
                logger.info("Processamento completo - todos os números já foram processados")
                return True

            print("\n🔧  INICIALIZAÇÃO")
            print("-" * 30)

            # Iniciar navegador
            print("🔵 Iniciando navegador...")
            logger.info("Iniciando navegador Chrome com perfil específico")
            if not self.iniciar_navegador():
                print("❌ Falha ao iniciar navegador")
                logger.error("Falha ao iniciar navegador Chrome")
                return False
            print("✅ Navegador iniciado")

            self.main_window_handle = self.driver.current_window_handle

            # Autenticar usuário
            print("🔐 Autenticação automática em andamento...")
            logger.info("Iniciando processo de autenticação no TJSP")
            if not self.autenticar_usuario():
                print("❌ Falha na autenticação")
                logger.error("Falha no processo de autenticação")
                return False

            # Verificar sessão (silencioso)
            logger.info("Verificando funcionamento da sessão após autenticação")
            if not self.verificar_sessao_ativa():
                logger.warning("Sessão não confirmada após autenticação inicial")

            # Configurar processamento
            total_restante = total_numeros - numero_inicial + 1
            ja_consultados = numero_inicial - 1

            print(f"\n🚀  PROCESSAMENTO")
            print("-" * 50)
            print(f"   Posição inicial: {numero_inicial:,}")
            print(f"   Já processados:  {ja_consultados:,}")
            print(f"   Restantes:       {total_restante:,}")
            print("-" * 50)
            print()

            logger.info(f"Processando números {numero_inicial} até {total_numeros} - Já consultados: {ja_consultados}, Restantes: {total_restante}")

            # Processar cada número com barra de progresso melhorada
            with tqdm(
                range(numero_inicial - 1, total_numeros),
                desc="📊 Progresso",
                unit="proc",
                initial=numero_inicial - 1,
                total=total_numeros,
                bar_format="{desc}: {percentage:3.0f}%|{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}, {rate_fmt}]",
                ncols=100,
                leave=True
            ) as pbar:
                for idx_numero in pbar:
                    numero_display = idx_numero + 1
                    numero_autos = numeros_filtrados[idx_numero]

                    # Verificação periódica de sessão (silenciosa)
                    if not self.verificar_sessao_periodica(numero_display, total_numeros):
                        print(f"\n❌ Sessão perdida - Interrompendo no processo {numero_display}")
                        logger.error(f"Processamento interrompido por falha de sessão no número {numero_display}")
                        break

                    self.processar_numero_autos(numero_display, numero_autos, total_numeros)

            # Adicionar espaçamento após o tqdm para separar do próximo processo
            print("\n")

            # Finalizar processamento
            self.checkpoint.salvar_checkpoint()
            stats_finais = self.checkpoint.obter_estatisticas()

            print(f"\n🏁  PROCESSAMENTO CONCLUÍDO")
            print("=" * 40)
            print(f"   Downloads realizados: {stats_finais['downloads_realizados']}")
            print(f"   Processos consultados: {stats_finais['total_consultados']}")
            print(f"   Falhas: {stats_finais['falhas']}")
            print("=" * 40)

            logger.info(f"Processamento concluído - Consultados: {stats_finais['total_consultados']}, Downloads: {stats_finais['downloads_realizados']}, Falhas: {stats_finais['falhas']}, Recuperações: {stats_finais.get('recuperacoes_sessao', 0)}")
            return True

        except Exception as e:
            print(f"\n💥 ERRO CRÍTICO: {e}")
            logger.critical(f"Erro crítico no processamento: {e}", exc_info=True)
            return False

        finally:
            # Limpar recursos (silencioso)
            if self.driver:
                logger.info("Fechando navegador")
                self.driver.quit()

            # Salvar checkpoint final
            self.checkpoint.salvar_checkpoint()
    
    def gerar_relatorio_excel(self, nome_arquivo=None):
        """Gera relatório em Excel com os resultados processados"""
        if nome_arquivo is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            nome_arquivo = f"relatorio_processamento_tjsp_{timestamp}.xlsx"
        
        caminho_excel = os.path.join(SCRIPT_DIR, nome_arquivo)
        
        try:
            # Extrair dados dos processados
            dados_processados = []
            for numero_autos, info in self.checkpoint.dados_checkpoint["processados"].items():
                resultado = info["resultado"]
                resultado["NumeroAutosPrincipal"] = numero_autos
                resultado["TimestampProcessamento"] = info["timestamp"]
                dados_processados.append(resultado)
            
            if dados_processados:
                df = pd.DataFrame(dados_processados)
                df.to_excel(caminho_excel, index=False, engine='openpyxl')
                print(f"\n💾 Relatório Excel gerado: {caminho_excel}")
                logger.info(f"Relatório Excel gerado: {caminho_excel}")
                return True
            else:
                print("ℹ️ Nenhum dado processado para gerar relatório.")
                return False
                
        except Exception as e:
            print(f"❌ Erro ao gerar relatório Excel: {e}")
            logger.error(f"Erro ao gerar relatório Excel: {e}")
            
            # Tentar CSV como fallback
            try:
                caminho_csv = caminho_excel.replace(".xlsx", ".csv")
                df.to_csv(caminho_csv, index=False, encoding='utf-8-sig')
                print(f"💾 Relatório CSV gerado: {caminho_csv}")
                logger.info(f"Relatório CSV gerado: {caminho_csv}")
                return True
            except Exception as e2:
                print(f"❌ Erro ao gerar relatório CSV: {e2}")
                logger.error(f"Erro ao gerar relatório CSV: {e2}")
                return False

def print_user_info(message, log_details=None):
    """
    Imprime informação limpa para o usuário e detalhes completos no log

    Args:
        message (str): Mensagem limpa para o usuário
        log_details (str): Detalhes completos para o log (opcional)
    """
    print(message)
    if log_details:
        logger.info(log_details)
    else:
        logger.info(message)

def exibir_cabecalho():
    """Exibe cabeçalho limpo e organizado"""
    print("\n" + "="*60)
    print("🚀  TJSP PROCESSADOR UNIFICADO")
    print("="*60)

def exibir_checkpoint_info(stats):
    """Exibe informações do checkpoint de forma organizada"""
    if stats["total_consultados"] > 0:
        print(f"\n📊  SITUAÇÃO ATUAL")
        print("-" * 30)
        print(f"   Processos consultados: {stats['total_consultados']}")
        print(f"   Downloads realizados:  {stats.get('sucessos', 0)}")
        print()

def solicitar_numero_inicial(stats):
    """Solicita número inicial de forma limpa"""
    if stats["total_consultados"] > 0:
        print("💡  Pressione ENTER para continuar automaticamente")
    else:
        print("💡  Pressione ENTER para iniciar do primeiro processo")

    print("-" * 30)

    try:
        num_input = input("🔢  Número inicial (ENTER=automático): ").strip()
        if num_input.isdigit() and int(num_input) > 0:
            return int(num_input)
        else:
            return 1
    except:
        return 1

def main():
    """Função principal"""
    exibir_cabecalho()

    # Inicializar processador
    processador = ProcessadorTJSPUnificado()

    # Verificar checkpoint
    stats = processador.checkpoint.obter_estatisticas()
    exibir_checkpoint_info(stats)

    # Solicitar número inicial
    num_inicial = solicitar_numero_inicial(stats)
    
    try:
        sucesso = processador.executar_processamento(num_inicial)
        
        if sucesso:
            # Gerar relatório
            gerar_relatorio = input("\n📊 Deseja gerar relatório Excel adicional dos resultados? (s/n): ").strip().lower()
            if gerar_relatorio == 's':
                processador.gerar_relatorio_excel()
        
        print("\n🏁 Fim do processamento!")
        print(f"💾 Checkpoint disponível em: {processador.checkpoint.arquivo_checkpoint_excel}")
        
    except KeyboardInterrupt:
        print("\n\n⚠️ Processamento interrompido pelo usuário.")
        print("💾 Checkpoint salvo automaticamente. Você pode retomar o processamento posteriormente.")
        processador.checkpoint.salvar_checkpoint()
    
    except Exception as e:
        print(f"\n💥 Erro inesperado: {e}")
        logger.error(f"Erro inesperado: {e}", exc_info=True)
    
    finally:
        input("Pressione Enter para sair.")

if __name__ == "__main__":
    main()