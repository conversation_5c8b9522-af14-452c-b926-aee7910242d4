﻿fal automor tudo beleza Hugo aqui da
aoti seja bem-vindo a mais um vídeo aqui
do nosso canal e hoje eu resolvi gravar
esse vídeo aqui de última hora né
geralmente a gente faz aquele vídeo mais
produzido né mais editado mas o objetivo
de hoje é para poder compartilhar com
você um rápido Insight né que nós
descobrimos aqui há pouco tempo que você
consegue fazer usando aí as suas
automações do ntn que é você poder
incluir a hiperautomação
Hugo o que que é hiperautomação eu já
ouvi falar disso mas eu não sei
necessariamente o que se trata disso tá
nesse vídeo a gente vai te explicar o
que que é vamos te mostrar exemplos de
ferramentas né que que podem ser
utilizadas na hiperautomação tá e ao
final vamos te mostrar um exemplo
prático de como você pode eh fazer essa
hiperautomação usando uma ferramenta
bastante conhecida aí na internet para
essa finalidade tá e Então vamos lá
primeiro aqui né definição sobre
hiperautomação é uma estratégia que usa
tecnologias inteligentes para
identificar e automatizar processos de
forma de aumentar a eficiência e
produtividade de uma empresa Ok até o
momento aqui mostra o que que significa
hiperautomação né olhando assim você já
pode imaginar algo mas isso aqui o ntn
já faz sim de certa forma sim tá mas não
faz juz ao nome hiperautomação ou seja
somente o ntn ele não faz juz a esse
nome por quê Porque o ntn ele é uma
automação de processos né a nível
backend né ou seja não é considerado
aqui uma automação de alto nível não é à
toa que se você precisar fazer ali um
tipo de interação web ou uma interação
desktop o ntn ele fica limitado tá
Inclusive a gente recebe várias agens de
alunos dúvidas né
E sobre essa questão né de tipo ah como
eu posso fazer uma automação que eu
consiga Eh sei lá mandar um comando para
uma impressora né ah como é que eu
consigo fazer uma automação e onde eu
faço login em uma página e na próxima eu
consiga capturar os dados eu consiga
fazer uma raspagem de dados né Isso só é
possível por conta da automação então o
ntn sozinho ele não consegue fazer tá
Então nesse vídeo aqui você vai aprender
de fato como é implementado
hiperautomação
tá então voltando aqui né aí Aqui tem
algumas características né da
hiperautomação que é redução de custos
né e melhoria na experiência do cliente
aumento da segurança liberação de tempo
para os funcionários né então a
princípio tudo parece que só o ntn já
atende isso aqui né mas a a diferença
ela vem nesse trecho aqui que é o que eu
quero que você preste muito atenção que
fala assim ó hiperautomação pode ser
implementada usando uma variedade de
ferramentas tá então significa o
seguinte que só o ntn não é suficiente
você precisa de usar outras ferramentas
para que sim você possa ter ali um
conceito de hi ação tá E aí olha só as
ferramentas que envolvem aqui por
exemplo automação robótica de processos
que é o rpa tá o rpa inclusive nós vamos
usar uma ferramenta aqui de rpa eu já
vou falar para você qual vai ser essa
ferramenta é considerada ali uma
automação de alto nível tá o ntn que é
automação ali por meio de apis né não é
uma automação de alto nível rpa é
considerado por quê Porque ela vai além
de usos de api mas também uma automação
ali web uma automação eh e desktop né
que é ali no seu próprio computador em
muitos casos até mesmo uma automação
embarcada né ou seja envolvendo ali
interação com
eh objetos físicos mesmo né Inclusive eu
já até falei com com alguns membros da
nossa comunidade aqui de ntn inclusive
até mesmo alunos que não tá tão longe de
futuramente né por meio aí da da Hiper
hiperautomação no
ntn a gente conseguir criar ali um
workflow uma automação que a gente
consegue por exemplo abrir a porta da
nossa casa acender uma luz né Eu
acredito que vai chegar nesse nível e
não está tão distante assim tá Inclusive
eu já vi um um shorts né no aqui mesmo
no no YouTube de uma pessoa fazendo uma
automação ali eh com alguma coisa física
né alguma algum
alguma coisa assim da casa tá mas isso
futuramente vai ficar como uma coisa bem
comum assim como a inteligência
artificial hoje já se tornou algo bem
comum tá então além de envolver o rpa né
a hiperautomação ela envolve
Inteligência Artificial que é o que
atualmente Tá bombando né Principalmente
no ntn muita gente tem buscado sobre
inteligência artificial né automações
inteligentes tá ó Suites gente de
gerenciamento de processos de negócio
que aí já seria o
ibpms isso aqui eu já não entendo muito
né E olha só que interessante
plataformas de integração com como
serviços que são IP ou seja aqui
entraria o ntn tá então você vê ali que
o ntn ele faz parte né Desse ecossistema
da IP automação tá dando para entender
mas só ele não é IP automação tá então
eh para finalizar aqui também envolve
por exemplo motores de busca de
informações né que aí já seria usar por
exemplo ali um Google pesquisa um Bing
pesquisa e etc tá então dentro disso
aqui tudo aí sim você tem a
hiperautomação né e finalizando aqui
fala que o termo ele foi usado pela
primeira vez em 2019 na lista de
tendências estratégicas da tecnologia de
GN Ok beleza então então uma vez que
você entendeu hiperautomação
Vamos então sobre eh as ferramentas que
podem ser utilizadas tá em conjunto com
ntn para que você possa ter essa hiper
automação é claro que você não precisa
eh ter todas elas aqui tá mas você tem
que ter no mínimo aí Duas né uma ali
atuando mais com essa questão do rpa e a
outra ali já pode por exemplo atuando
com essa parte de apis né de integrações
de serviço que é que é o ncn tá E aqui
eu resolvi eh Separar uma imagem tá para
te mostrar quais são essas ferramentas
de rpa que você consegue trabalhar ali
com o ntn tá aqui são as mais conhecidas
né eles listaram as 10 maiores digamos
assim mais conhecidas né que a maioria
das empresas usam que tem aqui por
exemplo a ief tá inclusive
e nós utilizamos ela aqui tá porém
acabou não sendo a escolhida né temos a
workfusion a nice ou Nice né a kiron o
BL BL Prism né Isso mesmo automation Ed
o pega o
cofax o Power automate que é da
Microsoft Tá e por último aqui o
automation n tá Eu particularmente na
época que eu já vinha estudando um
pouquinho de rpa eu conhecia mais esses
três aqui
tá eu cheguei a testar o eef ok e testei
também o Power automate o automate no
Ainda não testei Tá mas eu acredito
também que seja boa porém entre o Power
automation e o e o pf eu preferi o Power
automation por quê além del ele ser mais
simples tá eu inclusive é a ferramenta
que a gente escolheu para te mostrar
aqui nesse tutorial tá eh além de ser
mais simples ele é mais compatível ali
com o ambiente Microsoft então se você
usa aí por exemplo no seu computador
sistema operacional Windows tá você vai
conseguir fazer as automações com mais
facilidades envolvendo aí o desktop né
ali do Windows tá é claro você também
pode fazer automações envolvendo web
scrap né seja qual for o navegador não
precisa ser só somente o Ed né da
Microsoft Mas você pode ser com Chrome
com Firefox etc Tá mas a nível de
desktop o Power automation ele funciona
perfeitamente com o ambiente Windows tá
agora claro se você usa Mac eh que mais
eh Chrome OS né Tem vários sistemas
operacionais aí né Eh o ief pode ser a
melhor alternativa tá eh sendo que o
portom ele também mostrou ali ser mais
leve ele não é tão pesado assim e o pef
Eu já percebi que ele é um pouco mais
pesado porque você pode instalar ali
vários pacotes né então ele acaba
ficando muito pesado você também tem que
ter uma máquina muito boa né com
bastante CPU e memória tá E além disso
eu considerei o Power automate né mais
fácil mais simples de mexer né Eu tentei
fazer algumas automações ali no ipf e
levei uma coça né já o portom em poucos
cliques ali eu consegui
e fazer a automação fora o gravador que
ele tem que é uma ferramenta ali para
poder capturar e as interações que você
faz ali no navegador ou então no seu
desktop que ele já gera um script ali
automatizado E você já tem basicamente
ali um processo automatizado tá então
por esse motivo é que escolhemos o Power
automate da Microsoft tá eh Hugo Mas se
eu não quiser por exemplo usar uma
dessas ferramentas porque isso aqui aqui
pessoal seria o rpa eh low code no code
Tá Hugo eu consigo fazer com uma
linguagem de programação consegue tá
sendo que tem que ser uma linguagem de
programação que consiga atuar em todas
essas Vertentes que eu te falei né tem
que ser uma linguagem que você consegue
eh fazer uma automação web desktop eh
mobile e até mesmo embarcado que aí
seria essa automação com objetos físicos
tá
eh e aí antes de a gente já falar um
pouquinho da da Power automate né da
Microsoft eu queria só te mostrar esse
rank aqui tá das linguagens de
programações isso no ano de 2020 Tá Hugo
Por que você pegou um ranking aí um
pouco antigo né porque foi o ranking
mais atual que eu consegui do Instituto
aqui do ieee Spectrum tá que inclusive é
um um um um uma fonte aqui confiável tá
eh e eles têm aqui um uma ilustração que
ajuda a entender melhor
eh do porquê né você escolher
determinada linguagem ali para fazer por
exemplo a IP automação se você não
quiser usar as plataformas né low code
no code eh em primeiro lugar como você
pode ver aqui fica o Python tá porque o
Python ele faz automação tanto web
quanto desktop e esse aqui ó que é que é
um chip né significa embarcado Ou seja
você conseguir controlar ali um
dispositivo físico né por meio da da
automação tá E e aí logo embaixo vem
Java depois vem C depois vem c+ mais tem
JavaScript tá sendo que JavaScript é
mais se você fosse fazer web scrap tá se
você já for fazer ali uma automação
desktop embarcada você já não
conseguiria tá também tem o r também tá
que é voltado aí pra desktop Ok sendo
que não é que Python está em primeiro
lugar né você vê aqui ó por quê Porque
além de ser uma linguagem fácil né em
termo de sintaxe né também de uso ali né
uma linguagem Clean limpa né eu mesmo já
estudei um pouco de Python e olha não
tinha aquela preocupação de às vezes
esquecer ali ponto e vírgula né e é
muito mais prático né você conseguir
desenvolver ali um um programa ali com
poucas linhas de código né então além de
ser uma sintaxe muito simples é uma
linguagem é como posso dizer versátil né
Ou seja você conseguiria fazer
automações para todos esses fins aqui
com essas finalidades aqui né E isso
aqui caracteriza numa hiperautomação Ou
seja você poder automatizar um processo
ali que envolva fatores eh web desktop
ou até mesmo embarcado Ou seja você
consegue você não tem limitação para
aplicar a IP automação tá assim também
como poderia ser aqui o Java né o Java
Ele ficaria limitado na parte embarcada
né mas por exemplo você já conseguiria
fazer web e mobile né que é celular e
desktop de certa forma também poderia
ser classificado aqui como uma
hiperautomação tá o c+ mais mesma coisa
aqui ele só não pega web mas pegaria
celular desktop e embarcado Tá mas sem
dúvida Python é a melhor alternativa se
você quiser trabalhar com com IP
automação porém como vocês já entenderam
né auto ela atua com Automação mais
voltado para essa parte de low code no
code porque o nosso objetivo é fazer com
que você construa suas próprias
automações sem precisar ser programador
sem precisar Ter Experiências Com
codificação Esse é o propósito da Auto
então eu não vou aqui necessariamente e
te ensinar né a fazer hiperautomação com
python E aí por isso que nós optamos por
uma ferramenta no code tá eh ou low code
né vai depender como você interpreta que
é o Microsoft Power automate tá deixa eu
já até fechar essas telas aqui para não
atrapalhar e essa ferramenta aqui
pessoal é incrível é incrível tá ou seja
aqui vai abrir portas
para inúmeras possibilidades de
hiperautomação que você vai poder fazer
a partir de agora com com o ntn tá é
claro que aqui eu vou te mostrar apenas
um exemplo tá porém é temos expectativas
de explorar muito mais isso aqui lá no
nosso curso intensivão ntn tá lá a gente
vai criar talvez até mesmo um módulo né
de rpa eh só para poder tratar isso aqui
né seja usando Power automate ou então
até mesmo outras ferramentas de rpa Tá
mas o nosso intuito é a partir desse
vídeo aqui tá eu posso dizer que esse
vídeo aqui é um Marco tá aqui no
trabalho da aoti porque eu não vi nenhum
outro lugar né ensinando essa questão de
hiperautomação no ntn tá então se você
já ficou Maravilhado fazendo automações
com inteligência artificial você agora
vai ficar Maravilhado fazendo automações
com rpa ou seja as suas automações do
ntn vão ser consideradas agora
automações de alto nível ou seja com
hiperautomação usando aí a princípio né
de início agora o Power automate tá
então basicamente você vai pesquisar aí
no Google tá você pode vir aqui ó botar
assim ó Power eh automate né pode
colocar Power automate
tá E aí você já pode clicar geralmente
nesse primeiro link aqui né que é o
Power automate.com tá Quando você clicar
nele você vai ser direcionada ali para
um para uma tela de login da da
Microsoft tá é como você tivesse se
logando ali em um serviço da Microsoft
tá E aí se a sua conta ela for eh Se não
me engano tipo Business né uma conta de
negócios eh você já vai cair no
dashboard do Power automate igual a esse
aqui tá se não for eles vão sugerir você
criar uma nova conta ali Microsoft com
com esse tipo de negócio né que é do
tipo Business ali tá E aí você já vai
cair nesse dashboard aqui tá E aí você
precisa entender o seguinte com o Power
automate né da Microsoft você pode criar
automações diretamente
eh da plataforma deles web que é essa
daqui que eu tô acessando né que é make
Power powerautomate pcom tá ou você pode
criar através do aplicativo deles para
desktop ou seja um executável que você
vai lá instala no seu computador e você
também consegue criar ali processos né
automatizados rpa né diretamente do
aplicativo do desktop tá nesse tutorial
eu vou te ensinar a usar o Power
automate desktop tá porque a finalidade
É essa mesmo é você poder instalar ali
no seu computador e poder usar ali
diretamente do seu computador sem
precisar fazer nada aqui pela pela pela
versão web tá então se você tiver logado
aqui você pode vir aqui no rodapé tá e
clicar aqui ó Power automate para des
top tá Quando você clicar aqui já vai
iniciar o download do aplicativo para
você poder instalar no seu Windows tá é
claro você também poderia vir aqui e
escrever ó Power automate desktop E aí
já vai cair também aqui nesse primeiro
link tá você clica aqui ó e aí vai abrir
uma página aqui como se fosse uma
documentação E aí tem aqui ó eh Nesse
artigo instalar Power automate usando o
MSI installer né que é o instalador da
Microsoft E aí aqui ó nesse primeiro
item tá assim ó baixe o Power automate
instalador você clica aqui e aí
novamente vai baixar o o instalador para
o seu computador tá baixando o
instalador né já vou até fechar aqui
também vou deixar só aqui o ntn aberto
eh vai aparecer esse setup aqui para
você tá você vai instalar Ok e assim que
você instalar e executar né o o Power
automate você já vai cair nessa tela
aqui ó tá que é o Dash bord aqui tá E aí
você tem lá início onde você pode
conferir aí
e as últimas novidades né sobre Power
automate você também já vai ter acesso a
alguns templates aqui assim como o ntn
né que você vai lá no novo workflow Você
tem a opção de escolher se qu é de um
template ou começando do absoluto zero
mesma coisa tá então ele já te dá uma su
algumas sugestões aqui por exemplo de
automação com Excel né envolvendo o
Excel aí no seu computador tá tá
e tem automação web também como por
exemplo abrir uma página web e tirar um
screenshot né uma captura de tela ali de
uma página Web ó pegar dados de uma
página Web né que seria o Web scrap tá Ó
tem automações também na área de
trabalho que é por exemplo abrir uma
pasta e adicionar date time a nome de
arquivos copiar arquivos enviar texto e
para o bloco de notas executar um
aplicativo
imprimir documentos ó só que
interessante né isso aqui você já pris
você cria uma automação lá no NN né que
todos os dias à 9 da manhã ele mande um
comando de impressão pelo seu Windows e
Imprime uma folha olha só que incrível
ISO aqui né ó localizar excluir arquivos
vazios
enfim um monte de coisa aqui que você
consegue fazer de Hip automação Olha eu
tô eu tô muito assim sabe rado aqui que
eu fala gente Caraca isso aqui olha é um
mundo tá ó automações com que mais ó com
PDF né Por exemplo você e criar um PDF
obter imagem de um PDF mesclar PDF enfim
ó manipulação de texto também
né você classificar linhas de um arquivo
cortar linhas de um
arquivo que mais enfim um monte de coisa
aqui Interessante que dá para fazer tá E
aí nesse exemplo aqui tá desse vídeo nós
vamos fazer eh uma automação onde a
gente consegue por exemplo acessar ali
uma página tá fazer uma interação com
essa página que seria basicamente clicar
em um botão para ser redirecionado para
uma outra página E aí em seguida pegar
alguma alguma informação ali da página e
encaminhar pro nosso ntn tá vai ser
basicamente isso que vai até fazer
referência a esse tipo de automação que
muitos usados nos pede né ou então tenta
saber se dá para fazer que é às vezes
por exemplo pegar dados de uma página
Web onde exige ali né uma interação na
página por exemplo fazer um login clicar
num botão para ir pra próxima página
porque só pela URL a pessoa não consegue
acessar a página então às vezes ela tem
que clicar aliem um botão ou então fazer
um login para ir na página que ela quer
e pegar as informações Então esse
tutorial vai ser para te mostrar essa
possibilidade que você consegue fazer
com Power automate e ntn tá mas é claro
ao longo do tempo aí a gente pretende
explorar outros exemplos né de automação
e disponibilizar lá no nosso curso
intensivão ntn tá bom então a primeira
coisa que eu vou fazer aqui é clicar
aqui em novo fluxo tá E aí eu posso dar
um nome aqui né por exemplo eu vou dar o
nome de web web scrapping tá ó web
scrapp com rpa esse vai ser o nome você
não precisa ativar essa opção aqui tá
porque isso aqui é como se fosse para
você poder trabalhar ali com funções de
Excel né na na sua automação Então não
precisa marcar dá um criar aqui tá e o
legal o pessoal é que o portate também
ele já usa ali né o idioma padrão ali do
seu sistema operacional Então olha que
legal tudo em português tá isso aqui é
incrível Às vezes você vai usar uma
outra ferramenta de rpa está tudo em
inglês né Às vezes você vai tentar ali
mudar o idioma não tem em português Às
vez está em espanhol italiano né aqui
não aqui está tudo em português tá ok
estamos aqui então digamos assim no
nosso ambiente de desenvolvimento né a
nossa ide né do Power automate tá é
claro eu não vou ter muito detalhista
aqui até porque nós não somos
especialistas em Power automate tá
Inclusive tem bastante conteúdos aí no
YouTube que você pode procurar e também
empresas especialistas nesse assunto Tá
mas aqui eu vou fazer só um overview
para você entender a estrutura então
aqui no lado esquerdo nós temos as ações
né ou seja o que você pode fazer ali
durante a sua automação tá então por
exemplo nós temos aqui por categorias né
temos por exemplo ações envolvendo
variáveis condicionais né que é o case
né que é como se fosse o sutch lá do ntn
eh aqui ó o if né ah não perdão o sut
ele tá aqui mas por exemplo temos lá o
if lembra o if do do ntn Se for isso
faça isso se se não faça aquilo tá então
você tem estrutura condicional você tem
loop também que seria o loop over itens
lá do ntn você tem algumas coisas aqui
de controle de fluxo também que é pedir
para guardar né Tá vendo que é como se
fosse o weight né do ntn e mais
Eh você tem aqui o executar subfluxo que
é como se fosse o Execute workflow do
ntn que você pode chamar um outro
workflow né um sub workflow ali na no
seu workflow principal enfim você vê que
tem muita semelhança né assim em quesito
de conceito né de automação e aí nós
temos aqui várias coisas ó ações
envolvendo arquivos pastas ó http que
seria o http request do NN você poder
fazer requisições http a gente vai usar
essa automação aqui tá
eh aí tem aqui automações envolvendo o
navegador o Excel o Word tá vendo aí
você vê que muitas coisas que também são
coisas da Microsoft ó o Outlook né e
mais aí tem aqui envolvendo o banco de
dados e-mail ã texto data e hó PDF ó
criptografia
ocr tá isso aqui seria como você usasse
ali Ah o Vision né do Open Eye né ou
seja hoje pra gente poder ali extrair
informações de documentos né descrever
ali uma imagem a gente usa ali o Vision
do chat GPT né aqui você usaria OC para
poder identificar dados né ali de um
documento tá então você conseguiria
fazer sem precisar de uma llm tá isso
aqui é sensacional também tá E claro de
um arquivo local aí do seu computador ó
criptografia XML ó envolve até serviços
do Windows também tá para você ver como
a coisa é bem avançada aqui FTP né aws
ay né que mais
ó automação SAP tem até SAP Aqui também
tá enfim cognitivo Google ó linguagem
natural nossa ó ó análise de sentimento
olha só que interessante isso aqui que é
coisa que nós temos lá no ntn né lembra
o node lá de inteligência artificial
para analisar sentimentos para extrair
informações ó analisar sintaxe tá vendo
Então você pode usar todo esse conceito
também aqui dentro do Power automate
incrível isso aqui tá mas vamos vamos lá
então desse lado aqui as ações tá aqui
como você pode ver nós temos e o arquivo
Main né quando você desenvolve por
exemplo um programa de computador
geralmente tem um arquivo principal que
é o arquivo que reúne ali todas as
bibliotecas né métodos em um só lugar
para poder gerar o executável né então é
a mesma coisa aqui o Main é o arquivo
principal onde vai tá lá o seu fluxo
principal de de IP automação tá
eh e aí aqui do lado direito né né aqui
do lado esquerdo as ações do lado
direito nós temos as variáveis né que é
para você poder ir e armazenando ali
dados né ou seja dados de entrada dados
de saída né Para que elas possam passar
ali por cada etapa da sua automação que
é como se fosse ali aqui o editor de
expressão do ntn tá eh e aí você pode
controlar essas expressões essas
variáveis para poder ir referenciando em
cada etapa aqui do fluxo onde cada etapa
seria como se fosse um né no ntn tá bom
bom isso aqui é o básico que eu posso te
explicar e é o essencial para você
entender a primeira automação que vai
ser feita aqui tá bom ok a primeira
coisa que a gente vai fazer aqui é usar
o gravador né ou seja para quem tá
começando aí né fazendo aí as primeiras
automações Eu recomendo usar o gravador
por quê Porque ele vai mapeando as ações
que você fizer seja aqui no seu desktop
como também no navegador tá como o nosso
caso aqui a gente vai fazer um exemplo
de web scrap Então a gente vai usar o
gravador para uma interação web tá então
por exemplo eu vou abrir aqui uma nova
aba tá Eh claro eu vou tentar capturar
somente as ações essenciais para essa
automação aqui então Não eu não estou
capturando aqui por exemplo é o Abrir de
uma nova aba Eh escrever aqui a ur é
claro que o portal mate ele consegue
capturar tudo isso Tá Mas eu vou tentar
ir direto ao ponto para não ficar uma
coisa tão complexa assim tá então por
exemplo eu resolvi
eh pegar aqui uma página tá inclusive
até do W W3 School né onde eu ele simula
aqui uma um redirecionamento de página
tá então por exemplo se eu clicar aqui
nesse botão ele já me joga para uma
página né por meio de um fram E aí eu
consigo fazer o outras interações aqui
nessa página como por exemplo pegar
informações aqui dessa página ou seja
fazer uma raspagem de dados tá E aí
vamos usar isso aqui como exemplo nesse
nosso tutorial tá então eu vou fazer o
seguinte
eh eu já vou deixar aqui no ponto mas eu
vou fazer o seguinte eu vou clicar aqui
em gravar tá ó
gravar Ok E aí repare que sempre que eu
encostar aqui em algum elemento da
página o Power automate ele consegue
mapear tá seja para eu poder fazer uma
ação sobre esse elemento como também
para poder pegar algum algum dado dele
algum texto algum valor dele tá então
aqui primeira coisa que eu vou fazer é
clicar aqui
e dar um enter tá então você vê ó que
ele já pegou o meu Clique na barra aqui
de URL e também já pegou o meu enter né
onde eu apertei enter para ele poder
acessar novamente a página tá próximo
passo que eu vou fazer aqui é clicar
nesse botão replace document tá ó você
vê que ele já incluiu
aqui ele já incluiu aqui o acesso à
página que é essa daqui e já mapeou
também o clique que eu dei no botão ou
seja talvez eu já poderia
gravar clicando no botão eu não
precisaria nem acessar a barra de URL
aqui apertar enter não precisaria disso
é porque eu também estou iniciando aqui
no no power tomate tá então eu tô
aprendendo junto com vocês também beleza
mas aqui eu já vou te mostrar como a
gente trata isso aqui depois tá que a
automação já tiver pronta e aí em
seguida para finalizar eu posso capturar
essa informação aqui ó que seria o
título da página né eu posso ao invés de
clicar com o botão
e esquerdo aqui né para poder fazer uma
ação de clique eu vou clicar com o botão
direito
tá para aparecer essa caixa aqui de
seleção esse menu aqui né e eu vou
escolher essa opção aqui ó extrair valor
de elemento E aí vou escolher o primeiro
aqui que é o texto Ou seja eu quero
extrair esse valor aqui Learn to code tá
ó vou clicar pronto já extraiu E aí eu
posso simplesmente clicar aqui em
concluído
beleza
OK agora a gente pode fazer pequenos
ajustes aqui nessa automação para não
ser necessário ele passar por todas as
etapas aqui que a gente não considera
tão necessário aqui para o que a gente
quer ou seja resumidamente eu só quero
que ele acesse uma página da web clica
lá no botão e pegue um valor um texto da
página que ele redirecionou só isso
então se você for reparar o mais
importante seria começar a partir aqui ó
do item cinco né Cada cada retangulo
Zinho desse aqui é como se fosse um node
tá é uma etapa da automação então o mais
importante é começar aqui ó onde ele vai
iniciar ali uma Instância do Chrome tá e
já acessando uma página Então tudo isso
aqui ó eu posso
ó aí você clica aqui nos três
pontinhos e põ excluir tá ou
simplesmente você pode clicar em cima
assim selecionar aqui ficar cinza você
vai apertando na tecla D né que ele vai
apagando ó ó apaguei beleza basicamente
eu V deixar só esses quat noes aqui
essas quatro etap tá acessar a página
clicar lá no botão Obter
dados da página e aqui é basicamente um
comentário esse comentário aqui Pelo que
eu entendi é como se fosse gerar um log
ali no console né dizendo o seguinte
olha Eh terminou aqui a automação do do
do gravador tá você também poderia
simplesmente excluir ou seja ele não é
tão necessário assim tá
eh e aí a gente já pode começar a fazer
aqui os devidos ajustes para a automação
ficar ali mais consistente né né então
por exemplo aqui iniciar novo Chrome né
e eu posso fazer o seguinte ele pede
para eu iniciar no modo da instância já
em execução Ou seja a janela ali do
navegador que já está aberta tá ou se eu
quero fazer ou se eu quero abrir uma
nova Instância tá no caso aqui eu vou
optar em abrir uma nova instância porque
às vezes você pode rodar uma automação
que aquela janela ali daquele navegador
não está aberta né foi fechada e tal
então por segurança Vamos colocar aqui
uma nova Instância Tá mas antes Deixa eu
voltar aqui pro para o anterior e copiar
aqui a minha URL ó eu vou até recortar
tá vou escolher aqui Uma Nova Instância
vou colar aqui a minha URL Inicial que é
a URL que ele já vai acessar tá E aqui
no estado da janela ele deixou normal né
que seria tipo abrir uma janela ali eh
um pouco menor né mas eu vou deixar aqui
maximizado paraa janela ficar totalmente
aberta ali e podermos ver tudo né que
está ali na tela tá aqui em desktop de
destino pode deixar em computador local
mesmo tá bom deixa eu ver se tem alguma
coisa aqui no avançado não o avançado
não tem tá então é só isso aqui que você
vai fazer nesse node né ou nessa etapa
aqui de iniciar novo Chrome beleza claro
isso aqui porque eu fiz a raspagem de
dados no Chrome se você faz no Firefox e
aparecer iniciar novo Firefox se é no no
Ed da Microsoft ia ser iniciar novo o Ed
tá então vai depender do navegador que
você usa
Eh ok E aí uma coisa interessante aqui ó
para cada node aqui né Eh você tem ali
variáveis específicas então por exemplo
como é um node né uma etapa de iniciar
uma Instância do navegador a variável
padrão né que eu vou ter aqui disponível
é a variável browser que vai indicar
aquela Instância daquele navegador Deu
para entender então você pode deixar
isso aqui ativado tá para para você ter
ali a instância do navegador que vai ser
utilizado nessa automação que se chama
browser tá então eu vou dar aqui um
salvar beleza e aí aqui ó
é se você for ver é um outro node que se
chama Pressionar botão na página Web tá
E aí por parâmetro aqui ele pede para
você informar a Instância do navegador
repare que o próprio Power automate já
colocou a variável que referencia a tua
Instância tá variável no power automate
elas são representadas aqui por por por
esse símbolo de porcentagem tá então
dentro aqui do símbolo de porcentagem
vem o nome Ó browser tá então quando vem
assim ó dois símbolos de porcentagem e o
nome da variável é é porque aquilo ali é
uma variável tá E aí Aqui você pode
deixar do jeito que está não precisa
mexer Nada também tá então eu vou
cancelar aqui porque a gente não
precisou modificar nada tá já aqui em
obter detalhes eh da página que a gente
seria obter ali o texto né da página
você também não precisa mexer em nada
por ele já vai referenciar para você a
sua Instância tá então ele já localizou
aqui que a sua Instância tá nessa
variável browser tá aqui você também não
precisa mexer porque já tá o mapeamento
do elemento correto que no caso foi o
título da página que eu peguei eu
poderia pegar o subtítulo ou pegar ali
um parágrafo da página Então não precisa
mexer porque ele mapeou também o
elemento corretamente tá E e aí no caso
aqui eu pedi para ele pegar o atributo é
Inner text tá que seria basicamente o
texto que fica ali dentro da tag de um
elemento HTML tá é claro que você
poderia pegar outros atributos ali do do
do elemento Você poderia pegar por
exemplo o title Você poderia pegar o
href né Por exemplo se fosse um elemento
link você pegaria o RL pelo atributo
href tá enfim aí você pode deixar aqui
como innertext sem problema e aí ele
também vai ter uma variável específico
para essa ação aqui para essa etapa que
é o Inner text tá então você também não
precisa mexer Tá deixa ele ativado aqui
tá porque o elemento né o texto que a
gente pegou lá da página ele vai estar
sendo armazenado nesse Inner text Beleza
então memoriza esse essa variável ó
Inner text Ok então não preciso também
salvar vou cancelar aqui porque eu não
alterei nada tá E aí uma coisa que a
gente poderia fazer aqui após obter né a
a informação que a gente quer é fechar o
navegador ou seja não faz sentido deixar
ali a janela aberta né ou seja para cada
automação a gente só abre a janela pega
o que tem que pegar faz o que tem que
fazer e fecha pronto inclusive Isso é
uma boa prática até mesmo em questão de
performance né sobre questões de
vazamento de memória tá então eu vou
clicar aqui ó vou botar fechar
Ah não na verdade acho que é feche
navegador aqui ó feche navegador tá vou
dar dois cliques E aí repare que aqui no
parâmetro da instância e do navegador o
o próprio pomate já indica aqui o
browser para mim isso que é legal sabe
você não precisa ficar colocando as
variáveis tudo de novo na mão o Power
automate já Tem essa inteligência de
identificar que aquilo ali é uma
Instância do seu navegador que está
sendo usada ali na automação e ele já
coloca aqui e já referência aqui para
você tá então a única coisa que eu vou
fazer é só clicar em salvar Tá bom então
basicamente é isso aqui ó ele inicia um
um um novo uma nova Instância do Chrome
eh numa página específica clica no botão
dessa página e ao ser redirecionado ele
pega lá o título né Eh dessa página e
armazena numa variável chamado Inner
text já E aí por fim eu fecho o
navegador tá bom E aí eu posso clicar
aqui em salvar né para poder Salvar esse
meu script aqui essa minha
automação Ok e para executar eu
simplesmente clico aqui no Play né no
executar E aí ele vai começar ó a fazer
etapa por etapa então ele fal lá ó abriu
navegador Acessou a URL clicou no botão
e pegou ali o título Learn to code tá E
aí olha só que legal né
Eh e ele foi e fechou o navegador né que
é o que foi que a gente pediu aqui no
lado
direito você vai acompanhando aqui ó os
valores das variáveis tá vendo ó então ó
quando ele foi lá abrir o browser ele já
pegou aqui ó
é a Instância do navegador que é o
Chrome né uma janela do Chrome quando de
fato ele conseguiu pegar lá o texto da
página ele já armazenou aqui ó no na
variável innertext tá então se eu
encostar aqui ó eu consigo ver o valor
em tempo real né que é o linear code
beleza então web scrapping funcionando
perfeitamente em poucos ajustes aqui que
a gente fez né no no port automate ou
seja boa parte foi usando o gravador tá
E agora é onde entraria a questão aí da
Hiper automação né porque como eu falei
só isso aqui né então seria bem uma
hiper automação mas eu já posso começar
agora usar outras ferramentas em
conjunto aqui com o Power automate para
poder sim fazer uma hiperautomação e
aqui vamos usar o ntn ou seja vamos usar
uma automação do ntn tá bom antes de a
gente te mostrar eu sei que você deve
est bem curioso Aí bem ansioso dá um
like aí no vídeo se até o presente
momento você está ficando Maravilhado
com tudo isso aqui que você tá vendo tá
para poder apoiar aí nosso trabalho para
fazer com que esse vídeo alcance muito
mais usuários aí de ntn que nem sabe que
dava para fazer isso aqui tá E não
esqueça de se inscrever no canal é muito
importante que você se inscreva no canal
para nos ajudar dar aí a crescer mais e
mais no YouTube para nos incentivar né
para poder produzir mais conteúdos
incríveis como esses aqui que vão te
ajudar aí no seu dia a dia nas suas
automações Tá bom se você tem alguma
dúvida né do que a gente já abordou aí
pode também deixar aí nos comentários né
se você já usa rpa já usava rpa nas suas
automações donn deixa aí nos comentários
também sua experiência tá é muito bom a
gente ouvir né os feedback aí dos nossos
inscritos tá bom Ok então vamos lá vou
seguir aqui agora pra parte do ntn tá
então eu vou fechar essa tela aqui
OK vou criar um novo workflow do ntn tá
E aí quando a gente fala por exemplo uma
integração de uma ferramenta com ntn ou
seja pegar informações de uma ferramenta
e passar para ntn o que que a gente
lembra web Hook Ok web Hook do ntn ele
vai ficar esperando ali receber uma
resposta de uma aplicação externa para
então dar continuidade aqui em uma
automação né então o node que a gente
vai utilizar aqui é o Web Hook Tá Hugo e
se fosse o contrário né Se fosse por
exemplo do ntn passarmos para uma
ferramenta externa aí já seria o http
request tá onde por exemplo o ntn lá
pegou uma informação né Por exemplo
deixa eu colocar aqui ó
eu vou até colocar esse data train aqui
ó data é isso
mesmo Training esse aqui ó data data
Story Training
né vou pedir aqui para retornar todas as
pessoas né E aí sim né aqui ó deixa até
desativar para não dar erro desativar
aqui também Aí sim né Por exemplo você
teria
informações na su ntn e aqui você
gostaria de passar para uma automação do
do Power automate ou seja pegar as
informações lá do ntn e jogar aqui para
dentro do fluxo né do Power automate pro
Power automate seguir né Por exemplo
Você poderia pegar uma automação que
ah pegue todos os contatos por exemplo
do meu Google Agenda né aqui poderia ser
um node do Google Agenda né e salve no
Excel do meu computador tá então essas
informações sendo passadas
aqui via Api para o Power automate né
onde aqui Você poderia pegar essas
informações do ntn e salvar no Excel né
Por exemplo você tem lá Excel ó ó tá
vendo ó inserir linha na planilha tá
vendo Então você conseguiria fazer uma
automação que pegasse ali os os contatos
do Google Agenda que é um serviço web né
via api e salvar na sua planilha no no
seu Windows né no desktop do seu Windows
dá para fazer isso aqui tá porém como a
gente ainda tá aprendendo por automate a
gente ainda não sabe eh fazer essa esse
sentido né de ntn para Power Power
automate por enquanto então aqui a gente
vai fazer o inverso é do Power automate
para o ntn tá então excluindo tudo isso
aqui tá
E que que a gente vai fazer
aqui vamos pegar a URL aqui de produção
tá do nosso web Hook aqui a gente já
coloca no modo post tá e aqui você pode
usar essa mesma URL aqui tá ou
simplesmente usar uma URL amigável né
então eu poderia também vir aqui ó
colocar
Power botar assim ó Power ifen
automate tá deixar uma url mais amigável
então ó copiei a URL aqui tá não esqueça
tem que estar no modo post tá então
peguei a URL de produção já vou ativar
esse meu workflow aqui
tá nosso NN Aqui tá um pouquinho
lento mas ele já vai avançar aqui ok já
vou deixar aqui a aba executions né aqui
ativada né aqu ele ele tem um log aqui
que foi do teste que eu fiz né OK
beleza e aí o que que a gente vai fazer
vamos voltar lá pro nosso Power automate
tá
eh já estamos indo aqui para 46 minutos
né de tutorial porque realmente é um
assunto bem extenso né eu falo bastante
mas eu tenho certeza que tudo aqui que
você está vendo eh é de grande valor tá
E vai servir muito aí no seu dia a dia
eh bom e aí que que que a gente vai
fazer vamos aqui no power automate tá
Eh vamos adicionar isso aqui ó eh você
pode escrever http tá E aí tem uma ação
chamado invocar serviço web tá então
você vai dar dois cliques E aí primeira
coisa que a gente vai fazer aqui é colar
essa nossa URL de produção do ntn tá
método post ok que é igual que tá lá no
ntn aqui em aceitar e tipo de conteúdo
por padrão o portate vai deixar
application bar XML Mas aí você coloca
application barjon tá que é o formato de
dados que o ntn
trabalha bom antes de falar aqui sobre
cabeçalhos né seja de de de corpo ou
de aliás seja de personalizado ou de
solicitação né Eh um detalhe aqui muito
importante tá você vai vir aqui em
avançado e você vai desativar essa opção
aqui ó codificar corpo da solicitação tá
Por quê Porque se você não desativar não
sei por qual motivo o o p automate ele
não consegue interpretar o seu Jon que
você passa ali como parâmetro ele dá
erro tá então você tem que desativar
aquela opção ali tá bom eh e aí repare o
seguinte que nessa ação aqui de invocar
serviço web que se seria requisição http
igual http request do ntn tá ele vai ter
três variáveis específicas também para
essa ação tá que seria aqui eh os
parâmetros de cabeçário que ele retorna
né Eh os parâmetros de corpo né que ele
também retorna aqui tá e o o status né
de requisição né Por exemplo se for uma
requisição com sucesso ele vai retornar
200 se tiver alguma falha vai retornar
ali 401 400 3 né 422 enfim Ok então isso
aqui é para você ter acesso ao Status eh
da requisição até mesmo para você poder
colocar ali Talvez um if e determinar o
seguinte ó se o stat for com sucesso
faça isso se for com falha Faça aquilo
né OK E aí o que que a gente vai fazer
agora aqui para finalizar né a gente vai
montar aqui um corpo da requisição onde
eu vou passar esse texto que foi
capturado
eh lá na página né Eu vou passar ele
como um parâmetro chamado título tá que
vai no corpo da requisição então aqui em
corpo da
solicitação você vai abrir e fechar
Chaves e aí você vai abrir e fechar
aspas tá claro pode ser aspas simples ou
aspas duplas tá E aí você vai colocar o
nome do campo eu vou botar aqui título
tá que seria o título lá da página Eh aí
você pode colocar dois pontos abre e
fecha aspas novamente tá E aí dentro
dessa desse par de aspas aqui né esse
segundo par de aspas você pode clicar
aqui ó para selecionar uma variável que
você vai passar como valor Ok E aí eu
posso escolher aqui ó o Inner text tá
então vou clicar aqui ó Inner text
tá e pronto eu já tenho aqui uma
estrutura simples de Jon que vai ser
passado ali
e como parâmetro nessa requisição tá E
aí eu posso clicar aqui em salvar então
resumindo basicamente essa minha
automação vai abrir uma página vai
clicar no botão ali da página vai ser
redirecionado para uma outra página que
é um frame e vai conseguir pegar o
título desse dessa página desse frame
que foi aberto ali e vai enviar para o
ntn onde lá no no workflow do ntn eu
posso decidir o que que eu vou fazer em
seguida tá então vou salvar aqui tá
novamente ó repare que não tem nenhuma
execução aqui processada
Tá e agora eu vou Executar a minha
automação vamos ver o que que vai
acontecer bom se der erro aqui não tem
problema a gente vai analisar e vamos
ajustar ó abriu o navegador clicou no
botão pegou o texto lá linear learn code
né e enviou para o ntn por meio de uma
requisição http E aí a gente pode
acompanhar aqui ó nas variáveis ó pegou
a Instância do navegador Chrome o texto
que ele capturou lá na página armazenou
aqui no innertext tá e ao fazer a
requisição deu sucesso tá vendo ó
retornou aqui 200 né aqui eh no
cabeçalho né da requisição ele recebeu
lá o retorno do ntn que é o workflow was
started né ou seja o workflow foi
inicializado foi
foi inicializado né ou seja com sucesso
né quando o ntn retorna essa informação
é porque o workflow conseguiu receber
ali as informações com sucesso e
prosseguiu tá
eh e aqui seria basicamente as
informações que vem ali da requisição tá
é como se fosse o cabeçalho né da
requisição
beleza e olha só que incrível isso aqui
quando acessamos o log né que foi
processado aqui no NN olha só que
incrível isso aqui ou seja V as
informações da requisição que o Power
automate fez para web Hook do NN e olha
o que que ele tá passando aqui ó
exatamente o valor que foi capturado lá
no web scrap do Power aate incrível ISO
aqui tá E aí claro né Por exemplo Posso
copiar aqui o Jon né e simplesmente
simular aqui né um web
Hook com o manual Trigger né posso vir
aqui
ó editar aqui né apago isso aqui cola
isso
aqui pronto Fecha aqui
né Deixa eu desativar aqui o a
automação Ok E aí eu posso
executar E aí ele vai gerar ali os
mesmos dados do Web Hook né E aí por
exemplo eu posso dar prosseguimento à
automação né Esso aqui é como se fosse
web Hook né então por exemplo se eu
quisesse colocar aqui um node set né
para colocar parâmetros né então por
exemplo vamos dizer que isso aqui fosse
o nome do contato é claro ali foi um
exemplo de título da página mas pod por
exemplo poderia ser nome do contato Ou
seja você poderia fazer um web scrap que
pegasse ali dados de usuários né então
por exemplo poderia ser ali ó nome
contato ou então melhor contato traço
nome né E aí você poderia por exemplo
pegar aqui ó e colocar aqui tá da mesma
forma que poderia ser contato WhatsApp
contato telefone enfim poderia pegar
outras informações também eh ali do do
contato
tá e aqui né seria o parâmetro né então
por exemplo se eu executo aqui ele iria
gerar lá o parâmetro contato nome com o
nome da pessoa né E aí você poderia
fazer o que você bem entender né Por
exemplo aqui poderia ser o e-mail né
poderia ser o e-mail do contato aí você
por exemplo poderia colocar um node aqui
send e-mail né Por exemplo passando o
e-mail né ó aqui como
parâmetro para mandar um e-mail paraa
pessoa né ou seja uma uma hiperautomação
que vai em determinada página Pega o
e-mail da pessoa né e envia um e-mail
para ela ou seja um exemplo de Hiper
automação que você conseguiria fazer
eh entre o o Power automate com o ntn
incrível isso aqui né Ou seja merece
merece o seu like Vamos ser sincero
merece o like então deixa um like aí no
vídeo se você achou isso aqui incrível
tá se ficou alguma dúvida né todo esse
processo né quase uma hora aí né de de
conteúdo de vídeo deixa aí nos
comentários também tá ou então deixa no
chat se você tiver acompanhando esse
vídeo aí ao vivo tá e a Gente terá o
enorme prazer de responder você tá bom
então muito obrigado pela atenção um
grande abraço e te vejo numa próxima