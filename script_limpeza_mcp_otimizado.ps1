# Script de Limpeza e Otimização MCP
# Augment Code Orchestrator V5.0
# Data: 28/07/2025

Write-Host "=== SCRIPT DE LIMPEZA MCP OTIMIZADO ===" -ForegroundColor Cyan
Write-Host "Iniciando análise de processos..." -ForegroundColor Yellow

# Função para obter uso de memória em MB
function Get-MemoryUsageMB($process) {
    return [math]::Round($process.WorkingSet / 1MB, 2)
}

# Função para finalizar processos com segurança
function Stop-ProcessSafely($processId, $processName) {
    try {
        Stop-Process -Id $processId -Force -ErrorAction Stop
        Write-Host "✓ Finalizado: $processName (PID: $processId)" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "✗ Erro ao finalizar: $processName (PID: $processId)" -ForegroundColor Red
        return $false
    }
}

# 1. ANÁLISE INICIAL
Write-Host "`n1. ANÁLISE INICIAL DE PROCESSOS MCP" -ForegroundColor Cyan

$nodeProcesses = Get-WmiObject Win32_Process | Where-Object {$_.Name -eq "node.exe"}
$pythonProcesses = Get-WmiObject Win32_Process | Where-Object {$_.Name -eq "python.exe"}
$uvProcesses = Get-WmiObject Win32_Process | Where-Object {$_.Name -eq "uv.exe"}

Write-Host "Processos Node.js encontrados: $($nodeProcesses.Count)" -ForegroundColor White
Write-Host "Processos Python encontrados: $($pythonProcesses.Count)" -ForegroundColor White
Write-Host "Processos UV encontrados: $($uvProcesses.Count)" -ForegroundColor White

# 2. IDENTIFICAR SERVIDORES MCP DUPLICADOS
Write-Host "`n2. IDENTIFICANDO SERVIDORES MCP DUPLICADOS" -ForegroundColor Cyan

$mcpServers = @{}
$processesToKill = @()

foreach ($process in $nodeProcesses) {
    if ($process.CommandLine -match "mcp" -or $process.CommandLine -match "@modelcontextprotocol" -or 
        $process.CommandLine -match "@21st-dev" -or $process.CommandLine -match "@supabase" -or
        $process.CommandLine -match "@upstash" -or $process.CommandLine -match "@playwright") {
        
        # Extrair nome do servidor MCP
        $serverName = ""
        if ($process.CommandLine -match "@([^/]+/[^@\s]+)") {
            $serverName = $matches[1]
        }
        elseif ($process.CommandLine -match "blowback-context") {
            $serverName = "blowback-context"
        }
        
        if ($serverName -ne "") {
            if (-not $mcpServers.ContainsKey($serverName)) {
                $mcpServers[$serverName] = @()
            }
            $mcpServers[$serverName] += $process
        }
    }
}

# Identificar duplicatas
foreach ($serverName in $mcpServers.Keys) {
    $processes = $mcpServers[$serverName]
    if ($processes.Count -gt 1) {
        Write-Host "⚠️  Servidor duplicado: $serverName ($($processes.Count) instâncias)" -ForegroundColor Yellow
        
        # Manter apenas o primeiro processo, marcar outros para remoção
        for ($i = 1; $i -lt $processes.Count; $i++) {
            $processesToKill += $processes[$i]
        }
    }
    else {
        Write-Host "✓ Servidor único: $serverName" -ForegroundColor Green
    }
}

# 3. FINALIZAR PROCESSOS DUPLICADOS
Write-Host "`n3. FINALIZANDO PROCESSOS DUPLICADOS" -ForegroundColor Cyan

$killedCount = 0
$savedMemory = 0

foreach ($process in $processesToKill) {
    $memUsage = Get-MemoryUsageMB -process (Get-Process -Id $process.ProcessId -ErrorAction SilentlyContinue)
    if ($memUsage -gt 0) {
        $savedMemory += $memUsage
    }
    
    if (Stop-ProcessSafely -processId $process.ProcessId -processName "MCP Server") {
        $killedCount++
    }
}

# 4. LIMPEZA DE PROCESSOS ÓRFÃOS
Write-Host "`n4. LIMPEZA DE PROCESSOS ÓRFÃOS" -ForegroundColor Cyan

# Processos Node.js sem linha de comando (órfãos)
$orphanNodes = Get-Process node -ErrorAction SilentlyContinue | Where-Object {
    $wmiProcess = Get-WmiObject Win32_Process -Filter "ProcessId = $($_.Id)" -ErrorAction SilentlyContinue
    return $null -eq $wmiProcess -or [string]::IsNullOrEmpty($wmiProcess.CommandLine)
}

foreach ($orphan in $orphanNodes) {
    $memUsage = Get-MemoryUsageMB -process $orphan
    $savedMemory += $memUsage
    if (Stop-ProcessSafely -processId $orphan.Id -processName "Node.js Órfão") {
        $killedCount++
    }
}

# 5. OTIMIZAÇÃO DE PROCESSOS PYTHON MCP
Write-Host "`n5. OTIMIZAÇÃO DE PROCESSOS PYTHON MCP" -ForegroundColor Cyan

$pythonMcpProcesses = @()
foreach ($process in $pythonProcesses) {
    if ($process.CommandLine -match "windows-mcp" -and $process.CommandLine -match "main.py") {
        $pythonMcpProcesses += $process
    }
}

if ($pythonMcpProcesses.Count -gt 2) {
    Write-Host "⚠️  Múltiplas instâncias Python MCP encontradas: $($pythonMcpProcesses.Count)" -ForegroundColor Yellow
    
    # Manter apenas 2 instâncias (VSCode + Claude)
    for ($i = 2; $i -lt $pythonMcpProcesses.Count; $i++) {
        $memUsage = Get-MemoryUsageMB -process (Get-Process -Id $pythonMcpProcesses[$i].ProcessId -ErrorAction SilentlyContinue)
        $savedMemory += $memUsage
        if (Stop-ProcessSafely -processId $pythonMcpProcesses[$i].ProcessId -processName "Python MCP") {
            $killedCount++
        }
    }
}

# 6. RELATÓRIO FINAL
Write-Host "`n6. RELATÓRIO DE OTIMIZAÇÃO" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor White
Write-Host "Processos finalizados: $killedCount" -ForegroundColor Green
Write-Host "Memória liberada: $([math]::Round($savedMemory, 2)) MB" -ForegroundColor Green

# Análise pós-limpeza
$nodeProcessesAfter = (Get-Process node -ErrorAction SilentlyContinue).Count
$pythonProcessesAfter = (Get-Process python -ErrorAction SilentlyContinue).Count

Write-Host "`nProcessos Node.js restantes: $nodeProcessesAfter" -ForegroundColor White
Write-Host "Processos Python restantes: $pythonProcessesAfter" -ForegroundColor White

# 7. RECOMENDAÇÕES
Write-Host "`n7. RECOMENDAÇÕES ADICIONAIS" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor White
Write-Host "• Configure MCP apenas no VSCode para evitar duplicação" -ForegroundColor Yellow
Write-Host "• Reinicie VSCode e Claude Desktop após a limpeza" -ForegroundColor Yellow
Write-Host "• Execute este script semanalmente para manutenção" -ForegroundColor Yellow
Write-Host "• Monitore o consumo de memória regularmente" -ForegroundColor Yellow

Write-Host "`n✅ LIMPEZA CONCLUÍDA COM SUCESSO!" -ForegroundColor Green
Write-Host "Pressione qualquer tecla para continuar..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
