2025-07-28 17:34:51 [INFO] - criar_perfil_chrome_automatico - Perfil Chrome existente encontrado: C:/Users/<USER>/Profile_TJSP
2025-07-28 17:34:51 [INFO] - criar_perfil_chrome_automatico - Ambiente detectado: monte
2025-07-28 17:34:51 [INFO] - criar_perfil_chrome_automatico - Perfil Chrome: C:/Users/<USER>/Profile_TJSP
2025-07-28 17:34:51 [INFO] - criar_perfil_chrome_automatico - ChromeDriver: Gerenciamento automático
2025-07-28 17:34:51 [INFO] - detectar_ambiente - Ambiente detectado: monte
2025-07-28 17:34:51 [INFO] - detectar_ambiente - Perfil Chrome: C:/Users/<USER>/Profile_TJSP
2025-07-28 17:34:51 [INFO] - carregar_checkpoint - Checkpoint carregado: 172 registros consultados
2025-07-28 17:34:51 [INFO] - importar_modulos_download - <PERSON><PERSON><PERSON>lo tjsp_download.py importado com sucesso
2025-07-28 17:34:51 [INFO] - executar_processamento - Carregando números do arquivo: c:\Users\<USER>\Documents\Augment\Bipre\TJSP_PDF_e2e\tjsp\numeros_autos.xlsx
2025-07-28 17:34:51 [INFO] - carregar_numeros_excel - Carregando números de: c:\Users\<USER>\Documents\Augment\Bipre\TJSP_PDF_e2e\tjsp\numeros_autos.xlsx
2025-07-28 17:34:51 [INFO] - carregar_numeros_excel - Total números válidos carregados: 11405
2025-07-28 17:34:51 [INFO] - _determinar_numero_inicial_checkpoint - Checkpoint inteligente: usuário usou padrão, iniciando do número 173
2025-07-28 17:34:51 [INFO] - executar_processamento - Iniciando navegador Chrome com perfil específico
2025-07-28 17:34:51 [INFO] - iniciar_navegador - Iniciando navegador Chrome com perfil específico
2025-07-28 17:34:53 [INFO] - iniciar_navegador - Navegador iniciado com gerenciamento automático (método otimizado)
2025-07-28 17:34:53 [INFO] - executar_processamento - Iniciando processo de autenticação no TJSP
2025-07-28 17:34:53 [INFO] - autenticar_usuario - Iniciando autenticação automatizada com certificado digital.
2025-07-28 17:34:56 [INFO] - _verificar_autenticacao_existente - Verificando se já está autenticado
2025-07-28 17:34:57 [INFO] - _autenticar_certificado_automatico - Iniciando autenticação automática com certificado digital
2025-07-28 17:34:59 [INFO] - _autenticar_certificado_automatico - Aba Certificado Digital já está ativa
2025-07-28 17:35:01 [INFO] - _autenticar_certificado_automatico - Clicando no botão Entrar
2025-07-28 17:35:12 [INFO] - _verificar_sucesso_autenticacao - Autenticação automática bem-sucedida - Usuário: DENIS HENRIQUE SOUSA OLIVEIRA
2025-07-28 17:35:12 [INFO] - executar_processamento - Verificando funcionamento da sessão após autenticação
2025-07-28 17:35:12 [INFO] - verificar_sessao_ativa - Verificando se a sessão ainda está ativa
2025-07-28 17:35:12 [INFO] - verificar_sessao_ativa - Já na página de consulta
2025-07-28 17:35:13 [INFO] - verificar_sessao_ativa - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:35:13 [INFO] - _detectar_sessao_expirada - Nome de usuário válido detectado: 'DENIS HENRIQUE SOUSA OLIVEIRA' - sessão ativa
2025-07-28 17:35:13 [INFO] - verificar_sessao_ativa - Sessão ativa confirmada - Usuário: DENIS HENRIQUE SOUSA OLIVEIRA
2025-07-28 17:35:13 [INFO] - executar_processamento - Processando números 173 até 11405 - Já consultados: 172, Restantes: 11233
2025-07-28 17:35:13 [INFO] - processar_numero_autos - Processando número 173/11405: 0035122-76.2004.8.26.0053
2025-07-28 17:35:13 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:35:13 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:35:13 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:35:13 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:35:15 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:35:15 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:35:15 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:35:15 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:35:15 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:35:15 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:35:17 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:35:17 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:35:17 [INFO] - consultar_processo_principal - Consultando 0035122-76.2004.8.26.0053 (TJSP: 0035122762004 / Foro: 0053)
2025-07-28 17:35:19 [INFO] - consultar_processo_principal - Lista de resultados para 0035122-76.2004.8.26.0053.
2025-07-28 17:35:19 [INFO] - consultar_processo_principal - Link para 0035122-76.2004.8.26.0053 encontrado. Clicando...
2025-07-28 17:35:21 [INFO] - consultar_processo_principal - Detalhes de 0035122-76.2004.8.26.0053 carregados após clique.
2025-07-28 17:35:21 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 17:35:21 [INFO] - processar_numero_autos - Processo principal 0035122-76.2004.8.26.0053 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 17:35:21 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:35:21 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Caio Salles Manzotti (lower: caio salles manzotti)
2025-07-28 17:35:21 [INFO] - processar_numero_autos - Processo principal 0035122-76.2004.8.26.0053 sem partes proibidas
2025-07-28 17:35:21 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:35:23 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento eletrônico
2025-07-28 17:35:23 [INFO] - processar_numero_autos - Processo principal 0035122-76.2004.8.26.0053 com Palavra Proibida em Movimentações - Continuando para precatórios (filtro removido)
2025-07-28 17:35:23 [INFO] - processar_numero_autos - Processo principal 0035122-76.2004.8.26.0053 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 17:35:23 [INFO] - processar_numero_autos - Links de precatório encontrados com seletor principal para 0035122-76.2004.8.26.0053.
2025-07-28 17:35:23 [INFO] - processar_numero_autos - 1 link(s) 'Precatório' para 0035122-76.2004.8.26.0053.
2025-07-28 17:35:24 [INFO] - processar_numero_autos - Processando link Precatório 1/1: 'Precatório - 00001'
2025-07-28 17:35:26 [INFO] - extrair_numero_precatorio_completo_da_pagina - Extraindo N° precatório para principal: 0035122-76.2004.8.26.0053
2025-07-28 17:35:26 [INFO] - extrair_numero_precatorio_completo_da_pagina - N° precatório não encontrado via span.unj-larger ou N° base divergente. Usar fallback do texto do link.
2025-07-28 17:35:26 [INFO] - processar_numero_autos - N° precatório formado via texto do link (fallback principal): 0035122-76.2004.8.26.0053 (01)
2025-07-28 17:35:26 [INFO] - obter_nome_cliente_do_precatorio - Buscando nome do cliente (Reqte)...
2025-07-28 17:35:26 [INFO] - obter_nome_cliente_do_precatorio - Nome do cliente: Caio Salles Manzotti
2025-07-28 17:35:26 [INFO] - verificar_status_processo - Status: normal/em andamento.
2025-07-28 17:35:26 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:35:26 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Caio Salles Manzotti (lower: caio salles manzotti)
2025-07-28 17:35:27 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:35:28 [INFO] - verificar_palavras_proibidas_mov - Nenhuma palavra proibida.
2025-07-28 17:35:28 [INFO] - processar_numero_autos - Precatório '0035122-76.2004.8.26.0053 (01)' validado - tentando baixar ofício
2025-07-28 17:35:28 [INFO] - encontrar_oficio_requisitorio - Procurando 'Ofício Requisitório-Precatório Expedido'...
2025-07-28 17:35:29 [INFO] - encontrar_oficio_requisitorio - Link 'Ofício Requisitório-Precatório Expedido' encontrado!
2025-07-28 17:35:31 [INFO] - encontrar_oficio_requisitorio - Mudou para nova janela/aba. URL: https://esaj.tjsp.jus.br/pastadigital/abrirDocumentoEdt.do?nuProcesso=0035122-76.2004.8.26.0053&cdProcesso=1HZX55FEA0001&instanciaProcesso=pg&cdProcessoMaster=1HZX55FEA0000&cdForo=53&baseIndice=INDDS&nmAlias=PG5JMDS&tpOrigem=2&flOrigem=P&cdServico=190101&acessibilidade=false&ticket=6%2BMVo5lkhYgd7cFzN09bQMo7DbaRQP0ciU9v3jTQY9DeBxdKdyk%2FYfy%2FDhiHd%2BmJmYE4Szu9HN16FM7HcuSJluOiCmnwD082Bhwt7VI69S2iUEcHmbHPc5dZDXQxN9dhSSa%2FaaSwdKVZgUo3VY5mVJXav8I0xIIxnkJKU8XBAhT1vZtkMsMoTCfZC2FQSIsdpu5I0oERzG8vZnF6zX%2B3tbWf0lgJ5KvdiRmS8I88YzUgGjXBWOcKra1PGlypZB9oTh9iQscDPddDS2TXZNz5czLm72Pep3dAK0DgAz9rGVLNHMpEZaJHRiQYETkAbmTR6CDVwtspJ%2FFaedoWNQ46Oclfp2X4UW2L7fJXqJ2T8%2BrXMPtB8Kbp3zmtNPUw6hjV3yonncHxGcWxpgB9Ph3MsUsLyVRsNc9YvtF7XUWJJcvyRhnn7%2B72IFZbQto6b%2Fs8jUzwhSknJMInBR9aYDYeiCIVhLkpAM0oxnd1uZhAtv7Zihc8STjx9GdFSQ1OjNoytJJCWc%2F9rvPEfFxR7jkjEpCI8f0EaCf3gj15Mx3rx%2FM%3D
2025-07-28 17:35:31 [INFO] - atualizar_baixar_documento - Iniciando download avançado para: Oficio_Requisitorio_0035122-76_2004_8_26_0053_01.pdf
2025-07-28 17:35:31 [INFO] - atualizar_baixar_documento - Diretório de destino: c:\Users\<USER>\Documents\Augment\Bipre\TJSP_PDF_e2e\tjsp\downloads_completos
2025-07-28 17:35:31 [INFO] - atualizar_baixar_documento - URL atual: https://esaj.tjsp.jus.br/pastadigital/abrirDocumentoEdt.do?nuProcesso=0035122-76.2004.8.26.0053&cdProcesso=1HZX55FEA0001&instanciaProcesso=pg&cdProcessoMaster=1HZX55FEA0000&cdForo=53&baseIndice=INDDS&nmAlias=PG5JMDS&tpOrigem=2&flOrigem=P&cdServico=190101&acessibilidade=false&ticket=6%2BMVo5lkhYgd7cFzN09bQMo7DbaRQP0ciU9v3jTQY9DeBxdKdyk%2FYfy%2FDhiHd%2BmJmYE4Szu9HN16FM7HcuSJluOiCmnwD082Bhwt7VI69S2iUEcHmbHPc5dZDXQxN9dhSSa%2FaaSwdKVZgUo3VY5mVJXav8I0xIIxnkJKU8XBAhT1vZtkMsMoTCfZC2FQSIsdpu5I0oERzG8vZnF6zX%2B3tbWf0lgJ5KvdiRmS8I88YzUgGjXBWOcKra1PGlypZB9oTh9iQscDPddDS2TXZNz5czLm72Pep3dAK0DgAz9rGVLNHMpEZaJHRiQYETkAbmTR6CDVwtspJ%2FFaedoWNQ46Oclfp2X4UW2L7fJXqJ2T8%2BrXMPtB8Kbp3zmtNPUw6hjV3yonncHxGcWxpgB9Ph3MsUsLyVRsNc9YvtF7XUWJJcvyRhnn7%2B72IFZbQto6b%2Fs8jUzwhSknJMInBR9aYDYeiCIVhLkpAM0oxnd1uZhAtv7Zihc8STjx9GdFSQ1OjNoytJJCWc%2F9rvPEfFxR7jkjEpCI8f0EaCf3gj15Mx3rx%2FM%3D
2025-07-28 17:35:31 [INFO] - atualizar_baixar_documento - Página de documento PDF do TJSP detectada
2025-07-28 17:35:31 [INFO] - atualizar_baixar_documento - MÉTODO PRIORITÁRIO: Tentando localizar visualizador de PDF em iframes...
2025-07-28 17:35:31 [INFO] - atualizar_baixar_documento - Encontrados 1 iframes na página
2025-07-28 17:35:31 [INFO] - atualizar_baixar_documento - Tentando iframe 1... src='https://esaj.tjsp.jus.br/pastadigital/js/viewer/web/viewer.html?file=%2Fpastadigital%2FgetPDF.do%3FnuSeqRecurso%3D00001%26nuProcesso%3D0035122-76.2004...'
2025-07-28 17:35:31 [INFO] - atualizar_baixar_documento - Mudou para iframe 1
2025-07-28 17:35:32 [INFO] - atualizar_baixar_documento - Botão de download encontrado no iframe 1 com seletor ('id', 'download')
2025-07-28 17:35:33 [INFO] - atualizar_baixar_documento - Clique realizado no botão dentro do iframe
2025-07-28 17:35:37 [INFO] - atualizar_baixar_documento - Clique no botão de download realizado com sucesso, assumindo download iniciado
2025-07-28 17:35:37 [INFO] - identificar_novo_arquivo_download - Novo arquivo de download identificado: doc_27919482.pdf
2025-07-28 17:35:37 [INFO] - download_documento_unificado - Download bem-sucedido. Arquivo real: doc_27919482.pdf. Nome sugerido: Oficio_Requisitorio_0035122-76_2004_8_26_0053_01.pdf
2025-07-28 17:35:37 [INFO] - processar_numero_autos - Download para '0035122-76.2004.8.26.0053 (01)' CONCLUÍDO como 'doc_27919482.pdf'
2025-07-28 17:35:37 [INFO] - processar_numero_autos - FIM NÚMERO 173 (Tempo: 24.22s)
2025-07-28 17:35:38 [INFO] - processar_numero_autos - Processando número 174/11405: 0002917-91.2006.8.26.0483
2025-07-28 17:35:38 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:35:38 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 17:35:40 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 17:35:40 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:35:40 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:35:42 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:35:42 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:35:42 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:35:42 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:35:42 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:35:42 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:35:44 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:35:44 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:35:44 [INFO] - consultar_processo_principal - Consultando 0002917-91.2006.8.26.0483 (TJSP: 0002917912006 / Foro: 0483)
2025-07-28 17:35:46 [INFO] - consultar_processo_principal - Detalhes de 0002917-91.2006.8.26.0483 carregados diretamente.
2025-07-28 17:35:46 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 17:35:46 [INFO] - processar_numero_autos - Processo principal 0002917-91.2006.8.26.0483 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 17:35:46 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:35:46 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Roberto Abrahan (lower: roberto abrahan)
2025-07-28 17:35:46 [INFO] - processar_numero_autos - Processo principal 0002917-91.2006.8.26.0483 sem partes proibidas
2025-07-28 17:35:47 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:35:48 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento
2025-07-28 17:35:48 [INFO] - processar_numero_autos - Processo principal 0002917-91.2006.8.26.0483 com Palavra Proibida em Movimentações - Continuando para precatórios (filtro removido)
2025-07-28 17:35:48 [INFO] - processar_numero_autos - Processo principal 0002917-91.2006.8.26.0483 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 17:35:50 [INFO] - processar_numero_autos - Nenhum link de precatório (seletor principal) para 0002917-91.2006.8.26.0483. Tentando fallback...
2025-07-28 17:35:52 [INFO] - processar_numero_autos - Nenhum link 'Precatório' clicável para 0002917-91.2006.8.26.0483.
2025-07-28 17:35:53 [INFO] - processar_numero_autos - Processando número 175/11405: 0039384-93.2009.8.26.0053
2025-07-28 17:35:53 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:35:53 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 17:35:55 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 17:35:55 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:35:55 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:35:57 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:35:57 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:35:57 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:35:57 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:35:57 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:35:57 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:35:59 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:35:59 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:35:59 [INFO] - consultar_processo_principal - Consultando 0039384-93.2009.8.26.0053 (TJSP: 0039384932009 / Foro: 0053)
2025-07-28 17:36:01 [INFO] - consultar_processo_principal - Lista de resultados para 0039384-93.2009.8.26.0053.
2025-07-28 17:36:01 [INFO] - consultar_processo_principal - Link para 0039384-93.2009.8.26.0053 encontrado. Clicando...
2025-07-28 17:36:02 [INFO] - consultar_processo_principal - Detalhes de 0039384-93.2009.8.26.0053 carregados após clique.
2025-07-28 17:36:02 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 17:36:02 [INFO] - processar_numero_autos - Processo principal 0039384-93.2009.8.26.0053 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 17:36:02 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:36:02 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Edina Lucia Gonçalves de Maio (lower: edina lucia gonçalves de maio)
2025-07-28 17:36:02 [INFO] - processar_numero_autos - Processo principal 0039384-93.2009.8.26.0053 sem partes proibidas
2025-07-28 17:36:03 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:36:04 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento
2025-07-28 17:36:04 [INFO] - processar_numero_autos - Processo principal 0039384-93.2009.8.26.0053 com Palavra Proibida em Movimentações - Continuando para precatórios (filtro removido)
2025-07-28 17:36:04 [INFO] - processar_numero_autos - Processo principal 0039384-93.2009.8.26.0053 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 17:36:04 [INFO] - processar_numero_autos - Links de precatório encontrados com seletor principal para 0039384-93.2009.8.26.0053.
2025-07-28 17:36:04 [INFO] - processar_numero_autos - 1 link(s) 'Precatório' para 0039384-93.2009.8.26.0053.
2025-07-28 17:36:06 [INFO] - processar_numero_autos - Processando link Precatório 1/1: 'Precatório - 00001'
2025-07-28 17:36:08 [INFO] - extrair_numero_precatorio_completo_da_pagina - Extraindo N° precatório para principal: 0039384-93.2009.8.26.0053
2025-07-28 17:36:08 [INFO] - extrair_numero_precatorio_completo_da_pagina - N° precatório não encontrado via span.unj-larger ou N° base divergente. Usar fallback do texto do link.
2025-07-28 17:36:08 [INFO] - processar_numero_autos - N° precatório formado via texto do link (fallback principal): 0039384-93.2009.8.26.0053 (01)
2025-07-28 17:36:08 [INFO] - obter_nome_cliente_do_precatorio - Buscando nome do cliente (Reqte)...
2025-07-28 17:36:08 [INFO] - obter_nome_cliente_do_precatorio - Nome do cliente: Gisela Guaraci Peres Pinto
2025-07-28 17:36:08 [INFO] - verificar_status_processo - Status 'finalizado' detectado: arquivado
2025-07-28 17:36:08 [INFO] - processar_numero_autos - Precatório '0039384-93.2009.8.26.0053 (01)' status 'Finalizado' - pulando
2025-07-28 17:36:08 [INFO] - processar_numero_autos - Precatório rejeitado: 0039384-93.2009.8.26.0053 (01) - Cliente: Gisela Guaraci Peres Pinto - Motivo: Status finalizado
2025-07-28 17:36:08 [INFO] - processar_numero_autos - FIM NÚMERO 175 (Tempo: 15.64s)
2025-07-28 17:36:09 [INFO] - processar_numero_autos - Processando número 176/11405: 0029399-61.2013.8.26.0053
2025-07-28 17:36:09 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:36:09 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 17:36:11 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 17:36:11 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:36:11 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:36:13 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:36:13 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:36:13 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:36:13 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:36:13 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:36:13 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:36:15 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:36:15 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:36:15 [INFO] - consultar_processo_principal - Consultando 0029399-61.2013.8.26.0053 (TJSP: 0029399612013 / Foro: 0053)
2025-07-28 17:36:17 [INFO] - consultar_processo_principal - Lista de resultados para 0029399-61.2013.8.26.0053.
2025-07-28 17:36:17 [INFO] - consultar_processo_principal - Link para 0029399-61.2013.8.26.0053 encontrado. Clicando...
2025-07-28 17:36:18 [INFO] - consultar_processo_principal - Detalhes de 0029399-61.2013.8.26.0053 carregados após clique.
2025-07-28 17:36:18 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 17:36:18 [INFO] - processar_numero_autos - Processo principal 0029399-61.2013.8.26.0053 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 17:36:18 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:36:18 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Ademar Ribeiro Prates (lower: ademar ribeiro prates)
2025-07-28 17:36:18 [INFO] - processar_numero_autos - Processo principal 0029399-61.2013.8.26.0053 sem partes proibidas
2025-07-28 17:36:19 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:36:20 [INFO] - verificar_palavras_proibidas_mov - Nenhuma palavra proibida.
2025-07-28 17:36:20 [INFO] - processar_numero_autos - Processo principal 0029399-61.2013.8.26.0053 sem palavras proibidas
2025-07-28 17:36:20 [INFO] - processar_numero_autos - Processo principal 0029399-61.2013.8.26.0053 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 17:36:22 [INFO] - processar_numero_autos - Nenhum link de precatório (seletor principal) para 0029399-61.2013.8.26.0053. Tentando fallback...
2025-07-28 17:36:24 [INFO] - processar_numero_autos - Nenhum link 'Precatório' clicável para 0029399-61.2013.8.26.0053.
2025-07-28 17:36:25 [INFO] - processar_numero_autos - Processando número 177/11405: 0039860-29.2012.8.26.0053
2025-07-28 17:36:25 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:36:25 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 17:36:27 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 17:36:27 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:36:27 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:36:29 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:36:29 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:36:29 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:36:29 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:36:29 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:36:29 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:36:31 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:36:31 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:36:31 [INFO] - consultar_processo_principal - Consultando 0039860-29.2012.8.26.0053 (TJSP: 0039860292012 / Foro: 0053)
2025-07-28 17:36:32 [INFO] - consultar_processo_principal - Lista de resultados para 0039860-29.2012.8.26.0053.
2025-07-28 17:36:33 [INFO] - consultar_processo_principal - Link para 0039860-29.2012.8.26.0053 encontrado. Clicando...
2025-07-28 17:36:34 [INFO] - consultar_processo_principal - Detalhes de 0039860-29.2012.8.26.0053 carregados após clique.
2025-07-28 17:36:34 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 17:36:34 [INFO] - processar_numero_autos - Processo principal 0039860-29.2012.8.26.0053 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 17:36:34 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:36:34 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Theseo Darcy Bueno de Toledo Junior (lower: theseo darcy bueno de toledo junior)
2025-07-28 17:36:34 [INFO] - processar_numero_autos - Processo principal 0039860-29.2012.8.26.0053 sem partes proibidas
2025-07-28 17:36:35 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:36:36 [INFO] - verificar_palavras_proibidas_mov - Nenhuma palavra proibida.
2025-07-28 17:36:36 [INFO] - processar_numero_autos - Processo principal 0039860-29.2012.8.26.0053 sem palavras proibidas
2025-07-28 17:36:36 [INFO] - processar_numero_autos - Processo principal 0039860-29.2012.8.26.0053 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 17:36:36 [INFO] - processar_numero_autos - Links de precatório encontrados com seletor principal para 0039860-29.2012.8.26.0053.
2025-07-28 17:36:36 [INFO] - processar_numero_autos - 1 link(s) 'Precatório' para 0039860-29.2012.8.26.0053.
2025-07-28 17:36:38 [INFO] - processar_numero_autos - Processando link Precatório 1/1: 'Precatório - 00001'
2025-07-28 17:36:40 [INFO] - extrair_numero_precatorio_completo_da_pagina - Extraindo N° precatório para principal: 0039860-29.2012.8.26.0053
2025-07-28 17:36:40 [INFO] - extrair_numero_precatorio_completo_da_pagina - N° precatório não encontrado via span.unj-larger ou N° base divergente. Usar fallback do texto do link.
2025-07-28 17:36:40 [INFO] - processar_numero_autos - N° precatório formado via texto do link (fallback principal): 0039860-29.2012.8.26.0053 (01)
2025-07-28 17:36:40 [INFO] - obter_nome_cliente_do_precatorio - Buscando nome do cliente (Reqte)...
2025-07-28 17:36:40 [INFO] - obter_nome_cliente_do_precatorio - Nome do cliente: Theseo Darcy Bueno de Toledo Junior
2025-07-28 17:36:40 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 17:36:40 [INFO] - processar_numero_autos - Precatório '0039860-29.2012.8.26.0053 (01)' status 'Finalizado' - pulando
2025-07-28 17:36:40 [INFO] - processar_numero_autos - Precatório rejeitado: 0039860-29.2012.8.26.0053 (01) - Cliente: Theseo Darcy Bueno de Toledo Junior - Motivo: Status finalizado
2025-07-28 17:36:40 [INFO] - processar_numero_autos - FIM NÚMERO 177 (Tempo: 15.35s)
2025-07-28 17:36:41 [INFO] - processar_numero_autos - Processando número 178/11405: 0029153-11.2013.8.26.0071
2025-07-28 17:36:41 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:36:41 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 17:36:43 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 17:36:43 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:36:43 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:36:45 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:36:45 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:36:45 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:36:45 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:36:45 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:36:45 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:36:47 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:36:47 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:36:47 [INFO] - consultar_processo_principal - Consultando 0029153-11.2013.8.26.0071 (TJSP: 0029153112013 / Foro: 0071)
2025-07-28 17:36:49 [INFO] - consultar_processo_principal - Detalhes de 0029153-11.2013.8.26.0071 carregados diretamente.
2025-07-28 17:36:49 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 17:36:49 [INFO] - processar_numero_autos - Processo principal 0029153-11.2013.8.26.0071 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 17:36:49 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:36:49 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Edson Fernando Batochio (lower: edson fernando batochio)
2025-07-28 17:36:49 [INFO] - processar_numero_autos - Processo principal 0029153-11.2013.8.26.0071 sem partes proibidas
2025-07-28 17:36:49 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:36:51 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento
2025-07-28 17:36:51 [INFO] - processar_numero_autos - Processo principal 0029153-11.2013.8.26.0071 com Palavra Proibida em Movimentações - Continuando para precatórios (filtro removido)
2025-07-28 17:36:51 [INFO] - processar_numero_autos - Processo principal 0029153-11.2013.8.26.0071 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 17:36:53 [INFO] - processar_numero_autos - Nenhum link de precatório (seletor principal) para 0029153-11.2013.8.26.0071. Tentando fallback...
2025-07-28 17:36:54 [INFO] - processar_numero_autos - Nenhum link 'Precatório' clicável para 0029153-11.2013.8.26.0071.
2025-07-28 17:36:55 [INFO] - processar_numero_autos - Processando número 179/11405: 0008353-06.2003.8.26.0590
2025-07-28 17:36:55 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:36:55 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 17:36:57 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 17:36:57 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:36:57 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:36:59 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:36:59 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:36:59 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:36:59 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:36:59 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:36:59 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:37:01 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:37:01 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:37:01 [INFO] - consultar_processo_principal - Consultando 0008353-06.2003.8.26.0590 (TJSP: 0008353062003 / Foro: 0590)
2025-07-28 17:37:03 [INFO] - consultar_processo_principal - Lista de resultados para 0008353-06.2003.8.26.0590.
2025-07-28 17:37:03 [INFO] - consultar_processo_principal - Link para 0008353-06.2003.8.26.0590 encontrado. Clicando...
2025-07-28 17:37:05 [INFO] - consultar_processo_principal - Detalhes de 0008353-06.2003.8.26.0590 carregados após clique.
2025-07-28 17:37:05 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 17:37:05 [INFO] - processar_numero_autos - Processo principal 0008353-06.2003.8.26.0590 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 17:37:05 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:37:05 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Taina Dias Freitas Rep P Kelly Dias Pereira (lower: taina dias freitas rep p kelly dias pereira)
2025-07-28 17:37:05 [INFO] - processar_numero_autos - Processo principal 0008353-06.2003.8.26.0590 sem partes proibidas
2025-07-28 17:37:06 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:37:08 [INFO] - verificar_palavras_proibidas_mov - Nenhuma palavra proibida.
2025-07-28 17:37:08 [INFO] - processar_numero_autos - Processo principal 0008353-06.2003.8.26.0590 sem palavras proibidas
2025-07-28 17:37:08 [INFO] - processar_numero_autos - Processo principal 0008353-06.2003.8.26.0590 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 17:37:10 [INFO] - processar_numero_autos - Nenhum link de precatório (seletor principal) para 0008353-06.2003.8.26.0590. Tentando fallback...
2025-07-28 17:37:12 [INFO] - processar_numero_autos - Nenhum link 'Precatório' clicável para 0008353-06.2003.8.26.0590.
2025-07-28 17:37:12 [INFO] - verificar_sessao_ativa - Verificando se a sessão ainda está ativa
2025-07-28 17:37:12 [INFO] - verificar_sessao_ativa - Navegando para página de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 17:37:16 [INFO] - verificar_sessao_ativa - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:37:16 [INFO] - _detectar_sessao_expirada - Nome de usuário válido detectado: 'DENIS HENRIQUE SOUSA OLIVEIRA' - sessão ativa
2025-07-28 17:37:16 [INFO] - verificar_sessao_ativa - Sessão ativa confirmada - Usuário: DENIS HENRIQUE SOUSA OLIVEIRA
2025-07-28 17:37:16 [INFO] - processar_numero_autos - Processando número 180/11405: 1000308-06.2013.8.26.0053
2025-07-28 17:37:16 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:37:16 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:37:16 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:37:16 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:37:18 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:37:18 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:37:18 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:37:18 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:37:18 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:37:18 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:37:20 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:37:20 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:37:20 [INFO] - consultar_processo_principal - Consultando 1000308-06.2013.8.26.0053 (TJSP: 1000308062013 / Foro: 0053)
2025-07-28 17:37:21 [INFO] - consultar_processo_principal - Lista de resultados para 1000308-06.2013.8.26.0053.
2025-07-28 17:37:21 [INFO] - consultar_processo_principal - Link para 1000308-06.2013.8.26.0053 encontrado. Clicando...
2025-07-28 17:37:23 [INFO] - consultar_processo_principal - Detalhes de 1000308-06.2013.8.26.0053 carregados após clique.
2025-07-28 17:37:23 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 17:37:23 [INFO] - processar_numero_autos - Processo principal 1000308-06.2013.8.26.0053 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 17:37:23 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:37:23 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Leslie Caram Petrus (lower: leslie caram petrus)
2025-07-28 17:37:23 [INFO] - processar_numero_autos - Processo principal 1000308-06.2013.8.26.0053 sem partes proibidas
2025-07-28 17:37:24 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:37:25 [INFO] - verificar_palavras_proibidas_mov - Nenhuma palavra proibida.
2025-07-28 17:37:25 [INFO] - processar_numero_autos - Processo principal 1000308-06.2013.8.26.0053 sem palavras proibidas
2025-07-28 17:37:25 [INFO] - processar_numero_autos - Processo principal 1000308-06.2013.8.26.0053 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 17:37:25 [INFO] - processar_numero_autos - Links de precatório encontrados com seletor principal para 1000308-06.2013.8.26.0053.
2025-07-28 17:37:25 [INFO] - processar_numero_autos - 1 link(s) 'Precatório' para 1000308-06.2013.8.26.0053.
2025-07-28 17:37:27 [INFO] - processar_numero_autos - Processando link Precatório 1/1: 'Precatório - 00001'
2025-07-28 17:37:29 [INFO] - extrair_numero_precatorio_completo_da_pagina - Extraindo N° precatório para principal: 1000308-06.2013.8.26.0053
2025-07-28 17:37:29 [INFO] - extrair_numero_precatorio_completo_da_pagina - N° precatório não encontrado via span.unj-larger ou N° base divergente. Usar fallback do texto do link.
2025-07-28 17:37:29 [INFO] - processar_numero_autos - N° precatório formado via texto do link (fallback principal): 1000308-06.2013.8.26.0053 (01)
2025-07-28 17:37:29 [INFO] - obter_nome_cliente_do_precatorio - Buscando nome do cliente (Reqte)...
2025-07-28 17:37:29 [INFO] - obter_nome_cliente_do_precatorio - Nome do cliente: Leslie Caram Petrus
2025-07-28 17:37:29 [INFO] - verificar_status_processo - Status: normal/em andamento.
2025-07-28 17:37:29 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:37:29 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Leslie Caram Petrus (lower: leslie caram petrus)
2025-07-28 17:37:29 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:37:31 [INFO] - verificar_palavras_proibidas_mov - Nenhuma palavra proibida.
2025-07-28 17:37:31 [INFO] - processar_numero_autos - Precatório '1000308-06.2013.8.26.0053 (01)' validado - tentando baixar ofício
2025-07-28 17:37:31 [INFO] - encontrar_oficio_requisitorio - Procurando 'Ofício Requisitório-Precatório Expedido'...
2025-07-28 17:37:32 [INFO] - encontrar_oficio_requisitorio - Link 'Ofício Requisitório-Precatório Expedido' encontrado!
2025-07-28 17:37:33 [INFO] - encontrar_oficio_requisitorio - Mudou para nova janela/aba. URL: https://esaj.tjsp.jus.br/pastadigital/abrirDocumentoEdt.do?nuProcesso=1000308-06.2013.8.26.0053&cdProcesso=1H00057FD0001&instanciaProcesso=pg&cdProcessoMaster=1H00057FD0000&cdForo=53&baseIndice=INDDS&nmAlias=PG5JMDS&tpOrigem=2&flOrigem=P&cdServico=190101&acessibilidade=false&ticket=6%2BMVo5lkhYgd7cFzN09bQMo7DbaRQP0ciU9v3jTQY9DeBxdKdyk%2FYfy%2FDhiHd%2BmJxQjbsU3HKun3L%2BozC5jZi%2BOiCmnwD082Bhwt7VI69S2iUEcHmbHPc5dZDXQxN9dhSSa%2FaaSwdKVZgUo3VY5mVJXav8I0xIIxnkJKU8XBAhT1vZtkMsMoTCfZC2FQSIsdpu5I0oERzG8vZnF6zX%2B3tbWf0lgJ5KvdiRmS8I88YzUgGjXBWOcKra1PGlypZB9oTh9iQscDPddDS2TXZNz5czLm72Pep3dAK0DgAz9rGVLNHMpEZaJHRiQYETkAbmTR6CDVwtspJ%2FFaedoWNQ46Oclfp2X4UW2L7fJXqJ2T8%2BrXMPtB8Kbp3zmtNPUw6hjV3yonncHxGcWxpgB9Ph3MsUsLyVRsNc9YvtF7XUWJJcvyRhnn7%2B72IFZbQto6b%2Fs8jUzwhSknJMInBR9aYDYeiJzIAXeM9Ad4%2BwDxqvaBXuba2G%2BUzkqFThjGbUbtof8Y6XAlw%2F2XQaiMwTH0YVrj7Dj2sfg4X21OOul39%2FPmAgE%3D
2025-07-28 17:37:33 [INFO] - atualizar_baixar_documento - Iniciando download avançado para: Oficio_Requisitorio_1000308-06_2013_8_26_0053_01.pdf
2025-07-28 17:37:33 [INFO] - atualizar_baixar_documento - Diretório de destino: c:\Users\<USER>\Documents\Augment\Bipre\TJSP_PDF_e2e\tjsp\downloads_completos
2025-07-28 17:37:33 [INFO] - atualizar_baixar_documento - URL atual: https://esaj.tjsp.jus.br/pastadigital/abrirDocumentoEdt.do?nuProcesso=1000308-06.2013.8.26.0053&cdProcesso=1H00057FD0001&instanciaProcesso=pg&cdProcessoMaster=1H00057FD0000&cdForo=53&baseIndice=INDDS&nmAlias=PG5JMDS&tpOrigem=2&flOrigem=P&cdServico=190101&acessibilidade=false&ticket=6%2BMVo5lkhYgd7cFzN09bQMo7DbaRQP0ciU9v3jTQY9DeBxdKdyk%2FYfy%2FDhiHd%2BmJxQjbsU3HKun3L%2BozC5jZi%2BOiCmnwD082Bhwt7VI69S2iUEcHmbHPc5dZDXQxN9dhSSa%2FaaSwdKVZgUo3VY5mVJXav8I0xIIxnkJKU8XBAhT1vZtkMsMoTCfZC2FQSIsdpu5I0oERzG8vZnF6zX%2B3tbWf0lgJ5KvdiRmS8I88YzUgGjXBWOcKra1PGlypZB9oTh9iQscDPddDS2TXZNz5czLm72Pep3dAK0DgAz9rGVLNHMpEZaJHRiQYETkAbmTR6CDVwtspJ%2FFaedoWNQ46Oclfp2X4UW2L7fJXqJ2T8%2BrXMPtB8Kbp3zmtNPUw6hjV3yonncHxGcWxpgB9Ph3MsUsLyVRsNc9YvtF7XUWJJcvyRhnn7%2B72IFZbQto6b%2Fs8jUzwhSknJMInBR9aYDYeiJzIAXeM9Ad4%2BwDxqvaBXuba2G%2BUzkqFThjGbUbtof8Y6XAlw%2F2XQaiMwTH0YVrj7Dj2sfg4X21OOul39%2FPmAgE%3D
2025-07-28 17:37:33 [INFO] - atualizar_baixar_documento - Página de documento PDF do TJSP detectada
2025-07-28 17:37:33 [INFO] - atualizar_baixar_documento - MÉTODO PRIORITÁRIO: Tentando localizar visualizador de PDF em iframes...
2025-07-28 17:37:33 [INFO] - atualizar_baixar_documento - Encontrados 1 iframes na página
2025-07-28 17:37:33 [INFO] - atualizar_baixar_documento - Tentando iframe 1... src='https://esaj.tjsp.jus.br/pastadigital/js/viewer/web/viewer.html?file=%2Fpastadigital%2FgetPDF.do%3FnuSeqRecurso%3D00001%26nuProcesso%3D1000308-06.2013...'
2025-07-28 17:37:33 [INFO] - atualizar_baixar_documento - Mudou para iframe 1
2025-07-28 17:37:35 [INFO] - atualizar_baixar_documento - Botão de download encontrado no iframe 1 com seletor ('id', 'download')
2025-07-28 17:37:36 [INFO] - atualizar_baixar_documento - Clique realizado no botão dentro do iframe
2025-07-28 17:37:40 [INFO] - atualizar_baixar_documento - Clique no botão de download realizado com sucesso, assumindo download iniciado
2025-07-28 17:37:40 [INFO] - identificar_novo_arquivo_download - Novo arquivo de download identificado: doc_38343082.pdf
2025-07-28 17:37:40 [INFO] - download_documento_unificado - Download bem-sucedido. Arquivo real: doc_38343082.pdf. Nome sugerido: Oficio_Requisitorio_1000308-06_2013_8_26_0053_01.pdf
2025-07-28 17:37:40 [INFO] - processar_numero_autos - Download para '1000308-06.2013.8.26.0053 (01)' CONCLUÍDO como 'doc_38343082.pdf'
2025-07-28 17:37:40 [INFO] - processar_numero_autos - FIM NÚMERO 180 (Tempo: 24.57s)
2025-07-28 17:37:41 [INFO] - processar_numero_autos - Processando número 181/11405: 0122838-73.2006.8.26.0053
2025-07-28 17:37:41 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:37:41 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 17:37:43 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 17:37:43 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:37:43 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:37:45 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:37:45 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:37:45 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:37:45 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:37:45 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:37:45 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:37:47 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:37:47 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:37:47 [INFO] - consultar_processo_principal - Consultando 0122838-73.2006.8.26.0053 (TJSP: 0122838732006 / Foro: 0053)
2025-07-28 17:37:49 [INFO] - consultar_processo_principal - Lista de resultados para 0122838-73.2006.8.26.0053.
2025-07-28 17:37:49 [INFO] - consultar_processo_principal - Link para 0122838-73.2006.8.26.0053 encontrado. Clicando...
2025-07-28 17:37:50 [INFO] - consultar_processo_principal - Detalhes de 0122838-73.2006.8.26.0053 carregados após clique.
2025-07-28 17:37:51 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 17:37:51 [INFO] - processar_numero_autos - Processo principal 0122838-73.2006.8.26.0053 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 17:37:51 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:37:51 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Maria Aparecida Souza Martins (lower: maria aparecida souza martins)
2025-07-28 17:37:51 [INFO] - processar_numero_autos - Processo principal 0122838-73.2006.8.26.0053 sem partes proibidas
2025-07-28 17:37:51 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:37:53 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento
2025-07-28 17:37:53 [INFO] - processar_numero_autos - Processo principal 0122838-73.2006.8.26.0053 com Palavra Proibida em Movimentações - Continuando para precatórios (filtro removido)
2025-07-28 17:37:53 [INFO] - processar_numero_autos - Processo principal 0122838-73.2006.8.26.0053 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 17:37:53 [INFO] - processar_numero_autos - Links de precatório encontrados com seletor principal para 0122838-73.2006.8.26.0053.
2025-07-28 17:37:53 [INFO] - processar_numero_autos - 2 link(s) 'Precatório' para 0122838-73.2006.8.26.0053.
2025-07-28 17:37:54 [INFO] - processar_numero_autos - Processando link Precatório 1/2: 'Precatório - 00001'
2025-07-28 17:37:56 [INFO] - extrair_numero_precatorio_completo_da_pagina - Extraindo N° precatório para principal: 0122838-73.2006.8.26.0053
2025-07-28 17:37:56 [INFO] - extrair_numero_precatorio_completo_da_pagina - N° precatório não encontrado via span.unj-larger ou N° base divergente. Usar fallback do texto do link.
2025-07-28 17:37:56 [INFO] - processar_numero_autos - N° precatório formado via texto do link (fallback principal): 0122838-73.2006.8.26.0053 (01)
2025-07-28 17:37:56 [INFO] - obter_nome_cliente_do_precatorio - Buscando nome do cliente (Reqte)...
2025-07-28 17:37:56 [INFO] - obter_nome_cliente_do_precatorio - Nome do cliente: Maria Aparecida Souza Martins
2025-07-28 17:37:56 [INFO] - verificar_status_processo - Status 'finalizado' detectado: arquivado
2025-07-28 17:37:56 [INFO] - processar_numero_autos - Precatório '0122838-73.2006.8.26.0053 (01)' status 'Finalizado' - pulando
2025-07-28 17:37:56 [INFO] - processar_numero_autos - Precatório rejeitado: 0122838-73.2006.8.26.0053 (01) - Cliente: Maria Aparecida Souza Martins - Motivo: Status finalizado
2025-07-28 17:37:58 [INFO] - processar_numero_autos - Processando link Precatório 2/2: 'Precatório - 00003'
2025-07-28 17:38:00 [INFO] - extrair_numero_precatorio_completo_da_pagina - Extraindo N° precatório para principal: 0122838-73.2006.8.26.0053
2025-07-28 17:38:00 [INFO] - extrair_numero_precatorio_completo_da_pagina - N° precatório não encontrado via span.unj-larger ou N° base divergente. Usar fallback do texto do link.
2025-07-28 17:38:00 [INFO] - processar_numero_autos - N° precatório formado via texto do link (fallback principal): 0122838-73.2006.8.26.0053 (03)
2025-07-28 17:38:00 [INFO] - obter_nome_cliente_do_precatorio - Buscando nome do cliente (Reqte)...
2025-07-28 17:38:00 [INFO] - obter_nome_cliente_do_precatorio - Nome do cliente: Maria Aparecida Souza Martins
2025-07-28 17:38:00 [INFO] - verificar_status_processo - Status: normal/em andamento.
2025-07-28 17:38:00 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:38:00 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Maria Aparecida Souza Martins (lower: maria aparecida souza martins)
2025-07-28 17:38:00 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:38:02 [INFO] - verificar_palavras_proibidas_mov - Nenhuma palavra proibida.
2025-07-28 17:38:02 [INFO] - processar_numero_autos - Precatório '0122838-73.2006.8.26.0053 (03)' validado - tentando baixar ofício
2025-07-28 17:38:02 [INFO] - encontrar_oficio_requisitorio - Procurando 'Ofício Requisitório-Precatório Expedido'...
2025-07-28 17:38:03 [INFO] - encontrar_oficio_requisitorio - Link 'Ofício Requisitório-Precatório Expedido' encontrado!
2025-07-28 17:38:05 [INFO] - encontrar_oficio_requisitorio - Mudou para nova janela/aba. URL: https://esaj.tjsp.jus.br/pastadigital/abrirDocumentoEdt.do?nuProcesso=0122838-73.2006.8.26.0053&cdProcesso=1HZX6E6AE0003&instanciaProcesso=pg&cdProcessoMaster=1HZX6E6AE0000&cdForo=53&baseIndice=INDDS&nmAlias=PG5JMDS&tpOrigem=2&flOrigem=P&cdServico=190101&acessibilidade=false&ticket=6%2BMVo5lkhYgd7cFzN09bQMo7DbaRQP0ciU9v3jTQY9DeBxdKdyk%2FYfy%2FDhiHd%2BmJYXKbGm8yZERbXYj5Bf39iOOiCmnwD082Bhwt7VI69S2iUEcHmbHPc5dZDXQxN9dhSSa%2FaaSwdKVZgUo3VY5mVJXav8I0xIIxnkJKU8XBAhT1vZtkMsMoTCfZC2FQSIsdpu5I0oERzG8vZnF6zX%2B3tbWf0lgJ5KvdiRmS8I88YzUgGjXBWOcKra1PGlypZB9oTh9iQscDPddDS2TXZNz5czLm72Pep3dAK0DgAz9rGVLNHMpEZaJHRiQYETkAbmTR6CDVwtspJ%2FFaedoWNQ46Oclfp2X4UW2L7fJXqJ2T8%2BrXMPtB8Kbp3zmtNPUw6hjV3yonncHxGcWxpgB9Ph3MsUsLyVRsNc9YvtF7XUWJJcvyRhnn7%2B72IFZbQto6b%2Fs8jUzwhSknJMInBR9aYDYeiCUyDZ4b1eqxPZYYUZdMI%2BPTCF7NUWaD76AF3Mo4mNkieIm%2BIFEUGAqafUc%2FqORYqzj2sfg4X21OOul39%2FPmAgE%3D
2025-07-28 17:38:05 [INFO] - atualizar_baixar_documento - Iniciando download avançado para: Oficio_Requisitorio_0122838-73_2006_8_26_0053_03.pdf
2025-07-28 17:38:05 [INFO] - atualizar_baixar_documento - Diretório de destino: c:\Users\<USER>\Documents\Augment\Bipre\TJSP_PDF_e2e\tjsp\downloads_completos
2025-07-28 17:38:05 [INFO] - atualizar_baixar_documento - URL atual: https://esaj.tjsp.jus.br/pastadigital/abrirDocumentoEdt.do?nuProcesso=0122838-73.2006.8.26.0053&cdProcesso=1HZX6E6AE0003&instanciaProcesso=pg&cdProcessoMaster=1HZX6E6AE0000&cdForo=53&baseIndice=INDDS&nmAlias=PG5JMDS&tpOrigem=2&flOrigem=P&cdServico=190101&acessibilidade=false&ticket=6%2BMVo5lkhYgd7cFzN09bQMo7DbaRQP0ciU9v3jTQY9DeBxdKdyk%2FYfy%2FDhiHd%2BmJYXKbGm8yZERbXYj5Bf39iOOiCmnwD082Bhwt7VI69S2iUEcHmbHPc5dZDXQxN9dhSSa%2FaaSwdKVZgUo3VY5mVJXav8I0xIIxnkJKU8XBAhT1vZtkMsMoTCfZC2FQSIsdpu5I0oERzG8vZnF6zX%2B3tbWf0lgJ5KvdiRmS8I88YzUgGjXBWOcKra1PGlypZB9oTh9iQscDPddDS2TXZNz5czLm72Pep3dAK0DgAz9rGVLNHMpEZaJHRiQYETkAbmTR6CDVwtspJ%2FFaedoWNQ46Oclfp2X4UW2L7fJXqJ2T8%2BrXMPtB8Kbp3zmtNPUw6hjV3yonncHxGcWxpgB9Ph3MsUsLyVRsNc9YvtF7XUWJJcvyRhnn7%2B72IFZbQto6b%2Fs8jUzwhSknJMInBR9aYDYeiCUyDZ4b1eqxPZYYUZdMI%2BPTCF7NUWaD76AF3Mo4mNkieIm%2BIFEUGAqafUc%2FqORYqzj2sfg4X21OOul39%2FPmAgE%3D
2025-07-28 17:38:05 [INFO] - atualizar_baixar_documento - Página de documento PDF do TJSP detectada
2025-07-28 17:38:05 [INFO] - atualizar_baixar_documento - MÉTODO PRIORITÁRIO: Tentando localizar visualizador de PDF em iframes...
2025-07-28 17:38:05 [INFO] - atualizar_baixar_documento - Encontrados 1 iframes na página
2025-07-28 17:38:05 [INFO] - atualizar_baixar_documento - Tentando iframe 1... src='https://esaj.tjsp.jus.br/pastadigital/js/viewer/web/viewer.html?file=%2Fpastadigital%2FgetPDF.do%3FnuSeqRecurso%3D00003%26nuProcesso%3D0122838-73.2006...'
2025-07-28 17:38:05 [INFO] - atualizar_baixar_documento - Mudou para iframe 1
2025-07-28 17:38:06 [INFO] - atualizar_baixar_documento - Botão de download encontrado no iframe 1 com seletor ('id', 'download')
2025-07-28 17:38:07 [INFO] - atualizar_baixar_documento - Clique realizado no botão dentro do iframe
2025-07-28 17:38:11 [INFO] - atualizar_baixar_documento - Clique no botão de download realizado com sucesso, assumindo download iniciado
2025-07-28 17:38:11 [INFO] - identificar_novo_arquivo_download - Novo arquivo de download identificado: doc_36757161.pdf
2025-07-28 17:38:11 [INFO] - download_documento_unificado - Download bem-sucedido. Arquivo real: doc_36757161.pdf. Nome sugerido: Oficio_Requisitorio_0122838-73_2006_8_26_0053_03.pdf
2025-07-28 17:38:11 [INFO] - processar_numero_autos - Download para '0122838-73.2006.8.26.0053 (03)' CONCLUÍDO como 'doc_36757161.pdf'
2025-07-28 17:38:11 [INFO] - processar_numero_autos - FIM NÚMERO 181 (Tempo: 30.44s)
2025-07-28 17:38:12 [INFO] - processar_numero_autos - Processando número 182/11405: 0137872-54.2007.8.26.0053
2025-07-28 17:38:12 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:38:12 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 17:38:14 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 17:38:14 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:38:14 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:38:16 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:38:16 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:38:16 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:38:16 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:38:16 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:38:16 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:38:18 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:38:18 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:38:18 [INFO] - consultar_processo_principal - Consultando 0137872-54.2007.8.26.0053 (TJSP: 0137872542007 / Foro: 0053)
2025-07-28 17:38:20 [INFO] - consultar_processo_principal - Lista de resultados para 0137872-54.2007.8.26.0053.
2025-07-28 17:38:20 [INFO] - consultar_processo_principal - Link para 0137872-54.2007.8.26.0053 encontrado. Clicando...
2025-07-28 17:38:22 [INFO] - consultar_processo_principal - Detalhes de 0137872-54.2007.8.26.0053 carregados após clique.
2025-07-28 17:38:22 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 17:38:22 [INFO] - processar_numero_autos - Processo principal 0137872-54.2007.8.26.0053 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 17:38:22 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:38:22 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Celio Ramires (lower: celio ramires)
2025-07-28 17:38:22 [INFO] - processar_numero_autos - Processo principal 0137872-54.2007.8.26.0053 sem partes proibidas
2025-07-28 17:38:22 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:38:24 [INFO] - verificar_palavras_proibidas_mov - Nenhuma palavra proibida.
2025-07-28 17:38:24 [INFO] - processar_numero_autos - Processo principal 0137872-54.2007.8.26.0053 sem palavras proibidas
2025-07-28 17:38:24 [INFO] - processar_numero_autos - Processo principal 0137872-54.2007.8.26.0053 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 17:38:24 [INFO] - processar_numero_autos - Links de precatório encontrados com seletor principal para 0137872-54.2007.8.26.0053.
2025-07-28 17:38:24 [INFO] - processar_numero_autos - 1 link(s) 'Precatório' para 0137872-54.2007.8.26.0053.
2025-07-28 17:38:25 [INFO] - processar_numero_autos - Processando link Precatório 1/1: 'Precatório - 00001'
2025-07-28 17:38:28 [INFO] - extrair_numero_precatorio_completo_da_pagina - Extraindo N° precatório para principal: 0137872-54.2007.8.26.0053
2025-07-28 17:38:28 [INFO] - extrair_numero_precatorio_completo_da_pagina - N° precatório não encontrado via span.unj-larger ou N° base divergente. Usar fallback do texto do link.
2025-07-28 17:38:28 [INFO] - processar_numero_autos - N° precatório formado via texto do link (fallback principal): 0137872-54.2007.8.26.0053 (01)
2025-07-28 17:38:28 [INFO] - obter_nome_cliente_do_precatorio - Buscando nome do cliente (Reqte)...
2025-07-28 17:38:28 [INFO] - obter_nome_cliente_do_precatorio - Nome do cliente: Foz Sociedade de Advogados
2025-07-28 17:38:28 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 17:38:28 [INFO] - processar_numero_autos - Precatório '0137872-54.2007.8.26.0053 (01)' status 'Finalizado' - pulando
2025-07-28 17:38:28 [INFO] - processar_numero_autos - Precatório rejeitado: 0137872-54.2007.8.26.0053 (01) - Cliente: Foz Sociedade de Advogados - Motivo: Status finalizado
2025-07-28 17:38:28 [INFO] - processar_numero_autos - FIM NÚMERO 182 (Tempo: 16.01s)
2025-07-28 17:38:28 [INFO] - processar_numero_autos - Processando número 183/11405: 0116318-97.2006.8.26.0053
2025-07-28 17:38:28 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:38:28 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 17:38:31 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 17:38:31 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:38:31 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:38:33 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:38:33 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:38:33 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:38:33 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:38:33 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:38:33 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:38:35 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:38:35 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:38:35 [INFO] - consultar_processo_principal - Consultando 0116318-97.2006.8.26.0053 (TJSP: 0116318972006 / Foro: 0053)
2025-07-28 17:38:36 [INFO] - consultar_processo_principal - Lista de resultados para 0116318-97.2006.8.26.0053.
2025-07-28 17:38:36 [INFO] - consultar_processo_principal - Link para 0116318-97.2006.8.26.0053 encontrado. Clicando...
2025-07-28 17:38:38 [INFO] - consultar_processo_principal - Detalhes de 0116318-97.2006.8.26.0053 carregados após clique.
2025-07-28 17:38:38 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 17:38:38 [INFO] - processar_numero_autos - Processo principal 0116318-97.2006.8.26.0053 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 17:38:38 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:38:38 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Maria da Costa Ferreira (lower: maria da costa ferreira)
2025-07-28 17:38:38 [INFO] - processar_numero_autos - Processo principal 0116318-97.2006.8.26.0053 sem partes proibidas
2025-07-28 17:38:38 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:38:40 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento
2025-07-28 17:38:40 [INFO] - processar_numero_autos - Processo principal 0116318-97.2006.8.26.0053 com Palavra Proibida em Movimentações - Continuando para precatórios (filtro removido)
2025-07-28 17:38:40 [INFO] - processar_numero_autos - Processo principal 0116318-97.2006.8.26.0053 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 17:38:40 [INFO] - processar_numero_autos - Links de precatório encontrados com seletor principal para 0116318-97.2006.8.26.0053.
2025-07-28 17:38:40 [INFO] - processar_numero_autos - 1 link(s) 'Precatório' para 0116318-97.2006.8.26.0053.
2025-07-28 17:38:41 [INFO] - processar_numero_autos - Processando link Precatório 1/1: 'Precatório - 00001'
2025-07-28 17:38:43 [INFO] - extrair_numero_precatorio_completo_da_pagina - Extraindo N° precatório para principal: 0116318-97.2006.8.26.0053
2025-07-28 17:38:44 [INFO] - extrair_numero_precatorio_completo_da_pagina - N° precatório não encontrado via span.unj-larger ou N° base divergente. Usar fallback do texto do link.
2025-07-28 17:38:44 [INFO] - processar_numero_autos - N° precatório formado via texto do link (fallback principal): 0116318-97.2006.8.26.0053 (01)
2025-07-28 17:38:44 [INFO] - obter_nome_cliente_do_precatorio - Buscando nome do cliente (Reqte)...
2025-07-28 17:38:44 [INFO] - obter_nome_cliente_do_precatorio - Nome do cliente: Maria da Costa Ferreira
2025-07-28 17:38:44 [INFO] - verificar_status_processo - Status: normal/em andamento.
2025-07-28 17:38:44 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:38:44 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Maria da Costa Ferreira (lower: maria da costa ferreira)
2025-07-28 17:38:44 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:38:46 [INFO] - verificar_palavras_proibidas_mov - Nenhuma palavra proibida.
2025-07-28 17:38:46 [INFO] - processar_numero_autos - Precatório '0116318-97.2006.8.26.0053 (01)' validado - tentando baixar ofício
2025-07-28 17:38:46 [INFO] - encontrar_oficio_requisitorio - Procurando 'Ofício Requisitório-Precatório Expedido'...
2025-07-28 17:38:47 [INFO] - encontrar_oficio_requisitorio - Link 'Ofício Requisitório-Precatório Expedido' encontrado!
2025-07-28 17:38:48 [INFO] - encontrar_oficio_requisitorio - Mudou para nova janela/aba. URL: https://esaj.tjsp.jus.br/pastadigital/abrirDocumentoEdt.do?nuProcesso=0116318-97.2006.8.26.0053&cdProcesso=1HZX6E19A0001&instanciaProcesso=pg&cdProcessoMaster=1HZX6E19A0000&cdForo=53&baseIndice=INDDS&nmAlias=PG5JMDS&tpOrigem=2&flOrigem=P&cdServico=190101&acessibilidade=false&ticket=6%2BMVo5lkhYgd7cFzN09bQMo7DbaRQP0ciU9v3jTQY9DeBxdKdyk%2FYfy%2FDhiHd%2BmJOLknlCpeL8HbiHjT3rL7veOiCmnwD082Bhwt7VI69S2iUEcHmbHPc5dZDXQxN9dhSSa%2FaaSwdKVZgUo3VY5mVJXav8I0xIIxnkJKU8XBAhT1vZtkMsMoTCfZC2FQSIsdpu5I0oERzG8vZnF6zX%2B3tbWf0lgJ5KvdiRmS8I88YzUgGjXBWOcKra1PGlypZB9oTh9iQscDPddDS2TXZNz5czLm72Pep3dAK0DgAz9rGVLNHMpEZaJHRiQYETkAbmTR6CDVwtspJ%2FFaedoWNQ46Oclfp2X4UW2L7fJXqJ2T8%2BrXMPtB8Kbp3zmtNPUw6hjV3yonncHxGcWxpgB9Ph3MsUsLyVRsNc9YvtF7XUWJJcvyRhnn7%2B72IFZbQto6b%2Fs8jUzwhSknJMInBR9aYDYeiIv8M5uip5DnSOK0YlrkxyDTRwCPX6%2Bqwfv5NYTWSds1EMsGrKg6xWJwR6Kq9ZPbGDj2sfg4X21OOul39%2FPmAgE%3D
2025-07-28 17:38:48 [INFO] - atualizar_baixar_documento - Iniciando download avançado para: Oficio_Requisitorio_0116318-97_2006_8_26_0053_01.pdf
2025-07-28 17:38:48 [INFO] - atualizar_baixar_documento - Diretório de destino: c:\Users\<USER>\Documents\Augment\Bipre\TJSP_PDF_e2e\tjsp\downloads_completos
2025-07-28 17:38:48 [INFO] - atualizar_baixar_documento - URL atual: https://esaj.tjsp.jus.br/pastadigital/abrirDocumentoEdt.do?nuProcesso=0116318-97.2006.8.26.0053&cdProcesso=1HZX6E19A0001&instanciaProcesso=pg&cdProcessoMaster=1HZX6E19A0000&cdForo=53&baseIndice=INDDS&nmAlias=PG5JMDS&tpOrigem=2&flOrigem=P&cdServico=190101&acessibilidade=false&ticket=6%2BMVo5lkhYgd7cFzN09bQMo7DbaRQP0ciU9v3jTQY9DeBxdKdyk%2FYfy%2FDhiHd%2BmJOLknlCpeL8HbiHjT3rL7veOiCmnwD082Bhwt7VI69S2iUEcHmbHPc5dZDXQxN9dhSSa%2FaaSwdKVZgUo3VY5mVJXav8I0xIIxnkJKU8XBAhT1vZtkMsMoTCfZC2FQSIsdpu5I0oERzG8vZnF6zX%2B3tbWf0lgJ5KvdiRmS8I88YzUgGjXBWOcKra1PGlypZB9oTh9iQscDPddDS2TXZNz5czLm72Pep3dAK0DgAz9rGVLNHMpEZaJHRiQYETkAbmTR6CDVwtspJ%2FFaedoWNQ46Oclfp2X4UW2L7fJXqJ2T8%2BrXMPtB8Kbp3zmtNPUw6hjV3yonncHxGcWxpgB9Ph3MsUsLyVRsNc9YvtF7XUWJJcvyRhnn7%2B72IFZbQto6b%2Fs8jUzwhSknJMInBR9aYDYeiIv8M5uip5DnSOK0YlrkxyDTRwCPX6%2Bqwfv5NYTWSds1EMsGrKg6xWJwR6Kq9ZPbGDj2sfg4X21OOul39%2FPmAgE%3D
2025-07-28 17:38:48 [INFO] - atualizar_baixar_documento - Página de documento PDF do TJSP detectada
2025-07-28 17:38:48 [INFO] - atualizar_baixar_documento - MÉTODO PRIORITÁRIO: Tentando localizar visualizador de PDF em iframes...
2025-07-28 17:38:48 [INFO] - atualizar_baixar_documento - Encontrados 1 iframes na página
2025-07-28 17:38:48 [INFO] - atualizar_baixar_documento - Tentando iframe 1... src='https://esaj.tjsp.jus.br/pastadigital/js/viewer/web/viewer.html?file=%2Fpastadigital%2FgetPDF.do%3FnuSeqRecurso%3D00001%26nuProcesso%3D0116318-97.2006...'
2025-07-28 17:38:48 [INFO] - atualizar_baixar_documento - Mudou para iframe 1
2025-07-28 17:38:49 [INFO] - atualizar_baixar_documento - Botão de download encontrado no iframe 1 com seletor ('id', 'download')
2025-07-28 17:38:50 [INFO] - atualizar_baixar_documento - Clique realizado no botão dentro do iframe
2025-07-28 17:38:54 [INFO] - atualizar_baixar_documento - Clique no botão de download realizado com sucesso, assumindo download iniciado
2025-07-28 17:38:54 [INFO] - identificar_novo_arquivo_download - Novo arquivo de download identificado: doc_36853297.pdf
2025-07-28 17:38:54 [INFO] - download_documento_unificado - Download bem-sucedido. Arquivo real: doc_36853297.pdf. Nome sugerido: Oficio_Requisitorio_0116318-97_2006_8_26_0053_01.pdf
2025-07-28 17:38:54 [INFO] - processar_numero_autos - Download para '0116318-97.2006.8.26.0053 (01)' CONCLUÍDO como 'doc_36853297.pdf'
2025-07-28 17:38:55 [INFO] - processar_numero_autos - FIM NÚMERO 183 (Tempo: 26.41s)
2025-07-28 17:38:55 [INFO] - processar_numero_autos - Processando número 184/11405: 0003324-87.2010.8.26.0053
2025-07-28 17:38:55 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:38:55 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 17:38:58 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 17:38:58 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:38:58 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:39:00 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:39:00 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:39:00 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:39:00 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:39:00 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:39:00 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:39:02 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:39:02 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:39:02 [INFO] - consultar_processo_principal - Consultando 0003324-87.2010.8.26.0053 (TJSP: 0003324872010 / Foro: 0053)
2025-07-28 17:39:03 [INFO] - consultar_processo_principal - Lista de resultados para 0003324-87.2010.8.26.0053.
2025-07-28 17:39:03 [INFO] - consultar_processo_principal - Link para 0003324-87.2010.8.26.0053 encontrado. Clicando...
2025-07-28 17:39:05 [INFO] - consultar_processo_principal - Detalhes de 0003324-87.2010.8.26.0053 carregados após clique.
2025-07-28 17:39:05 [INFO] - verificar_status_processo - Status 'finalizado' (texto da página).
2025-07-28 17:39:05 [INFO] - processar_numero_autos - Processo principal 0003324-87.2010.8.26.0053 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 17:39:05 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:39:05 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Maurilio Azzi (lower: maurilio azzi)
2025-07-28 17:39:05 [INFO] - processar_numero_autos - Processo principal 0003324-87.2010.8.26.0053 sem partes proibidas
2025-07-28 17:39:06 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:39:07 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento
2025-07-28 17:39:07 [INFO] - processar_numero_autos - Processo principal 0003324-87.2010.8.26.0053 com Palavra Proibida em Movimentações - Continuando para precatórios (filtro removido)
2025-07-28 17:39:07 [INFO] - processar_numero_autos - Processo principal 0003324-87.2010.8.26.0053 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 17:39:07 [INFO] - processar_numero_autos - Links de precatório encontrados com seletor principal para 0003324-87.2010.8.26.0053.
2025-07-28 17:39:07 [INFO] - processar_numero_autos - 2 link(s) 'Precatório' para 0003324-87.2010.8.26.0053.
2025-07-28 17:39:09 [INFO] - processar_numero_autos - Processando link Precatório 1/2: 'Precatório - 00001'
2025-07-28 17:39:11 [INFO] - extrair_numero_precatorio_completo_da_pagina - Extraindo N° precatório para principal: 0003324-87.2010.8.26.0053
2025-07-28 17:39:11 [INFO] - extrair_numero_precatorio_completo_da_pagina - N° precatório não encontrado via span.unj-larger ou N° base divergente. Usar fallback do texto do link.
2025-07-28 17:39:11 [INFO] - processar_numero_autos - N° precatório formado via texto do link (fallback principal): 0003324-87.2010.8.26.0053 (01)
2025-07-28 17:39:11 [INFO] - obter_nome_cliente_do_precatorio - Buscando nome do cliente (Reqte)...
2025-07-28 17:39:11 [INFO] - obter_nome_cliente_do_precatorio - Nome do cliente: Luizemir Wolney Carvalho Lago
2025-07-28 17:39:11 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 17:39:11 [INFO] - processar_numero_autos - Precatório '0003324-87.2010.8.26.0053 (01)' status 'Finalizado' - pulando
2025-07-28 17:39:11 [INFO] - processar_numero_autos - Precatório rejeitado: 0003324-87.2010.8.26.0053 (01) - Cliente: Luizemir Wolney Carvalho Lago - Motivo: Status finalizado
2025-07-28 17:39:13 [INFO] - processar_numero_autos - Processando link Precatório 2/2: 'Precatório - 00004'
2025-07-28 17:39:15 [INFO] - extrair_numero_precatorio_completo_da_pagina - Extraindo N° precatório para principal: 0003324-87.2010.8.26.0053
2025-07-28 17:39:15 [INFO] - extrair_numero_precatorio_completo_da_pagina - N° precatório não encontrado via span.unj-larger ou N° base divergente. Usar fallback do texto do link.
2025-07-28 17:39:15 [INFO] - processar_numero_autos - N° precatório formado via texto do link (fallback principal): 0003324-87.2010.8.26.0053 (04)
2025-07-28 17:39:15 [INFO] - obter_nome_cliente_do_precatorio - Buscando nome do cliente (Reqte)...
2025-07-28 17:39:15 [INFO] - obter_nome_cliente_do_precatorio - Nome do cliente: Luizemir Wolney Carvalho Lago
2025-07-28 17:39:15 [INFO] - verificar_status_processo - Status: normal/em andamento.
2025-07-28 17:39:15 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:39:15 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Luizemir Wolney Carvalho Lago (lower: luizemir wolney carvalho lago)
2025-07-28 17:39:15 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:39:17 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento eletrônico
2025-07-28 17:39:17 [INFO] - processar_numero_autos - Precatório '0003324-87.2010.8.26.0053 (04)' com Palavra Proibida - pulando
2025-07-28 17:39:17 [INFO] - processar_numero_autos - Precatório rejeitado: 0003324-87.2010.8.26.0053 (04) - Cliente: Luizemir Wolney Carvalho Lago - Motivo: Palavras proibidas
2025-07-28 17:39:17 [INFO] - processar_numero_autos - FIM NÚMERO 184 (Tempo: 21.59s)
2025-07-28 17:39:18 [INFO] - processar_numero_autos - Processando número 185/11405: 0002903-73.2005.8.26.0053
2025-07-28 17:39:18 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:39:18 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 17:39:20 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 17:39:20 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:39:20 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:39:22 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:39:22 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:39:22 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:39:22 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:39:22 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:39:22 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:39:24 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:39:24 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:39:24 [INFO] - consultar_processo_principal - Consultando 0002903-73.2005.8.26.0053 (TJSP: 0002903732005 / Foro: 0053)
2025-07-28 17:39:25 [INFO] - consultar_processo_principal - Lista de resultados para 0002903-73.2005.8.26.0053.
2025-07-28 17:39:25 [INFO] - consultar_processo_principal - Link para 0002903-73.2005.8.26.0053 encontrado. Clicando...
2025-07-28 17:39:27 [INFO] - consultar_processo_principal - Detalhes de 0002903-73.2005.8.26.0053 carregados após clique.
2025-07-28 17:39:27 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 17:39:27 [INFO] - processar_numero_autos - Processo principal 0002903-73.2005.8.26.0053 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 17:39:27 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:39:27 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Carlos Alvares de Oliveira (lower: carlos alvares de oliveira)
2025-07-28 17:39:27 [INFO] - processar_numero_autos - Processo principal 0002903-73.2005.8.26.0053 sem partes proibidas
2025-07-28 17:39:28 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:39:29 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento
2025-07-28 17:39:29 [INFO] - processar_numero_autos - Processo principal 0002903-73.2005.8.26.0053 com Palavra Proibida em Movimentações - Continuando para precatórios (filtro removido)
2025-07-28 17:39:29 [INFO] - processar_numero_autos - Processo principal 0002903-73.2005.8.26.0053 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 17:39:30 [INFO] - processar_numero_autos - Links de precatório encontrados com seletor principal para 0002903-73.2005.8.26.0053.
2025-07-28 17:39:30 [INFO] - processar_numero_autos - 2 link(s) 'Precatório' para 0002903-73.2005.8.26.0053.
2025-07-28 17:39:31 [INFO] - processar_numero_autos - Processando link Precatório 1/2: 'Precatório - 00001'
2025-07-28 17:39:33 [INFO] - extrair_numero_precatorio_completo_da_pagina - Extraindo N° precatório para principal: 0002903-73.2005.8.26.0053
2025-07-28 17:39:33 [INFO] - extrair_numero_precatorio_completo_da_pagina - N° precatório não encontrado via span.unj-larger ou N° base divergente. Usar fallback do texto do link.
2025-07-28 17:39:33 [INFO] - processar_numero_autos - N° precatório formado via texto do link (fallback principal): 0002903-73.2005.8.26.0053 (01)
2025-07-28 17:39:33 [INFO] - obter_nome_cliente_do_precatorio - Buscando nome do cliente (Reqte)...
2025-07-28 17:39:33 [INFO] - obter_nome_cliente_do_precatorio - Nome do cliente: Deodato Thiago
2025-07-28 17:39:33 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 17:39:33 [INFO] - processar_numero_autos - Precatório '0002903-73.2005.8.26.0053 (01)' status 'Finalizado' - pulando
2025-07-28 17:39:33 [INFO] - processar_numero_autos - Precatório rejeitado: 0002903-73.2005.8.26.0053 (01) - Cliente: Deodato Thiago - Motivo: Status finalizado
2025-07-28 17:39:35 [INFO] - processar_numero_autos - Processando link Precatório 2/2: 'Precatório - 00003'
2025-07-28 17:39:37 [INFO] - extrair_numero_precatorio_completo_da_pagina - Extraindo N° precatório para principal: 0002903-73.2005.8.26.0053
2025-07-28 17:39:37 [INFO] - extrair_numero_precatorio_completo_da_pagina - N° precatório não encontrado via span.unj-larger ou N° base divergente. Usar fallback do texto do link.
2025-07-28 17:39:37 [INFO] - processar_numero_autos - N° precatório formado via texto do link (fallback principal): 0002903-73.2005.8.26.0053 (03)
2025-07-28 17:39:37 [INFO] - obter_nome_cliente_do_precatorio - Buscando nome do cliente (Reqte)...
2025-07-28 17:39:37 [INFO] - obter_nome_cliente_do_precatorio - Nome do cliente: Deodato Thiago
2025-07-28 17:39:37 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 17:39:37 [INFO] - processar_numero_autos - Precatório '0002903-73.2005.8.26.0053 (03)' status 'Finalizado' - pulando
2025-07-28 17:39:37 [INFO] - processar_numero_autos - Precatório rejeitado: 0002903-73.2005.8.26.0053 (03) - Cliente: Deodato Thiago - Motivo: Status finalizado
2025-07-28 17:39:37 [INFO] - processar_numero_autos - FIM NÚMERO 185 (Tempo: 19.47s)
2025-07-28 17:39:38 [INFO] - processar_numero_autos - Processando número 186/11405: 0107274-54.2006.8.26.0053
2025-07-28 17:39:38 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:39:38 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 17:39:40 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 17:39:40 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:39:40 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:39:42 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:39:42 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:39:42 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:39:42 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:39:42 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:39:42 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:39:44 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:39:44 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:39:44 [INFO] - consultar_processo_principal - Consultando 0107274-54.2006.8.26.0053 (TJSP: 0107274542006 / Foro: 0053)
2025-07-28 17:39:46 [INFO] - consultar_processo_principal - Lista de resultados para 0107274-54.2006.8.26.0053.
2025-07-28 17:39:46 [INFO] - consultar_processo_principal - Link para 0107274-54.2006.8.26.0053 encontrado. Clicando...
2025-07-28 17:39:47 [INFO] - consultar_processo_principal - Detalhes de 0107274-54.2006.8.26.0053 carregados após clique.
2025-07-28 17:39:47 [INFO] - verificar_status_processo - Status 'finalizado' (texto da página).
2025-07-28 17:39:47 [INFO] - processar_numero_autos - Processo principal 0107274-54.2006.8.26.0053 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 17:39:47 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:39:47 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Adriana Rocha (lower: adriana rocha)
2025-07-28 17:39:47 [INFO] - processar_numero_autos - Processo principal 0107274-54.2006.8.26.0053 sem partes proibidas
2025-07-28 17:39:48 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:39:49 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento
2025-07-28 17:39:49 [INFO] - processar_numero_autos - Processo principal 0107274-54.2006.8.26.0053 com Palavra Proibida em Movimentações - Continuando para precatórios (filtro removido)
2025-07-28 17:39:49 [INFO] - processar_numero_autos - Processo principal 0107274-54.2006.8.26.0053 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 17:39:50 [INFO] - processar_numero_autos - Links de precatório encontrados com seletor principal para 0107274-54.2006.8.26.0053.
2025-07-28 17:39:50 [INFO] - processar_numero_autos - 1 link(s) 'Precatório' para 0107274-54.2006.8.26.0053.
2025-07-28 17:39:51 [INFO] - processar_numero_autos - Processando link Precatório 1/1: 'Precatório - 00001'
2025-07-28 17:39:53 [INFO] - extrair_numero_precatorio_completo_da_pagina - Extraindo N° precatório para principal: 0107274-54.2006.8.26.0053
2025-07-28 17:39:53 [INFO] - extrair_numero_precatorio_completo_da_pagina - N° precatório não encontrado via span.unj-larger ou N° base divergente. Usar fallback do texto do link.
2025-07-28 17:39:53 [INFO] - processar_numero_autos - N° precatório formado via texto do link (fallback principal): 0107274-54.2006.8.26.0053 (01)
2025-07-28 17:39:53 [INFO] - obter_nome_cliente_do_precatorio - Buscando nome do cliente (Reqte)...
2025-07-28 17:39:53 [INFO] - obter_nome_cliente_do_precatorio - Nome do cliente: Adriana Rocha
2025-07-28 17:39:53 [INFO] - verificar_status_processo - Status: normal/em andamento.
2025-07-28 17:39:53 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:39:53 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Adriana Rocha (lower: adriana rocha)
2025-07-28 17:39:54 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:39:55 [INFO] - verificar_palavras_proibidas_mov - Nenhuma palavra proibida.
2025-07-28 17:39:55 [INFO] - processar_numero_autos - Precatório '0107274-54.2006.8.26.0053 (01)' validado - tentando baixar ofício
2025-07-28 17:39:55 [INFO] - encontrar_oficio_requisitorio - Procurando 'Ofício Requisitório-Precatório Expedido'...
2025-07-28 17:39:56 [INFO] - encontrar_oficio_requisitorio - Link 'Ofício Requisitório-Precatório Expedido' encontrado!
2025-07-28 17:39:58 [INFO] - encontrar_oficio_requisitorio - Mudou para nova janela/aba. URL: https://esaj.tjsp.jus.br/pastadigital/abrirDocumentoEdt.do?nuProcesso=0107274-54.2006.8.26.0053&cdProcesso=1HZX6DUA20001&instanciaProcesso=pg&cdProcessoMaster=1HZX6DUA20000&cdForo=53&baseIndice=INDDS&nmAlias=PG5JMDS&tpOrigem=2&flOrigem=P&cdServico=190101&acessibilidade=false&ticket=6%2BMVo5lkhYgd7cFzN09bQMo7DbaRQP0ciU9v3jTQY9DeBxdKdyk%2FYfy%2FDhiHd%2BmJbvKelDk9M4AbcgVOIBfslOOiCmnwD082Bhwt7VI69S2iUEcHmbHPc5dZDXQxN9dhSSa%2FaaSwdKVZgUo3VY5mVJXav8I0xIIxnkJKU8XBAhT1vZtkMsMoTCfZC2FQSIsdpu5I0oERzG8vZnF6zX%2B3tbWf0lgJ5KvdiRmS8I88YzUgGjXBWOcKra1PGlypZB9oTh9iQscDPddDS2TXZNz5czLm72Pep3dAK0DgAz9rGVLNHMpEZaJHRiQYETkAbmTR6CDVwtspJ%2FFaedoWNQ46Oclfp2X4UW2L7fJXqJ2T8%2BrXMPtB8Kbp3zmtNPUw6hjV3yonncHxGcWxpgB9Ph3MsUsLyVRsNc9YvtF7XUWJJcvyRhnn7%2B72IFZbQto6b%2Fs8jUzwhSknJMInBR9aYDYeiH2mer60M5hZO6yIsZCy2exn3lbe6WoA%2BIEdQ%2BDxU%2B33CpGIAcdFp9ZCyx567xsA6K%2B5IJsocWnT6HvjWOCEk6c%3D
2025-07-28 17:39:58 [INFO] - atualizar_baixar_documento - Iniciando download avançado para: Oficio_Requisitorio_0107274-54_2006_8_26_0053_01.pdf
2025-07-28 17:39:58 [INFO] - atualizar_baixar_documento - Diretório de destino: c:\Users\<USER>\Documents\Augment\Bipre\TJSP_PDF_e2e\tjsp\downloads_completos
2025-07-28 17:39:58 [INFO] - atualizar_baixar_documento - URL atual: https://esaj.tjsp.jus.br/pastadigital/abrirDocumentoEdt.do?nuProcesso=0107274-54.2006.8.26.0053&cdProcesso=1HZX6DUA20001&instanciaProcesso=pg&cdProcessoMaster=1HZX6DUA20000&cdForo=53&baseIndice=INDDS&nmAlias=PG5JMDS&tpOrigem=2&flOrigem=P&cdServico=190101&acessibilidade=false&ticket=6%2BMVo5lkhYgd7cFzN09bQMo7DbaRQP0ciU9v3jTQY9DeBxdKdyk%2FYfy%2FDhiHd%2BmJbvKelDk9M4AbcgVOIBfslOOiCmnwD082Bhwt7VI69S2iUEcHmbHPc5dZDXQxN9dhSSa%2FaaSwdKVZgUo3VY5mVJXav8I0xIIxnkJKU8XBAhT1vZtkMsMoTCfZC2FQSIsdpu5I0oERzG8vZnF6zX%2B3tbWf0lgJ5KvdiRmS8I88YzUgGjXBWOcKra1PGlypZB9oTh9iQscDPddDS2TXZNz5czLm72Pep3dAK0DgAz9rGVLNHMpEZaJHRiQYETkAbmTR6CDVwtspJ%2FFaedoWNQ46Oclfp2X4UW2L7fJXqJ2T8%2BrXMPtB8Kbp3zmtNPUw6hjV3yonncHxGcWxpgB9Ph3MsUsLyVRsNc9YvtF7XUWJJcvyRhnn7%2B72IFZbQto6b%2Fs8jUzwhSknJMInBR9aYDYeiH2mer60M5hZO6yIsZCy2exn3lbe6WoA%2BIEdQ%2BDxU%2B33CpGIAcdFp9ZCyx567xsA6K%2B5IJsocWnT6HvjWOCEk6c%3D
2025-07-28 17:39:58 [INFO] - atualizar_baixar_documento - Página de documento PDF do TJSP detectada
2025-07-28 17:39:58 [INFO] - atualizar_baixar_documento - MÉTODO PRIORITÁRIO: Tentando localizar visualizador de PDF em iframes...
2025-07-28 17:39:58 [INFO] - atualizar_baixar_documento - Encontrados 1 iframes na página
2025-07-28 17:39:58 [INFO] - atualizar_baixar_documento - Tentando iframe 1... src='https://esaj.tjsp.jus.br/pastadigital/js/viewer/web/viewer.html?file=%2Fpastadigital%2FgetPDF.do%3FnuSeqRecurso%3D00001%26nuProcesso%3D0107274-54.2006...'
2025-07-28 17:39:58 [INFO] - atualizar_baixar_documento - Mudou para iframe 1
2025-07-28 17:39:59 [INFO] - atualizar_baixar_documento - Botão de download encontrado no iframe 1 com seletor ('id', 'download')
2025-07-28 17:40:00 [INFO] - atualizar_baixar_documento - Clique realizado no botão dentro do iframe
2025-07-28 17:40:04 [INFO] - atualizar_baixar_documento - Clique no botão de download realizado com sucesso, assumindo download iniciado
2025-07-28 17:40:04 [INFO] - identificar_novo_arquivo_download - Novo arquivo de download identificado: doc_37741649.pdf
2025-07-28 17:40:04 [INFO] - download_documento_unificado - Download bem-sucedido. Arquivo real: doc_37741649.pdf. Nome sugerido: Oficio_Requisitorio_0107274-54_2006_8_26_0053_01.pdf
2025-07-28 17:40:04 [INFO] - processar_numero_autos - Download para '0107274-54.2006.8.26.0053 (01)' CONCLUÍDO como 'doc_37741649.pdf'
2025-07-28 17:40:04 [INFO] - processar_numero_autos - FIM NÚMERO 186 (Tempo: 26.73s)
2025-07-28 17:40:05 [INFO] - processar_numero_autos - Processando número 187/11405: 0413174-91.1996.8.26.0053
2025-07-28 17:40:05 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:40:05 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 17:40:07 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 17:40:07 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:40:07 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:40:09 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:40:09 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:40:09 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:40:09 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:40:09 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:40:09 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:40:11 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:40:11 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:40:11 [INFO] - consultar_processo_principal - Consultando 0413174-91.1996.8.26.0053 (TJSP: 0413174911996 / Foro: 0053)
2025-07-28 17:40:13 [INFO] - consultar_processo_principal - Lista de resultados para 0413174-91.1996.8.26.0053.
2025-07-28 17:40:13 [INFO] - consultar_processo_principal - Link para 0413174-91.1996.8.26.0053 encontrado. Clicando...
2025-07-28 17:40:15 [INFO] - consultar_processo_principal - Detalhes de 0413174-91.1996.8.26.0053 carregados após clique.
2025-07-28 17:40:15 [INFO] - verificar_status_processo - Status 'finalizado' (texto da página).
2025-07-28 17:40:15 [INFO] - processar_numero_autos - Processo principal 0413174-91.1996.8.26.0053 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 17:40:15 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:40:15 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Urbano Cavalcante de Almeida - Antecipacao de Tutela (lower: urbano cavalcante de almeida - antecipacao de tutela)
2025-07-28 17:40:15 [INFO] - processar_numero_autos - Processo principal 0413174-91.1996.8.26.0053 sem partes proibidas
2025-07-28 17:40:15 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:40:17 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento
2025-07-28 17:40:17 [INFO] - processar_numero_autos - Processo principal 0413174-91.1996.8.26.0053 com Palavra Proibida em Movimentações - Continuando para precatórios (filtro removido)
2025-07-28 17:40:17 [INFO] - processar_numero_autos - Processo principal 0413174-91.1996.8.26.0053 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 17:40:17 [INFO] - processar_numero_autos - Links de precatório encontrados com seletor principal para 0413174-91.1996.8.26.0053.
2025-07-28 17:40:17 [INFO] - processar_numero_autos - 1 link(s) 'Precatório' para 0413174-91.1996.8.26.0053.
2025-07-28 17:40:18 [INFO] - processar_numero_autos - Processando link Precatório 1/1: 'Precatório - 00001'
2025-07-28 17:40:20 [INFO] - extrair_numero_precatorio_completo_da_pagina - Extraindo N° precatório para principal: 0413174-91.1996.8.26.0053
2025-07-28 17:40:20 [INFO] - extrair_numero_precatorio_completo_da_pagina - N° precatório não encontrado via span.unj-larger ou N° base divergente. Usar fallback do texto do link.
2025-07-28 17:40:20 [INFO] - processar_numero_autos - N° precatório formado via texto do link (fallback principal): 0413174-91.1996.8.26.0053 (01)
2025-07-28 17:40:20 [INFO] - obter_nome_cliente_do_precatorio - Buscando nome do cliente (Reqte)...
2025-07-28 17:40:21 [INFO] - obter_nome_cliente_do_precatorio - Nome do cliente: Urbano Cavalcante de Almeida - Antecipacao de Tutela
2025-07-28 17:40:21 [INFO] - verificar_status_processo - Status: normal/em andamento.
2025-07-28 17:40:21 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:40:21 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Urbano Cavalcante de Almeida - Antecipacao de Tutela (lower: urbano cavalcante de almeida - antecipacao de tutela)
2025-07-28 17:40:21 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:40:22 [INFO] - verificar_palavras_proibidas_mov - Nenhuma palavra proibida.
2025-07-28 17:40:22 [INFO] - processar_numero_autos - Precatório '0413174-91.1996.8.26.0053 (01)' validado - tentando baixar ofício
2025-07-28 17:40:22 [INFO] - encontrar_oficio_requisitorio - Procurando 'Ofício Requisitório-Precatório Expedido'...
2025-07-28 17:40:24 [INFO] - encontrar_oficio_requisitorio - Link 'Ofício Requisitório-Precatório Expedido' encontrado!
2025-07-28 17:40:25 [INFO] - encontrar_oficio_requisitorio - Mudou para nova janela/aba. URL: https://esaj.tjsp.jus.br/pastadigital/abrirDocumentoEdt.do?nuProcesso=0413174-91.1996.8.26.0053&cdProcesso=1HZX0M29I0001&instanciaProcesso=pg&cdProcessoMaster=1HZX0M29I0000&cdForo=53&baseIndice=INDDS&nmAlias=PG5JMDS&tpOrigem=2&flOrigem=P&cdServico=190101&acessibilidade=false&ticket=6%2BMVo5lkhYgd7cFzN09bQMo7DbaRQP0ciU9v3jTQY9DeBxdKdyk%2FYfy%2FDhiHd%2BmJNsLv5MJ0C5n4F2PD1i3KWuOiCmnwD082Bhwt7VI69S2iUEcHmbHPc5dZDXQxN9dhSSa%2FaaSwdKVZgUo3VY5mVJXav8I0xIIxnkJKU8XBAhT1vZtkMsMoTCfZC2FQSIsdpu5I0oERzG8vZnF6zX%2B3tbWf0lgJ5KvdiRmS8I88YzUgGjXBWOcKra1PGlypZB9oTh9iQscDPddDS2TXZNz5czLm72Pep3dAK0DgAz9rGVLNHMpEZaJHRiQYETkAbmTR6CDVwtspJ%2FFaedoWNQ46Oclfp2X4UW2L7fJXqJ2T8%2BrXMPtB8Kbp3zmtNPUw6hjV3yonncHxGcWxpgB9Ph3MsUsLyVRsNc9YvtF7XUWJJcvyRhnn7%2B72IFZbQto6b%2Fs8jUzwhSknJMInBR9aYDYeiPkfGNySyeQx3dJT6MN0knhlFoQRiX3wOQkBGNnLglbXVSCcIhhQPOxDCH0sjhE4yreAW8K1QS4nv6gI3ibKLEE%3D
2025-07-28 17:40:25 [INFO] - atualizar_baixar_documento - Iniciando download avançado para: Oficio_Requisitorio_0413174-91_1996_8_26_0053_01.pdf
2025-07-28 17:40:25 [INFO] - atualizar_baixar_documento - Diretório de destino: c:\Users\<USER>\Documents\Augment\Bipre\TJSP_PDF_e2e\tjsp\downloads_completos
2025-07-28 17:40:25 [INFO] - atualizar_baixar_documento - URL atual: https://esaj.tjsp.jus.br/pastadigital/abrirDocumentoEdt.do?nuProcesso=0413174-91.1996.8.26.0053&cdProcesso=1HZX0M29I0001&instanciaProcesso=pg&cdProcessoMaster=1HZX0M29I0000&cdForo=53&baseIndice=INDDS&nmAlias=PG5JMDS&tpOrigem=2&flOrigem=P&cdServico=190101&acessibilidade=false&ticket=6%2BMVo5lkhYgd7cFzN09bQMo7DbaRQP0ciU9v3jTQY9DeBxdKdyk%2FYfy%2FDhiHd%2BmJNsLv5MJ0C5n4F2PD1i3KWuOiCmnwD082Bhwt7VI69S2iUEcHmbHPc5dZDXQxN9dhSSa%2FaaSwdKVZgUo3VY5mVJXav8I0xIIxnkJKU8XBAhT1vZtkMsMoTCfZC2FQSIsdpu5I0oERzG8vZnF6zX%2B3tbWf0lgJ5KvdiRmS8I88YzUgGjXBWOcKra1PGlypZB9oTh9iQscDPddDS2TXZNz5czLm72Pep3dAK0DgAz9rGVLNHMpEZaJHRiQYETkAbmTR6CDVwtspJ%2FFaedoWNQ46Oclfp2X4UW2L7fJXqJ2T8%2BrXMPtB8Kbp3zmtNPUw6hjV3yonncHxGcWxpgB9Ph3MsUsLyVRsNc9YvtF7XUWJJcvyRhnn7%2B72IFZbQto6b%2Fs8jUzwhSknJMInBR9aYDYeiPkfGNySyeQx3dJT6MN0knhlFoQRiX3wOQkBGNnLglbXVSCcIhhQPOxDCH0sjhE4yreAW8K1QS4nv6gI3ibKLEE%3D
2025-07-28 17:40:25 [INFO] - atualizar_baixar_documento - Página de documento PDF do TJSP detectada
2025-07-28 17:40:25 [INFO] - atualizar_baixar_documento - MÉTODO PRIORITÁRIO: Tentando localizar visualizador de PDF em iframes...
2025-07-28 17:40:25 [INFO] - atualizar_baixar_documento - Encontrados 1 iframes na página
2025-07-28 17:40:25 [INFO] - atualizar_baixar_documento - Tentando iframe 1... src='https://esaj.tjsp.jus.br/pastadigital/js/viewer/web/viewer.html?file=%2Fpastadigital%2FgetPDF.do%3FnuSeqRecurso%3D00001%26nuProcesso%3D0413174-91.1996...'
2025-07-28 17:40:25 [INFO] - atualizar_baixar_documento - Mudou para iframe 1
2025-07-28 17:40:26 [INFO] - atualizar_baixar_documento - Botão de download encontrado no iframe 1 com seletor ('id', 'download')
2025-07-28 17:40:27 [INFO] - atualizar_baixar_documento - Clique realizado no botão dentro do iframe
2025-07-28 17:40:32 [INFO] - atualizar_baixar_documento - Clique no botão de download realizado com sucesso, assumindo download iniciado
2025-07-28 17:40:32 [INFO] - identificar_novo_arquivo_download - Novo arquivo de download identificado: doc_38277764.pdf
2025-07-28 17:40:32 [INFO] - download_documento_unificado - Download bem-sucedido. Arquivo real: doc_38277764.pdf. Nome sugerido: Oficio_Requisitorio_0413174-91_1996_8_26_0053_01.pdf
2025-07-28 17:40:32 [INFO] - processar_numero_autos - Download para '0413174-91.1996.8.26.0053 (01)' CONCLUÍDO como 'doc_38277764.pdf'
2025-07-28 17:40:32 [INFO] - processar_numero_autos - FIM NÚMERO 187 (Tempo: 26.85s)
2025-07-28 17:40:32 [INFO] - processar_numero_autos - Processando número 188/11405: 0040641-56.2009.8.26.0053
2025-07-28 17:40:32 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:40:32 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 17:40:35 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 17:40:35 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:40:35 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:40:37 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:40:37 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:40:37 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:40:37 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:40:37 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:40:37 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:40:39 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:40:39 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:40:39 [INFO] - consultar_processo_principal - Consultando 0040641-56.2009.8.26.0053 (TJSP: 0040641562009 / Foro: 0053)
2025-07-28 17:40:41 [INFO] - consultar_processo_principal - Lista de resultados para 0040641-56.2009.8.26.0053.
2025-07-28 17:40:41 [INFO] - consultar_processo_principal - Link para 0040641-56.2009.8.26.0053 encontrado. Clicando...
2025-07-28 17:40:42 [INFO] - consultar_processo_principal - Detalhes de 0040641-56.2009.8.26.0053 carregados após clique.
2025-07-28 17:40:42 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 17:40:42 [INFO] - processar_numero_autos - Processo principal 0040641-56.2009.8.26.0053 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 17:40:42 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:40:42 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Ana Lucia Monteiro Martini de Lolo (lower: ana lucia monteiro martini de lolo)
2025-07-28 17:40:42 [INFO] - processar_numero_autos - Processo principal 0040641-56.2009.8.26.0053 sem partes proibidas
2025-07-28 17:40:43 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:40:44 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento
2025-07-28 17:40:44 [INFO] - processar_numero_autos - Processo principal 0040641-56.2009.8.26.0053 com Palavra Proibida em Movimentações - Continuando para precatórios (filtro removido)
2025-07-28 17:40:44 [INFO] - processar_numero_autos - Processo principal 0040641-56.2009.8.26.0053 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 17:40:44 [INFO] - processar_numero_autos - Links de precatório encontrados com seletor principal para 0040641-56.2009.8.26.0053.
2025-07-28 17:40:44 [INFO] - processar_numero_autos - 1 link(s) 'Precatório' para 0040641-56.2009.8.26.0053.
2025-07-28 17:40:46 [INFO] - processar_numero_autos - Processando link Precatório 1/1: 'Precatório - 00001'
2025-07-28 17:40:48 [INFO] - extrair_numero_precatorio_completo_da_pagina - Extraindo N° precatório para principal: 0040641-56.2009.8.26.0053
2025-07-28 17:40:48 [INFO] - extrair_numero_precatorio_completo_da_pagina - N° precatório não encontrado via span.unj-larger ou N° base divergente. Usar fallback do texto do link.
2025-07-28 17:40:48 [INFO] - processar_numero_autos - N° precatório formado via texto do link (fallback principal): 0040641-56.2009.8.26.0053 (01)
2025-07-28 17:40:48 [INFO] - obter_nome_cliente_do_precatorio - Buscando nome do cliente (Reqte)...
2025-07-28 17:40:48 [INFO] - obter_nome_cliente_do_precatorio - Nome do cliente: Advocacia Sandoval Filho
2025-07-28 17:40:48 [INFO] - verificar_status_processo - Status 'finalizado' detectado: arquivado
2025-07-28 17:40:48 [INFO] - processar_numero_autos - Precatório '0040641-56.2009.8.26.0053 (01)' status 'Finalizado' - pulando
2025-07-28 17:40:48 [INFO] - processar_numero_autos - Precatório rejeitado: 0040641-56.2009.8.26.0053 (01) - Cliente: Advocacia Sandoval Filho - Motivo: Status finalizado
2025-07-28 17:40:48 [INFO] - processar_numero_autos - FIM NÚMERO 188 (Tempo: 15.84s)
2025-07-28 17:40:49 [INFO] - processar_numero_autos - Processando número 189/11405: 0013200-47.2002.8.26.0053
2025-07-28 17:40:49 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:40:49 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 17:40:51 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 17:40:51 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:40:51 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:40:53 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:40:53 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:40:53 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:40:53 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:40:53 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:40:53 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:40:55 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:40:55 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:40:55 [INFO] - consultar_processo_principal - Consultando 0013200-47.2002.8.26.0053 (TJSP: 0013200472002 / Foro: 0053)
2025-07-28 17:40:57 [INFO] - consultar_processo_principal - Lista de resultados para 0013200-47.2002.8.26.0053.
2025-07-28 17:40:57 [INFO] - consultar_processo_principal - Link para 0013200-47.2002.8.26.0053 encontrado. Clicando...
2025-07-28 17:40:58 [INFO] - consultar_processo_principal - Detalhes de 0013200-47.2002.8.26.0053 carregados após clique.
2025-07-28 17:40:58 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 17:40:58 [INFO] - processar_numero_autos - Processo principal 0013200-47.2002.8.26.0053 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 17:40:58 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:40:58 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Vera Lúcia Menezes Weingrill (lower: vera lúcia menezes weingrill)
2025-07-28 17:40:58 [INFO] - processar_numero_autos - Processo principal 0013200-47.2002.8.26.0053 sem partes proibidas
2025-07-28 17:40:59 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:41:00 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento
2025-07-28 17:41:00 [INFO] - processar_numero_autos - Processo principal 0013200-47.2002.8.26.0053 com Palavra Proibida em Movimentações - Continuando para precatórios (filtro removido)
2025-07-28 17:41:00 [INFO] - processar_numero_autos - Processo principal 0013200-47.2002.8.26.0053 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 17:41:00 [INFO] - processar_numero_autos - Links de precatório encontrados com seletor principal para 0013200-47.2002.8.26.0053.
2025-07-28 17:41:00 [INFO] - processar_numero_autos - 1 link(s) 'Precatório' para 0013200-47.2002.8.26.0053.
2025-07-28 17:41:02 [INFO] - processar_numero_autos - Processando link Precatório 1/1: 'Precatório - 00001'
2025-07-28 17:41:04 [INFO] - extrair_numero_precatorio_completo_da_pagina - Extraindo N° precatório para principal: 0013200-47.2002.8.26.0053
2025-07-28 17:41:04 [INFO] - extrair_numero_precatorio_completo_da_pagina - N° precatório não encontrado via span.unj-larger ou N° base divergente. Usar fallback do texto do link.
2025-07-28 17:41:04 [INFO] - processar_numero_autos - N° precatório formado via texto do link (fallback principal): 0013200-47.2002.8.26.0053 (01)
2025-07-28 17:41:04 [INFO] - obter_nome_cliente_do_precatorio - Buscando nome do cliente (Reqte)...
2025-07-28 17:41:04 [INFO] - obter_nome_cliente_do_precatorio - Nome do cliente: Vera Lúcia Menezes Weingrill
2025-07-28 17:41:04 [INFO] - verificar_status_processo - Status 'finalizado' detectado: arquivado
2025-07-28 17:41:04 [INFO] - processar_numero_autos - Precatório '0013200-47.2002.8.26.0053 (01)' status 'Finalizado' - pulando
2025-07-28 17:41:04 [INFO] - processar_numero_autos - Precatório rejeitado: 0013200-47.2002.8.26.0053 (01) - Cliente: Vera Lúcia Menezes Weingrill - Motivo: Status finalizado
2025-07-28 17:41:04 [INFO] - processar_numero_autos - FIM NÚMERO 189 (Tempo: 15.33s)
2025-07-28 17:41:05 [INFO] - verificar_sessao_ativa - Verificando se a sessão ainda está ativa
2025-07-28 17:41:05 [INFO] - verificar_sessao_ativa - Navegando para página de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 17:41:08 [INFO] - verificar_sessao_ativa - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:41:08 [INFO] - _detectar_sessao_expirada - Nome de usuário válido detectado: 'DENIS HENRIQUE SOUSA OLIVEIRA' - sessão ativa
2025-07-28 17:41:08 [INFO] - verificar_sessao_ativa - Sessão ativa confirmada - Usuário: DENIS HENRIQUE SOUSA OLIVEIRA
2025-07-28 17:41:08 [INFO] - processar_numero_autos - Processando número 190/11405: 0408929-32.1999.8.26.0053
2025-07-28 17:41:08 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:41:08 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:41:08 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:41:08 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:41:10 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:41:10 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:41:10 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:41:10 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:41:10 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:41:10 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:41:12 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:41:12 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:41:12 [INFO] - consultar_processo_principal - Consultando 0408929-32.1999.8.26.0053 (TJSP: 0408929321999 / Foro: 0053)
2025-07-28 17:41:14 [INFO] - consultar_processo_principal - Lista de resultados para 0408929-32.1999.8.26.0053.
2025-07-28 17:41:14 [INFO] - consultar_processo_principal - Link para 0408929-32.1999.8.26.0053 encontrado. Clicando...
2025-07-28 17:41:15 [INFO] - consultar_processo_principal - Detalhes de 0408929-32.1999.8.26.0053 carregados após clique.
2025-07-28 17:41:15 [INFO] - verificar_status_processo - Status 'finalizado' (texto da página).
2025-07-28 17:41:15 [INFO] - processar_numero_autos - Processo principal 0408929-32.1999.8.26.0053 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 17:41:15 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:41:15 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Dalva Cassia de Araujo Camara (lower: dalva cassia de araujo camara)
2025-07-28 17:41:15 [INFO] - processar_numero_autos - Processo principal 0408929-32.1999.8.26.0053 sem partes proibidas
2025-07-28 17:41:16 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:41:17 [INFO] - verificar_palavras_proibidas_mov - Nenhuma palavra proibida.
2025-07-28 17:41:17 [INFO] - processar_numero_autos - Processo principal 0408929-32.1999.8.26.0053 sem palavras proibidas
2025-07-28 17:41:17 [INFO] - processar_numero_autos - Processo principal 0408929-32.1999.8.26.0053 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 17:41:17 [INFO] - processar_numero_autos - Links de precatório encontrados com seletor principal para 0408929-32.1999.8.26.0053.
2025-07-28 17:41:17 [INFO] - processar_numero_autos - 2 link(s) 'Precatório' para 0408929-32.1999.8.26.0053.
2025-07-28 17:41:19 [INFO] - processar_numero_autos - Processando link Precatório 1/2: 'Precatório - 00001'
2025-07-28 17:41:21 [INFO] - extrair_numero_precatorio_completo_da_pagina - Extraindo N° precatório para principal: 0408929-32.1999.8.26.0053
2025-07-28 17:41:21 [INFO] - extrair_numero_precatorio_completo_da_pagina - N° precatório não encontrado via span.unj-larger ou N° base divergente. Usar fallback do texto do link.
2025-07-28 17:41:21 [INFO] - processar_numero_autos - N° precatório formado via texto do link (fallback principal): 0408929-32.1999.8.26.0053 (01)
2025-07-28 17:41:21 [INFO] - obter_nome_cliente_do_precatorio - Buscando nome do cliente (Reqte)...
2025-07-28 17:41:21 [ERROR] - obter_nome_cliente_do_precatorio - Nome do cliente não localizado.
2025-07-28 17:41:21 [INFO] - verificar_status_processo - Status 'finalizado' detectado: arquivado
2025-07-28 17:41:21 [INFO] - processar_numero_autos - Precatório '0408929-32.1999.8.26.0053 (01)' status 'Finalizado' - pulando
2025-07-28 17:41:21 [INFO] - processar_numero_autos - Precatório rejeitado: 0408929-32.1999.8.26.0053 (01) - Cliente: CLIENTE NÃO LOCALIZADO - Motivo: Status finalizado
2025-07-28 17:41:23 [INFO] - processar_numero_autos - Processando link Precatório 2/2: 'Precatório - 00002'
2025-07-28 17:41:25 [INFO] - extrair_numero_precatorio_completo_da_pagina - Extraindo N° precatório para principal: 0408929-32.1999.8.26.0053
2025-07-28 17:41:25 [INFO] - extrair_numero_precatorio_completo_da_pagina - N° precatório não encontrado via span.unj-larger ou N° base divergente. Usar fallback do texto do link.
2025-07-28 17:41:25 [INFO] - processar_numero_autos - N° precatório formado via texto do link (fallback principal): 0408929-32.1999.8.26.0053 (02)
2025-07-28 17:41:25 [INFO] - obter_nome_cliente_do_precatorio - Buscando nome do cliente (Reqte)...
2025-07-28 17:41:25 [ERROR] - obter_nome_cliente_do_precatorio - Nome do cliente não localizado.
2025-07-28 17:41:25 [INFO] - verificar_status_processo - Status: normal/em andamento.
2025-07-28 17:41:25 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:41:25 [ERROR] - verificar_partes_processo - Reqte não localizado para verificação de partes.
2025-07-28 17:41:25 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:41:27 [INFO] - verificar_palavras_proibidas_mov - Nenhuma palavra proibida.
2025-07-28 17:41:27 [INFO] - processar_numero_autos - Precatório '0408929-32.1999.8.26.0053 (02)' validado - tentando baixar ofício
2025-07-28 17:41:27 [INFO] - encontrar_oficio_requisitorio - Procurando 'Ofício Requisitório-Precatório Expedido'...
2025-07-28 17:41:28 [INFO] - encontrar_oficio_requisitorio - Link 'Ofício Requisitório-Precatório Expedido' encontrado!
2025-07-28 17:41:30 [INFO] - encontrar_oficio_requisitorio - Mudou para nova janela/aba. URL: https://esaj.tjsp.jus.br/pastadigital/abrirDocumentoEdt.do?nuProcesso=0408929-32.1999.8.26.0053&cdProcesso=1HZX2E9SX0002&instanciaProcesso=pg&cdProcessoMaster=1HZX2E9SX0000&cdForo=53&baseIndice=INDDS&nmAlias=PG5JMDS&tpOrigem=2&flOrigem=P&cdServico=190101&acessibilidade=false&ticket=6%2BMVo5lkhYgd7cFzN09bQMo7DbaRQP0ciU9v3jTQY9DeBxdKdyk%2FYfy%2FDhiHd%2BmJObwIkrpYYaeQ8bEuVoh5hOOiCmnwD082Bhwt7VI69S2iUEcHmbHPc5dZDXQxN9dhSSa%2FaaSwdKVZgUo3VY5mVJXav8I0xIIxnkJKU8XBAhT1vZtkMsMoTCfZC2FQSIsdpu5I0oERzG8vZnF6zX%2B3tbWf0lgJ5KvdiRmS8I88YzUgGjXBWOcKra1PGlypZB9oTh9iQscDPddDS2TXZNz5czLm72Pep3dAK0DgAz9rGVLNHMpEZaJHRiQYETkAbmTR6CDVwtspJ%2FFaedoWNQ46Oclfp2X4UW2L7fJXqJ2T8%2BrXMPtB8Kbp3zmtNPUw6hjV3yonncHxGcWxpgB9Ph3MsUsLyVRsNc9YvtF7XUWJJcvyRhnn7%2B72IFZbQto6b%2Fs8jUzwhSknJMInBR9aYDYeiKS2Bw4bnYVk1pk5%2FQrjwSyJuS3NivjA8hKythCz2urnpjCKvJbNVKN%2BLhX8wFmQL5CI8f0EaCf3gj15Mx3rx%2FM%3D
2025-07-28 17:41:30 [INFO] - atualizar_baixar_documento - Iniciando download avançado para: Oficio_Requisitorio_0408929-32_1999_8_26_0053_02.pdf
2025-07-28 17:41:30 [INFO] - atualizar_baixar_documento - Diretório de destino: c:\Users\<USER>\Documents\Augment\Bipre\TJSP_PDF_e2e\tjsp\downloads_completos
2025-07-28 17:41:30 [INFO] - atualizar_baixar_documento - URL atual: https://esaj.tjsp.jus.br/pastadigital/abrirDocumentoEdt.do?nuProcesso=0408929-32.1999.8.26.0053&cdProcesso=1HZX2E9SX0002&instanciaProcesso=pg&cdProcessoMaster=1HZX2E9SX0000&cdForo=53&baseIndice=INDDS&nmAlias=PG5JMDS&tpOrigem=2&flOrigem=P&cdServico=190101&acessibilidade=false&ticket=6%2BMVo5lkhYgd7cFzN09bQMo7DbaRQP0ciU9v3jTQY9DeBxdKdyk%2FYfy%2FDhiHd%2BmJObwIkrpYYaeQ8bEuVoh5hOOiCmnwD082Bhwt7VI69S2iUEcHmbHPc5dZDXQxN9dhSSa%2FaaSwdKVZgUo3VY5mVJXav8I0xIIxnkJKU8XBAhT1vZtkMsMoTCfZC2FQSIsdpu5I0oERzG8vZnF6zX%2B3tbWf0lgJ5KvdiRmS8I88YzUgGjXBWOcKra1PGlypZB9oTh9iQscDPddDS2TXZNz5czLm72Pep3dAK0DgAz9rGVLNHMpEZaJHRiQYETkAbmTR6CDVwtspJ%2FFaedoWNQ46Oclfp2X4UW2L7fJXqJ2T8%2BrXMPtB8Kbp3zmtNPUw6hjV3yonncHxGcWxpgB9Ph3MsUsLyVRsNc9YvtF7XUWJJcvyRhnn7%2B72IFZbQto6b%2Fs8jUzwhSknJMInBR9aYDYeiKS2Bw4bnYVk1pk5%2FQrjwSyJuS3NivjA8hKythCz2urnpjCKvJbNVKN%2BLhX8wFmQL5CI8f0EaCf3gj15Mx3rx%2FM%3D
2025-07-28 17:41:30 [INFO] - atualizar_baixar_documento - Página de documento PDF do TJSP detectada
2025-07-28 17:41:30 [INFO] - atualizar_baixar_documento - MÉTODO PRIORITÁRIO: Tentando localizar visualizador de PDF em iframes...
2025-07-28 17:41:30 [INFO] - atualizar_baixar_documento - Encontrados 1 iframes na página
2025-07-28 17:41:30 [INFO] - atualizar_baixar_documento - Tentando iframe 1... src='https://esaj.tjsp.jus.br/pastadigital/js/viewer/web/viewer.html?file=%2Fpastadigital%2FgetPDF.do%3FnuSeqRecurso%3D00002%26nuProcesso%3D0408929-32.1999...'
2025-07-28 17:41:30 [INFO] - atualizar_baixar_documento - Mudou para iframe 1
2025-07-28 17:41:31 [INFO] - atualizar_baixar_documento - Botão de download encontrado no iframe 1 com seletor ('id', 'download')
2025-07-28 17:41:32 [INFO] - atualizar_baixar_documento - Clique realizado no botão dentro do iframe
2025-07-28 17:41:36 [INFO] - atualizar_baixar_documento - Clique no botão de download realizado com sucesso, assumindo download iniciado
2025-07-28 17:41:36 [INFO] - identificar_novo_arquivo_download - Novo arquivo de download identificado: doc_30714309.pdf
2025-07-28 17:41:36 [INFO] - download_documento_unificado - Download bem-sucedido. Arquivo real: doc_30714309.pdf. Nome sugerido: Oficio_Requisitorio_0408929-32_1999_8_26_0053_02.pdf
2025-07-28 17:41:36 [INFO] - processar_numero_autos - Download para '0408929-32.1999.8.26.0053 (02)' CONCLUÍDO como 'doc_30714309.pdf'
2025-07-28 17:41:36 [INFO] - processar_numero_autos - FIM NÚMERO 190 (Tempo: 28.29s)
2025-07-28 17:41:37 [INFO] - processar_numero_autos - Processando número 191/11405: 0019108-41.2009.8.26.0053
2025-07-28 17:41:37 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:41:37 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 17:41:39 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 17:41:39 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:41:39 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:41:41 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:41:41 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:41:41 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:41:41 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:41:41 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:41:41 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:41:43 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:41:43 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:41:43 [INFO] - consultar_processo_principal - Consultando 0019108-41.2009.8.26.0053 (TJSP: 0019108412009 / Foro: 0053)
2025-07-28 17:41:45 [INFO] - consultar_processo_principal - Lista de resultados para 0019108-41.2009.8.26.0053.
2025-07-28 17:41:45 [INFO] - consultar_processo_principal - Link para 0019108-41.2009.8.26.0053 encontrado. Clicando...
2025-07-28 17:41:46 [INFO] - consultar_processo_principal - Detalhes de 0019108-41.2009.8.26.0053 carregados após clique.
2025-07-28 17:41:46 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 17:41:46 [INFO] - processar_numero_autos - Processo principal 0019108-41.2009.8.26.0053 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 17:41:46 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:41:46 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Gilberto Henrique Borges (lower: gilberto henrique borges)
2025-07-28 17:41:46 [INFO] - processar_numero_autos - Processo principal 0019108-41.2009.8.26.0053 sem partes proibidas
2025-07-28 17:41:47 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:41:48 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento eletrônico
2025-07-28 17:41:48 [INFO] - processar_numero_autos - Processo principal 0019108-41.2009.8.26.0053 com Palavra Proibida em Movimentações - Continuando para precatórios (filtro removido)
2025-07-28 17:41:48 [INFO] - processar_numero_autos - Processo principal 0019108-41.2009.8.26.0053 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 17:41:48 [INFO] - processar_numero_autos - Links de precatório encontrados com seletor principal para 0019108-41.2009.8.26.0053.
2025-07-28 17:41:48 [INFO] - processar_numero_autos - 1 link(s) 'Precatório' para 0019108-41.2009.8.26.0053.
2025-07-28 17:41:50 [INFO] - processar_numero_autos - Processando link Precatório 1/1: 'Precatório - 00001'
2025-07-28 17:41:52 [INFO] - extrair_numero_precatorio_completo_da_pagina - Extraindo N° precatório para principal: 0019108-41.2009.8.26.0053
2025-07-28 17:41:52 [INFO] - extrair_numero_precatorio_completo_da_pagina - N° precatório não encontrado via span.unj-larger ou N° base divergente. Usar fallback do texto do link.
2025-07-28 17:41:52 [INFO] - processar_numero_autos - N° precatório formado via texto do link (fallback principal): 0019108-41.2009.8.26.0053 (01)
2025-07-28 17:41:52 [INFO] - obter_nome_cliente_do_precatorio - Buscando nome do cliente (Reqte)...
2025-07-28 17:41:52 [INFO] - obter_nome_cliente_do_precatorio - Nome do cliente: Gilberto Henrique Borges
2025-07-28 17:41:52 [INFO] - verificar_status_processo - Status: normal/em andamento.
2025-07-28 17:41:52 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:41:52 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Gilberto Henrique Borges (lower: gilberto henrique borges)
2025-07-28 17:41:53 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:41:54 [INFO] - verificar_palavras_proibidas_mov - Nenhuma palavra proibida.
2025-07-28 17:41:54 [INFO] - processar_numero_autos - Precatório '0019108-41.2009.8.26.0053 (01)' validado - tentando baixar ofício
2025-07-28 17:41:54 [INFO] - encontrar_oficio_requisitorio - Procurando 'Ofício Requisitório-Precatório Expedido'...
2025-07-28 17:41:55 [INFO] - encontrar_oficio_requisitorio - Link 'Ofício Requisitório-Precatório Expedido' encontrado!
2025-07-28 17:41:57 [INFO] - encontrar_oficio_requisitorio - Mudou para nova janela/aba. URL: https://esaj.tjsp.jus.br/pastadigital/abrirDocumentoEdt.do?nuProcesso=0019108-41.2009.8.26.0053&cdProcesso=1H0000UBQ0001&instanciaProcesso=pg&cdProcessoMaster=1H0000UBQ0000&cdForo=53&baseIndice=INDDS&nmAlias=PG5JMDS&tpOrigem=2&flOrigem=P&cdServico=190101&acessibilidade=false&ticket=6%2BMVo5lkhYgd7cFzN09bQMo7DbaRQP0ciU9v3jTQY9DeBxdKdyk%2FYfy%2FDhiHd%2BmJci1OKJniNpnmqvMVY%2Bi2k%2BOiCmnwD082Bhwt7VI69S2iUEcHmbHPc5dZDXQxN9dhSSa%2FaaSwdKVZgUo3VY5mVJXav8I0xIIxnkJKU8XBAhT1vZtkMsMoTCfZC2FQSIsdpu5I0oERzG8vZnF6zX%2B3tbWf0lgJ5KvdiRmS8I88YzUgGjXBWOcKra1PGlypZB9oTh9iQscDPddDS2TXZNz5czLm72Pep3dAK0DgAz9rGVLNHMpEZaJHRiQYETkAbmTR6CDVwtspJ%2FFaedoWNQ46Oclfp2X4UW2L7fJXqJ2T8%2BrXMPtB8Kbp3zmtNPUw6hjV3yonncHxGcWxpgB9Ph3MsUsLyVRsNc9YvtF7XUWJJcvyRhnn7%2B72IFZbQto6b%2Fs8jUzwhSknJMInBR9aYDYeiPtLYBB%2BSC9ePJIJg%2FsaynwTA1OIr13INvjTlPX2n%2B%2FAQJY8e0jODjkYgPD%2Bmo8E5%2B%2BZIe4uQsbxwu4OEDqD3kg%3D
2025-07-28 17:41:57 [INFO] - atualizar_baixar_documento - Iniciando download avançado para: Oficio_Requisitorio_0019108-41_2009_8_26_0053_01.pdf
2025-07-28 17:41:57 [INFO] - atualizar_baixar_documento - Diretório de destino: c:\Users\<USER>\Documents\Augment\Bipre\TJSP_PDF_e2e\tjsp\downloads_completos
2025-07-28 17:41:57 [INFO] - atualizar_baixar_documento - URL atual: https://esaj.tjsp.jus.br/pastadigital/abrirDocumentoEdt.do?nuProcesso=0019108-41.2009.8.26.0053&cdProcesso=1H0000UBQ0001&instanciaProcesso=pg&cdProcessoMaster=1H0000UBQ0000&cdForo=53&baseIndice=INDDS&nmAlias=PG5JMDS&tpOrigem=2&flOrigem=P&cdServico=190101&acessibilidade=false&ticket=6%2BMVo5lkhYgd7cFzN09bQMo7DbaRQP0ciU9v3jTQY9DeBxdKdyk%2FYfy%2FDhiHd%2BmJci1OKJniNpnmqvMVY%2Bi2k%2BOiCmnwD082Bhwt7VI69S2iUEcHmbHPc5dZDXQxN9dhSSa%2FaaSwdKVZgUo3VY5mVJXav8I0xIIxnkJKU8XBAhT1vZtkMsMoTCfZC2FQSIsdpu5I0oERzG8vZnF6zX%2B3tbWf0lgJ5KvdiRmS8I88YzUgGjXBWOcKra1PGlypZB9oTh9iQscDPddDS2TXZNz5czLm72Pep3dAK0DgAz9rGVLNHMpEZaJHRiQYETkAbmTR6CDVwtspJ%2FFaedoWNQ46Oclfp2X4UW2L7fJXqJ2T8%2BrXMPtB8Kbp3zmtNPUw6hjV3yonncHxGcWxpgB9Ph3MsUsLyVRsNc9YvtF7XUWJJcvyRhnn7%2B72IFZbQto6b%2Fs8jUzwhSknJMInBR9aYDYeiPtLYBB%2BSC9ePJIJg%2FsaynwTA1OIr13INvjTlPX2n%2B%2FAQJY8e0jODjkYgPD%2Bmo8E5%2B%2BZIe4uQsbxwu4OEDqD3kg%3D
2025-07-28 17:41:57 [INFO] - atualizar_baixar_documento - Página de documento PDF do TJSP detectada
2025-07-28 17:41:57 [INFO] - atualizar_baixar_documento - MÉTODO PRIORITÁRIO: Tentando localizar visualizador de PDF em iframes...
2025-07-28 17:41:57 [INFO] - atualizar_baixar_documento - Encontrados 1 iframes na página
2025-07-28 17:41:57 [INFO] - atualizar_baixar_documento - Tentando iframe 1... src='https://esaj.tjsp.jus.br/pastadigital/js/viewer/web/viewer.html?file=%2Fpastadigital%2FgetPDF.do%3FnuSeqRecurso%3D00001%26nuProcesso%3D0019108-41.2009...'
2025-07-28 17:41:57 [INFO] - atualizar_baixar_documento - Mudou para iframe 1
2025-07-28 17:41:58 [INFO] - atualizar_baixar_documento - Botão de download encontrado no iframe 1 com seletor ('id', 'download')
2025-07-28 17:41:59 [INFO] - atualizar_baixar_documento - Clique realizado no botão dentro do iframe
2025-07-28 17:42:03 [INFO] - atualizar_baixar_documento - Clique no botão de download realizado com sucesso, assumindo download iniciado
2025-07-28 17:42:03 [INFO] - identificar_novo_arquivo_download - Novo arquivo de download identificado: doc_38322011.pdf
2025-07-28 17:42:03 [INFO] - download_documento_unificado - Download bem-sucedido. Arquivo real: doc_38322011.pdf. Nome sugerido: Oficio_Requisitorio_0019108-41_2009_8_26_0053_01.pdf
2025-07-28 17:42:03 [INFO] - processar_numero_autos - Download para '0019108-41.2009.8.26.0053 (01)' CONCLUÍDO como 'doc_38322011.pdf'
2025-07-28 17:42:03 [INFO] - processar_numero_autos - FIM NÚMERO 191 (Tempo: 26.56s)
2025-07-28 17:42:04 [INFO] - processar_numero_autos - Processando número 192/11405: 0023130-11.2010.8.26.0053
2025-07-28 17:42:04 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:42:04 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 17:42:06 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 17:42:06 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:42:06 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:42:08 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:42:08 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:42:08 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:42:08 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:42:08 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:42:08 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:42:10 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:42:10 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:42:10 [INFO] - consultar_processo_principal - Consultando 0023130-11.2010.8.26.0053 (TJSP: 0023130112010 / Foro: 0053)
2025-07-28 17:42:12 [INFO] - consultar_processo_principal - Lista de resultados para 0023130-11.2010.8.26.0053.
2025-07-28 17:42:12 [INFO] - consultar_processo_principal - Link para 0023130-11.2010.8.26.0053 encontrado. Clicando...
2025-07-28 17:42:13 [INFO] - consultar_processo_principal - Detalhes de 0023130-11.2010.8.26.0053 carregados após clique.
2025-07-28 17:42:14 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 17:42:14 [INFO] - processar_numero_autos - Processo principal 0023130-11.2010.8.26.0053 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 17:42:14 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:42:14 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Violante Andrade e Silva - espólio (lower: violante andrade e silva - espólio)
2025-07-28 17:42:14 [INFO] - processar_numero_autos - Processo principal 0023130-11.2010.8.26.0053 sem partes proibidas
2025-07-28 17:42:14 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:42:16 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento eletrônico
2025-07-28 17:42:16 [INFO] - processar_numero_autos - Processo principal 0023130-11.2010.8.26.0053 com Palavra Proibida em Movimentações - Continuando para precatórios (filtro removido)
2025-07-28 17:42:16 [INFO] - processar_numero_autos - Processo principal 0023130-11.2010.8.26.0053 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 17:42:16 [INFO] - processar_numero_autos - Links de precatório encontrados com seletor principal para 0023130-11.2010.8.26.0053.
2025-07-28 17:42:16 [INFO] - processar_numero_autos - 5 link(s) 'Precatório' para 0023130-11.2010.8.26.0053.
2025-07-28 17:42:17 [INFO] - processar_numero_autos - Processando link Precatório 1/5: 'Precatório - 00001'
2025-07-28 17:42:19 [INFO] - extrair_numero_precatorio_completo_da_pagina - Extraindo N° precatório para principal: 0023130-11.2010.8.26.0053
2025-07-28 17:42:19 [INFO] - extrair_numero_precatorio_completo_da_pagina - N° precatório não encontrado via span.unj-larger ou N° base divergente. Usar fallback do texto do link.
2025-07-28 17:42:19 [INFO] - processar_numero_autos - N° precatório formado via texto do link (fallback principal): 0023130-11.2010.8.26.0053 (01)
2025-07-28 17:42:19 [INFO] - obter_nome_cliente_do_precatorio - Buscando nome do cliente (Reqte)...
2025-07-28 17:42:19 [INFO] - obter_nome_cliente_do_precatorio - Nome do cliente: Violante Andrade e Silva
2025-07-28 17:42:19 [INFO] - verificar_status_processo - Status 'finalizado' detectado: arquivado
2025-07-28 17:42:19 [INFO] - processar_numero_autos - Precatório '0023130-11.2010.8.26.0053 (01)' status 'Finalizado' - pulando
2025-07-28 17:42:19 [INFO] - processar_numero_autos - Precatório rejeitado: 0023130-11.2010.8.26.0053 (01) - Cliente: Violante Andrade e Silva - Motivo: Status finalizado
2025-07-28 17:42:21 [INFO] - processar_numero_autos - Processando link Precatório 2/5: 'Precatório - 00006'
2025-07-28 17:42:23 [INFO] - extrair_numero_precatorio_completo_da_pagina - Extraindo N° precatório para principal: 0023130-11.2010.8.26.0053
2025-07-28 17:42:23 [INFO] - extrair_numero_precatorio_completo_da_pagina - N° precatório não encontrado via span.unj-larger ou N° base divergente. Usar fallback do texto do link.
2025-07-28 17:42:23 [INFO] - processar_numero_autos - N° precatório formado via texto do link (fallback principal): 0023130-11.2010.8.26.0053 (06)
2025-07-28 17:42:23 [INFO] - obter_nome_cliente_do_precatorio - Buscando nome do cliente (Reqte)...
2025-07-28 17:42:23 [INFO] - obter_nome_cliente_do_precatorio - Nome do cliente: Helio Martins Lacerda
2025-07-28 17:42:23 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 17:42:23 [INFO] - processar_numero_autos - Precatório '0023130-11.2010.8.26.0053 (06)' status 'Finalizado' - pulando
2025-07-28 17:42:23 [INFO] - processar_numero_autos - Precatório rejeitado: 0023130-11.2010.8.26.0053 (06) - Cliente: Helio Martins Lacerda - Motivo: Status finalizado
2025-07-28 17:42:25 [INFO] - processar_numero_autos - Processando link Precatório 3/5: 'Precatório - 00007'
2025-07-28 17:42:27 [INFO] - extrair_numero_precatorio_completo_da_pagina - Extraindo N° precatório para principal: 0023130-11.2010.8.26.0053
2025-07-28 17:42:27 [INFO] - extrair_numero_precatorio_completo_da_pagina - N° precatório não encontrado via span.unj-larger ou N° base divergente. Usar fallback do texto do link.
2025-07-28 17:42:27 [INFO] - processar_numero_autos - N° precatório formado via texto do link (fallback principal): 0023130-11.2010.8.26.0053 (07)
2025-07-28 17:42:27 [INFO] - obter_nome_cliente_do_precatorio - Buscando nome do cliente (Reqte)...
2025-07-28 17:42:27 [INFO] - obter_nome_cliente_do_precatorio - Nome do cliente: Angélica de Abreu Lacerda Combe
2025-07-28 17:42:27 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 17:42:27 [INFO] - processar_numero_autos - Precatório '0023130-11.2010.8.26.0053 (07)' status 'Finalizado' - pulando
2025-07-28 17:42:27 [INFO] - processar_numero_autos - Precatório rejeitado: 0023130-11.2010.8.26.0053 (07) - Cliente: Angélica de Abreu Lacerda Combe - Motivo: Status finalizado
2025-07-28 17:42:28 [INFO] - processar_numero_autos - Processando link Precatório 4/5: 'Precatório - 00008'
2025-07-28 17:42:30 [INFO] - extrair_numero_precatorio_completo_da_pagina - Extraindo N° precatório para principal: 0023130-11.2010.8.26.0053
2025-07-28 17:42:30 [INFO] - extrair_numero_precatorio_completo_da_pagina - N° precatório não encontrado via span.unj-larger ou N° base divergente. Usar fallback do texto do link.
2025-07-28 17:42:30 [INFO] - processar_numero_autos - N° precatório formado via texto do link (fallback principal): 0023130-11.2010.8.26.0053 (08)
2025-07-28 17:42:30 [INFO] - obter_nome_cliente_do_precatorio - Buscando nome do cliente (Reqte)...
2025-07-28 17:42:30 [INFO] - obter_nome_cliente_do_precatorio - Nome do cliente: Alexandre de Abreu Lacerda
2025-07-28 17:42:30 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 17:42:30 [INFO] - processar_numero_autos - Precatório '0023130-11.2010.8.26.0053 (08)' status 'Finalizado' - pulando
2025-07-28 17:42:30 [INFO] - processar_numero_autos - Precatório rejeitado: 0023130-11.2010.8.26.0053 (08) - Cliente: Alexandre de Abreu Lacerda - Motivo: Status finalizado
2025-07-28 17:42:32 [INFO] - processar_numero_autos - Processando link Precatório 5/5: 'Precatório - 00009'
2025-07-28 17:42:34 [INFO] - extrair_numero_precatorio_completo_da_pagina - Extraindo N° precatório para principal: 0023130-11.2010.8.26.0053
2025-07-28 17:42:34 [INFO] - extrair_numero_precatorio_completo_da_pagina - N° precatório não encontrado via span.unj-larger ou N° base divergente. Usar fallback do texto do link.
2025-07-28 17:42:34 [INFO] - processar_numero_autos - N° precatório formado via texto do link (fallback principal): 0023130-11.2010.8.26.0053 (09)
2025-07-28 17:42:34 [INFO] - obter_nome_cliente_do_precatorio - Buscando nome do cliente (Reqte)...
2025-07-28 17:42:34 [INFO] - obter_nome_cliente_do_precatorio - Nome do cliente: Fernando de Abreu Lacerda
2025-07-28 17:42:34 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 17:42:34 [INFO] - processar_numero_autos - Precatório '0023130-11.2010.8.26.0053 (09)' status 'Finalizado' - pulando
2025-07-28 17:42:34 [INFO] - processar_numero_autos - Precatório rejeitado: 0023130-11.2010.8.26.0053 (09) - Cliente: Fernando de Abreu Lacerda - Motivo: Status finalizado
2025-07-28 17:42:34 [INFO] - processar_numero_autos - FIM NÚMERO 192 (Tempo: 30.29s)
2025-07-28 17:42:35 [INFO] - processar_numero_autos - Processando número 193/11405: 0030422-81.2009.8.26.0053
2025-07-28 17:42:35 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:42:35 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 17:42:37 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 17:42:37 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:42:37 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:42:39 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:42:39 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:42:39 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:42:39 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:42:39 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:42:39 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:42:41 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:42:41 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:42:41 [INFO] - consultar_processo_principal - Consultando 0030422-81.2009.8.26.0053 (TJSP: 0030422812009 / Foro: 0053)
2025-07-28 17:42:43 [INFO] - consultar_processo_principal - Lista de resultados para 0030422-81.2009.8.26.0053.
2025-07-28 17:42:43 [INFO] - consultar_processo_principal - Link para 0030422-81.2009.8.26.0053 encontrado. Clicando...
2025-07-28 17:42:44 [INFO] - consultar_processo_principal - Detalhes de 0030422-81.2009.8.26.0053 carregados após clique.
2025-07-28 17:42:44 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 17:42:44 [INFO] - processar_numero_autos - Processo principal 0030422-81.2009.8.26.0053 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 17:42:44 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:42:44 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Amelia Sanches Daniel (lower: amelia sanches daniel)
2025-07-28 17:42:44 [INFO] - processar_numero_autos - Processo principal 0030422-81.2009.8.26.0053 sem partes proibidas
2025-07-28 17:42:45 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:42:46 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento
2025-07-28 17:42:46 [INFO] - processar_numero_autos - Processo principal 0030422-81.2009.8.26.0053 com Palavra Proibida em Movimentações - Continuando para precatórios (filtro removido)
2025-07-28 17:42:46 [INFO] - processar_numero_autos - Processo principal 0030422-81.2009.8.26.0053 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 17:42:46 [INFO] - processar_numero_autos - Links de precatório encontrados com seletor principal para 0030422-81.2009.8.26.0053.
2025-07-28 17:42:46 [INFO] - processar_numero_autos - 1 link(s) 'Precatório' para 0030422-81.2009.8.26.0053.
2025-07-28 17:42:48 [INFO] - processar_numero_autos - Processando link Precatório 1/1: 'Precatório - 00001'
2025-07-28 17:42:50 [INFO] - extrair_numero_precatorio_completo_da_pagina - Extraindo N° precatório para principal: 0030422-81.2009.8.26.0053
2025-07-28 17:42:50 [INFO] - extrair_numero_precatorio_completo_da_pagina - N° precatório não encontrado via span.unj-larger ou N° base divergente. Usar fallback do texto do link.
2025-07-28 17:42:50 [INFO] - processar_numero_autos - N° precatório formado via texto do link (fallback principal): 0030422-81.2009.8.26.0053 (01)
2025-07-28 17:42:50 [INFO] - obter_nome_cliente_do_precatorio - Buscando nome do cliente (Reqte)...
2025-07-28 17:42:50 [INFO] - obter_nome_cliente_do_precatorio - Nome do cliente: Amelia Sanches Daniel
2025-07-28 17:42:50 [INFO] - verificar_status_processo - Status: normal/em andamento.
2025-07-28 17:42:50 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:42:50 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Amelia Sanches Daniel (lower: amelia sanches daniel)
2025-07-28 17:42:51 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:42:52 [INFO] - verificar_palavras_proibidas_mov - Nenhuma palavra proibida.
2025-07-28 17:42:52 [INFO] - processar_numero_autos - Precatório '0030422-81.2009.8.26.0053 (01)' validado - tentando baixar ofício
2025-07-28 17:42:52 [INFO] - encontrar_oficio_requisitorio - Procurando 'Ofício Requisitório-Precatório Expedido'...
2025-07-28 17:42:53 [INFO] - encontrar_oficio_requisitorio - Link 'Ofício Requisitório-Precatório Expedido' encontrado!
2025-07-28 17:42:55 [INFO] - encontrar_oficio_requisitorio - Mudou para nova janela/aba. URL: https://esaj.tjsp.jus.br/pastadigital/abrirDocumentoEdt.do?nuProcesso=0030422-81.2009.8.26.0053&cdProcesso=1H000137Z0001&instanciaProcesso=pg&cdProcessoMaster=1H000137Z0000&cdForo=53&baseIndice=INDDS&nmAlias=PG5JMDS&tpOrigem=2&flOrigem=P&cdServico=190101&acessibilidade=false&ticket=6%2BMVo5lkhYgd7cFzN09bQMo7DbaRQP0ciU9v3jTQY9DeBxdKdyk%2FYfy%2FDhiHd%2BmJ13iWTHfUP3n1ZlVNjMNHz%2BOiCmnwD082Bhwt7VI69S2iUEcHmbHPc5dZDXQxN9dhSSa%2FaaSwdKVZgUo3VY5mVJXav8I0xIIxnkJKU8XBAhT1vZtkMsMoTCfZC2FQSIsdpu5I0oERzG8vZnF6zX%2B3tbWf0lgJ5KvdiRmS8I88YzUgGjXBWOcKra1PGlypZB9oTh9iQscDPddDS2TXZNz5czLm72Pep3dAK0DgAz9rGVLNHMpEZaJHRiQYETkAbmTR6CDVwtspJ%2FFaedoWNQ46Oclfp2X4UW2L7fJXqJ2T8%2BrXMPtB8Kbp3zmtNPUw6hjV3yonncHxGcWxpgB9Ph3MsUsLyVRsNc9YvtF7XUWJJcvyRhnn7%2B72IFZbQto6b%2Fs8jUzwhSknJMInBR9aYDYeiL9AgJjZopK8mcXkK1VXtp7E0JDobID6BKYAj6JpTuwdRWQcMkrBUGtNo7%2F1j9mXxoDrdQqT9zg%2FDrUnnO17l7g%3D
2025-07-28 17:42:55 [INFO] - atualizar_baixar_documento - Iniciando download avançado para: Oficio_Requisitorio_0030422-81_2009_8_26_0053_01.pdf
2025-07-28 17:42:55 [INFO] - atualizar_baixar_documento - Diretório de destino: c:\Users\<USER>\Documents\Augment\Bipre\TJSP_PDF_e2e\tjsp\downloads_completos
2025-07-28 17:42:55 [INFO] - atualizar_baixar_documento - URL atual: https://esaj.tjsp.jus.br/pastadigital/abrirDocumentoEdt.do?nuProcesso=0030422-81.2009.8.26.0053&cdProcesso=1H000137Z0001&instanciaProcesso=pg&cdProcessoMaster=1H000137Z0000&cdForo=53&baseIndice=INDDS&nmAlias=PG5JMDS&tpOrigem=2&flOrigem=P&cdServico=190101&acessibilidade=false&ticket=6%2BMVo5lkhYgd7cFzN09bQMo7DbaRQP0ciU9v3jTQY9DeBxdKdyk%2FYfy%2FDhiHd%2BmJ13iWTHfUP3n1ZlVNjMNHz%2BOiCmnwD082Bhwt7VI69S2iUEcHmbHPc5dZDXQxN9dhSSa%2FaaSwdKVZgUo3VY5mVJXav8I0xIIxnkJKU8XBAhT1vZtkMsMoTCfZC2FQSIsdpu5I0oERzG8vZnF6zX%2B3tbWf0lgJ5KvdiRmS8I88YzUgGjXBWOcKra1PGlypZB9oTh9iQscDPddDS2TXZNz5czLm72Pep3dAK0DgAz9rGVLNHMpEZaJHRiQYETkAbmTR6CDVwtspJ%2FFaedoWNQ46Oclfp2X4UW2L7fJXqJ2T8%2BrXMPtB8Kbp3zmtNPUw6hjV3yonncHxGcWxpgB9Ph3MsUsLyVRsNc9YvtF7XUWJJcvyRhnn7%2B72IFZbQto6b%2Fs8jUzwhSknJMInBR9aYDYeiL9AgJjZopK8mcXkK1VXtp7E0JDobID6BKYAj6JpTuwdRWQcMkrBUGtNo7%2F1j9mXxoDrdQqT9zg%2FDrUnnO17l7g%3D
2025-07-28 17:42:55 [INFO] - atualizar_baixar_documento - Página de documento PDF do TJSP detectada
2025-07-28 17:42:55 [INFO] - atualizar_baixar_documento - MÉTODO PRIORITÁRIO: Tentando localizar visualizador de PDF em iframes...
2025-07-28 17:42:55 [INFO] - atualizar_baixar_documento - Encontrados 1 iframes na página
2025-07-28 17:42:55 [INFO] - atualizar_baixar_documento - Tentando iframe 1... src='https://esaj.tjsp.jus.br/pastadigital/js/viewer/web/viewer.html?file=%2Fpastadigital%2FgetPDF.do%3FnuSeqRecurso%3D00001%26nuProcesso%3D0030422-81.2009...'
2025-07-28 17:42:55 [INFO] - atualizar_baixar_documento - Mudou para iframe 1
2025-07-28 17:42:56 [INFO] - atualizar_baixar_documento - Botão de download encontrado no iframe 1 com seletor ('id', 'download')
2025-07-28 17:42:57 [INFO] - atualizar_baixar_documento - Clique realizado no botão dentro do iframe
2025-07-28 17:43:01 [INFO] - atualizar_baixar_documento - Clique no botão de download realizado com sucesso, assumindo download iniciado
2025-07-28 17:43:01 [INFO] - identificar_novo_arquivo_download - Novo arquivo de download identificado: doc_38454731.pdf
2025-07-28 17:43:01 [INFO] - download_documento_unificado - Download bem-sucedido. Arquivo real: doc_38454731.pdf. Nome sugerido: Oficio_Requisitorio_0030422-81_2009_8_26_0053_01.pdf
2025-07-28 17:43:01 [INFO] - processar_numero_autos - Download para '0030422-81.2009.8.26.0053 (01)' CONCLUÍDO como 'doc_38454731.pdf'
2025-07-28 17:43:01 [INFO] - processar_numero_autos - FIM NÚMERO 193 (Tempo: 26.52s)
2025-07-28 17:43:02 [INFO] - processar_numero_autos - Processando número 194/11405: 0019706-33.2005.8.26.0506
2025-07-28 17:43:02 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:43:02 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 17:43:04 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 17:43:04 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:43:04 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:43:06 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:43:06 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:43:06 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:43:06 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:43:06 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:43:06 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:43:08 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:43:08 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:43:08 [INFO] - consultar_processo_principal - Consultando 0019706-33.2005.8.26.0506 (TJSP: 0019706332005 / Foro: 0506)
2025-07-28 17:43:10 [INFO] - consultar_processo_principal - Lista de resultados para 0019706-33.2005.8.26.0506.
2025-07-28 17:43:10 [INFO] - consultar_processo_principal - Link para 0019706-33.2005.8.26.0506 encontrado. Clicando...
2025-07-28 17:43:11 [INFO] - consultar_processo_principal - Detalhes de 0019706-33.2005.8.26.0506 carregados após clique.
2025-07-28 17:43:11 [INFO] - verificar_status_processo - Status 'finalizado' (texto da página).
2025-07-28 17:43:11 [INFO] - processar_numero_autos - Processo principal 0019706-33.2005.8.26.0506 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 17:43:11 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:43:11 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Rita Fernandes Leite (lower: rita fernandes leite)
2025-07-28 17:43:11 [INFO] - processar_numero_autos - Processo principal 0019706-33.2005.8.26.0506 sem partes proibidas
2025-07-28 17:43:12 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:43:14 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: cessão de crédito
2025-07-28 17:43:14 [INFO] - processar_numero_autos - Processo principal 0019706-33.2005.8.26.0506 com Palavra Proibida em Movimentações - Continuando para precatórios (filtro removido)
2025-07-28 17:43:14 [INFO] - processar_numero_autos - Processo principal 0019706-33.2005.8.26.0506 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 17:43:16 [INFO] - processar_numero_autos - Nenhum link de precatório (seletor principal) para 0019706-33.2005.8.26.0506. Tentando fallback...
2025-07-28 17:43:17 [INFO] - processar_numero_autos - Nenhum link 'Precatório' clicável para 0019706-33.2005.8.26.0506.
2025-07-28 17:43:18 [INFO] - processar_numero_autos - Processando número 195/11405: 0124919-92.2006.8.26.0053
2025-07-28 17:43:18 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:43:18 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 17:43:20 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 17:43:20 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:43:20 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:43:22 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:43:22 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:43:22 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:43:22 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:43:22 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:43:22 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:43:24 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:43:24 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:43:24 [INFO] - consultar_processo_principal - Consultando 0124919-92.2006.8.26.0053 (TJSP: 0124919922006 / Foro: 0053)
2025-07-28 17:43:26 [INFO] - consultar_processo_principal - Lista de resultados para 0124919-92.2006.8.26.0053.
2025-07-28 17:43:26 [INFO] - consultar_processo_principal - Link para 0124919-92.2006.8.26.0053 encontrado. Clicando...
2025-07-28 17:43:27 [INFO] - consultar_processo_principal - Detalhes de 0124919-92.2006.8.26.0053 carregados após clique.
2025-07-28 17:43:27 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 17:43:27 [INFO] - processar_numero_autos - Processo principal 0124919-92.2006.8.26.0053 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 17:43:27 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:43:27 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Angela Carvalho Cruz (lower: angela carvalho cruz)
2025-07-28 17:43:27 [INFO] - processar_numero_autos - Processo principal 0124919-92.2006.8.26.0053 sem partes proibidas
2025-07-28 17:43:28 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:43:29 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento eletrônico
2025-07-28 17:43:29 [INFO] - processar_numero_autos - Processo principal 0124919-92.2006.8.26.0053 com Palavra Proibida em Movimentações - Continuando para precatórios (filtro removido)
2025-07-28 17:43:29 [INFO] - processar_numero_autos - Processo principal 0124919-92.2006.8.26.0053 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 17:43:30 [INFO] - processar_numero_autos - Links de precatório encontrados com seletor principal para 0124919-92.2006.8.26.0053.
2025-07-28 17:43:30 [INFO] - processar_numero_autos - 1 link(s) 'Precatório' para 0124919-92.2006.8.26.0053.
2025-07-28 17:43:31 [INFO] - processar_numero_autos - Processando link Precatório 1/1: 'Precatório - 00001'
2025-07-28 17:43:33 [INFO] - extrair_numero_precatorio_completo_da_pagina - Extraindo N° precatório para principal: 0124919-92.2006.8.26.0053
2025-07-28 17:43:33 [INFO] - extrair_numero_precatorio_completo_da_pagina - N° precatório não encontrado via span.unj-larger ou N° base divergente. Usar fallback do texto do link.
2025-07-28 17:43:33 [INFO] - processar_numero_autos - N° precatório formado via texto do link (fallback principal): 0124919-92.2006.8.26.0053 (01)
2025-07-28 17:43:33 [INFO] - obter_nome_cliente_do_precatorio - Buscando nome do cliente (Reqte)...
2025-07-28 17:43:33 [INFO] - obter_nome_cliente_do_precatorio - Nome do cliente: Angela Carvalho Cruz
2025-07-28 17:43:33 [INFO] - verificar_status_processo - Status: normal/em andamento.
2025-07-28 17:43:33 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:43:33 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Angela Carvalho Cruz (lower: angela carvalho cruz)
2025-07-28 17:43:34 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:43:35 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento
2025-07-28 17:43:35 [INFO] - processar_numero_autos - Precatório '0124919-92.2006.8.26.0053 (01)' com Palavra Proibida - pulando
2025-07-28 17:43:35 [INFO] - processar_numero_autos - Precatório rejeitado: 0124919-92.2006.8.26.0053 (01) - Cliente: Angela Carvalho Cruz - Motivo: Palavras proibidas
2025-07-28 17:43:35 [INFO] - processar_numero_autos - FIM NÚMERO 195 (Tempo: 17.20s)
2025-07-28 17:43:36 [INFO] - processar_numero_autos - Processando número 196/11405: 0010385-72.2005.8.26.0053
2025-07-28 17:43:36 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:43:36 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 17:43:38 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 17:43:38 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:43:38 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:43:40 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:43:40 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:43:40 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:43:40 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:43:40 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:43:40 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:43:42 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:43:42 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:43:42 [INFO] - consultar_processo_principal - Consultando 0010385-72.2005.8.26.0053 (TJSP: 0010385722005 / Foro: 0053)
2025-07-28 17:43:44 [INFO] - consultar_processo_principal - Lista de resultados para 0010385-72.2005.8.26.0053.
2025-07-28 17:43:44 [INFO] - consultar_processo_principal - Link para 0010385-72.2005.8.26.0053 encontrado. Clicando...
2025-07-28 17:43:45 [INFO] - consultar_processo_principal - Detalhes de 0010385-72.2005.8.26.0053 carregados após clique.
2025-07-28 17:43:45 [INFO] - verificar_status_processo - Status 'finalizado' detectado: relação: 0156/2019 teor do ato: vistos. trata-se de processo físico com expedição de precatório digital em fase de aguardar o pagamento. a tramitação do precatório nesse setor de execuções contra a fazenda pública se dará no incidente digital nº 0010385-72.2005.8.26.0053/02, onde deverá prosseguir com peticionamento eletrônico obrigatório pelos advogados. mantenham-se os autos físicos em cartório pelo prazo de 30 dias para eventual consulta e após arquive-se lançando a movimentação 61614 - arquivado provisoriamente. quando da satisfação integral do débito deverá ser lançada a movimentação de baixa definitiva nesses autos. intime-se com urgência. advogados(s): newton borali (oab 53466/sp)
2025-07-28 17:43:45 [INFO] - processar_numero_autos - Processo principal 0010385-72.2005.8.26.0053 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 17:43:45 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:43:45 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Maria Eugênia Moreira Costa Ferreira (lower: maria eugênia moreira costa ferreira)
2025-07-28 17:43:45 [INFO] - processar_numero_autos - Processo principal 0010385-72.2005.8.26.0053 sem partes proibidas
2025-07-28 17:43:46 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:43:47 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento
2025-07-28 17:43:47 [INFO] - processar_numero_autos - Processo principal 0010385-72.2005.8.26.0053 com Palavra Proibida em Movimentações - Continuando para precatórios (filtro removido)
2025-07-28 17:43:47 [INFO] - processar_numero_autos - Processo principal 0010385-72.2005.8.26.0053 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 17:43:47 [INFO] - processar_numero_autos - Links de precatório encontrados com seletor principal para 0010385-72.2005.8.26.0053.
2025-07-28 17:43:47 [INFO] - processar_numero_autos - 1 link(s) 'Precatório' para 0010385-72.2005.8.26.0053.
2025-07-28 17:43:49 [INFO] - processar_numero_autos - Processando link Precatório 1/1: 'Precatório - 00002'
2025-07-28 17:43:51 [INFO] - extrair_numero_precatorio_completo_da_pagina - Extraindo N° precatório para principal: 0010385-72.2005.8.26.0053
2025-07-28 17:43:51 [INFO] - extrair_numero_precatorio_completo_da_pagina - N° precatório não encontrado via span.unj-larger ou N° base divergente. Usar fallback do texto do link.
2025-07-28 17:43:51 [INFO] - processar_numero_autos - N° precatório formado via texto do link (fallback principal): 0010385-72.2005.8.26.0053 (02)
2025-07-28 17:43:51 [INFO] - obter_nome_cliente_do_precatorio - Buscando nome do cliente (Reqte)...
2025-07-28 17:43:51 [INFO] - obter_nome_cliente_do_precatorio - Nome do cliente: Maria Moreira Costa Ferreira
2025-07-28 17:43:51 [INFO] - verificar_status_processo - Status: normal/em andamento.
2025-07-28 17:43:51 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:43:51 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Maria Moreira Costa Ferreira (lower: maria moreira costa ferreira)
2025-07-28 17:43:51 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:43:53 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento
2025-07-28 17:43:53 [INFO] - processar_numero_autos - Precatório '0010385-72.2005.8.26.0053 (02)' com Palavra Proibida - pulando
2025-07-28 17:43:53 [INFO] - processar_numero_autos - Precatório rejeitado: 0010385-72.2005.8.26.0053 (02) - Cliente: Maria Moreira Costa Ferreira - Motivo: Palavras proibidas
2025-07-28 17:43:53 [INFO] - processar_numero_autos - FIM NÚMERO 196 (Tempo: 17.23s)
2025-07-28 17:43:54 [INFO] - processar_numero_autos - Processando número 197/11405: 0114172-83.2006.8.26.0053
2025-07-28 17:43:54 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:43:54 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 17:43:56 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 17:43:56 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:43:56 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:43:58 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:43:58 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:43:58 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:43:58 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:43:58 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:43:58 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:44:00 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:44:00 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:44:00 [INFO] - consultar_processo_principal - Consultando 0114172-83.2006.8.26.0053 (TJSP: 0114172832006 / Foro: 0053)
2025-07-28 17:44:01 [INFO] - consultar_processo_principal - Lista de resultados para 0114172-83.2006.8.26.0053.
2025-07-28 17:44:02 [INFO] - consultar_processo_principal - Link para 0114172-83.2006.8.26.0053 encontrado. Clicando...
2025-07-28 17:44:03 [INFO] - consultar_processo_principal - Detalhes de 0114172-83.2006.8.26.0053 carregados após clique.
2025-07-28 17:44:03 [INFO] - verificar_status_processo - Status 'finalizado' detectado: relação: 0960/2018 teor do ato: vistos. trata-se de processo físico com expedição de precatório digital em fase de aguardar o pagamento. a tramitação do precatório nesse setor de execuções contra a fazenda pública se dará no incidente digital nº 0114172-83.2006.8.26.0053/01, onde deverá prosseguir com peticionamento eletrônico obrigatório pelos advogados. mantenham-se os autos físicos em cartório pelo prazo de 30 dias para eventual consulta e após arquive-se lançando a movimentação 61614 - arquivado provisoriamente. quando da satisfação integral do débito deverá ser lançada a movimentação de baixa definitiva nesses autos. intime-se com urgência. advogados(s): marcus vinicius armani alves (oab 223813/sp), felippo scolari neto (oab 75667/sp), celso luiz bini fernandes (oab 171105/sp), andré almeida garcia (oab 184018/sp), vera helena pereira vidigal bucci (oab 69243/sp)
2025-07-28 17:44:03 [INFO] - processar_numero_autos - Processo principal 0114172-83.2006.8.26.0053 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 17:44:03 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:44:03 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Rubens Ribeiro Rodrigues (lower: rubens ribeiro rodrigues)
2025-07-28 17:44:03 [INFO] - processar_numero_autos - Processo principal 0114172-83.2006.8.26.0053 sem partes proibidas
2025-07-28 17:44:04 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:44:05 [INFO] - verificar_palavras_proibidas_mov - Nenhuma palavra proibida.
2025-07-28 17:44:05 [INFO] - processar_numero_autos - Processo principal 0114172-83.2006.8.26.0053 sem palavras proibidas
2025-07-28 17:44:05 [INFO] - processar_numero_autos - Processo principal 0114172-83.2006.8.26.0053 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 17:44:05 [INFO] - processar_numero_autos - Links de precatório encontrados com seletor principal para 0114172-83.2006.8.26.0053.
2025-07-28 17:44:05 [INFO] - processar_numero_autos - 1 link(s) 'Precatório' para 0114172-83.2006.8.26.0053.
2025-07-28 17:44:07 [INFO] - processar_numero_autos - Processando link Precatório 1/1: 'Precatório - 00001'
2025-07-28 17:44:09 [INFO] - extrair_numero_precatorio_completo_da_pagina - Extraindo N° precatório para principal: 0114172-83.2006.8.26.0053
2025-07-28 17:44:09 [INFO] - extrair_numero_precatorio_completo_da_pagina - N° precatório não encontrado via span.unj-larger ou N° base divergente. Usar fallback do texto do link.
2025-07-28 17:44:09 [INFO] - processar_numero_autos - N° precatório formado via texto do link (fallback principal): 0114172-83.2006.8.26.0053 (01)
2025-07-28 17:44:09 [INFO] - obter_nome_cliente_do_precatorio - Buscando nome do cliente (Reqte)...
2025-07-28 17:44:09 [INFO] - obter_nome_cliente_do_precatorio - Nome do cliente: Scolari, Garcia e Oliveira Filho Sociedade de Advogados
2025-07-28 17:44:09 [INFO] - verificar_status_processo - Status: normal/em andamento.
2025-07-28 17:44:09 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:44:09 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Scolari, Garcia e Oliveira Filho Sociedade de Advogados (lower: scolari, garcia e oliveira filho sociedade de advogados)
2025-07-28 17:44:09 [INFO] - verificar_partes_processo - Parte proibida (PJ) encontrada no Reqte: 'sociedade' em 'Scolari, Garcia e Oliveira Filho Sociedade de Advogados'
2025-07-28 17:44:09 [INFO] - processar_numero_autos - Precatório '0114172-83.2006.8.26.0053 (01)' com Parte Proibida - pulando
2025-07-28 17:44:09 [INFO] - processar_numero_autos - Precatório rejeitado: 0114172-83.2006.8.26.0053 (01) - Cliente: Scolari, Garcia e Oliveira Filho Sociedade de Advogados - Motivo: Parte proibida (PJ)
2025-07-28 17:44:09 [INFO] - processar_numero_autos - FIM NÚMERO 197 (Tempo: 15.20s)
2025-07-28 17:44:09 [INFO] - processar_numero_autos - Processando número 198/11405: 0002540-74.2003.8.26.0597
2025-07-28 17:44:09 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:44:09 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 17:44:12 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 17:44:12 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:44:12 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:44:14 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:44:14 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:44:14 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:44:14 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:44:14 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:44:14 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:44:16 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:44:16 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:44:16 [INFO] - consultar_processo_principal - Consultando 0002540-74.2003.8.26.0597 (TJSP: 0002540742003 / Foro: 0597)
2025-07-28 17:44:17 [INFO] - consultar_processo_principal - Lista de resultados para 0002540-74.2003.8.26.0597.
2025-07-28 17:44:17 [INFO] - consultar_processo_principal - Link para 0002540-74.2003.8.26.0597 encontrado. Clicando...
2025-07-28 17:44:19 [INFO] - consultar_processo_principal - Detalhes de 0002540-74.2003.8.26.0597 carregados após clique.
2025-07-28 17:44:19 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 17:44:19 [INFO] - processar_numero_autos - Processo principal 0002540-74.2003.8.26.0597 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 17:44:19 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:44:19 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Eliza Soares de Castro (lower: eliza soares de castro)
2025-07-28 17:44:19 [INFO] - processar_numero_autos - Processo principal 0002540-74.2003.8.26.0597 sem partes proibidas
2025-07-28 17:44:19 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:44:21 [INFO] - verificar_palavras_proibidas_mov - Nenhuma palavra proibida.
2025-07-28 17:44:21 [INFO] - processar_numero_autos - Processo principal 0002540-74.2003.8.26.0597 sem palavras proibidas
2025-07-28 17:44:21 [INFO] - processar_numero_autos - Processo principal 0002540-74.2003.8.26.0597 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 17:44:23 [INFO] - processar_numero_autos - Nenhum link de precatório (seletor principal) para 0002540-74.2003.8.26.0597. Tentando fallback...
2025-07-28 17:44:25 [INFO] - processar_numero_autos - Nenhum link 'Precatório' clicável para 0002540-74.2003.8.26.0597.
2025-07-28 17:44:25 [INFO] - processar_numero_autos - Processando número 199/11405: 0000259-97.2010.8.26.0372
2025-07-28 17:44:25 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:44:25 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 17:44:27 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 17:44:27 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:44:27 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:44:29 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:44:29 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:44:29 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:44:29 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:44:29 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:44:29 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:44:31 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:44:31 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:44:31 [INFO] - consultar_processo_principal - Consultando 0000259-97.2010.8.26.0372 (TJSP: 0000259972010 / Foro: 0372)
2025-07-28 17:44:33 [INFO] - consultar_processo_principal - Detalhes de 0000259-97.2010.8.26.0372 carregados diretamente.
2025-07-28 17:44:33 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 17:44:33 [INFO] - processar_numero_autos - Processo principal 0000259-97.2010.8.26.0372 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 17:44:33 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:44:33 [ERROR] - verificar_partes_processo - Reqte não localizado para verificação de partes.
2025-07-28 17:44:33 [INFO] - processar_numero_autos - Processo principal 0000259-97.2010.8.26.0372 sem partes proibidas
2025-07-28 17:44:34 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:44:35 [INFO] - verificar_palavras_proibidas_mov - Nenhuma palavra proibida.
2025-07-28 17:44:35 [INFO] - processar_numero_autos - Processo principal 0000259-97.2010.8.26.0372 sem palavras proibidas
2025-07-28 17:44:35 [INFO] - processar_numero_autos - Processo principal 0000259-97.2010.8.26.0372 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 17:44:37 [INFO] - processar_numero_autos - Nenhum link de precatório (seletor principal) para 0000259-97.2010.8.26.0372. Tentando fallback...
2025-07-28 17:44:39 [INFO] - processar_numero_autos - Nenhum link 'Precatório' clicável para 0000259-97.2010.8.26.0372.
2025-07-28 17:44:40 [INFO] - verificar_sessao_ativa - Verificando se a sessão ainda está ativa
2025-07-28 17:44:40 [INFO] - verificar_sessao_ativa - Navegando para página de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 17:44:43 [INFO] - verificar_sessao_ativa - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:44:43 [INFO] - _detectar_sessao_expirada - Nome de usuário válido detectado: 'DENIS HENRIQUE SOUSA OLIVEIRA' - sessão ativa
2025-07-28 17:44:43 [INFO] - verificar_sessao_ativa - Sessão ativa confirmada - Usuário: DENIS HENRIQUE SOUSA OLIVEIRA
2025-07-28 17:44:43 [INFO] - processar_numero_autos - Processando número 200/11405: 0022488-48.2004.8.26.0053
2025-07-28 17:44:43 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:44:43 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:44:43 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:44:43 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:44:45 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:44:45 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:44:45 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:44:45 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:44:45 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:44:45 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:44:47 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:44:47 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:44:47 [INFO] - consultar_processo_principal - Consultando 0022488-48.2004.8.26.0053 (TJSP: 0022488482004 / Foro: 0053)
2025-07-28 17:44:48 [INFO] - consultar_processo_principal - Lista de resultados para 0022488-48.2004.8.26.0053.
2025-07-28 17:44:48 [INFO] - consultar_processo_principal - Link para 0022488-48.2004.8.26.0053 encontrado. Clicando...
2025-07-28 17:44:50 [INFO] - consultar_processo_principal - Detalhes de 0022488-48.2004.8.26.0053 carregados após clique.
2025-07-28 17:44:50 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 17:44:50 [INFO] - processar_numero_autos - Processo principal 0022488-48.2004.8.26.0053 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 17:44:50 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:44:50 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Joaquim Reis Laranjeira Neto (lower: joaquim reis laranjeira neto)
2025-07-28 17:44:50 [INFO] - processar_numero_autos - Processo principal 0022488-48.2004.8.26.0053 sem partes proibidas
2025-07-28 17:44:51 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:44:52 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento
2025-07-28 17:44:52 [INFO] - processar_numero_autos - Processo principal 0022488-48.2004.8.26.0053 com Palavra Proibida em Movimentações - Continuando para precatórios (filtro removido)
2025-07-28 17:44:52 [INFO] - processar_numero_autos - Processo principal 0022488-48.2004.8.26.0053 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 17:44:52 [INFO] - processar_numero_autos - Links de precatório encontrados com seletor principal para 0022488-48.2004.8.26.0053.
2025-07-28 17:44:52 [INFO] - processar_numero_autos - 1 link(s) 'Precatório' para 0022488-48.2004.8.26.0053.
2025-07-28 17:44:54 [INFO] - processar_numero_autos - Processando link Precatório 1/1: 'Precatório - 00001'
2025-07-28 17:44:56 [INFO] - extrair_numero_precatorio_completo_da_pagina - Extraindo N° precatório para principal: 0022488-48.2004.8.26.0053
2025-07-28 17:44:56 [INFO] - extrair_numero_precatorio_completo_da_pagina - N° precatório não encontrado via span.unj-larger ou N° base divergente. Usar fallback do texto do link.
2025-07-28 17:44:56 [INFO] - processar_numero_autos - N° precatório formado via texto do link (fallback principal): 0022488-48.2004.8.26.0053 (01)
2025-07-28 17:44:56 [INFO] - obter_nome_cliente_do_precatorio - Buscando nome do cliente (Reqte)...
2025-07-28 17:44:56 [INFO] - obter_nome_cliente_do_precatorio - Nome do cliente: Evelina Farelli Laranjeira
2025-07-28 17:44:56 [INFO] - verificar_status_processo - Status: normal/em andamento.
2025-07-28 17:44:56 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:44:56 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Evelina Farelli Laranjeira (lower: evelina farelli laranjeira)
2025-07-28 17:44:56 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:44:58 [INFO] - verificar_palavras_proibidas_mov - Nenhuma palavra proibida.
2025-07-28 17:44:58 [INFO] - processar_numero_autos - Precatório '0022488-48.2004.8.26.0053 (01)' validado - tentando baixar ofício
2025-07-28 17:44:58 [INFO] - encontrar_oficio_requisitorio - Procurando 'Ofício Requisitório-Precatório Expedido'...
2025-07-28 17:44:59 [INFO] - encontrar_oficio_requisitorio - Link 'Ofício Requisitório-Precatório Expedido' encontrado!
2025-07-28 17:45:00 [INFO] - encontrar_oficio_requisitorio - Mudou para nova janela/aba. URL: https://esaj.tjsp.jus.br/pastadigital/abrirDocumentoEdt.do?nuProcesso=0022488-48.2004.8.26.0053&cdProcesso=1HZX555NC0001&instanciaProcesso=pg&cdProcessoMaster=1HZX555NC0000&cdForo=53&baseIndice=INDDS&nmAlias=PG5JMDS&tpOrigem=2&flOrigem=P&cdServico=190101&acessibilidade=false&ticket=6%2BMVo5lkhYgd7cFzN09bQMo7DbaRQP0ciU9v3jTQY9DeBxdKdyk%2FYfy%2FDhiHd%2BmJK2KOUgFPzE%2BXW1QcbuUoxOOiCmnwD082Bhwt7VI69S2iUEcHmbHPc5dZDXQxN9dhSSa%2FaaSwdKVZgUo3VY5mVJXav8I0xIIxnkJKU8XBAhT1vZtkMsMoTCfZC2FQSIsdpu5I0oERzG8vZnF6zX%2B3tbWf0lgJ5KvdiRmS8I88YzUgGjXBWOcKra1PGlypZB9oTh9iQscDPddDS2TXZNz5czLm72Pep3dAK0DgAz9rGVLNHMpEZaJHRiQYETkAbmTR6CDVwtspJ%2FFaedoWNQ46Oclfp2X4UW2L7fJXqJ2T8%2BrXMPtB8Kbp3zmtNPUw6hjV3yonncHxGcWxpgB9Ph3MsUsLyVRsNc9YvtF7XUWJJcvyRhnn7%2B72IFZbQto6b%2Fs8jUzwhSknJMInBR9aYDYeiMLGC0NTDn4%2BMComDwy4Q8lsfXE6btrhJQ8uL7kRCCjHw8jOSZrmWp4rIKg1ggj3RYzET43WH0E%2FrF2jkd2uMuA%3D
2025-07-28 17:45:00 [INFO] - atualizar_baixar_documento - Iniciando download avançado para: Oficio_Requisitorio_0022488-48_2004_8_26_0053_01.pdf
2025-07-28 17:45:00 [INFO] - atualizar_baixar_documento - Diretório de destino: c:\Users\<USER>\Documents\Augment\Bipre\TJSP_PDF_e2e\tjsp\downloads_completos
2025-07-28 17:45:00 [INFO] - atualizar_baixar_documento - URL atual: https://esaj.tjsp.jus.br/pastadigital/abrirDocumentoEdt.do?nuProcesso=0022488-48.2004.8.26.0053&cdProcesso=1HZX555NC0001&instanciaProcesso=pg&cdProcessoMaster=1HZX555NC0000&cdForo=53&baseIndice=INDDS&nmAlias=PG5JMDS&tpOrigem=2&flOrigem=P&cdServico=190101&acessibilidade=false&ticket=6%2BMVo5lkhYgd7cFzN09bQMo7DbaRQP0ciU9v3jTQY9DeBxdKdyk%2FYfy%2FDhiHd%2BmJK2KOUgFPzE%2BXW1QcbuUoxOOiCmnwD082Bhwt7VI69S2iUEcHmbHPc5dZDXQxN9dhSSa%2FaaSwdKVZgUo3VY5mVJXav8I0xIIxnkJKU8XBAhT1vZtkMsMoTCfZC2FQSIsdpu5I0oERzG8vZnF6zX%2B3tbWf0lgJ5KvdiRmS8I88YzUgGjXBWOcKra1PGlypZB9oTh9iQscDPddDS2TXZNz5czLm72Pep3dAK0DgAz9rGVLNHMpEZaJHRiQYETkAbmTR6CDVwtspJ%2FFaedoWNQ46Oclfp2X4UW2L7fJXqJ2T8%2BrXMPtB8Kbp3zmtNPUw6hjV3yonncHxGcWxpgB9Ph3MsUsLyVRsNc9YvtF7XUWJJcvyRhnn7%2B72IFZbQto6b%2Fs8jUzwhSknJMInBR9aYDYeiMLGC0NTDn4%2BMComDwy4Q8lsfXE6btrhJQ8uL7kRCCjHw8jOSZrmWp4rIKg1ggj3RYzET43WH0E%2FrF2jkd2uMuA%3D
2025-07-28 17:45:00 [INFO] - atualizar_baixar_documento - Página de documento PDF do TJSP detectada
2025-07-28 17:45:00 [INFO] - atualizar_baixar_documento - MÉTODO PRIORITÁRIO: Tentando localizar visualizador de PDF em iframes...
2025-07-28 17:45:00 [INFO] - atualizar_baixar_documento - Encontrados 1 iframes na página
2025-07-28 17:45:00 [INFO] - atualizar_baixar_documento - Tentando iframe 1... src='https://esaj.tjsp.jus.br/pastadigital/js/viewer/web/viewer.html?file=%2Fpastadigital%2FgetPDF.do%3FnuSeqRecurso%3D00001%26nuProcesso%3D0022488-48.2004...'
2025-07-28 17:45:00 [INFO] - atualizar_baixar_documento - Mudou para iframe 1
2025-07-28 17:45:02 [INFO] - atualizar_baixar_documento - Botão de download encontrado no iframe 1 com seletor ('id', 'download')
2025-07-28 17:45:03 [INFO] - atualizar_baixar_documento - Clique realizado no botão dentro do iframe
2025-07-28 17:45:07 [INFO] - atualizar_baixar_documento - Clique no botão de download realizado com sucesso, assumindo download iniciado
2025-07-28 17:45:07 [INFO] - identificar_novo_arquivo_download - Novo arquivo de download identificado: doc_38492636.pdf
2025-07-28 17:45:07 [INFO] - download_documento_unificado - Download bem-sucedido. Arquivo real: doc_38492636.pdf. Nome sugerido: Oficio_Requisitorio_0022488-48_2004_8_26_0053_01.pdf
2025-07-28 17:45:07 [INFO] - processar_numero_autos - Download para '0022488-48.2004.8.26.0053 (01)' CONCLUÍDO como 'doc_38492636.pdf'
2025-07-28 17:45:07 [INFO] - processar_numero_autos - FIM NÚMERO 200 (Tempo: 24.36s)
2025-07-28 17:45:08 [INFO] - processar_numero_autos - Processando número 201/11405: 0012523-80.2003.8.26.0053
2025-07-28 17:45:08 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:45:08 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 17:45:10 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 17:45:10 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:45:10 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:45:12 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:45:12 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:45:12 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:45:12 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:45:12 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:45:12 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:45:14 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:45:14 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:45:14 [INFO] - consultar_processo_principal - Consultando 0012523-80.2003.8.26.0053 (TJSP: 0012523802003 / Foro: 0053)
2025-07-28 17:45:15 [INFO] - consultar_processo_principal - Lista de resultados para 0012523-80.2003.8.26.0053.
2025-07-28 17:45:15 [INFO] - consultar_processo_principal - Link para 0012523-80.2003.8.26.0053 encontrado. Clicando...
2025-07-28 17:45:17 [INFO] - consultar_processo_principal - Detalhes de 0012523-80.2003.8.26.0053 carregados após clique.
2025-07-28 17:45:17 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 17:45:17 [INFO] - processar_numero_autos - Processo principal 0012523-80.2003.8.26.0053 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 17:45:17 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:45:17 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Luzimar de Oliveira (lower: luzimar de oliveira)
2025-07-28 17:45:17 [INFO] - processar_numero_autos - Processo principal 0012523-80.2003.8.26.0053 sem partes proibidas
2025-07-28 17:45:18 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:45:19 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento
2025-07-28 17:45:19 [INFO] - processar_numero_autos - Processo principal 0012523-80.2003.8.26.0053 com Palavra Proibida em Movimentações - Continuando para precatórios (filtro removido)
2025-07-28 17:45:19 [INFO] - processar_numero_autos - Processo principal 0012523-80.2003.8.26.0053 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 17:45:19 [INFO] - processar_numero_autos - Links de precatório encontrados com seletor principal para 0012523-80.2003.8.26.0053.
2025-07-28 17:45:19 [INFO] - processar_numero_autos - 1 link(s) 'Precatório' para 0012523-80.2003.8.26.0053.
2025-07-28 17:45:21 [INFO] - processar_numero_autos - Processando link Precatório 1/1: 'Precatório - 00002'
2025-07-28 17:45:23 [INFO] - extrair_numero_precatorio_completo_da_pagina - Extraindo N° precatório para principal: 0012523-80.2003.8.26.0053
2025-07-28 17:45:23 [INFO] - extrair_numero_precatorio_completo_da_pagina - N° precatório não encontrado via span.unj-larger ou N° base divergente. Usar fallback do texto do link.
2025-07-28 17:45:23 [INFO] - processar_numero_autos - N° precatório formado via texto do link (fallback principal): 0012523-80.2003.8.26.0053 (02)
2025-07-28 17:45:23 [INFO] - obter_nome_cliente_do_precatorio - Buscando nome do cliente (Reqte)...
2025-07-28 17:45:23 [INFO] - obter_nome_cliente_do_precatorio - Nome do cliente: Luzimar de Oliveira
2025-07-28 17:45:23 [INFO] - verificar_status_processo - Status: normal/em andamento.
2025-07-28 17:45:23 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:45:23 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Luzimar de Oliveira (lower: luzimar de oliveira)
2025-07-28 17:45:23 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:45:25 [INFO] - verificar_palavras_proibidas_mov - Nenhuma palavra proibida.
2025-07-28 17:45:25 [INFO] - processar_numero_autos - Precatório '0012523-80.2003.8.26.0053 (02)' validado - tentando baixar ofício
2025-07-28 17:45:25 [INFO] - encontrar_oficio_requisitorio - Procurando 'Ofício Requisitório-Precatório Expedido'...
2025-07-28 17:45:26 [INFO] - encontrar_oficio_requisitorio - Link 'Ofício Requisitório-Precatório Expedido' encontrado!
2025-07-28 17:45:28 [INFO] - encontrar_oficio_requisitorio - Mudou para nova janela/aba. URL: https://esaj.tjsp.jus.br/pastadigital/abrirDocumentoEdt.do?nuProcesso=0012523-80.2003.8.26.0053&cdProcesso=1HZX4JICR0002&instanciaProcesso=pg&cdProcessoMaster=1HZX4JICR0000&cdForo=53&baseIndice=INDDS&nmAlias=PG5JMDS&tpOrigem=2&flOrigem=P&cdServico=190101&acessibilidade=false&ticket=6%2BMVo5lkhYgd7cFzN09bQMo7DbaRQP0ciU9v3jTQY9DeBxdKdyk%2FYfy%2FDhiHd%2BmJkNhQnj2cZgJYatRMaU9HG%2BOiCmnwD082Bhwt7VI69S2iUEcHmbHPc5dZDXQxN9dhSSa%2FaaSwdKVZgUo3VY5mVJXav8I0xIIxnkJKU8XBAhT1vZtkMsMoTCfZC2FQSIsdpu5I0oERzG8vZnF6zX%2B3tbWf0lgJ5KvdiRmS8I88YzUgGjXBWOcKra1PGlypZB9oTh9iQscDPddDS2TXZNz5czLm72Pep3dAK0DgAz9rGVLNHMpEZaJHRiQYETkAbmTR6CDVwtspJ%2FFaedoWNQ46Oclfp2X4UW2L7fJXqJ2T8%2BrXMPtB8Kbp3zmtNPUw6hjV3yonncHxGcWxpgB9Ph3MsUsLyVRsNc9YvtF7XUWJJcvyRhnn7%2B72IFZbQto6b%2Fs8jUzwhSknJMInBR9aYDYeiEUMpQ%2BJ3PnZc2K9qVL3zwO2P6KC1oOuo4r%2BByt9XoOve%2Fh%2F5VDuQ6J4msBb31luUYDrdQqT9zg%2FDrUnnO17l7g%3D
2025-07-28 17:45:28 [INFO] - atualizar_baixar_documento - Iniciando download avançado para: Oficio_Requisitorio_0012523-80_2003_8_26_0053_02.pdf
2025-07-28 17:45:28 [INFO] - atualizar_baixar_documento - Diretório de destino: c:\Users\<USER>\Documents\Augment\Bipre\TJSP_PDF_e2e\tjsp\downloads_completos
2025-07-28 17:45:28 [INFO] - atualizar_baixar_documento - URL atual: https://esaj.tjsp.jus.br/pastadigital/abrirDocumentoEdt.do?nuProcesso=0012523-80.2003.8.26.0053&cdProcesso=1HZX4JICR0002&instanciaProcesso=pg&cdProcessoMaster=1HZX4JICR0000&cdForo=53&baseIndice=INDDS&nmAlias=PG5JMDS&tpOrigem=2&flOrigem=P&cdServico=190101&acessibilidade=false&ticket=6%2BMVo5lkhYgd7cFzN09bQMo7DbaRQP0ciU9v3jTQY9DeBxdKdyk%2FYfy%2FDhiHd%2BmJkNhQnj2cZgJYatRMaU9HG%2BOiCmnwD082Bhwt7VI69S2iUEcHmbHPc5dZDXQxN9dhSSa%2FaaSwdKVZgUo3VY5mVJXav8I0xIIxnkJKU8XBAhT1vZtkMsMoTCfZC2FQSIsdpu5I0oERzG8vZnF6zX%2B3tbWf0lgJ5KvdiRmS8I88YzUgGjXBWOcKra1PGlypZB9oTh9iQscDPddDS2TXZNz5czLm72Pep3dAK0DgAz9rGVLNHMpEZaJHRiQYETkAbmTR6CDVwtspJ%2FFaedoWNQ46Oclfp2X4UW2L7fJXqJ2T8%2BrXMPtB8Kbp3zmtNPUw6hjV3yonncHxGcWxpgB9Ph3MsUsLyVRsNc9YvtF7XUWJJcvyRhnn7%2B72IFZbQto6b%2Fs8jUzwhSknJMInBR9aYDYeiEUMpQ%2BJ3PnZc2K9qVL3zwO2P6KC1oOuo4r%2BByt9XoOve%2Fh%2F5VDuQ6J4msBb31luUYDrdQqT9zg%2FDrUnnO17l7g%3D
2025-07-28 17:45:28 [INFO] - atualizar_baixar_documento - Página de documento PDF do TJSP detectada
2025-07-28 17:45:28 [INFO] - atualizar_baixar_documento - MÉTODO PRIORITÁRIO: Tentando localizar visualizador de PDF em iframes...
2025-07-28 17:45:28 [INFO] - atualizar_baixar_documento - Encontrados 1 iframes na página
2025-07-28 17:45:28 [INFO] - atualizar_baixar_documento - Tentando iframe 1... src='https://esaj.tjsp.jus.br/pastadigital/js/viewer/web/viewer.html?file=%2Fpastadigital%2FgetPDF.do%3FnuSeqRecurso%3D00002%26nuProcesso%3D0012523-80.2003...'
2025-07-28 17:45:28 [INFO] - atualizar_baixar_documento - Mudou para iframe 1
2025-07-28 17:45:29 [INFO] - atualizar_baixar_documento - Botão de download encontrado no iframe 1 com seletor ('id', 'download')
2025-07-28 17:45:30 [INFO] - atualizar_baixar_documento - Clique realizado no botão dentro do iframe
2025-07-28 17:45:34 [INFO] - atualizar_baixar_documento - Clique no botão de download realizado com sucesso, assumindo download iniciado
2025-07-28 17:45:34 [INFO] - identificar_novo_arquivo_download - Novo arquivo de download identificado: doc_38491203.pdf
2025-07-28 17:45:34 [INFO] - download_documento_unificado - Download bem-sucedido. Arquivo real: doc_38491203.pdf. Nome sugerido: Oficio_Requisitorio_0012523-80_2003_8_26_0053_02.pdf
2025-07-28 17:45:34 [INFO] - processar_numero_autos - Download para '0012523-80.2003.8.26.0053 (02)' CONCLUÍDO como 'doc_38491203.pdf'
2025-07-28 17:45:34 [INFO] - processar_numero_autos - FIM NÚMERO 201 (Tempo: 26.46s)
2025-07-28 17:45:35 [INFO] - processar_numero_autos - Processando número 202/11405: 0024087-22.2004.8.26.0053
2025-07-28 17:45:35 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:45:35 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 17:45:37 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 17:45:37 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:45:37 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:45:39 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:45:39 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:45:39 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:45:39 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:45:39 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:45:39 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:45:41 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:45:41 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:45:41 [INFO] - consultar_processo_principal - Consultando 0024087-22.2004.8.26.0053 (TJSP: 0024087222004 / Foro: 0053)
2025-07-28 17:45:43 [INFO] - consultar_processo_principal - Lista de resultados para 0024087-22.2004.8.26.0053.
2025-07-28 17:45:43 [INFO] - consultar_processo_principal - Link para 0024087-22.2004.8.26.0053 encontrado. Clicando...
2025-07-28 17:45:44 [INFO] - consultar_processo_principal - Detalhes de 0024087-22.2004.8.26.0053 carregados após clique.
2025-07-28 17:45:44 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 17:45:44 [INFO] - processar_numero_autos - Processo principal 0024087-22.2004.8.26.0053 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 17:45:44 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:45:44 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Innocencia Paes Picini (lower: innocencia paes picini)
2025-07-28 17:45:44 [INFO] - processar_numero_autos - Processo principal 0024087-22.2004.8.26.0053 sem partes proibidas
2025-07-28 17:45:45 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:45:46 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento
2025-07-28 17:45:46 [INFO] - processar_numero_autos - Processo principal 0024087-22.2004.8.26.0053 com Palavra Proibida em Movimentações - Continuando para precatórios (filtro removido)
2025-07-28 17:45:46 [INFO] - processar_numero_autos - Processo principal 0024087-22.2004.8.26.0053 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 17:45:46 [INFO] - processar_numero_autos - Links de precatório encontrados com seletor principal para 0024087-22.2004.8.26.0053.
2025-07-28 17:45:46 [INFO] - processar_numero_autos - 1 link(s) 'Precatório' para 0024087-22.2004.8.26.0053.
2025-07-28 17:45:48 [INFO] - processar_numero_autos - Processando link Precatório 1/1: 'Precatório - 00001'
2025-07-28 17:45:50 [INFO] - extrair_numero_precatorio_completo_da_pagina - Extraindo N° precatório para principal: 0024087-22.2004.8.26.0053
2025-07-28 17:45:50 [INFO] - extrair_numero_precatorio_completo_da_pagina - N° precatório não encontrado via span.unj-larger ou N° base divergente. Usar fallback do texto do link.
2025-07-28 17:45:50 [INFO] - processar_numero_autos - N° precatório formado via texto do link (fallback principal): 0024087-22.2004.8.26.0053 (01)
2025-07-28 17:45:50 [INFO] - obter_nome_cliente_do_precatorio - Buscando nome do cliente (Reqte)...
2025-07-28 17:45:50 [INFO] - obter_nome_cliente_do_precatorio - Nome do cliente: Pedro Celli Neto
2025-07-28 17:45:50 [INFO] - verificar_status_processo - Status 'finalizado' detectado: arquivado
2025-07-28 17:45:50 [INFO] - processar_numero_autos - Precatório '0024087-22.2004.8.26.0053 (01)' status 'Finalizado' - pulando
2025-07-28 17:45:50 [INFO] - processar_numero_autos - Precatório rejeitado: 0024087-22.2004.8.26.0053 (01) - Cliente: Pedro Celli Neto - Motivo: Status finalizado
2025-07-28 17:45:50 [INFO] - processar_numero_autos - FIM NÚMERO 202 (Tempo: 15.32s)
2025-07-28 17:45:51 [INFO] - processar_numero_autos - Processando número 203/11405: 0015610-44.2003.8.26.0053
2025-07-28 17:45:51 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:45:51 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 17:45:53 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 17:45:53 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:45:53 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:45:55 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:45:55 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:45:55 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:45:55 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:45:55 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:45:55 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:45:57 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:45:57 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:45:57 [INFO] - consultar_processo_principal - Consultando 0015610-44.2003.8.26.0053 (TJSP: 0015610442003 / Foro: 0053)
2025-07-28 17:45:58 [INFO] - consultar_processo_principal - Lista de resultados para 0015610-44.2003.8.26.0053.
2025-07-28 17:45:58 [INFO] - consultar_processo_principal - Link para 0015610-44.2003.8.26.0053 encontrado. Clicando...
2025-07-28 17:46:00 [INFO] - consultar_processo_principal - Detalhes de 0015610-44.2003.8.26.0053 carregados após clique.
2025-07-28 17:46:00 [INFO] - verificar_status_processo - Status 'finalizado' (texto da página).
2025-07-28 17:46:00 [INFO] - processar_numero_autos - Processo principal 0015610-44.2003.8.26.0053 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 17:46:00 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:46:00 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Arthur Francisco (falecido) (lower: arthur francisco (falecido))
2025-07-28 17:46:00 [INFO] - processar_numero_autos - Processo principal 0015610-44.2003.8.26.0053 sem partes proibidas
2025-07-28 17:46:01 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:46:02 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento
2025-07-28 17:46:02 [INFO] - processar_numero_autos - Processo principal 0015610-44.2003.8.26.0053 com Palavra Proibida em Movimentações - Continuando para precatórios (filtro removido)
2025-07-28 17:46:02 [INFO] - processar_numero_autos - Processo principal 0015610-44.2003.8.26.0053 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 17:46:02 [INFO] - processar_numero_autos - Links de precatório encontrados com seletor principal para 0015610-44.2003.8.26.0053.
2025-07-28 17:46:02 [INFO] - processar_numero_autos - 1 link(s) 'Precatório' para 0015610-44.2003.8.26.0053.
2025-07-28 17:46:04 [INFO] - processar_numero_autos - Processando link Precatório 1/1: 'Precatório - 00001'
2025-07-28 17:46:06 [INFO] - extrair_numero_precatorio_completo_da_pagina - Extraindo N° precatório para principal: 0015610-44.2003.8.26.0053
2025-07-28 17:46:06 [INFO] - extrair_numero_precatorio_completo_da_pagina - N° precatório não encontrado via span.unj-larger ou N° base divergente. Usar fallback do texto do link.
2025-07-28 17:46:06 [INFO] - processar_numero_autos - N° precatório formado via texto do link (fallback principal): 0015610-44.2003.8.26.0053 (01)
2025-07-28 17:46:06 [INFO] - obter_nome_cliente_do_precatorio - Buscando nome do cliente (Reqte)...
2025-07-28 17:46:06 [INFO] - obter_nome_cliente_do_precatorio - Nome do cliente: Arthur Franciscoo
2025-07-28 17:46:06 [INFO] - verificar_status_processo - Status: normal/em andamento.
2025-07-28 17:46:06 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:46:06 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Arthur Franciscoo (lower: arthur franciscoo)
2025-07-28 17:46:06 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:46:08 [INFO] - verificar_palavras_proibidas_mov - Nenhuma palavra proibida.
2025-07-28 17:46:08 [INFO] - processar_numero_autos - Precatório '0015610-44.2003.8.26.0053 (01)' validado - tentando baixar ofício
2025-07-28 17:46:08 [INFO] - encontrar_oficio_requisitorio - Procurando 'Ofício Requisitório-Precatório Expedido'...
2025-07-28 17:46:09 [INFO] - encontrar_oficio_requisitorio - Link 'Ofício Requisitório-Precatório Expedido' encontrado!
2025-07-28 17:46:11 [INFO] - encontrar_oficio_requisitorio - Mudou para nova janela/aba. URL: https://esaj.tjsp.jus.br/pastadigital/abrirDocumentoEdt.do?nuProcesso=0015610-44.2003.8.26.0053&cdProcesso=1HZX4JKQI0001&instanciaProcesso=pg&cdProcessoMaster=1HZX4JKQI0000&cdForo=53&baseIndice=INDDS&nmAlias=PG5JMDS&tpOrigem=2&flOrigem=P&cdServico=190101&acessibilidade=false&ticket=6%2BMVo5lkhYgd7cFzN09bQMo7DbaRQP0ciU9v3jTQY9DeBxdKdyk%2FYfy%2FDhiHd%2BmJwwjqF4T1xFNvnIakLE%2BE6OOiCmnwD082Bhwt7VI69S2iUEcHmbHPc5dZDXQxN9dhSSa%2FaaSwdKVZgUo3VY5mVJXav8I0xIIxnkJKU8XBAhT1vZtkMsMoTCfZC2FQSIsdpu5I0oERzG8vZnF6zX%2B3tbWf0lgJ5KvdiRmS8I88YzUgGjXBWOcKra1PGlypZB9oTh9iQscDPddDS2TXZNz5czLm72Pep3dAK0DgAz9rGVLNHMpEZaJHRiQYETkAbmTR6CDVwtspJ%2FFaedoWNQ46Oclfp2X4UW2L7fJXqJ2T8%2BrXMPtB8Kbp3zmtNPUw6hjV3yonncHxGcWxpgB9Ph3MsUsLyVRsNc9YvtF7XUWJJcvyRhnn7%2B72IFZbQto6b%2Fs8jUzwhSknJMInBR9aYDYeiGqwXvviSSP2Irf0OuAeIJiktlczQ8sYDEpHLF2CbOcZeZWoTJzq4EuoNHj6ts2XSpCI8f0EaCf3gj15Mx3rx%2FM%3D
2025-07-28 17:46:11 [INFO] - atualizar_baixar_documento - Iniciando download avançado para: Oficio_Requisitorio_0015610-44_2003_8_26_0053_01.pdf
2025-07-28 17:46:11 [INFO] - atualizar_baixar_documento - Diretório de destino: c:\Users\<USER>\Documents\Augment\Bipre\TJSP_PDF_e2e\tjsp\downloads_completos
2025-07-28 17:46:11 [INFO] - atualizar_baixar_documento - URL atual: https://esaj.tjsp.jus.br/pastadigital/abrirDocumentoEdt.do?nuProcesso=0015610-44.2003.8.26.0053&cdProcesso=1HZX4JKQI0001&instanciaProcesso=pg&cdProcessoMaster=1HZX4JKQI0000&cdForo=53&baseIndice=INDDS&nmAlias=PG5JMDS&tpOrigem=2&flOrigem=P&cdServico=190101&acessibilidade=false&ticket=6%2BMVo5lkhYgd7cFzN09bQMo7DbaRQP0ciU9v3jTQY9DeBxdKdyk%2FYfy%2FDhiHd%2BmJwwjqF4T1xFNvnIakLE%2BE6OOiCmnwD082Bhwt7VI69S2iUEcHmbHPc5dZDXQxN9dhSSa%2FaaSwdKVZgUo3VY5mVJXav8I0xIIxnkJKU8XBAhT1vZtkMsMoTCfZC2FQSIsdpu5I0oERzG8vZnF6zX%2B3tbWf0lgJ5KvdiRmS8I88YzUgGjXBWOcKra1PGlypZB9oTh9iQscDPddDS2TXZNz5czLm72Pep3dAK0DgAz9rGVLNHMpEZaJHRiQYETkAbmTR6CDVwtspJ%2FFaedoWNQ46Oclfp2X4UW2L7fJXqJ2T8%2BrXMPtB8Kbp3zmtNPUw6hjV3yonncHxGcWxpgB9Ph3MsUsLyVRsNc9YvtF7XUWJJcvyRhnn7%2B72IFZbQto6b%2Fs8jUzwhSknJMInBR9aYDYeiGqwXvviSSP2Irf0OuAeIJiktlczQ8sYDEpHLF2CbOcZeZWoTJzq4EuoNHj6ts2XSpCI8f0EaCf3gj15Mx3rx%2FM%3D
2025-07-28 17:46:11 [INFO] - atualizar_baixar_documento - Página de documento PDF do TJSP detectada
2025-07-28 17:46:11 [INFO] - atualizar_baixar_documento - MÉTODO PRIORITÁRIO: Tentando localizar visualizador de PDF em iframes...
2025-07-28 17:46:11 [INFO] - atualizar_baixar_documento - Encontrados 1 iframes na página
2025-07-28 17:46:11 [INFO] - atualizar_baixar_documento - Tentando iframe 1... src='https://esaj.tjsp.jus.br/pastadigital/js/viewer/web/viewer.html?file=%2Fpastadigital%2FgetPDF.do%3FnuSeqRecurso%3D00001%26nuProcesso%3D0015610-44.2003...'
2025-07-28 17:46:11 [INFO] - atualizar_baixar_documento - Mudou para iframe 1
2025-07-28 17:46:12 [INFO] - atualizar_baixar_documento - Botão de download encontrado no iframe 1 com seletor ('id', 'download')
2025-07-28 17:46:13 [INFO] - atualizar_baixar_documento - Clique realizado no botão dentro do iframe
2025-07-28 17:46:17 [INFO] - atualizar_baixar_documento - Clique no botão de download realizado com sucesso, assumindo download iniciado
2025-07-28 17:46:17 [INFO] - identificar_novo_arquivo_download - Novo arquivo de download identificado: doc_38626325.pdf
2025-07-28 17:46:17 [INFO] - download_documento_unificado - Download bem-sucedido. Arquivo real: doc_38626325.pdf. Nome sugerido: Oficio_Requisitorio_0015610-44_2003_8_26_0053_01.pdf
2025-07-28 17:46:17 [INFO] - processar_numero_autos - Download para '0015610-44.2003.8.26.0053 (01)' CONCLUÍDO como 'doc_38626325.pdf'
2025-07-28 17:46:17 [INFO] - processar_numero_autos - FIM NÚMERO 203 (Tempo: 26.54s)
2025-07-28 17:46:18 [INFO] - processar_numero_autos - Processando número 204/11405: 0000739-73.2005.8.26.0106
2025-07-28 17:46:18 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:46:18 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 17:46:20 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 17:46:20 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:46:20 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:46:22 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:46:22 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:46:22 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:46:22 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:46:22 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:46:22 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:46:24 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:46:24 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:46:24 [INFO] - consultar_processo_principal - Consultando 0000739-73.2005.8.26.0106 (TJSP: 0000739732005 / Foro: 0106)
2025-07-28 17:46:26 [INFO] - consultar_processo_principal - Lista de resultados para 0000739-73.2005.8.26.0106.
2025-07-28 17:46:26 [INFO] - consultar_processo_principal - Link para 0000739-73.2005.8.26.0106 encontrado. Clicando...
2025-07-28 17:46:27 [INFO] - consultar_processo_principal - Detalhes de 0000739-73.2005.8.26.0106 carregados após clique.
2025-07-28 17:46:27 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 17:46:27 [INFO] - processar_numero_autos - Processo principal 0000739-73.2005.8.26.0106 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 17:46:27 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:46:27 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Francisvaldo da Silva (lower: francisvaldo da silva)
2025-07-28 17:46:27 [INFO] - processar_numero_autos - Processo principal 0000739-73.2005.8.26.0106 sem partes proibidas
2025-07-28 17:46:28 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:46:29 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento
2025-07-28 17:46:29 [INFO] - processar_numero_autos - Processo principal 0000739-73.2005.8.26.0106 com Palavra Proibida em Movimentações - Continuando para precatórios (filtro removido)
2025-07-28 17:46:29 [INFO] - processar_numero_autos - Processo principal 0000739-73.2005.8.26.0106 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 17:46:31 [INFO] - processar_numero_autos - Nenhum link de precatório (seletor principal) para 0000739-73.2005.8.26.0106. Tentando fallback...
2025-07-28 17:46:33 [INFO] - processar_numero_autos - Nenhum link 'Precatório' clicável para 0000739-73.2005.8.26.0106.
2025-07-28 17:46:34 [INFO] - processar_numero_autos - Processando número 205/11405: 0001784-48.2003.8.26.0053
2025-07-28 17:46:34 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:46:34 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 17:46:36 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 17:46:36 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:46:36 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:46:38 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:46:38 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:46:38 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:46:38 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:46:38 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:46:38 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:46:40 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:46:40 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:46:40 [INFO] - consultar_processo_principal - Consultando 0001784-48.2003.8.26.0053 (TJSP: 0001784482003 / Foro: 0053)
2025-07-28 17:46:42 [INFO] - consultar_processo_principal - Lista de resultados para 0001784-48.2003.8.26.0053.
2025-07-28 17:46:42 [INFO] - consultar_processo_principal - Link para 0001784-48.2003.8.26.0053 encontrado. Clicando...
2025-07-28 17:46:43 [INFO] - consultar_processo_principal - Detalhes de 0001784-48.2003.8.26.0053 carregados após clique.
2025-07-28 17:46:43 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 17:46:43 [INFO] - processar_numero_autos - Processo principal 0001784-48.2003.8.26.0053 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 17:46:43 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:46:43 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Anézio Vasco de Lima (lower: anézio vasco de lima)
2025-07-28 17:46:43 [INFO] - processar_numero_autos - Processo principal 0001784-48.2003.8.26.0053 sem partes proibidas
2025-07-28 17:46:44 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:46:45 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento
2025-07-28 17:46:45 [INFO] - processar_numero_autos - Processo principal 0001784-48.2003.8.26.0053 com Palavra Proibida em Movimentações - Continuando para precatórios (filtro removido)
2025-07-28 17:46:45 [INFO] - processar_numero_autos - Processo principal 0001784-48.2003.8.26.0053 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 17:46:45 [INFO] - processar_numero_autos - Links de precatório encontrados com seletor principal para 0001784-48.2003.8.26.0053.
2025-07-28 17:46:45 [INFO] - processar_numero_autos - 1 link(s) 'Precatório' para 0001784-48.2003.8.26.0053.
2025-07-28 17:46:47 [INFO] - processar_numero_autos - Processando link Precatório 1/1: 'Precatório - 00003'
2025-07-28 17:46:49 [INFO] - extrair_numero_precatorio_completo_da_pagina - Extraindo N° precatório para principal: 0001784-48.2003.8.26.0053
2025-07-28 17:46:49 [INFO] - extrair_numero_precatorio_completo_da_pagina - N° precatório não encontrado via span.unj-larger ou N° base divergente. Usar fallback do texto do link.
2025-07-28 17:46:49 [INFO] - processar_numero_autos - N° precatório formado via texto do link (fallback principal): 0001784-48.2003.8.26.0053 (03)
2025-07-28 17:46:49 [INFO] - obter_nome_cliente_do_precatorio - Buscando nome do cliente (Reqte)...
2025-07-28 17:46:49 [INFO] - obter_nome_cliente_do_precatorio - Nome do cliente: Anézio Vasco de Lima
2025-07-28 17:46:49 [INFO] - verificar_status_processo - Status: normal/em andamento.
2025-07-28 17:46:49 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:46:49 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Anézio Vasco de Lima (lower: anézio vasco de lima)
2025-07-28 17:46:49 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:46:51 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: cessão de crédito
2025-07-28 17:46:51 [INFO] - processar_numero_autos - Precatório '0001784-48.2003.8.26.0053 (03)' com Palavra Proibida - pulando
2025-07-28 17:46:51 [INFO] - processar_numero_autos - Precatório rejeitado: 0001784-48.2003.8.26.0053 (03) - Cliente: Anézio Vasco de Lima - Motivo: Palavras proibidas
2025-07-28 17:46:51 [INFO] - processar_numero_autos - FIM NÚMERO 205 (Tempo: 17.17s)
2025-07-28 17:46:52 [INFO] - processar_numero_autos - Processando número 206/11405: 0008619-08.2010.8.26.0053
2025-07-28 17:46:52 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:46:52 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 17:46:54 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 17:46:54 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:46:54 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:46:56 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:46:56 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:46:56 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:46:56 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:46:56 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:46:56 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:46:58 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:46:58 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:46:58 [INFO] - consultar_processo_principal - Consultando 0008619-08.2010.8.26.0053 (TJSP: 0008619082010 / Foro: 0053)
2025-07-28 17:46:59 [INFO] - consultar_processo_principal - Lista de resultados para 0008619-08.2010.8.26.0053.
2025-07-28 17:46:59 [INFO] - consultar_processo_principal - Link para 0008619-08.2010.8.26.0053 encontrado. Clicando...
2025-07-28 17:47:01 [INFO] - consultar_processo_principal - Detalhes de 0008619-08.2010.8.26.0053 carregados após clique.
2025-07-28 17:47:01 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 17:47:01 [INFO] - processar_numero_autos - Processo principal 0008619-08.2010.8.26.0053 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 17:47:01 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:47:01 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Lionice Pereira dos Santos (lower: lionice pereira dos santos)
2025-07-28 17:47:01 [INFO] - processar_numero_autos - Processo principal 0008619-08.2010.8.26.0053 sem partes proibidas
2025-07-28 17:47:01 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:47:03 [INFO] - verificar_palavras_proibidas_mov - Nenhuma palavra proibida.
2025-07-28 17:47:03 [INFO] - processar_numero_autos - Processo principal 0008619-08.2010.8.26.0053 sem palavras proibidas
2025-07-28 17:47:03 [INFO] - processar_numero_autos - Processo principal 0008619-08.2010.8.26.0053 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 17:47:03 [INFO] - processar_numero_autos - Links de precatório encontrados com seletor principal para 0008619-08.2010.8.26.0053.
2025-07-28 17:47:03 [INFO] - processar_numero_autos - 1 link(s) 'Precatório' para 0008619-08.2010.8.26.0053.
2025-07-28 17:47:04 [INFO] - processar_numero_autos - Processando link Precatório 1/1: 'Precatório - 00001'
2025-07-28 17:47:06 [INFO] - extrair_numero_precatorio_completo_da_pagina - Extraindo N° precatório para principal: 0008619-08.2010.8.26.0053
2025-07-28 17:47:06 [INFO] - extrair_numero_precatorio_completo_da_pagina - N° precatório não encontrado via span.unj-larger ou N° base divergente. Usar fallback do texto do link.
2025-07-28 17:47:06 [INFO] - processar_numero_autos - N° precatório formado via texto do link (fallback principal): 0008619-08.2010.8.26.0053 (01)
2025-07-28 17:47:06 [INFO] - obter_nome_cliente_do_precatorio - Buscando nome do cliente (Reqte)...
2025-07-28 17:47:06 [INFO] - obter_nome_cliente_do_precatorio - Nome do cliente: Lionice Pereira dos Santos
2025-07-28 17:47:06 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 17:47:06 [INFO] - processar_numero_autos - Precatório '0008619-08.2010.8.26.0053 (01)' status 'Finalizado' - pulando
2025-07-28 17:47:06 [INFO] - processar_numero_autos - Precatório rejeitado: 0008619-08.2010.8.26.0053 (01) - Cliente: Lionice Pereira dos Santos - Motivo: Status finalizado
2025-07-28 17:47:07 [INFO] - processar_numero_autos - FIM NÚMERO 206 (Tempo: 15.08s)
2025-07-28 17:47:07 [INFO] - processar_numero_autos - Processando número 207/11405: 0033959-61.2004.8.26.0053
2025-07-28 17:47:07 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:47:07 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 17:47:09 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 17:47:09 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:47:09 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:47:11 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:47:11 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:47:11 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:47:11 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:47:11 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:47:11 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:47:13 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:47:13 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:47:13 [INFO] - consultar_processo_principal - Consultando 0033959-61.2004.8.26.0053 (TJSP: 0033959612004 / Foro: 0053)
2025-07-28 17:47:15 [INFO] - consultar_processo_principal - Lista de resultados para 0033959-61.2004.8.26.0053.
2025-07-28 17:47:15 [INFO] - consultar_processo_principal - Link para 0033959-61.2004.8.26.0053 encontrado. Clicando...
2025-07-28 17:47:17 [INFO] - consultar_processo_principal - Detalhes de 0033959-61.2004.8.26.0053 carregados após clique.
2025-07-28 17:47:17 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 17:47:17 [INFO] - processar_numero_autos - Processo principal 0033959-61.2004.8.26.0053 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 17:47:17 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:47:17 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Neiva Terezinha Bignardi Scalon Martins (lower: neiva terezinha bignardi scalon martins)
2025-07-28 17:47:17 [INFO] - processar_numero_autos - Processo principal 0033959-61.2004.8.26.0053 sem partes proibidas
2025-07-28 17:47:17 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:47:19 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento
2025-07-28 17:47:19 [INFO] - processar_numero_autos - Processo principal 0033959-61.2004.8.26.0053 com Palavra Proibida em Movimentações - Continuando para precatórios (filtro removido)
2025-07-28 17:47:19 [INFO] - processar_numero_autos - Processo principal 0033959-61.2004.8.26.0053 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 17:47:19 [INFO] - processar_numero_autos - Links de precatório encontrados com seletor principal para 0033959-61.2004.8.26.0053.
2025-07-28 17:47:19 [INFO] - processar_numero_autos - 2 link(s) 'Precatório' para 0033959-61.2004.8.26.0053.
2025-07-28 17:47:20 [INFO] - processar_numero_autos - Processando link Precatório 1/2: 'Precatório - 00001'
2025-07-28 17:47:22 [INFO] - extrair_numero_precatorio_completo_da_pagina - Extraindo N° precatório para principal: 0033959-61.2004.8.26.0053
2025-07-28 17:47:22 [INFO] - extrair_numero_precatorio_completo_da_pagina - N° precatório não encontrado via span.unj-larger ou N° base divergente. Usar fallback do texto do link.
2025-07-28 17:47:22 [INFO] - processar_numero_autos - N° precatório formado via texto do link (fallback principal): 0033959-61.2004.8.26.0053 (01)
2025-07-28 17:47:22 [INFO] - obter_nome_cliente_do_precatorio - Buscando nome do cliente (Reqte)...
2025-07-28 17:47:22 [INFO] - obter_nome_cliente_do_precatorio - Nome do cliente: Neiva Terezinha Bignardi Scalon Martins
2025-07-28 17:47:22 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 17:47:22 [INFO] - processar_numero_autos - Precatório '0033959-61.2004.8.26.0053 (01)' status 'Finalizado' - pulando
2025-07-28 17:47:22 [INFO] - processar_numero_autos - Precatório rejeitado: 0033959-61.2004.8.26.0053 (01) - Cliente: Neiva Terezinha Bignardi Scalon Martins - Motivo: Status finalizado
2025-07-28 17:47:24 [INFO] - processar_numero_autos - Processando link Precatório 2/2: 'Precatório - 00002'
2025-07-28 17:47:26 [INFO] - extrair_numero_precatorio_completo_da_pagina - Extraindo N° precatório para principal: 0033959-61.2004.8.26.0053
2025-07-28 17:47:26 [INFO] - extrair_numero_precatorio_completo_da_pagina - N° precatório não encontrado via span.unj-larger ou N° base divergente. Usar fallback do texto do link.
2025-07-28 17:47:26 [INFO] - processar_numero_autos - N° precatório formado via texto do link (fallback principal): 0033959-61.2004.8.26.0053 (02)
2025-07-28 17:47:26 [INFO] - obter_nome_cliente_do_precatorio - Buscando nome do cliente (Reqte)...
2025-07-28 17:47:26 [INFO] - obter_nome_cliente_do_precatorio - Nome do cliente: Neiva Terezinha Bignardi Scalon Martins
2025-07-28 17:47:26 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 17:47:26 [INFO] - processar_numero_autos - Precatório '0033959-61.2004.8.26.0053 (02)' status 'Finalizado' - pulando
2025-07-28 17:47:26 [INFO] - processar_numero_autos - Precatório rejeitado: 0033959-61.2004.8.26.0053 (02) - Cliente: Neiva Terezinha Bignardi Scalon Martins - Motivo: Status finalizado
2025-07-28 17:47:26 [INFO] - processar_numero_autos - FIM NÚMERO 207 (Tempo: 18.98s)
2025-07-28 17:47:27 [INFO] - processar_numero_autos - Processando número 208/11405: 0019186-16.2001.8.26.0053
2025-07-28 17:47:27 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:47:27 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 17:47:29 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 17:47:29 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:47:29 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:47:31 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:47:31 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:47:31 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:47:31 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:47:31 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:47:31 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:47:33 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:47:33 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:47:33 [INFO] - consultar_processo_principal - Consultando 0019186-16.2001.8.26.0053 (TJSP: 0019186162001 / Foro: 0053)
2025-07-28 17:47:34 [INFO] - consultar_processo_principal - Lista de resultados para 0019186-16.2001.8.26.0053.
2025-07-28 17:47:35 [INFO] - consultar_processo_principal - Link para 0019186-16.2001.8.26.0053 encontrado. Clicando...
2025-07-28 17:47:36 [INFO] - consultar_processo_principal - Detalhes de 0019186-16.2001.8.26.0053 carregados após clique.
2025-07-28 17:47:36 [INFO] - verificar_status_processo - Status 'finalizado' (texto da página).
2025-07-28 17:47:36 [INFO] - processar_numero_autos - Processo principal 0019186-16.2001.8.26.0053 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 17:47:36 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:47:36 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Clayton Eduardo Wege (lower: clayton eduardo wege)
2025-07-28 17:47:36 [INFO] - processar_numero_autos - Processo principal 0019186-16.2001.8.26.0053 sem partes proibidas
2025-07-28 17:47:37 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:47:38 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento
2025-07-28 17:47:38 [INFO] - processar_numero_autos - Processo principal 0019186-16.2001.8.26.0053 com Palavra Proibida em Movimentações - Continuando para precatórios (filtro removido)
2025-07-28 17:47:38 [INFO] - processar_numero_autos - Processo principal 0019186-16.2001.8.26.0053 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 17:47:38 [INFO] - processar_numero_autos - Links de precatório encontrados com seletor principal para 0019186-16.2001.8.26.0053.
2025-07-28 17:47:38 [INFO] - processar_numero_autos - 1 link(s) 'Precatório' para 0019186-16.2001.8.26.0053.
2025-07-28 17:47:40 [INFO] - processar_numero_autos - Processando link Precatório 1/1: 'Precatório - 00002'
2025-07-28 17:47:42 [INFO] - extrair_numero_precatorio_completo_da_pagina - Extraindo N° precatório para principal: 0019186-16.2001.8.26.0053
2025-07-28 17:47:42 [INFO] - extrair_numero_precatorio_completo_da_pagina - N° precatório não encontrado via span.unj-larger ou N° base divergente. Usar fallback do texto do link.
2025-07-28 17:47:42 [INFO] - processar_numero_autos - N° precatório formado via texto do link (fallback principal): 0019186-16.2001.8.26.0053 (02)
2025-07-28 17:47:42 [INFO] - obter_nome_cliente_do_precatorio - Buscando nome do cliente (Reqte)...
2025-07-28 17:47:42 [INFO] - obter_nome_cliente_do_precatorio - Nome do cliente: Clayton Eduardo Wege
2025-07-28 17:47:42 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 17:47:42 [INFO] - processar_numero_autos - Precatório '0019186-16.2001.8.26.0053 (02)' status 'Finalizado' - pulando
2025-07-28 17:47:42 [INFO] - processar_numero_autos - Precatório rejeitado: 0019186-16.2001.8.26.0053 (02) - Cliente: Clayton Eduardo Wege - Motivo: Status finalizado
2025-07-28 17:47:42 [INFO] - processar_numero_autos - FIM NÚMERO 208 (Tempo: 15.10s)
2025-07-28 17:47:42 [INFO] - processar_numero_autos - Processando número 209/11405: 0012769-42.2004.8.26.0053
2025-07-28 17:47:42 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:47:42 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 17:47:45 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 17:47:45 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:47:45 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:47:47 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:47:47 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:47:47 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:47:47 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:47:47 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:47:47 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:47:49 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:47:49 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:47:49 [INFO] - consultar_processo_principal - Consultando 0012769-42.2004.8.26.0053 (TJSP: 0012769422004 / Foro: 0053)
2025-07-28 17:47:50 [INFO] - consultar_processo_principal - Lista de resultados para 0012769-42.2004.8.26.0053.
2025-07-28 17:47:50 [INFO] - consultar_processo_principal - Link para 0012769-42.2004.8.26.0053 encontrado. Clicando...
2025-07-28 17:47:52 [INFO] - consultar_processo_principal - Detalhes de 0012769-42.2004.8.26.0053 carregados após clique.
2025-07-28 17:47:52 [INFO] - verificar_status_processo - Status 'finalizado' detectado: arquivado pelo novo sistema sgdau 2º volume (1º não foi localizado)
2025-07-28 17:47:52 [INFO] - processar_numero_autos - Processo principal 0012769-42.2004.8.26.0053 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 17:47:52 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:47:52 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Maria Angela Xavier Visconti Oliveira (lower: maria angela xavier visconti oliveira)
2025-07-28 17:47:52 [INFO] - processar_numero_autos - Processo principal 0012769-42.2004.8.26.0053 sem partes proibidas
2025-07-28 17:47:52 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:47:54 [INFO] - verificar_palavras_proibidas_mov - Nenhuma palavra proibida.
2025-07-28 17:47:54 [INFO] - processar_numero_autos - Processo principal 0012769-42.2004.8.26.0053 sem palavras proibidas
2025-07-28 17:47:54 [INFO] - processar_numero_autos - Processo principal 0012769-42.2004.8.26.0053 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 17:47:54 [INFO] - processar_numero_autos - Links de precatório encontrados com seletor principal para 0012769-42.2004.8.26.0053.
2025-07-28 17:47:54 [INFO] - processar_numero_autos - 1 link(s) 'Precatório' para 0012769-42.2004.8.26.0053.
2025-07-28 17:47:55 [INFO] - processar_numero_autos - Processando link Precatório 1/1: 'Precatório - 00001'
2025-07-28 17:47:57 [INFO] - extrair_numero_precatorio_completo_da_pagina - Extraindo N° precatório para principal: 0012769-42.2004.8.26.0053
2025-07-28 17:47:57 [INFO] - extrair_numero_precatorio_completo_da_pagina - N° precatório não encontrado via span.unj-larger ou N° base divergente. Usar fallback do texto do link.
2025-07-28 17:47:57 [INFO] - processar_numero_autos - N° precatório formado via texto do link (fallback principal): 0012769-42.2004.8.26.0053 (01)
2025-07-28 17:47:57 [INFO] - obter_nome_cliente_do_precatorio - Buscando nome do cliente (Reqte)...
2025-07-28 17:47:57 [INFO] - obter_nome_cliente_do_precatorio - Nome do cliente: Maria Angela Xavier Visconti Oliveira
2025-07-28 17:47:57 [INFO] - verificar_status_processo - Status: normal/em andamento.
2025-07-28 17:47:57 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:47:57 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Maria Angela Xavier Visconti Oliveira (lower: maria angela xavier visconti oliveira)
2025-07-28 17:47:58 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:47:59 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento eletrônico
2025-07-28 17:47:59 [INFO] - processar_numero_autos - Precatório '0012769-42.2004.8.26.0053 (01)' com Palavra Proibida - pulando
2025-07-28 17:47:59 [INFO] - processar_numero_autos - Precatório rejeitado: 0012769-42.2004.8.26.0053 (01) - Cliente: Maria Angela Xavier Visconti Oliveira - Motivo: Palavras proibidas
2025-07-28 17:47:59 [INFO] - processar_numero_autos - FIM NÚMERO 209 (Tempo: 17.03s)
2025-07-28 17:48:00 [INFO] - verificar_sessao_ativa - Verificando se a sessão ainda está ativa
2025-07-28 17:48:00 [INFO] - verificar_sessao_ativa - Navegando para página de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 17:48:03 [INFO] - verificar_sessao_ativa - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:48:03 [INFO] - _detectar_sessao_expirada - Nome de usuário válido detectado: 'DENIS HENRIQUE SOUSA OLIVEIRA' - sessão ativa
2025-07-28 17:48:03 [INFO] - verificar_sessao_ativa - Sessão ativa confirmada - Usuário: DENIS HENRIQUE SOUSA OLIVEIRA
2025-07-28 17:48:03 [INFO] - processar_numero_autos - Processando número 210/11405: 0415506-65.1995.8.26.0053
2025-07-28 17:48:03 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:48:03 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:48:03 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:48:03 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:48:05 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:48:05 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:48:05 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:48:05 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:48:05 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:48:05 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:48:07 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:48:07 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:48:07 [INFO] - consultar_processo_principal - Consultando 0415506-65.1995.8.26.0053 (TJSP: 0415506651995 / Foro: 0053)
2025-07-28 17:48:09 [INFO] - consultar_processo_principal - Lista de resultados para 0415506-65.1995.8.26.0053.
2025-07-28 17:48:09 [INFO] - consultar_processo_principal - Link para 0415506-65.1995.8.26.0053 encontrado. Clicando...
2025-07-28 17:48:11 [INFO] - consultar_processo_principal - Detalhes de 0415506-65.1995.8.26.0053 carregados após clique.
2025-07-28 17:48:11 [INFO] - verificar_status_processo - Status 'finalizado' detectado: relação: 1524/2018 teor do ato: vistos. trata-se de processo físico com expedição de precatório digital em fase de aguardar o pagamento. a tramitação do precatório nesse setor de execuções contra a fazenda pública se dará no incidente digital nº 0415506-65.1995.8.26.0053/04, onde deverá prosseguir com peticionamento eletrônico obrigatório pelos advogados. mantenham-se os autos físicos em cartório pelo prazo de 30 dias para eventual consulta e após arquive-se lançando a movimentação 61614 - arquivado provisoriamente. quando da satisfação integral do débito deverá ser lançada a movimentação de baixa definitiva nesses autos. intime-se com urgência. advogados(s): dulce ataliba nogueira leite (oab 112868/sp), jane terezinha de carvalho gomes (oab 138357/sp), marisa midori ishii (oab 170080/sp), daniela barreiro barbosa (oab 187101/sp), lucas melo nóbrega (oab 272529/sp), vera helena pereira vidigal bucci (oab 69243/sp), ricardo innocenti (oab 36381/sp)
2025-07-28 17:48:11 [INFO] - processar_numero_autos - Processo principal 0415506-65.1995.8.26.0053 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 17:48:11 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:48:11 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Tereza Sakaguchi Franklin (lower: tereza sakaguchi franklin)
2025-07-28 17:48:11 [INFO] - processar_numero_autos - Processo principal 0415506-65.1995.8.26.0053 sem partes proibidas
2025-07-28 17:48:11 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:48:13 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento
2025-07-28 17:48:13 [INFO] - processar_numero_autos - Processo principal 0415506-65.1995.8.26.0053 com Palavra Proibida em Movimentações - Continuando para precatórios (filtro removido)
2025-07-28 17:48:13 [INFO] - processar_numero_autos - Processo principal 0415506-65.1995.8.26.0053 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 17:48:13 [INFO] - processar_numero_autos - Links de precatório encontrados com seletor principal para 0415506-65.1995.8.26.0053.
2025-07-28 17:48:13 [INFO] - processar_numero_autos - 1 link(s) 'Precatório' para 0415506-65.1995.8.26.0053.
2025-07-28 17:48:14 [INFO] - processar_numero_autos - Processando link Precatório 1/1: 'Precatório - 00004'
2025-07-28 17:48:16 [INFO] - extrair_numero_precatorio_completo_da_pagina - Extraindo N° precatório para principal: 0415506-65.1995.8.26.0053
2025-07-28 17:48:16 [INFO] - extrair_numero_precatorio_completo_da_pagina - N° precatório não encontrado via span.unj-larger ou N° base divergente. Usar fallback do texto do link.
2025-07-28 17:48:16 [INFO] - processar_numero_autos - N° precatório formado via texto do link (fallback principal): 0415506-65.1995.8.26.0053 (04)
2025-07-28 17:48:16 [INFO] - obter_nome_cliente_do_precatorio - Buscando nome do cliente (Reqte)...
2025-07-28 17:48:16 [INFO] - obter_nome_cliente_do_precatorio - Nome do cliente: Tereza Sakaguchi Franklin
2025-07-28 17:48:16 [INFO] - verificar_status_processo - Status: normal/em andamento.
2025-07-28 17:48:16 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:48:16 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Tereza Sakaguchi Franklin (lower: tereza sakaguchi franklin)
2025-07-28 17:48:17 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:48:18 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento eletrônico
2025-07-28 17:48:18 [INFO] - processar_numero_autos - Precatório '0415506-65.1995.8.26.0053 (04)' com Palavra Proibida - pulando
2025-07-28 17:48:18 [INFO] - processar_numero_autos - Precatório rejeitado: 0415506-65.1995.8.26.0053 (04) - Cliente: Tereza Sakaguchi Franklin - Motivo: Palavras proibidas
2025-07-28 17:48:18 [INFO] - processar_numero_autos - FIM NÚMERO 210 (Tempo: 15.08s)
2025-07-28 17:48:19 [INFO] - processar_numero_autos - Processando número 211/11405: 0302562-72.1985.8.26.0053
2025-07-28 17:48:19 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:48:19 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 17:48:21 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 17:48:21 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:48:21 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:48:23 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:48:23 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:48:23 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:48:23 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:48:23 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:48:23 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:48:25 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:48:25 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:48:25 [INFO] - consultar_processo_principal - Consultando 0302562-72.1985.8.26.0053 (TJSP: 0302562721985 / Foro: 0053)
2025-07-28 17:48:27 [INFO] - consultar_processo_principal - Lista de resultados para 0302562-72.1985.8.26.0053.
2025-07-28 17:48:27 [INFO] - consultar_processo_principal - Link para 0302562-72.1985.8.26.0053 encontrado. Clicando...
2025-07-28 17:48:29 [INFO] - consultar_processo_principal - Detalhes de 0302562-72.1985.8.26.0053 carregados após clique.
2025-07-28 17:48:29 [INFO] - verificar_status_processo - Status 'finalizado' (texto da página).
2025-07-28 17:48:29 [INFO] - processar_numero_autos - Processo principal 0302562-72.1985.8.26.0053 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 17:48:29 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:48:29 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Denise Maria Mariotti (lower: denise maria mariotti)
2025-07-28 17:48:29 [INFO] - processar_numero_autos - Processo principal 0302562-72.1985.8.26.0053 sem partes proibidas
2025-07-28 17:48:29 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:48:31 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento eletrônico
2025-07-28 17:48:31 [INFO] - processar_numero_autos - Processo principal 0302562-72.1985.8.26.0053 com Palavra Proibida em Movimentações - Continuando para precatórios (filtro removido)
2025-07-28 17:48:31 [INFO] - processar_numero_autos - Processo principal 0302562-72.1985.8.26.0053 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 17:48:31 [INFO] - processar_numero_autos - Links de precatório encontrados com seletor principal para 0302562-72.1985.8.26.0053.
2025-07-28 17:48:31 [INFO] - processar_numero_autos - 2 link(s) 'Precatório' para 0302562-72.1985.8.26.0053.
2025-07-28 17:48:32 [INFO] - processar_numero_autos - Processando link Precatório 1/2: 'Precatório - 00001'
2025-07-28 17:48:35 [INFO] - extrair_numero_precatorio_completo_da_pagina - Extraindo N° precatório para principal: 0302562-72.1985.8.26.0053
2025-07-28 17:48:35 [INFO] - extrair_numero_precatorio_completo_da_pagina - N° precatório não encontrado via span.unj-larger ou N° base divergente. Usar fallback do texto do link.
2025-07-28 17:48:35 [INFO] - processar_numero_autos - N° precatório formado via texto do link (fallback principal): 0302562-72.1985.8.26.0053 (01)
2025-07-28 17:48:35 [INFO] - obter_nome_cliente_do_precatorio - Buscando nome do cliente (Reqte)...
2025-07-28 17:48:35 [INFO] - obter_nome_cliente_do_precatorio - Nome do cliente: Ulysses Campos Nicolau
2025-07-28 17:48:35 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 17:48:35 [INFO] - processar_numero_autos - Precatório '0302562-72.1985.8.26.0053 (01)' status 'Finalizado' - pulando
2025-07-28 17:48:35 [INFO] - processar_numero_autos - Precatório rejeitado: 0302562-72.1985.8.26.0053 (01) - Cliente: Ulysses Campos Nicolau - Motivo: Status finalizado
2025-07-28 17:48:37 [INFO] - processar_numero_autos - Processando link Precatório 2/2: 'Precatório - 00004'
2025-07-28 17:48:39 [INFO] - extrair_numero_precatorio_completo_da_pagina - Extraindo N° precatório para principal: 0302562-72.1985.8.26.0053
2025-07-28 17:48:39 [INFO] - extrair_numero_precatorio_completo_da_pagina - N° precatório não encontrado via span.unj-larger ou N° base divergente. Usar fallback do texto do link.
2025-07-28 17:48:39 [INFO] - processar_numero_autos - N° precatório formado via texto do link (fallback principal): 0302562-72.1985.8.26.0053 (04)
2025-07-28 17:48:39 [INFO] - obter_nome_cliente_do_precatorio - Buscando nome do cliente (Reqte)...
2025-07-28 17:48:39 [INFO] - obter_nome_cliente_do_precatorio - Nome do cliente: Ulysses Campos Nicolau
2025-07-28 17:48:39 [INFO] - verificar_status_processo - Status: normal/em andamento.
2025-07-28 17:48:39 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:48:40 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Ulysses Campos Nicolau (lower: ulysses campos nicolau)
2025-07-28 17:48:40 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:48:42 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: cessão de crédito
2025-07-28 17:48:42 [INFO] - processar_numero_autos - Precatório '0302562-72.1985.8.26.0053 (04)' com Palavra Proibida - pulando
2025-07-28 17:48:42 [INFO] - processar_numero_autos - Precatório rejeitado: 0302562-72.1985.8.26.0053 (04) - Cliente: Ulysses Campos Nicolau - Motivo: Palavras proibidas
2025-07-28 17:48:42 [INFO] - processar_numero_autos - FIM NÚMERO 211 (Tempo: 22.90s)
2025-07-28 17:48:42 [INFO] - processar_numero_autos - Processando número 212/11405: 0407086-08.1994.8.26.0053
2025-07-28 17:48:42 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:48:42 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 17:48:44 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 17:48:44 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:48:45 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:48:47 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:48:47 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:48:47 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:48:47 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:48:47 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:48:47 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:48:49 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:48:49 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:48:49 [INFO] - consultar_processo_principal - Consultando 0407086-08.1994.8.26.0053 (TJSP: 0407086081994 / Foro: 0053)
2025-07-28 17:48:52 [INFO] - consultar_processo_principal - Lista de resultados para 0407086-08.1994.8.26.0053.
2025-07-28 17:48:52 [INFO] - consultar_processo_principal - Link para 0407086-08.1994.8.26.0053 encontrado. Clicando...
2025-07-28 17:48:53 [INFO] - consultar_processo_principal - Detalhes de 0407086-08.1994.8.26.0053 carregados após clique.
2025-07-28 17:48:54 [INFO] - verificar_status_processo - Status 'finalizado' (texto da página).
2025-07-28 17:48:54 [INFO] - processar_numero_autos - Processo principal 0407086-08.1994.8.26.0053 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 17:48:54 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:48:54 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Petrocoque S/A Indústria e Comércio (lower: petrocoque s/a indústria e comércio)
2025-07-28 17:48:54 [INFO] - verificar_partes_processo - Parte proibida (PJ) encontrada no Reqte: 's/a' em 'Petrocoque S/A Indústria e Comércio'
2025-07-28 17:48:54 [INFO] - processar_numero_autos - Processo principal 0407086-08.1994.8.26.0053 com Parte Proibida - Continuando para precatórios (filtro removido)
2025-07-28 17:48:54 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:48:56 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento
2025-07-28 17:48:56 [INFO] - processar_numero_autos - Processo principal 0407086-08.1994.8.26.0053 com Palavra Proibida em Movimentações - Continuando para precatórios (filtro removido)
2025-07-28 17:48:56 [INFO] - processar_numero_autos - Processo principal 0407086-08.1994.8.26.0053 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 17:48:56 [INFO] - processar_numero_autos - Links de precatório encontrados com seletor principal para 0407086-08.1994.8.26.0053.
2025-07-28 17:48:56 [INFO] - processar_numero_autos - 1 link(s) 'Precatório' para 0407086-08.1994.8.26.0053.
2025-07-28 17:48:57 [INFO] - processar_numero_autos - Processando link Precatório 1/1: 'Precatório - 00001'
2025-07-28 17:48:59 [INFO] - extrair_numero_precatorio_completo_da_pagina - Extraindo N° precatório para principal: 0407086-08.1994.8.26.0053
2025-07-28 17:48:59 [INFO] - extrair_numero_precatorio_completo_da_pagina - N° precatório não encontrado via span.unj-larger ou N° base divergente. Usar fallback do texto do link.
2025-07-28 17:48:59 [INFO] - processar_numero_autos - N° precatório formado via texto do link (fallback principal): 0407086-08.1994.8.26.0053 (01)
2025-07-28 17:48:59 [INFO] - obter_nome_cliente_do_precatorio - Buscando nome do cliente (Reqte)...
2025-07-28 17:48:59 [INFO] - obter_nome_cliente_do_precatorio - Nome do cliente: Petrocoque S/A Indústria e Comércio
2025-07-28 17:48:59 [INFO] - verificar_status_processo - Status 'finalizado' detectado: arquivado
2025-07-28 17:48:59 [INFO] - processar_numero_autos - Precatório '0407086-08.1994.8.26.0053 (01)' status 'Finalizado' - pulando
2025-07-28 17:48:59 [INFO] - processar_numero_autos - Precatório rejeitado: 0407086-08.1994.8.26.0053 (01) - Cliente: Petrocoque S/A Indústria e Comércio - Motivo: Status finalizado
2025-07-28 17:49:00 [INFO] - processar_numero_autos - FIM NÚMERO 212 (Tempo: 17.27s)
2025-07-28 17:49:00 [INFO] - processar_numero_autos - Processando número 213/11405: 0007410-77.2005.8.26.0053
2025-07-28 17:49:00 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:49:00 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 17:49:02 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 17:49:02 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:49:02 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:49:04 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:49:04 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:49:04 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:49:04 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:49:04 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:49:04 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:49:06 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:49:06 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:49:06 [INFO] - consultar_processo_principal - Consultando 0007410-77.2005.8.26.0053 (TJSP: 0007410772005 / Foro: 0053)
2025-07-28 17:49:08 [INFO] - consultar_processo_principal - Lista de resultados para 0007410-77.2005.8.26.0053.
2025-07-28 17:49:08 [INFO] - consultar_processo_principal - Link para 0007410-77.2005.8.26.0053 encontrado. Clicando...
2025-07-28 17:49:10 [INFO] - consultar_processo_principal - Detalhes de 0007410-77.2005.8.26.0053 carregados após clique.
2025-07-28 17:49:10 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 17:49:10 [INFO] - processar_numero_autos - Processo principal 0007410-77.2005.8.26.0053 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 17:49:10 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:49:10 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Vera Lúcia Rinke (lower: vera lúcia rinke)
2025-07-28 17:49:10 [INFO] - processar_numero_autos - Processo principal 0007410-77.2005.8.26.0053 sem partes proibidas
2025-07-28 17:49:10 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:49:11 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento
2025-07-28 17:49:11 [INFO] - processar_numero_autos - Processo principal 0007410-77.2005.8.26.0053 com Palavra Proibida em Movimentações - Continuando para precatórios (filtro removido)
2025-07-28 17:49:11 [INFO] - processar_numero_autos - Processo principal 0007410-77.2005.8.26.0053 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 17:49:12 [INFO] - processar_numero_autos - Links de precatório encontrados com seletor principal para 0007410-77.2005.8.26.0053.
2025-07-28 17:49:12 [INFO] - processar_numero_autos - 1 link(s) 'Precatório' para 0007410-77.2005.8.26.0053.
2025-07-28 17:49:13 [INFO] - processar_numero_autos - Processando link Precatório 1/1: 'Precatório - 00001'
2025-07-28 17:49:15 [INFO] - extrair_numero_precatorio_completo_da_pagina - Extraindo N° precatório para principal: 0007410-77.2005.8.26.0053
2025-07-28 17:49:15 [INFO] - extrair_numero_precatorio_completo_da_pagina - N° precatório não encontrado via span.unj-larger ou N° base divergente. Usar fallback do texto do link.
2025-07-28 17:49:15 [INFO] - processar_numero_autos - N° precatório formado via texto do link (fallback principal): 0007410-77.2005.8.26.0053 (01)
2025-07-28 17:49:15 [INFO] - obter_nome_cliente_do_precatorio - Buscando nome do cliente (Reqte)...
2025-07-28 17:49:15 [INFO] - obter_nome_cliente_do_precatorio - Nome do cliente: Foz Sociedade de Advogados
2025-07-28 17:49:15 [INFO] - verificar_status_processo - Status 'finalizado' detectado: arquivado
2025-07-28 17:49:15 [INFO] - processar_numero_autos - Precatório '0007410-77.2005.8.26.0053 (01)' status 'Finalizado' - pulando
2025-07-28 17:49:15 [INFO] - processar_numero_autos - Precatório rejeitado: 0007410-77.2005.8.26.0053 (01) - Cliente: Foz Sociedade de Advogados - Motivo: Status finalizado
2025-07-28 17:49:15 [INFO] - processar_numero_autos - FIM NÚMERO 213 (Tempo: 15.03s)
2025-07-28 17:49:16 [INFO] - processar_numero_autos - Processando número 214/11405: 0411305-64.1994.8.26.0053
2025-07-28 17:49:16 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:49:16 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 17:49:18 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 17:49:18 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:49:18 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:49:20 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:49:20 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:49:20 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:49:20 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:49:20 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:49:20 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:49:22 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 17:49:22 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 17:49:22 [INFO] - consultar_processo_principal - Consultando 0411305-64.1994.8.26.0053 (TJSP: 0411305641994 / Foro: 0053)
2025-07-28 17:49:24 [INFO] - consultar_processo_principal - Lista de resultados para 0411305-64.1994.8.26.0053.
2025-07-28 17:49:24 [INFO] - consultar_processo_principal - Link para 0411305-64.1994.8.26.0053 encontrado. Clicando...
2025-07-28 17:49:26 [INFO] - consultar_processo_principal - Detalhes de 0411305-64.1994.8.26.0053 carregados após clique.
2025-07-28 17:49:26 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 17:49:26 [INFO] - processar_numero_autos - Processo principal 0411305-64.1994.8.26.0053 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 17:49:26 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 17:49:26 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Carlos Fuga (lower: carlos fuga)
2025-07-28 17:49:26 [INFO] - processar_numero_autos - Processo principal 0411305-64.1994.8.26.0053 sem partes proibidas
2025-07-28 17:49:26 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 17:49:28 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento
2025-07-28 17:49:28 [INFO] - processar_numero_autos - Processo principal 0411305-64.1994.8.26.0053 com Palavra Proibida em Movimentações - Continuando para precatórios (filtro removido)
2025-07-28 17:49:28 [INFO] - processar_numero_autos - Processo principal 0411305-64.1994.8.26.0053 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 17:49:28 [INFO] - processar_numero_autos - Links de precatório encontrados com seletor principal para 0411305-64.1994.8.26.0053.
2025-07-28 17:49:28 [INFO] - processar_numero_autos - 1 link(s) 'Precatório' para 0411305-64.1994.8.26.0053.
2025-07-28 17:49:29 [INFO] - processar_numero_autos - Processando link Precatório 1/1: 'Precatório - 00002'
2025-07-28 17:49:31 [INFO] - extrair_numero_precatorio_completo_da_pagina - Extraindo N° precatório para principal: 0411305-64.1994.8.26.0053
2025-07-28 17:49:31 [INFO] - extrair_numero_precatorio_completo_da_pagina - N° precatório não encontrado via span.unj-larger ou N° base divergente. Usar fallback do texto do link.
2025-07-28 17:49:31 [INFO] - processar_numero_autos - N° precatório formado via texto do link (fallback principal): 0411305-64.1994.8.26.0053 (02)
2025-07-28 17:49:31 [INFO] - obter_nome_cliente_do_precatorio - Buscando nome do cliente (Reqte)...
2025-07-28 17:49:31 [INFO] - obter_nome_cliente_do_precatorio - Nome do cliente: Palladio Avicena Pereira Borba
2025-07-28 17:49:31 [INFO] - verificar_status_processo - Status 'finalizado' detectado: arquivado
2025-07-28 17:49:31 [INFO] - processar_numero_autos - Precatório '0411305-64.1994.8.26.0053 (02)' status 'Finalizado' - pulando
2025-07-28 17:49:31 [INFO] - processar_numero_autos - Precatório rejeitado: 0411305-64.1994.8.26.0053 (02) - Cliente: Palladio Avicena Pereira Borba - Motivo: Status finalizado
2025-07-28 17:49:31 [INFO] - processar_numero_autos - FIM NÚMERO 214 (Tempo: 15.42s)
2025-07-28 17:49:32 [INFO] - processar_numero_autos - Processando número 215/11405: 0027430-31.2001.8.26.0053
2025-07-28 17:49:32 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:49:32 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 17:49:34 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 17:49:34 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 17:49:34 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 17:49:36 [ERROR] - verificar_login_tjsp_especifico - Erro na verificação específica do TJSP: Message: tab crashed
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff66637e935+77845]
	GetHandleVerifier [0x0x7ff66637e990+77936]
	(No symbol) [0x0x7ff666139b0c]
	(No symbol) [0x0x7ff66612727b]
	(No symbol) [0x0x7ff666124eab]
	(No symbol) [0x0x7ff66612590f]
	(No symbol) [0x0x7ff66613453e]
	(No symbol) [0x0x7ff66614a4f1]
	(No symbol) [0x0x7ff66615165a]
	(No symbol) [0x0x7ff6661260ad]
	(No symbol) [0x0x7ff666149ce1]
	(No symbol) [0x0x7ff6661e09e1]
	(No symbol) [0x0x7ff6661b86a3]
	(No symbol) [0x0x7ff666181791]
	(No symbol) [0x0x7ff666182523]
	GetHandleVerifier [0x0x7ff66665684d+3059501]
	GetHandleVerifier [0x0x7ff666650c0d+3035885]
	GetHandleVerifier [0x0x7ff666670400+3164896]
	GetHandleVerifier [0x0x7ff666398c3e+185118]
	GetHandleVerifier [0x0x7ff6663a054f+216111]
	GetHandleVerifier [0x0x7ff6663872e4+113092]
	GetHandleVerifier [0x0x7ff666387499+113529]
	GetHandleVerifier [0x0x7ff66636e298+10616]
	BaseThreadInitThunk [0x0x7ffe617be8d7+23]
	RtlUserThreadStart [0x0x7ffe622dc34c+44]

2025-07-28 17:49:36 [INFO] - verificar_sessao_ativa - Verificando se a sessão ainda está ativa
2025-07-28 17:49:36 [ERROR] - verificar_sessao_ativa - Erro ao verificar sessão: Message: tab crashed
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff66637e935+77845]
	GetHandleVerifier [0x0x7ff66637e990+77936]
	(No symbol) [0x0x7ff666139b0c]
	(No symbol) [0x0x7ff66612727b]
	(No symbol) [0x0x7ff666124eab]
	(No symbol) [0x0x7ff66612590f]
	(No symbol) [0x0x7ff66613453e]
	(No symbol) [0x0x7ff66614a4f1]
	(No symbol) [0x0x7ff66615165a]
	(No symbol) [0x0x7ff6661260ad]
	(No symbol) [0x0x7ff666149ce1]
	(No symbol) [0x0x7ff6661e09e1]
	(No symbol) [0x0x7ff6661b86a3]
	(No symbol) [0x0x7ff666181791]
	(No symbol) [0x0x7ff666182523]
	GetHandleVerifier [0x0x7ff66665684d+3059501]
	GetHandleVerifier [0x0x7ff666650c0d+3035885]
	GetHandleVerifier [0x0x7ff666670400+3164896]
	GetHandleVerifier [0x0x7ff666398c3e+185118]
	GetHandleVerifier [0x0x7ff6663a054f+216111]
	GetHandleVerifier [0x0x7ff6663872e4+113092]
	GetHandleVerifier [0x0x7ff666387499+113529]
	GetHandleVerifier [0x0x7ff66636e298+10616]
	BaseThreadInitThunk [0x0x7ffe617be8d7+23]
	RtlUserThreadStart [0x0x7ffe622dc34c+44]

2025-07-28 17:49:36 [WARNING] - verificar_e_renovar_sessao - Sessão expirada detectada - Renovando automaticamente
2025-07-28 17:49:36 [INFO] - _renovar_sessao_automaticamente - Iniciando renovação automática da sessão
2025-07-28 17:49:36 [ERROR] - _renovar_sessao_automaticamente - Erro na renovação automática: Message: tab crashed
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff66637e935+77845]
	GetHandleVerifier [0x0x7ff66637e990+77936]
	(No symbol) [0x0x7ff666139b0c]
	(No symbol) [0x0x7ff66612727b]
	(No symbol) [0x0x7ff666124eab]
	(No symbol) [0x0x7ff66612590f]
	(No symbol) [0x0x7ff66613453e]
	(No symbol) [0x0x7ff66614a4f1]
	(No symbol) [0x0x7ff66615165a]
	(No symbol) [0x0x7ff6661260ad]
	(No symbol) [0x0x7ff666149ce1]
	(No symbol) [0x0x7ff6661e09e1]
	(No symbol) [0x0x7ff6661b86a3]
	(No symbol) [0x0x7ff666181791]
	(No symbol) [0x0x7ff666182523]
	GetHandleVerifier [0x0x7ff66665684d+3059501]
	GetHandleVerifier [0x0x7ff666650c0d+3035885]
	GetHandleVerifier [0x0x7ff666670400+3164896]
	GetHandleVerifier [0x0x7ff666398c3e+185118]
	GetHandleVerifier [0x0x7ff6663a054f+216111]
	GetHandleVerifier [0x0x7ff6663872e4+113092]
	GetHandleVerifier [0x0x7ff666387499+113529]
	GetHandleVerifier [0x0x7ff66636e298+10616]
	BaseThreadInitThunk [0x0x7ffe617be8d7+23]
	RtlUserThreadStart [0x0x7ffe622dc34c+44]

2025-07-28 17:49:36 [ERROR] - processar_numero_autos - 0027430-31.2001.8.26.0053: Falha na verificação de sessão.
2025-07-28 17:49:36 [INFO] - processar_numero_autos - Processando número 216/11405: 0017040-31.2003.8.26.0053
2025-07-28 17:49:36 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:49:36 [ERROR] - garantir_url_consulta - Erro ao garantir URL de consulta: Message: tab crashed
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff66637e935+77845]
	GetHandleVerifier [0x0x7ff66637e990+77936]
	(No symbol) [0x0x7ff666139b0c]
	(No symbol) [0x0x7ff66612727b]
	(No symbol) [0x0x7ff666124eab]
	(No symbol) [0x0x7ff66612590f]
	(No symbol) [0x0x7ff66613453e]
	(No symbol) [0x0x7ff66614a4f1]
	(No symbol) [0x0x7ff66615165a]
	(No symbol) [0x0x7ff6661260ad]
	(No symbol) [0x0x7ff666149ce1]
	(No symbol) [0x0x7ff6661e09e1]
	(No symbol) [0x0x7ff6661b86a3]
	(No symbol) [0x0x7ff666181791]
	(No symbol) [0x0x7ff666182523]
	GetHandleVerifier [0x0x7ff66665684d+3059501]
	GetHandleVerifier [0x0x7ff666650c0d+3035885]
	GetHandleVerifier [0x0x7ff666670400+3164896]
	GetHandleVerifier [0x0x7ff666398c3e+185118]
	GetHandleVerifier [0x0x7ff6663a054f+216111]
	GetHandleVerifier [0x0x7ff6663872e4+113092]
	GetHandleVerifier [0x0x7ff666387499+113529]
	GetHandleVerifier [0x0x7ff66636e298+10616]
	BaseThreadInitThunk [0x0x7ffe617be8d7+23]
	RtlUserThreadStart [0x0x7ffe622dc34c+44]

2025-07-28 17:49:36 [ERROR] - processar_numero_autos - 0017040-31.2003.8.26.0053: Falha na verificação de sessão.
2025-07-28 17:49:36 [INFO] - processar_numero_autos - Processando número 217/11405: 0424396-51.1999.8.26.0053
2025-07-28 17:49:36 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:49:36 [ERROR] - garantir_url_consulta - Erro ao garantir URL de consulta: Message: tab crashed
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff66637e935+77845]
	GetHandleVerifier [0x0x7ff66637e990+77936]
	(No symbol) [0x0x7ff666139b0c]
	(No symbol) [0x0x7ff66612727b]
	(No symbol) [0x0x7ff666124eab]
	(No symbol) [0x0x7ff66612590f]
	(No symbol) [0x0x7ff66613453e]
	(No symbol) [0x0x7ff66614a4f1]
	(No symbol) [0x0x7ff66615165a]
	(No symbol) [0x0x7ff6661260ad]
	(No symbol) [0x0x7ff666149ce1]
	(No symbol) [0x0x7ff6661e09e1]
	(No symbol) [0x0x7ff6661b86a3]
	(No symbol) [0x0x7ff666181791]
	(No symbol) [0x0x7ff666182523]
	GetHandleVerifier [0x0x7ff66665684d+3059501]
	GetHandleVerifier [0x0x7ff666650c0d+3035885]
	GetHandleVerifier [0x0x7ff666670400+3164896]
	GetHandleVerifier [0x0x7ff666398c3e+185118]
	GetHandleVerifier [0x0x7ff6663a054f+216111]
	GetHandleVerifier [0x0x7ff6663872e4+113092]
	GetHandleVerifier [0x0x7ff666387499+113529]
	GetHandleVerifier [0x0x7ff66636e298+10616]
	BaseThreadInitThunk [0x0x7ffe617be8d7+23]
	RtlUserThreadStart [0x0x7ffe622dc34c+44]

2025-07-28 17:49:36 [ERROR] - processar_numero_autos - 0424396-51.1999.8.26.0053: Falha na verificação de sessão.
2025-07-28 17:49:36 [INFO] - processar_numero_autos - Processando número 218/11405: 0100945-55.2008.8.26.0053
2025-07-28 17:49:36 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:49:36 [ERROR] - garantir_url_consulta - Erro ao garantir URL de consulta: Message: tab crashed
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff66637e935+77845]
	GetHandleVerifier [0x0x7ff66637e990+77936]
	(No symbol) [0x0x7ff666139b0c]
	(No symbol) [0x0x7ff66612727b]
	(No symbol) [0x0x7ff666124eab]
	(No symbol) [0x0x7ff66612590f]
	(No symbol) [0x0x7ff66613453e]
	(No symbol) [0x0x7ff66614a4f1]
	(No symbol) [0x0x7ff66615165a]
	(No symbol) [0x0x7ff6661260ad]
	(No symbol) [0x0x7ff666149ce1]
	(No symbol) [0x0x7ff6661e09e1]
	(No symbol) [0x0x7ff6661b86a3]
	(No symbol) [0x0x7ff666181791]
	(No symbol) [0x0x7ff666182523]
	GetHandleVerifier [0x0x7ff66665684d+3059501]
	GetHandleVerifier [0x0x7ff666650c0d+3035885]
	GetHandleVerifier [0x0x7ff666670400+3164896]
	GetHandleVerifier [0x0x7ff666398c3e+185118]
	GetHandleVerifier [0x0x7ff6663a054f+216111]
	GetHandleVerifier [0x0x7ff6663872e4+113092]
	GetHandleVerifier [0x0x7ff666387499+113529]
	GetHandleVerifier [0x0x7ff66636e298+10616]
	BaseThreadInitThunk [0x0x7ffe617be8d7+23]
	RtlUserThreadStart [0x0x7ffe622dc34c+44]

2025-07-28 17:49:36 [ERROR] - processar_numero_autos - 0100945-55.2008.8.26.0053: Falha na verificação de sessão.
2025-07-28 17:49:36 [INFO] - processar_numero_autos - Processando número 219/11405: 0001100-79.2010.8.26.0053
2025-07-28 17:49:36 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 17:49:36 [ERROR] - garantir_url_consulta - Erro ao garantir URL de consulta: Message: tab crashed
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff66637e935+77845]
	GetHandleVerifier [0x0x7ff66637e990+77936]
	(No symbol) [0x0x7ff666139b0c]
	(No symbol) [0x0x7ff66612727b]
	(No symbol) [0x0x7ff666124eab]
	(No symbol) [0x0x7ff66612590f]
	(No symbol) [0x0x7ff66613453e]
	(No symbol) [0x0x7ff66614a4f1]
	(No symbol) [0x0x7ff66615165a]
	(No symbol) [0x0x7ff6661260ad]
	(No symbol) [0x0x7ff666149ce1]
	(No symbol) [0x0x7ff6661e09e1]
	(No symbol) [0x0x7ff6661b86a3]
	(No symbol) [0x0x7ff666181791]
	(No symbol) [0x0x7ff666182523]
	GetHandleVerifier [0x0x7ff66665684d+3059501]
	GetHandleVerifier [0x0x7ff666650c0d+3035885]
	GetHandleVerifier [0x0x7ff666670400+3164896]
	GetHandleVerifier [0x0x7ff666398c3e+185118]
	GetHandleVerifier [0x0x7ff6663a054f+216111]
	GetHandleVerifier [0x0x7ff6663872e4+113092]
	GetHandleVerifier [0x0x7ff666387499+113529]
	GetHandleVerifier [0x0x7ff66636e298+10616]
	BaseThreadInitThunk [0x0x7ffe617be8d7+23]
	RtlUserThreadStart [0x0x7ffe622dc34c+44]

2025-07-28 17:49:36 [ERROR] - processar_numero_autos - 0001100-79.2010.8.26.0053: Falha na verificação de sessão.
2025-07-28 17:49:36 [INFO] - verificar_sessao_ativa - Verificando se a sessão ainda está ativa
2025-07-28 17:49:36 [ERROR] - verificar_sessao_ativa - Erro ao verificar sessão: Message: tab crashed
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff66637e935+77845]
	GetHandleVerifier [0x0x7ff66637e990+77936]
	(No symbol) [0x0x7ff666139b0c]
	(No symbol) [0x0x7ff66612727b]
	(No symbol) [0x0x7ff666124eab]
	(No symbol) [0x0x7ff66612590f]
	(No symbol) [0x0x7ff66613453e]
	(No symbol) [0x0x7ff66614a4f1]
	(No symbol) [0x0x7ff66615165a]
	(No symbol) [0x0x7ff6661260ad]
	(No symbol) [0x0x7ff666149ce1]
	(No symbol) [0x0x7ff6661e09e1]
	(No symbol) [0x0x7ff6661b86a3]
	(No symbol) [0x0x7ff666181791]
	(No symbol) [0x0x7ff666182523]
	GetHandleVerifier [0x0x7ff66665684d+3059501]
	GetHandleVerifier [0x0x7ff666650c0d+3035885]
	GetHandleVerifier [0x0x7ff666670400+3164896]
	GetHandleVerifier [0x0x7ff666398c3e+185118]
	GetHandleVerifier [0x0x7ff6663a054f+216111]
	GetHandleVerifier [0x0x7ff6663872e4+113092]
	GetHandleVerifier [0x0x7ff666387499+113529]
	GetHandleVerifier [0x0x7ff66636e298+10616]
	BaseThreadInitThunk [0x0x7ffe617be8d7+23]
	RtlUserThreadStart [0x0x7ffe622dc34c+44]

2025-07-28 17:49:36 [WARNING] - verificar_sessao_periodica - Sessão expirada no processo 220 - Iniciando recuperação
2025-07-28 17:49:36 [INFO] - _recuperar_sessao_automaticamente - RECUPERAÇÃO DE SESSÃO: Iniciando renovação automática
2025-07-28 17:49:36 [ERROR] - _recuperar_sessao_automaticamente - RECUPERAÇÃO DE SESSÃO: Erro durante recuperação: Message: tab crashed
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff66637e935+77845]
	GetHandleVerifier [0x0x7ff66637e990+77936]
	(No symbol) [0x0x7ff666139b0c]
	(No symbol) [0x0x7ff66612727b]
	(No symbol) [0x0x7ff666124eab]
	(No symbol) [0x0x7ff66612590f]
	(No symbol) [0x0x7ff66613453e]
	(No symbol) [0x0x7ff66614a4f1]
	(No symbol) [0x0x7ff66615165a]
	(No symbol) [0x0x7ff6661260ad]
	(No symbol) [0x0x7ff666149ce1]
	(No symbol) [0x0x7ff6661e09e1]
	(No symbol) [0x0x7ff6661b86a3]
	(No symbol) [0x0x7ff666181791]
	(No symbol) [0x0x7ff666182523]
	GetHandleVerifier [0x0x7ff66665684d+3059501]
	GetHandleVerifier [0x0x7ff666650c0d+3035885]
	GetHandleVerifier [0x0x7ff666670400+3164896]
	GetHandleVerifier [0x0x7ff666398c3e+185118]
	GetHandleVerifier [0x0x7ff6663a054f+216111]
	GetHandleVerifier [0x0x7ff6663872e4+113092]
	GetHandleVerifier [0x0x7ff666387499+113529]
	GetHandleVerifier [0x0x7ff66636e298+10616]
	BaseThreadInitThunk [0x0x7ffe617be8d7+23]
	RtlUserThreadStart [0x0x7ffe622dc34c+44]

2025-07-28 17:49:36 [ERROR] - verificar_sessao_periodica - Falha na recuperação automática no processo 220
2025-07-28 17:49:36 [ERROR] - executar_processamento - Processamento interrompido por falha de sessão no número 220
2025-07-28 17:49:37 [INFO] - executar_processamento - Processamento concluído - Consultados: 577, Downloads: 21, Falhas: 0, Recuperações: 0
2025-07-28 17:49:37 [INFO] - executar_processamento - Fechando navegador
