﻿e automatizador tudo bem Hugo aqui em
mais um vídeo do canal eu vou te ensinar
como carregar arquivos locais né ou seja
do seu próprio computador em suas
automações do ntn Lembrando que isso só
é possível pelo ntn local host tá aquele
que você instala e configura no próprio
servidor Inclusive a gente ensina como
fazer aqui nesse tutorial do canal tá E
quem tem o ntn remoto né que instala em
uma VPS e você consegue Car arquivos que
estão dentro do Servidor tá Inclusive a
gente fala mais sobre isso lá no nosso
curso intensivo de ntn que é o
intensivão ntn Tá bom mas esse tutorial
aqui a gente vai focar nos arquivos
locais tá que inclusive é uma dúvida
muito comum aí dos dos usuários
iniciantes né em ntn que às vezes
precisa subir ali uma imagem uma lista
de contatos um vídeo né do seu
computador e não sabe como fazer tá bom
então basicamente é eu vou inicializar
aqui o meu ntn tá por meio do prompt de
comando tá então vou dar aqui o comando
npx nhn
tá bom como o nhn da minha máquina ele
já está atualizado ele não vai passar
por uma atualização ele já vai
inicializar aqui
tá ó inicializou vou apertar zero né
para poder carregar aqui o o meu
NN Ok E aí vou adicionar aqui um novo
workflow E aí o primeiro node que eu vou
colocar aqui é um
read WR né files from Disk tá Ou seja é
um node que ele vai ler algum arquivo
que está no na minha máquina local tá E
e aqui diferente né da da versão eh
remota né do ntn que é instalado no
servidor por exemplo no servidor você
colocaria por exemplo o caminho né então
vamos dizer que eu tem ali uma imagem eh
que fica na no diretório Rot né que é
geralmente é a pasta raiz do dos
Servidores né barra aí vamos dizer que
eu tenho uma subpasta chamada imagens né
E aí eu coloco barra Eh sei
lá
imagem
01.png tá então é assim que
você lê né um arquivo ali que está no
seu servidor né onde tá rodando o ntn
quando é máquina local que é o seu
computador aí esse aqui só muda um
pouquinho Como assim no caso eu tenho
que começar aqui informando é a unidade
do disco né que está o meu arquivo
geralmente está em C dois pontos né se
você salva aí na sua área de trabalho ou
qualquer outro lugar ali geralmente é
ser dois pontos A não ser que você tenha
ali um segundo HD né como por exemplo no
caso aqui do meu notebook eh eu tenho
dois HDs né o original do notebook e um
que eu coloquei como adicional que
inclusive é até do meu notebook antigo
tá eh então se eu quisesse por exemplo
pegar do HD secundário né eu colocaria d
dois pontos né porque ele foi mapeado
como D2 pontos isso aí você pode ver eh
aqui mesmo no seu explorador de arquivos
né do Windows Então quando você vem aqui
em este computador né você vai ver quais
são os seus discos aqui tá então no meu
caso aqui tem o C2 pontos que é o o HD
principal e o D2 pontos que é o HD eh
adicional aqui tá bom eh eu coloquei
aqui um arquivo
eh para teste tá no caso eu coloquei
dois coloquei aqui uma planilha csv né
que muita gente às vezes usa ali uma
automação para enviar né para uma lista
de contatos e coloquei aqui também uma
imagem
né inclusive até uma imagem aqui de
feliz aniversário tá e você pode
carregar esses dois arquivos aqui na sua
automação donn local de qual forma
fazendo o devido mapeamento aqui tá eh
sendo que por exemplo se você vir aqui ó
por exemplo eh clicar aqui e botar Deixa
eu botar aqui mais opções colocar
propriedades
tá ó aqui você vai ver o local do
arquivo tá vendo ó é C2 pontos
né Eh contra barra e aí Claro você
complementaria com o nome do arquivo tá
da mesma forma aqui por exemplo quer ver
ó se eu chegar aqui e botar assim eh
deixa eu vou criar uma aqui uma nova
pasta tá Vou botar aqui
eh arquivos arquivos
traço nem sei se pode traço
né botar aqui ó traço ntn tá um
exemplo ó criei essa pasta aqui eu vou
até jogar esses dois arquivos aqui para
dentro tá
ó Ok então agora se eu for lá
consultar o local do arquivo já aparece
isso aqui ó tá vendo ó C2 P bar arquivos
ntn Ok E aí Eu daria a barra e colocaria
o nome do arquivo esse seria o caminho
completo do arquivo tá então por exemplo
você pode muito bem chegar aqui copiar
né
E e aí você cola aqui tá sendo que tem
um detalhe aqui por mais que esteja ali
o caminho certo né na sua máquina e o
ntn ele não aceita o contrabarra tá no
caso você tem que colocar aqui é barra
porém barra barra dupla tá tá então da
mesma forma né Por exemplo Vou colocar
aqui ó barra dupla Ok então da mesma
forma né que lá no servidor remoto você
coloca uma barra só né assim
ó né Aí você vai colocando ali o caminho
né aqui você vai colocar barra também
mas mas tem que ser barra dupla tá então
ficaria c dois pontos barra barra né aí
o nome da pasta Onde está meus arquivos
se tivesse uma outra pasta dentro dessa
eu colocaria também aqui ó barra barra
subpasta né que aí seria o nome da da
outra pasta que tá aqui dentro ó né Se
aqui tivesse uma uma outra pasta
né Por exemplo podia até botar assim ó
eh imagem imagem e aqui eu posso colocar
lista lista tá
eu posso jogar lista para cá e posso
jogar imagem para cá tá então né Se eu
quisesse aqui ó referenciar a subpasta
eu colocaria por exemplo imagem né ó
imagem tá E aí dentro de imagem é onde
de fato a gente tem ali a nossa imagem
que se chama Nel jpg tá então eu posso
simplesmente vir aqui dar um um barra
barra novamente né barra dupla e
escrever
n.jpg tá
aí eu posso até colocar aqui o nome Ó é
carrega imagem
tá E aí se eu
executar ele vai conseguir carregar o
arquivo
do do meu computador local tá é claro
que quando ele vai exibir o diretório
ele exibe lá com uma barra apenas tá
eh mas o o por que aqui tem que ser
barra barra porque é uma forma ali né do
node JS né conseguir interpretar que é
um um diretório ali local de uma máquina
local por isso que a gente acaba
colocando essa barra a mais tá ok então
se eu clicar aqui em viio a imagem já
vai aparecer para mim olha que legal né
a imagem que está no meu computador né
ela já aparece aqui perfeitamente na
minha automação do ntn tá agora se eu
quisesse carregar a lista mesmo esquema
né Eu poderia até mesmo duplicar aqui ó
duplico
né E aí eu posso colocar aqui ó
Carrega carrega
lista
tá E aí aqui ó ã eu posso mudar a imagem
para a lista né que é o nome da pasta lá
que eu
coloquei e o nome do arquivo é
ó leads pcsv tá então completa aqui com
leads P csv tá lembrando que o ntn ele
basicamente lê qualquer arquivo aqui não
importa a extensão tá ele consegue ler e
você utilizar ele aqui na na sua
automação tá então por exemplo vou
executar aqui
ó carregou a imagem
tá E vamos ver a lista ó a também
carregou tá vendo ó Ele já reconheceu
aqui que é um arquivo csv né o diretório
dele tá vendo ó que é C2 pont barra
arquivos traço ntn barra lista a
extensão csv mimi Type também ele já
interpreta que é um arquivo csv e aqui o
tamanho dele né 84 byes bem pequenininho
tá E aí eu posso clicar aqui em View
sendo que o o Chrome né como ele não tem
ali um plugin uma extensão para
interpretar csv ele acaba não
conseguindo mostrar entrar aqui tá mas
não tem problema só o fato de aparecer
esse binário aqui significa que leu com
sucesso tá aí por exemplo no caso de
lista
eh eu posso muito bem por
exemplo fazer ali né um um loop né
quando a gente quer fazer por exemplo um
envio massa pros contatos né eu posso
usar o loop aqui para ter o controle do
lote tá inclusive isso aqui
é uma boa prática sempre que você for
trabalhar com listas em suas automações
use sempre uma estrutura de loop tá para
qu para que a pi que vai estar por
exemplo fazendo o envio para aquele
contato seja por exemplo um um WhatsApp
um e-mail um SMS ela não venha ser
sobrecarregada com múltiplas requisições
no mesmo tempo tá bom então isso aqui é
uma boa
prática então V botar aqui para cada
Lead né para cada lead
e aqui né eu colocaria ali uma espécie É
claro aqui é um no Operation né mas
vamos supor que ele fosse aqui uma API
né de WhatsApp então por exemplo envia
WhatsApp para o Lead né para o
Lead mesma forma né que você também
poderia ter
um outro sei lá um outro http
request já usando uma API de SMS né
então poderia ser envi SMS
né para o Lead
n
Ok bom a gente também poderia fazer
assim ó aí seria execução
ver
aqui a gente pode deixar em sequência
mesmo ok e aqui por último poderia ser
um envia e-mail
né envia e-mail tá
para o
lit aí poderia até mesmo colocar um e né
Se eu
quisesse aguardar aqui 5 segundos
né 5
segundos e aí eu já conecto aqui ó para
ele fazer
o o loop
né
Beleza Ah tá esqueci um detalhe também
aqui a gente pode e extrair né o
conteúdo da nossa planilha né porque
para que o loop over item funcione né
você tem que estar com uma lista de
itens tá aqui ele não retorna uma lista
de itens né ele retorna só um binário
que é o o csv a planilha tá mas aí eu
posso extrair
eh o conteúdo que tem nessa planilha né
que é uma lista né eu posso trazer aqui
para o meu ntn num formato ali de lista
né Jon tá inclusive ó se eu formatar
formatar não né se eu abrir
eh se eu abrir aqui
ó eu vou colocar até para editar como
como um TXT né com meu Notepad mais mais
então dentro desse csv eu tenho lá uma
coluna nome eh ponto e vírgula WhatsApp
tá geralmente quando se trabalha ali com
csv os valores né as colunas elas são
separadas ali por ponto em vírgula tá
eh Opa Acabei fechando sem
querer então esse ponto vírgula aqui que
poderia ser também uma vírgula é chamado
de delimitador tá então meu delimitador
aqui das minhas colunas é ponto e
vírgula tá então eu tenho aqui uma
coluna chamado nome e outra chamado
WhatsApp tá então cada Lead aqui com seu
respectivo nome e respectivo WhatsApp
Beleza então Eh eu posso vir aqui e
adicionar esse node aqui chamado extract
from file tá onde eu posso pedir para
extrair de um csv tá e eu deixo aqui o
binary input como data né que é o valor
padrão tá
eh e aqui em apption eu adiciono esse
delimitador e acrescento aqui ponto e
vírgula tá se o seu delimitador é do seu
csv da sua planilha né for ponto e
vírgula eu acredito que você não precisa
colocar isso aqui porque por padrão ele
considera a vírgula né como delimitador
padrão de um de uma lista né de uma
planilha tá mas tem casos que é ponto e
vírgula então você vai adicionar ponto e
vírgula aqui no delimitador Ok E aí vou
até colocar aqui ó
eh
extrai extrai lista de leads
né E aí por exemplo vou executar aqui ó
e pronto ó ele já conseguiu ó extrair os
leads aqui lá do meu sv
Tá três leads aqui e aí a princípio esse
loop ele rodaria três vezes né porque
para cada Lead ele vai mandar lá um
WhatsApp vai mandar um sms e vai mandar
um e-mail né aí vai aguardar 5 segundos
E aí vai pro segundo e assim
sucessivamente
tá é claro que né se por exemplo aqui
fosse por exemplo um envio de imagem né
no WhatsApp eu ainda poderia ir lá no
meu carrega imagem né eu nem sei se tem
como ver daqui
Ah tá porque não não está conectado mas
eu poderia muito bem Ah deixa eu ver
aqui eu poderia muito bem usar um merge
aqui né
aqui eu poderia
colocar imagem
né E aqui na
lista posso botar a lista
né aqui seria a lista né já que eu tô
alterando
lá ok e por exemplo aqui deixa eu até
cancelar aqui por exemplo aqui já de
imagem né e eu também poderia enviar
numa automação aqui por exemplo se fosse
só o WhatsApp né Por exemplo se eu
chegasse aqui
vamos fazer o seguinte eu
vou vou colocar aqui um set porque aí
fica mais fácil para entender
tá
vamos vou até botar aqui ó envia imagem
envia imagem WhatsApp tá
então por exemplo vamos dizer que isso
aqui fosse um http request né pra gente
poder
eh enviar ali uma imagem né da P
inclusive aí no canal da Auto tem um
monte de tutorial ensinando como você
fazer o envio né de texto ou mídia no
WhatsApp tá então isso aqui é só para
poder simular e então por exemplo Às
vezes a gente tem um campo lá por
exemplo Number né que é o número da
pessoa aí eu eu deixaria como string
mesmo tá mais um seria o aí poderia ser
né o o pef Né pef não perdão é que seria
a imagem né O pef que é o URL da imagem
tá e mais um aqui poderia ser o
caption que é a legenda que vai embaixo
da imagem né quando você manda por
exemplo uma imagem para alguém você já
pode mandar um texto ali embaixo nela
dela né ou seja em um uma única mensagem
vai o o vai o a imagem mais o comunicado
tá então aqui no caption eu poderia e
até mencionar né o nome da pessoa né
Quer ver ó só para para facilitar aqui
entendimento executar
aqui por exemplo aqui eu tenho um nome
né então eu poderia simplesmente pegar
aqui o nome
né poderia pegar o
nome e poderia colocar aqui ó Feliz
Aniversário Feliz Aniversário e aí o
nome da pessoa né E aí aqui embaixo eu
poderia colocar um uma mensagem mais
elaborada né de aniversário né põe aqui
a
mensagem completa de
aniversário OK aí aqui em Number né eu
poderia pegar aqui o WhatsApp do Lead né
E aqui em PF seria
eh a imagem né que eu vou estar enviando
ali para o o Lead tá Ahã
sendo o quê quando a gente fala de eh
envio de imagem por exemplo por pelo uma
page WhatsApp Geralmente as apis elas
utilizam eh o base 64 tá eh então por
exemplo eu não conseguiria utilizar a
imagem aqui em binário por quê para que
eu possa usar uma imagem em binário por
exemplo napp na page WhatsApp eu tenho
que colocar ali uma url dela uma url
remota tá como essa imagem el local eu
não consigo ter uma url aqui para poder
utilizar tá então para que você possa
usar ali um arquivo local para disparar
por exemplo para um WhatsApp você tem
que converter isso aqui em base 64 então
basicamente e eu adicionaria aqui o node
exct from file tá E aí escolhe essa
opção aqui ó que é move file to base 64
tá onde aqui em Destination vou colocar
base 64 e aqui aqui ó input binar Field
o nome da minha imagem Aliás o nome do
binário da imagem é imagem né resolvi
colocar como imagem mesmo
tá até posso botar aqui ó obtém o base
64 da
imagem por exemplo deixa até pausar
esse se eu executar aqui ó ele já vai
gerar o base4 para mim tá vendo ó
E aí sim né eu poderia por exemplo eh
chegar aqui
né poderia usar o merge
né no formato combine
eh por posição
tá eu vou colocar aqui ó
unifica unifica
datos conecta aqui PR que eu posso subir
aqui deixa até rodar aqui
pronto OK então além de ter ali né o
nome eh WhatsApp né do Lead eh para cada
um deles né eu teria ali
o o base M4 associado tá então eu
poderia chegar aqui em PF e simplesmente
arrastar aqui o base 64
tá ou
seja rodando Isso aqui
né Posso até diminuir o intervalo aqui
vou deixar Ah vou deixar 5 segundos
mesmo tá então com essa automação aqui
eu conseguiria enviar eh para cada Lead
dessa minha lista local ok uma mensagem
de feliz aniversário usando uma imagem
local também para cada Lead diretamente
do WhatsApp tá é claro que esse set aqui
como eu falei né ele está simulando um
http request
né então ele estaria rodando isso aqui
três vezes né bem que aqui calma aí ele
pegou um item
só ah não tá certo a que colocar aqui
todas as combinações possíveis né Por
quê Porque ele geraria três itens né e
cada item com o base 64 ali da da imagem
né ok Aqui só tá mostrando Um item mas
no caso ele ele gerou três né então isso
aqui é como ele rodasse três vezes tá
vendo ó então ele manda ali uma WhatsApp
com uma imagem né de feliz aniversário e
a mensagem de feliz aniversário pro Lead
um né Manda pro Lead dois também tá E aí
você vê que cada Lead aqui ó a mensagem
sai diferente né vai sair a mesma imagem
mas com dizer ali ó Feliz Aniversário
beltrano né Feliz Aniversário
Fulano Feliz Aniversário ciclano né ou
seja tudo isso usando uma lista e uma
imagem local ou seja diretamente do seu
computador sem você ter a necessidade
ali eh de subir isso em um servidor tá
então é isso aí eu espero que você tenha
gostado desse tutorial Não deixe de dar
like aí nesse vídeo né porque isso ajuda
o canal né o YouTube entende ali que é
um conteúdo eh interessante e ele vai
divulgar para mais pessoas tá e não
deixe de se inscrever no canal também tá
porque sempre que a gente lançar aí um
novo tutorial você já vai ficar por
dentro tá bom E isso também acaba aí o
nosso trabalho Beleza então Desde já
obrigado pela atenção e te vejo no
próximo tutorial valeu