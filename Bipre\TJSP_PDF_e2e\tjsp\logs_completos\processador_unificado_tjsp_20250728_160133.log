2025-07-28 16:01:33 [INFO] - criar_perfil_chrome_automatico - Perfil Chrome existente encontrado: C:/Users/<USER>/Profile_TJSP
2025-07-28 16:01:33 [INFO] - criar_perfil_chrome_automatico - Ambiente detectado: monte
2025-07-28 16:01:33 [INFO] - criar_perfil_chrome_automatico - Perfil Chrome: C:/Users/<USER>/Profile_TJSP
2025-07-28 16:01:33 [INFO] - criar_perfil_chrome_automatico - ChromeDriver: Gerenciamento automático
2025-07-28 16:01:33 [INFO] - detectar_ambiente - Ambiente detectado: monte
2025-07-28 16:01:33 [INFO] - detectar_ambiente - Perfil Chrome: C:/Users/<USER>/Profile_TJSP
2025-07-28 16:01:33 [INFO] - carregar_checkpoint - Checkpoint carregado: 43 registros consultados
2025-07-28 16:01:33 [INFO] - importar_modulos_download - <PERSON><PERSON><PERSON>lo tjsp_download.py importado com sucesso
2025-07-28 16:01:33 [INFO] - executar_processamento - Carregando números do arquivo: C:\Users\<USER>\Documents\Augment\Bipre\TJSP_PDF_e2e\tjsp\numeros_autos.xlsx
2025-07-28 16:01:33 [INFO] - carregar_numeros_excel - Carregando números de: C:\Users\<USER>\Documents\Augment\Bipre\TJSP_PDF_e2e\tjsp\numeros_autos.xlsx
2025-07-28 16:01:34 [INFO] - carregar_numeros_excel - Total números válidos carregados: 11405
2025-07-28 16:01:34 [INFO] - _determinar_numero_inicial_checkpoint - Checkpoint inteligente: usuário usou padrão, iniciando do número 44
2025-07-28 16:01:34 [INFO] - executar_processamento - Iniciando navegador Chrome com perfil específico
2025-07-28 16:01:34 [INFO] - iniciar_navegador - Iniciando navegador Chrome com perfil específico
2025-07-28 16:01:35 [INFO] - iniciar_navegador - Navegador iniciado com gerenciamento automático (método otimizado)
2025-07-28 16:01:35 [INFO] - executar_processamento - Iniciando processo de autenticação no TJSP
2025-07-28 16:01:35 [INFO] - autenticar_usuario - Iniciando autenticação automatizada com certificado digital.
2025-07-28 16:01:38 [INFO] - _verificar_autenticacao_existente - Verificando se já está autenticado
2025-07-28 16:01:39 [INFO] - _autenticar_certificado_automatico - Iniciando autenticação automática com certificado digital
2025-07-28 16:01:41 [INFO] - _autenticar_certificado_automatico - Aba Certificado Digital já está ativa
2025-07-28 16:01:43 [INFO] - _autenticar_certificado_automatico - Clicando no botão Entrar
2025-07-28 16:01:54 [INFO] - _verificar_sucesso_autenticacao - Autenticação automática bem-sucedida - Usuário: DENIS HENRIQUE SOUSA OLIVEIRA
2025-07-28 16:01:54 [INFO] - executar_processamento - Verificando funcionamento da sessão após autenticação
2025-07-28 16:01:54 [INFO] - verificar_sessao_ativa - Verificando se a sessão ainda está ativa
2025-07-28 16:01:54 [INFO] - verificar_sessao_ativa - Já na página de consulta
2025-07-28 16:01:55 [INFO] - verificar_sessao_ativa - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:01:55 [INFO] - _detectar_sessao_expirada - Nome de usuário válido detectado: 'DENIS HENRIQUE SOUSA OLIVEIRA' - sessão ativa
2025-07-28 16:01:55 [INFO] - verificar_sessao_ativa - Sessão ativa confirmada - Usuário: DENIS HENRIQUE SOUSA OLIVEIRA
2025-07-28 16:01:55 [INFO] - executar_processamento - Processando números 44 até 11405 - Já consultados: 43, Restantes: 11362
2025-07-28 16:01:55 [INFO] - processar_numero_autos - Processando número 44/11405: 0108775-43.2006.8.26.0053
2025-07-28 16:01:55 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:01:55 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:01:55 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:01:55 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:01:57 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:01:57 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:01:57 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:01:57 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:01:57 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:01:57 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:01:59 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:01:59 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:01:59 [INFO] - consultar_processo_principal - Consultando 0108775-43.2006.8.26.0053 (TJSP: 0108775432006 / Foro: 0053)
2025-07-28 16:02:02 [INFO] - consultar_processo_principal - Lista de resultados para 0108775-43.2006.8.26.0053.
2025-07-28 16:02:02 [INFO] - consultar_processo_principal - Link para 0108775-43.2006.8.26.0053 encontrado. Clicando...
2025-07-28 16:02:04 [INFO] - consultar_processo_principal - Detalhes de 0108775-43.2006.8.26.0053 carregados após clique.
2025-07-28 16:02:04 [INFO] - verificar_status_processo - Status 'finalizado' (texto da página).
2025-07-28 16:02:04 [INFO] - processar_numero_autos - Processo principal 0108775-43.2006.8.26.0053 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 16:02:04 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 16:02:04 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Lucinda Gomes Carlos (lower: lucinda gomes carlos)
2025-07-28 16:02:04 [INFO] - processar_numero_autos - Processo principal 0108775-43.2006.8.26.0053 sem partes proibidas
2025-07-28 16:02:05 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 16:02:06 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento
2025-07-28 16:02:06 [INFO] - processar_numero_autos - Processo principal 0108775-43.2006.8.26.0053 com Palavra Proibida em Movimentações - Continuando para precatórios (filtro removido)
2025-07-28 16:02:06 [INFO] - processar_numero_autos - Processo principal 0108775-43.2006.8.26.0053 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 16:02:08 [INFO] - processar_numero_autos - Nenhum link de precatório (seletor principal) para 0108775-43.2006.8.26.0053. Tentando fallback...
2025-07-28 16:02:10 [INFO] - processar_numero_autos - Nenhum link 'Precatório' clicável para 0108775-43.2006.8.26.0053.
2025-07-28 16:02:10 [INFO] - processar_numero_autos - Processando número 45/11405: 5000310-88.2014.8.26.0014
2025-07-28 16:02:10 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:02:10 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 16:02:13 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 16:02:13 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:02:13 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:02:15 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:02:15 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:02:15 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:02:15 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:02:15 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:02:15 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:02:17 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:02:17 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:02:17 [INFO] - consultar_processo_principal - Consultando 5000310-88.2014.8.26.0014 (TJSP: 5000310882014 / Foro: 0014)
2025-07-28 16:02:18 [INFO] - consultar_processo_principal - Lista de resultados para 5000310-88.2014.8.26.0014.
2025-07-28 16:02:18 [INFO] - consultar_processo_principal - Link para 5000310-88.2014.8.26.0014 encontrado. Clicando...
2025-07-28 16:02:20 [INFO] - consultar_processo_principal - Detalhes de 5000310-88.2014.8.26.0014 carregados após clique.
2025-07-28 16:02:20 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 16:02:20 [INFO] - processar_numero_autos - Processo principal 5000310-88.2014.8.26.0014 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 16:02:20 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 16:02:20 [ERROR] - verificar_partes_processo - Reqte não localizado para verificação de partes.
2025-07-28 16:02:20 [INFO] - processar_numero_autos - Processo principal 5000310-88.2014.8.26.0014 sem partes proibidas
2025-07-28 16:02:21 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 16:02:22 [INFO] - verificar_palavras_proibidas_mov - Nenhuma palavra proibida.
2025-07-28 16:02:22 [INFO] - processar_numero_autos - Processo principal 5000310-88.2014.8.26.0014 sem palavras proibidas
2025-07-28 16:02:22 [INFO] - processar_numero_autos - Processo principal 5000310-88.2014.8.26.0014 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 16:02:24 [INFO] - processar_numero_autos - Nenhum link de precatório (seletor principal) para 5000310-88.2014.8.26.0014. Tentando fallback...
2025-07-28 16:02:26 [INFO] - processar_numero_autos - Nenhum link 'Precatório' clicável para 5000310-88.2014.8.26.0014.
2025-07-28 16:02:26 [INFO] - processar_numero_autos - Processando número 46/11405: 0013612-41.2003.8.26.0053
2025-07-28 16:02:26 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:02:26 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 16:02:29 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 16:02:29 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:02:29 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:02:31 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:02:31 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:02:31 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:02:31 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:02:31 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:02:31 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:02:33 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:02:33 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:02:33 [INFO] - consultar_processo_principal - Consultando 0013612-41.2003.8.26.0053 (TJSP: 0013612412003 / Foro: 0053)
2025-07-28 16:02:34 [INFO] - consultar_processo_principal - Lista de resultados para 0013612-41.2003.8.26.0053.
2025-07-28 16:02:34 [INFO] - consultar_processo_principal - Link para 0013612-41.2003.8.26.0053 encontrado. Clicando...
2025-07-28 16:02:36 [INFO] - consultar_processo_principal - Detalhes de 0013612-41.2003.8.26.0053 carregados após clique.
2025-07-28 16:02:36 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 16:02:36 [INFO] - processar_numero_autos - Processo principal 0013612-41.2003.8.26.0053 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 16:02:36 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 16:02:36 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Concremat Engenharia S/A (lower: concremat engenharia s/a)
2025-07-28 16:02:36 [INFO] - verificar_partes_processo - Parte proibida (PJ) encontrada no Reqte: 's/a' em 'Concremat Engenharia S/A'
2025-07-28 16:02:36 [INFO] - processar_numero_autos - Processo principal 0013612-41.2003.8.26.0053 com Parte Proibida - Continuando para precatórios (filtro removido)
2025-07-28 16:02:37 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 16:02:38 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento
2025-07-28 16:02:38 [INFO] - processar_numero_autos - Processo principal 0013612-41.2003.8.26.0053 com Palavra Proibida em Movimentações - Continuando para precatórios (filtro removido)
2025-07-28 16:02:38 [INFO] - processar_numero_autos - Processo principal 0013612-41.2003.8.26.0053 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 16:02:38 [INFO] - processar_numero_autos - Links de precatório encontrados com seletor principal para 0013612-41.2003.8.26.0053.
2025-07-28 16:02:38 [INFO] - processar_numero_autos - 1 link(s) 'Precatório' para 0013612-41.2003.8.26.0053.
2025-07-28 16:02:40 [INFO] - processar_numero_autos - Processando link Precatório 1/1: 'Precatório - 00002'
2025-07-28 16:02:43 [INFO] - extrair_numero_precatorio_completo_da_pagina - Extraindo N° precatório para principal: 0013612-41.2003.8.26.0053
2025-07-28 16:02:43 [INFO] - extrair_numero_precatorio_completo_da_pagina - N° precatório não encontrado via span.unj-larger ou N° base divergente. Usar fallback do texto do link.
2025-07-28 16:02:43 [INFO] - processar_numero_autos - N° precatório formado via texto do link (fallback principal): 0013612-41.2003.8.26.0053 (02)
2025-07-28 16:02:43 [INFO] - obter_nome_cliente_do_precatorio - Buscando nome do cliente (Reqte)...
2025-07-28 16:02:43 [INFO] - obter_nome_cliente_do_precatorio - Nome do cliente: Concremat Engenharia S/A
2025-07-28 16:02:43 [INFO] - verificar_status_processo - Status: normal/em andamento.
2025-07-28 16:02:43 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 16:02:43 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Concremat Engenharia S/A (lower: concremat engenharia s/a)
2025-07-28 16:02:43 [INFO] - verificar_partes_processo - Parte proibida (PJ) encontrada no Reqte: 's/a' em 'Concremat Engenharia S/A'
2025-07-28 16:02:43 [INFO] - processar_numero_autos - Precatório '0013612-41.2003.8.26.0053 (02)' com Parte Proibida - pulando
2025-07-28 16:02:43 [INFO] - processar_numero_autos - Precatório rejeitado: 0013612-41.2003.8.26.0053 (02) - Cliente: Concremat Engenharia S/A - Motivo: Parte proibida (PJ)
2025-07-28 16:02:43 [INFO] - processar_numero_autos - FIM NÚMERO 46 (Tempo: 16.88s)
2025-07-28 16:02:44 [INFO] - processar_numero_autos - Processando número 47/11405: 0413621-50.1994.8.26.0053
2025-07-28 16:02:44 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:02:44 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 16:02:46 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 16:02:46 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:02:46 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:02:48 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:02:48 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:02:48 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:02:48 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:02:48 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:02:48 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:02:50 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:02:50 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:02:50 [INFO] - consultar_processo_principal - Consultando 0413621-50.1994.8.26.0053 (TJSP: 0413621501994 / Foro: 0053)
2025-07-28 16:02:58 [INFO] - consultar_processo_principal - Lista de resultados para 0413621-50.1994.8.26.0053.
2025-07-28 16:02:58 [INFO] - consultar_processo_principal - Link para 0413621-50.1994.8.26.0053 encontrado. Clicando...
2025-07-28 16:03:00 [INFO] - consultar_processo_principal - Detalhes de 0413621-50.1994.8.26.0053 carregados após clique.
2025-07-28 16:03:00 [INFO] - verificar_status_processo - Status 'finalizado' (texto da página).
2025-07-28 16:03:00 [INFO] - processar_numero_autos - Processo principal 0413621-50.1994.8.26.0053 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 16:03:00 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 16:03:00 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Refrescos Ipiranga S.a. (lower: refrescos ipiranga s.a.)
2025-07-28 16:03:00 [INFO] - verificar_partes_processo - Parte proibida (PJ) encontrada no Reqte: 's.a' em 'Refrescos Ipiranga S.a.'
2025-07-28 16:03:00 [INFO] - processar_numero_autos - Processo principal 0413621-50.1994.8.26.0053 com Parte Proibida - Continuando para precatórios (filtro removido)
2025-07-28 16:03:01 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 16:03:02 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento
2025-07-28 16:03:02 [INFO] - processar_numero_autos - Processo principal 0413621-50.1994.8.26.0053 com Palavra Proibida em Movimentações - Continuando para precatórios (filtro removido)
2025-07-28 16:03:02 [INFO] - processar_numero_autos - Processo principal 0413621-50.1994.8.26.0053 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 16:03:04 [INFO] - processar_numero_autos - Nenhum link de precatório (seletor principal) para 0413621-50.1994.8.26.0053. Tentando fallback...
2025-07-28 16:03:06 [INFO] - processar_numero_autos - Nenhum link 'Precatório' clicável para 0413621-50.1994.8.26.0053.
2025-07-28 16:03:06 [INFO] - processar_numero_autos - Processando número 48/11405: 0914146-10.1973.8.26.0053
2025-07-28 16:03:06 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:03:06 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 16:03:09 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 16:03:09 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:03:09 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:03:11 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:03:11 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:03:11 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:03:11 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:03:11 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:03:11 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:03:13 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:03:13 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:03:13 [INFO] - consultar_processo_principal - Consultando 0914146-10.1973.8.26.0053 (TJSP: 0914146101973 / Foro: 0053)
2025-07-28 16:03:15 [INFO] - consultar_processo_principal - Lista de resultados para 0914146-10.1973.8.26.0053.
2025-07-28 16:03:15 [INFO] - consultar_processo_principal - Link para 0914146-10.1973.8.26.0053 encontrado. Clicando...
2025-07-28 16:03:17 [INFO] - consultar_processo_principal - Detalhes de 0914146-10.1973.8.26.0053 carregados após clique.
2025-07-28 16:03:17 [INFO] - verificar_status_processo - Status 'finalizado' (texto da página).
2025-07-28 16:03:17 [INFO] - processar_numero_autos - Processo principal 0914146-10.1973.8.26.0053 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 16:03:17 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 16:03:17 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Fazenda Pública do Estado de São Paulo (lower: fazenda pública do estado de são paulo)
2025-07-28 16:03:17 [INFO] - verificar_partes_processo - Parte proibida (PJ) encontrada no Reqte: 'fazenda' em 'Fazenda Pública do Estado de São Paulo'
2025-07-28 16:03:17 [INFO] - processar_numero_autos - Processo principal 0914146-10.1973.8.26.0053 com Parte Proibida - Continuando para precatórios (filtro removido)
2025-07-28 16:03:18 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 16:03:19 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento
2025-07-28 16:03:19 [INFO] - processar_numero_autos - Processo principal 0914146-10.1973.8.26.0053 com Palavra Proibida em Movimentações - Continuando para precatórios (filtro removido)
2025-07-28 16:03:19 [INFO] - processar_numero_autos - Processo principal 0914146-10.1973.8.26.0053 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 16:03:21 [INFO] - processar_numero_autos - Nenhum link de precatório (seletor principal) para 0914146-10.1973.8.26.0053. Tentando fallback...
2025-07-28 16:03:23 [INFO] - processar_numero_autos - Nenhum link 'Precatório' clicável para 0914146-10.1973.8.26.0053.
2025-07-28 16:03:24 [INFO] - processar_numero_autos - Processando número 49/11405: 0414679-59.1992.8.26.0053
2025-07-28 16:03:24 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:03:24 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 16:03:26 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 16:03:26 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:03:26 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:03:28 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:03:28 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:03:28 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:03:28 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:03:28 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:03:28 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:03:30 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:03:30 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:03:30 [INFO] - consultar_processo_principal - Consultando 0414679-59.1992.8.26.0053 (TJSP: 0414679591992 / Foro: 0053)
2025-07-28 16:03:32 [INFO] - consultar_processo_principal - Lista de resultados para 0414679-59.1992.8.26.0053.
2025-07-28 16:03:32 [INFO] - consultar_processo_principal - Link para 0414679-59.1992.8.26.0053 encontrado. Clicando...
2025-07-28 16:03:34 [INFO] - consultar_processo_principal - Detalhes de 0414679-59.1992.8.26.0053 carregados após clique.
2025-07-28 16:03:34 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 16:03:34 [INFO] - processar_numero_autos - Processo principal 0414679-59.1992.8.26.0053 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 16:03:34 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 16:03:34 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Hellen Cristina Nunes Ferreira (lower: hellen cristina nunes ferreira)
2025-07-28 16:03:34 [INFO] - processar_numero_autos - Processo principal 0414679-59.1992.8.26.0053 sem partes proibidas
2025-07-28 16:03:34 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 16:03:36 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento
2025-07-28 16:03:36 [INFO] - processar_numero_autos - Processo principal 0414679-59.1992.8.26.0053 com Palavra Proibida em Movimentações - Continuando para precatórios (filtro removido)
2025-07-28 16:03:36 [INFO] - processar_numero_autos - Processo principal 0414679-59.1992.8.26.0053 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 16:03:38 [INFO] - processar_numero_autos - Nenhum link de precatório (seletor principal) para 0414679-59.1992.8.26.0053. Tentando fallback...
2025-07-28 16:03:39 [INFO] - processar_numero_autos - Nenhum link 'Precatório' clicável para 0414679-59.1992.8.26.0053.
2025-07-28 16:03:40 [INFO] - verificar_sessao_ativa - Verificando se a sessão ainda está ativa
2025-07-28 16:03:40 [INFO] - verificar_sessao_ativa - Navegando para página de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 16:03:43 [INFO] - verificar_sessao_ativa - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:03:43 [INFO] - _detectar_sessao_expirada - Nome de usuário válido detectado: 'DENIS HENRIQUE SOUSA OLIVEIRA' - sessão ativa
2025-07-28 16:03:43 [INFO] - verificar_sessao_ativa - Sessão ativa confirmada - Usuário: DENIS HENRIQUE SOUSA OLIVEIRA
2025-07-28 16:03:43 [INFO] - processar_numero_autos - Processando número 50/11405: 0002741-70.2001.8.26.0198
2025-07-28 16:03:43 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:03:43 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:03:43 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:03:43 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:03:45 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:03:45 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:03:45 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:03:45 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:03:45 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:03:45 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:03:47 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:03:47 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:03:47 [INFO] - consultar_processo_principal - Consultando 0002741-70.2001.8.26.0198 (TJSP: 0002741702001 / Foro: 0198)
2025-07-28 16:04:03 [INFO] - consultar_processo_principal - Lista de resultados para 0002741-70.2001.8.26.0198.
2025-07-28 16:04:03 [INFO] - consultar_processo_principal - Link para 0002741-70.2001.8.26.0198 encontrado. Clicando...
2025-07-28 16:04:10 [INFO] - consultar_processo_principal - Detalhes de 0002741-70.2001.8.26.0198 carregados após clique.
2025-07-28 16:04:10 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 16:04:10 [INFO] - processar_numero_autos - Processo principal 0002741-70.2001.8.26.0198 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 16:04:10 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 16:04:10 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Maria Castanho Lourenco (lower: maria castanho lourenco)
2025-07-28 16:04:10 [INFO] - processar_numero_autos - Processo principal 0002741-70.2001.8.26.0198 sem partes proibidas
2025-07-28 16:04:10 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 16:04:12 [INFO] - verificar_palavras_proibidas_mov - Nenhuma palavra proibida.
2025-07-28 16:04:12 [INFO] - processar_numero_autos - Processo principal 0002741-70.2001.8.26.0198 sem palavras proibidas
2025-07-28 16:04:12 [INFO] - processar_numero_autos - Processo principal 0002741-70.2001.8.26.0198 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 16:04:12 [INFO] - processar_numero_autos - Links de precatório encontrados com seletor principal para 0002741-70.2001.8.26.0198.
2025-07-28 16:04:12 [INFO] - processar_numero_autos - 1 link(s) 'Precatório' para 0002741-70.2001.8.26.0198.
2025-07-28 16:04:14 [INFO] - processar_numero_autos - Processando link Precatório 1/1: 'Precatório - 00001'
2025-07-28 16:04:16 [INFO] - extrair_numero_precatorio_completo_da_pagina - Extraindo N° precatório para principal: 0002741-70.2001.8.26.0198
2025-07-28 16:04:16 [INFO] - extrair_numero_precatorio_completo_da_pagina - N° precatório não encontrado via span.unj-larger ou N° base divergente. Usar fallback do texto do link.
2025-07-28 16:04:16 [INFO] - processar_numero_autos - N° precatório formado via texto do link (fallback principal): 0002741-70.2001.8.26.0198 (01)
2025-07-28 16:04:16 [INFO] - obter_nome_cliente_do_precatorio - Buscando nome do cliente (Reqte)...
2025-07-28 16:04:16 [ERROR] - obter_nome_cliente_do_precatorio - Nome do cliente não localizado.
2025-07-28 16:04:16 [INFO] - verificar_status_processo - Status: normal/em andamento.
2025-07-28 16:04:16 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 16:04:16 [ERROR] - verificar_partes_processo - Reqte não localizado para verificação de partes.
2025-07-28 16:04:17 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 16:04:18 [INFO] - verificar_palavras_proibidas_mov - Nenhuma palavra proibida.
2025-07-28 16:04:18 [INFO] - processar_numero_autos - Precatório '0002741-70.2001.8.26.0198 (01)' validado - tentando baixar ofício
2025-07-28 16:04:18 [INFO] - encontrar_oficio_requisitorio - Procurando 'Ofício Requisitório-Precatório Expedido'...
2025-07-28 16:04:23 [ERROR] - encontrar_oficio_requisitorio - Link exato não encontrado/clicável.
2025-07-28 16:04:24 [INFO] - processar_numero_autos - FIM NÚMERO 50 (Tempo: 40.62s)
2025-07-28 16:04:24 [INFO] - processar_numero_autos - Processando número 51/11405: 0010613-48.2002.8.26.0604
2025-07-28 16:04:24 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:04:24 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 16:04:26 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 16:04:26 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:04:26 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:04:28 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:04:28 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:04:28 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:04:28 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:04:28 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:04:28 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:04:31 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:04:31 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:04:31 [INFO] - consultar_processo_principal - Consultando 0010613-48.2002.8.26.0604 (TJSP: 0010613482002 / Foro: 0604)
2025-07-28 16:04:32 [INFO] - consultar_processo_principal - Lista de resultados para 0010613-48.2002.8.26.0604.
2025-07-28 16:04:32 [INFO] - consultar_processo_principal - Link para 0010613-48.2002.8.26.0604 encontrado. Clicando...
2025-07-28 16:04:34 [INFO] - consultar_processo_principal - Detalhes de 0010613-48.2002.8.26.0604 carregados após clique.
2025-07-28 16:04:34 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 16:04:34 [INFO] - processar_numero_autos - Processo principal 0010613-48.2002.8.26.0604 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 16:04:34 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 16:04:34 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Luiz Moraes Machado (lower: luiz moraes machado)
2025-07-28 16:04:34 [INFO] - processar_numero_autos - Processo principal 0010613-48.2002.8.26.0604 sem partes proibidas
2025-07-28 16:04:35 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 16:04:36 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento
2025-07-28 16:04:36 [INFO] - processar_numero_autos - Processo principal 0010613-48.2002.8.26.0604 com Palavra Proibida em Movimentações - Continuando para precatórios (filtro removido)
2025-07-28 16:04:36 [INFO] - processar_numero_autos - Processo principal 0010613-48.2002.8.26.0604 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 16:04:38 [INFO] - processar_numero_autos - Nenhum link de precatório (seletor principal) para 0010613-48.2002.8.26.0604. Tentando fallback...
2025-07-28 16:04:40 [INFO] - processar_numero_autos - Nenhum link 'Precatório' clicável para 0010613-48.2002.8.26.0604.
2025-07-28 16:04:41 [INFO] - processar_numero_autos - Processando número 52/11405: 0000436-98.2002.8.26.0224
2025-07-28 16:04:41 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:04:41 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 16:04:43 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 16:04:43 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:04:43 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:04:45 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:04:45 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:04:45 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:04:45 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:04:45 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:04:45 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:04:47 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:04:47 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:04:47 [INFO] - consultar_processo_principal - Consultando 0000436-98.2002.8.26.0224 (TJSP: 0000436982002 / Foro: 0224)
2025-07-28 16:04:51 [INFO] - consultar_processo_principal - Detalhes de 0000436-98.2002.8.26.0224 carregados diretamente.
2025-07-28 16:04:51 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 16:04:51 [INFO] - processar_numero_autos - Processo principal 0000436-98.2002.8.26.0224 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 16:04:51 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 16:04:51 [ERROR] - verificar_partes_processo - Reqte não localizado para verificação de partes.
2025-07-28 16:04:51 [INFO] - processar_numero_autos - Processo principal 0000436-98.2002.8.26.0224 sem partes proibidas
2025-07-28 16:04:52 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 16:04:53 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento
2025-07-28 16:04:53 [INFO] - processar_numero_autos - Processo principal 0000436-98.2002.8.26.0224 com Palavra Proibida em Movimentações - Continuando para precatórios (filtro removido)
2025-07-28 16:04:53 [INFO] - processar_numero_autos - Processo principal 0000436-98.2002.8.26.0224 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 16:04:56 [INFO] - processar_numero_autos - Nenhum link de precatório (seletor principal) para 0000436-98.2002.8.26.0224. Tentando fallback...
2025-07-28 16:04:57 [INFO] - processar_numero_autos - Nenhum link 'Precatório' clicável para 0000436-98.2002.8.26.0224.
2025-07-28 16:04:58 [INFO] - processar_numero_autos - Processando número 53/11405: 0020971-42.2003.8.26.0053
2025-07-28 16:04:58 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:04:58 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 16:05:00 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 16:05:00 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:05:00 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:05:02 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:05:02 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:05:02 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:05:02 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:05:02 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:05:02 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:05:04 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:05:04 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:05:04 [INFO] - consultar_processo_principal - Consultando 0020971-42.2003.8.26.0053 (TJSP: 0020971422003 / Foro: 0053)
2025-07-28 16:05:06 [INFO] - consultar_processo_principal - Lista de resultados para 0020971-42.2003.8.26.0053.
2025-07-28 16:05:06 [INFO] - consultar_processo_principal - Link para 0020971-42.2003.8.26.0053 encontrado. Clicando...
2025-07-28 16:05:08 [INFO] - consultar_processo_principal - Detalhes de 0020971-42.2003.8.26.0053 carregados após clique.
2025-07-28 16:05:08 [INFO] - verificar_status_processo - Status 'finalizado' detectado: relação: 0507/2024 teor do ato: vistos nada mais havendo para o precatório ep/processo depre nº 7004897-53.2013.8.26.0500, pois quitada a integralidade do crédito requisitado em favor de maria regina bueno, julgo extinto o processo com relação aos seus credores, nos termos do artigo 924, inciso ii, do código de processo civil. considerando que não há interesse recursal das partes, o trânsito em julgado deve ser considerado a partir da presente decisão. expeça-se ofício à depre para as devidas providências quanto à extinção do precatório 7004897-53.2013.8.26.0500. após, providencie a serventia judicial a baixa do presente feito movimentação 61615 arquivado definitivamente. p.r.i.c. advogados(s): odair leal serotini (oab 133605/sp)
2025-07-28 16:05:08 [INFO] - processar_numero_autos - Processo principal 0020971-42.2003.8.26.0053 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 16:05:08 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 16:05:08 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Maria Regina Bueno (lower: maria regina bueno)
2025-07-28 16:05:08 [INFO] - processar_numero_autos - Processo principal 0020971-42.2003.8.26.0053 sem partes proibidas
2025-07-28 16:05:09 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 16:05:10 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento eletrônico
2025-07-28 16:05:10 [INFO] - processar_numero_autos - Processo principal 0020971-42.2003.8.26.0053 com Palavra Proibida em Movimentações - Continuando para precatórios (filtro removido)
2025-07-28 16:05:10 [INFO] - processar_numero_autos - Processo principal 0020971-42.2003.8.26.0053 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 16:05:12 [INFO] - processar_numero_autos - Nenhum link de precatório (seletor principal) para 0020971-42.2003.8.26.0053. Tentando fallback...
2025-07-28 16:05:14 [INFO] - processar_numero_autos - Nenhum link 'Precatório' clicável para 0020971-42.2003.8.26.0053.
2025-07-28 16:05:15 [INFO] - processar_numero_autos - Processando número 54/11405: 0019213-28.2003.8.26.0053
2025-07-28 16:05:15 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:05:15 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 16:05:17 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 16:05:17 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:05:17 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:05:19 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:05:19 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:05:19 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:05:19 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:05:19 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:05:19 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:05:21 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:05:21 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:05:21 [INFO] - consultar_processo_principal - Consultando 0019213-28.2003.8.26.0053 (TJSP: 0019213282003 / Foro: 0053)
2025-07-28 16:05:23 [INFO] - consultar_processo_principal - Lista de resultados para 0019213-28.2003.8.26.0053.
2025-07-28 16:05:23 [INFO] - consultar_processo_principal - Link para 0019213-28.2003.8.26.0053 encontrado. Clicando...
2025-07-28 16:05:25 [INFO] - consultar_processo_principal - Detalhes de 0019213-28.2003.8.26.0053 carregados após clique.
2025-07-28 16:05:25 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 16:05:25 [INFO] - processar_numero_autos - Processo principal 0019213-28.2003.8.26.0053 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 16:05:25 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 16:05:25 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Flavio Manzatto (lower: flavio manzatto)
2025-07-28 16:05:25 [INFO] - processar_numero_autos - Processo principal 0019213-28.2003.8.26.0053 sem partes proibidas
2025-07-28 16:05:26 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 16:05:27 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento
2025-07-28 16:05:27 [INFO] - processar_numero_autos - Processo principal 0019213-28.2003.8.26.0053 com Palavra Proibida em Movimentações - Continuando para precatórios (filtro removido)
2025-07-28 16:05:27 [INFO] - processar_numero_autos - Processo principal 0019213-28.2003.8.26.0053 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 16:05:29 [INFO] - processar_numero_autos - Nenhum link de precatório (seletor principal) para 0019213-28.2003.8.26.0053. Tentando fallback...
2025-07-28 16:05:31 [INFO] - processar_numero_autos - Nenhum link 'Precatório' clicável para 0019213-28.2003.8.26.0053.
2025-07-28 16:05:32 [INFO] - processar_numero_autos - Processando número 55/11405: 0014692-27.2010.8.26.0269
2025-07-28 16:05:32 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:05:32 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 16:05:34 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 16:05:34 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:05:34 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:05:36 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:05:36 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:05:36 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:05:36 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:05:36 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:05:36 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:05:38 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:05:38 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:05:38 [INFO] - consultar_processo_principal - Consultando 0014692-27.2010.8.26.0269 (TJSP: 0014692272010 / Foro: 0269)
2025-07-28 16:05:40 [INFO] - consultar_processo_principal - Detalhes de 0014692-27.2010.8.26.0269 carregados diretamente.
2025-07-28 16:05:40 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 16:05:40 [INFO] - processar_numero_autos - Processo principal 0014692-27.2010.8.26.0269 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 16:05:40 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 16:05:40 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Edinéia de Oliveira (lower: edinéia de oliveira)
2025-07-28 16:05:40 [INFO] - processar_numero_autos - Processo principal 0014692-27.2010.8.26.0269 sem partes proibidas
2025-07-28 16:05:41 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 16:05:42 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento eletrônico
2025-07-28 16:05:42 [INFO] - processar_numero_autos - Processo principal 0014692-27.2010.8.26.0269 com Palavra Proibida em Movimentações - Continuando para precatórios (filtro removido)
2025-07-28 16:05:42 [INFO] - processar_numero_autos - Processo principal 0014692-27.2010.8.26.0269 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 16:05:44 [INFO] - processar_numero_autos - Nenhum link de precatório (seletor principal) para 0014692-27.2010.8.26.0269. Tentando fallback...
2025-07-28 16:05:46 [INFO] - processar_numero_autos - Nenhum link 'Precatório' clicável para 0014692-27.2010.8.26.0269.
2025-07-28 16:05:47 [INFO] - processar_numero_autos - Processando número 56/11405: 0029268-67.2005.8.26.0053
2025-07-28 16:05:47 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:05:47 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 16:05:49 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 16:05:49 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:05:49 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:05:51 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:05:51 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:05:51 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:05:51 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:05:51 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:05:51 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:05:53 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:05:53 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:05:53 [INFO] - consultar_processo_principal - Consultando 0029268-67.2005.8.26.0053 (TJSP: 0029268672005 / Foro: 0053)
2025-07-28 16:05:55 [INFO] - consultar_processo_principal - Lista de resultados para 0029268-67.2005.8.26.0053.
2025-07-28 16:05:55 [INFO] - consultar_processo_principal - Link para 0029268-67.2005.8.26.0053 encontrado. Clicando...
2025-07-28 16:05:57 [INFO] - consultar_processo_principal - Detalhes de 0029268-67.2005.8.26.0053 carregados após clique.
2025-07-28 16:05:57 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 16:05:57 [INFO] - processar_numero_autos - Processo principal 0029268-67.2005.8.26.0053 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 16:05:57 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 16:05:57 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Claudio Moraes da Silva (lower: claudio moraes da silva)
2025-07-28 16:05:57 [INFO] - processar_numero_autos - Processo principal 0029268-67.2005.8.26.0053 sem partes proibidas
2025-07-28 16:05:57 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 16:05:59 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento
2025-07-28 16:05:59 [INFO] - processar_numero_autos - Processo principal 0029268-67.2005.8.26.0053 com Palavra Proibida em Movimentações - Continuando para precatórios (filtro removido)
2025-07-28 16:05:59 [INFO] - processar_numero_autos - Processo principal 0029268-67.2005.8.26.0053 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 16:06:01 [INFO] - processar_numero_autos - Nenhum link de precatório (seletor principal) para 0029268-67.2005.8.26.0053. Tentando fallback...
2025-07-28 16:06:03 [INFO] - processar_numero_autos - Nenhum link 'Precatório' clicável para 0029268-67.2005.8.26.0053.
2025-07-28 16:06:03 [INFO] - processar_numero_autos - Processando número 57/11405: 0021572-53.2000.8.26.0053
2025-07-28 16:06:03 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:06:03 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 16:06:06 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 16:06:06 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:06:06 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:06:08 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:06:08 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:06:08 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:06:08 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:06:08 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:06:08 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:06:10 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:06:10 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:06:10 [INFO] - consultar_processo_principal - Consultando 0021572-53.2000.8.26.0053 (TJSP: 0021572532000 / Foro: 0053)
2025-07-28 16:06:12 [INFO] - consultar_processo_principal - Lista de resultados para 0021572-53.2000.8.26.0053.
2025-07-28 16:06:12 [INFO] - consultar_processo_principal - Link para 0021572-53.2000.8.26.0053 encontrado. Clicando...
2025-07-28 16:06:23 [INFO] - consultar_processo_principal - Detalhes de 0021572-53.2000.8.26.0053 carregados após clique.
2025-07-28 16:06:23 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 16:06:23 [INFO] - processar_numero_autos - Processo principal 0021572-53.2000.8.26.0053 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 16:06:23 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 16:06:23 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Adalgisa Raimunda da Silva (lower: adalgisa raimunda da silva)
2025-07-28 16:06:23 [INFO] - processar_numero_autos - Processo principal 0021572-53.2000.8.26.0053 sem partes proibidas
2025-07-28 16:06:23 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 16:06:25 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento
2025-07-28 16:06:25 [INFO] - processar_numero_autos - Processo principal 0021572-53.2000.8.26.0053 com Palavra Proibida em Movimentações - Continuando para precatórios (filtro removido)
2025-07-28 16:06:25 [INFO] - processar_numero_autos - Processo principal 0021572-53.2000.8.26.0053 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 16:06:27 [INFO] - processar_numero_autos - Nenhum link de precatório (seletor principal) para 0021572-53.2000.8.26.0053. Tentando fallback...
2025-07-28 16:06:28 [INFO] - processar_numero_autos - Nenhum link 'Precatório' clicável para 0021572-53.2000.8.26.0053.
2025-07-28 16:06:29 [INFO] - processar_numero_autos - Processando número 58/11405: 0009037-71.2005.8.26.0650
2025-07-28 16:06:29 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:06:29 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 16:06:31 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 16:06:31 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:06:31 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:06:33 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:06:33 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:06:33 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:06:33 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:06:33 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:06:33 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:06:35 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:06:35 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:06:35 [INFO] - consultar_processo_principal - Consultando 0009037-71.2005.8.26.0650 (TJSP: 0009037712005 / Foro: 0650)
2025-07-28 16:06:37 [INFO] - consultar_processo_principal - Lista de resultados para 0009037-71.2005.8.26.0650.
2025-07-28 16:06:37 [INFO] - consultar_processo_principal - Link para 0009037-71.2005.8.26.0650 encontrado. Clicando...
2025-07-28 16:06:40 [INFO] - consultar_processo_principal - Detalhes de 0009037-71.2005.8.26.0650 carregados após clique.
2025-07-28 16:06:40 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 16:06:40 [INFO] - processar_numero_autos - Processo principal 0009037-71.2005.8.26.0650 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 16:06:40 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 16:06:40 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Maria Isabel dos Santos (lower: maria isabel dos santos)
2025-07-28 16:06:40 [INFO] - processar_numero_autos - Processo principal 0009037-71.2005.8.26.0650 sem partes proibidas
2025-07-28 16:06:40 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 16:06:42 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento
2025-07-28 16:06:42 [INFO] - processar_numero_autos - Processo principal 0009037-71.2005.8.26.0650 com Palavra Proibida em Movimentações - Continuando para precatórios (filtro removido)
2025-07-28 16:06:42 [INFO] - processar_numero_autos - Processo principal 0009037-71.2005.8.26.0650 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 16:06:44 [INFO] - processar_numero_autos - Nenhum link de precatório (seletor principal) para 0009037-71.2005.8.26.0650. Tentando fallback...
2025-07-28 16:06:46 [INFO] - processar_numero_autos - Nenhum link 'Precatório' clicável para 0009037-71.2005.8.26.0650.
2025-07-28 16:06:46 [INFO] - processar_numero_autos - Processando número 59/11405: 0002429-44.2001.8.26.0053
2025-07-28 16:06:46 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:06:46 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 16:06:48 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 16:06:48 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:06:48 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:06:50 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:06:50 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:06:51 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:06:51 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:06:51 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:06:51 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:06:53 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:06:53 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:06:53 [INFO] - consultar_processo_principal - Consultando 0002429-44.2001.8.26.0053 (TJSP: 0002429442001 / Foro: 0053)
2025-07-28 16:07:11 [INFO] - consultar_processo_principal - Lista de resultados para 0002429-44.2001.8.26.0053.
2025-07-28 16:07:11 [INFO] - consultar_processo_principal - Link para 0002429-44.2001.8.26.0053 encontrado. Clicando...
2025-07-28 16:07:14 [INFO] - consultar_processo_principal - Detalhes de 0002429-44.2001.8.26.0053 carregados após clique.
2025-07-28 16:07:14 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 16:07:14 [INFO] - processar_numero_autos - Processo principal 0002429-44.2001.8.26.0053 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 16:07:14 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 16:07:14 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Antonio Wanderley Marini (lower: antonio wanderley marini)
2025-07-28 16:07:14 [INFO] - processar_numero_autos - Processo principal 0002429-44.2001.8.26.0053 sem partes proibidas
2025-07-28 16:07:15 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 16:07:16 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento eletrônico
2025-07-28 16:07:16 [INFO] - processar_numero_autos - Processo principal 0002429-44.2001.8.26.0053 com Palavra Proibida em Movimentações - Continuando para precatórios (filtro removido)
2025-07-28 16:07:16 [INFO] - processar_numero_autos - Processo principal 0002429-44.2001.8.26.0053 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 16:07:19 [INFO] - processar_numero_autos - Nenhum link de precatório (seletor principal) para 0002429-44.2001.8.26.0053. Tentando fallback...
2025-07-28 16:07:20 [INFO] - processar_numero_autos - Nenhum link 'Precatório' clicável para 0002429-44.2001.8.26.0053.
2025-07-28 16:07:21 [INFO] - verificar_sessao_ativa - Verificando se a sessão ainda está ativa
2025-07-28 16:07:21 [INFO] - verificar_sessao_ativa - Navegando para página de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 16:07:24 [INFO] - verificar_sessao_ativa - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:07:24 [INFO] - _detectar_sessao_expirada - Nome de usuário válido detectado: 'DENIS HENRIQUE SOUSA OLIVEIRA' - sessão ativa
2025-07-28 16:07:24 [INFO] - verificar_sessao_ativa - Sessão ativa confirmada - Usuário: DENIS HENRIQUE SOUSA OLIVEIRA
2025-07-28 16:07:24 [INFO] - processar_numero_autos - Processando número 60/11405: 0031021-59.2005.8.26.0053
2025-07-28 16:07:24 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:07:24 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:07:24 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:07:24 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:07:26 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:07:26 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:07:26 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:07:26 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:07:26 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:07:26 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:07:28 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:07:28 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:07:28 [INFO] - consultar_processo_principal - Consultando 0031021-59.2005.8.26.0053 (TJSP: 0031021592005 / Foro: 0053)
2025-07-28 16:07:32 [INFO] - consultar_processo_principal - Lista de resultados para 0031021-59.2005.8.26.0053.
2025-07-28 16:07:32 [INFO] - consultar_processo_principal - Link para 0031021-59.2005.8.26.0053 encontrado. Clicando...
2025-07-28 16:07:35 [INFO] - consultar_processo_principal - Detalhes de 0031021-59.2005.8.26.0053 carregados após clique.
2025-07-28 16:07:35 [INFO] - verificar_status_processo - Status 'finalizado' detectado: arquivado
2025-07-28 16:07:35 [INFO] - processar_numero_autos - Processo principal 0031021-59.2005.8.26.0053 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 16:07:35 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 16:07:35 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Maria Trecco (lower: maria trecco)
2025-07-28 16:07:35 [INFO] - processar_numero_autos - Processo principal 0031021-59.2005.8.26.0053 sem partes proibidas
2025-07-28 16:07:35 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 16:07:37 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento
2025-07-28 16:07:37 [INFO] - processar_numero_autos - Processo principal 0031021-59.2005.8.26.0053 com Palavra Proibida em Movimentações - Continuando para precatórios (filtro removido)
2025-07-28 16:07:37 [INFO] - processar_numero_autos - Processo principal 0031021-59.2005.8.26.0053 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 16:07:39 [INFO] - processar_numero_autos - Nenhum link de precatório (seletor principal) para 0031021-59.2005.8.26.0053. Tentando fallback...
2025-07-28 16:07:40 [INFO] - processar_numero_autos - Nenhum link 'Precatório' clicável para 0031021-59.2005.8.26.0053.
2025-07-28 16:07:41 [INFO] - processar_numero_autos - Processando número 61/11405: 0001905-44.2004.8.26.0602
2025-07-28 16:07:41 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:07:41 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 16:07:43 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 16:07:43 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:07:43 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:07:45 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:07:45 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:07:45 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:07:45 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:07:45 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:07:45 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:07:48 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:07:48 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:07:48 [INFO] - consultar_processo_principal - Consultando 0001905-44.2004.8.26.0602 (TJSP: 0001905442004 / Foro: 0602)
2025-07-28 16:07:50 [INFO] - consultar_processo_principal - Lista de resultados para 0001905-44.2004.8.26.0602.
2025-07-28 16:07:50 [INFO] - consultar_processo_principal - Link para 0001905-44.2004.8.26.0602 encontrado. Clicando...
2025-07-28 16:07:52 [INFO] - consultar_processo_principal - Detalhes de 0001905-44.2004.8.26.0602 carregados após clique.
2025-07-28 16:07:52 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 16:07:52 [INFO] - processar_numero_autos - Processo principal 0001905-44.2004.8.26.0602 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 16:07:52 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 16:07:53 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Silvana da Costa Lopes (lower: silvana da costa lopes)
2025-07-28 16:07:53 [INFO] - processar_numero_autos - Processo principal 0001905-44.2004.8.26.0602 sem partes proibidas
2025-07-28 16:07:53 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 16:07:55 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento
2025-07-28 16:07:55 [INFO] - processar_numero_autos - Processo principal 0001905-44.2004.8.26.0602 com Palavra Proibida em Movimentações - Continuando para precatórios (filtro removido)
2025-07-28 16:07:55 [INFO] - processar_numero_autos - Processo principal 0001905-44.2004.8.26.0602 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 16:07:57 [INFO] - processar_numero_autos - Nenhum link de precatório (seletor principal) para 0001905-44.2004.8.26.0602. Tentando fallback...
2025-07-28 16:07:58 [INFO] - processar_numero_autos - Nenhum link 'Precatório' clicável para 0001905-44.2004.8.26.0602.
2025-07-28 16:07:59 [INFO] - processar_numero_autos - Processando número 62/11405: 0006919-67.2008.8.26.0602
2025-07-28 16:07:59 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:07:59 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 16:08:01 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 16:08:01 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:08:01 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:08:03 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:08:03 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:08:03 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:08:03 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:08:03 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:08:03 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:08:05 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:08:05 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:08:05 [INFO] - consultar_processo_principal - Consultando 0006919-67.2008.8.26.0602 (TJSP: 0006919672008 / Foro: 0602)
2025-07-28 16:08:08 [INFO] - consultar_processo_principal - Lista de resultados para 0006919-67.2008.8.26.0602.
2025-07-28 16:08:08 [INFO] - consultar_processo_principal - Link para 0006919-67.2008.8.26.0602 encontrado. Clicando...
2025-07-28 16:08:11 [INFO] - consultar_processo_principal - Detalhes de 0006919-67.2008.8.26.0602 carregados após clique.
2025-07-28 16:08:11 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 16:08:11 [INFO] - processar_numero_autos - Processo principal 0006919-67.2008.8.26.0602 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 16:08:11 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 16:08:11 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Valmei Miranda (lower: valmei miranda)
2025-07-28 16:08:11 [INFO] - processar_numero_autos - Processo principal 0006919-67.2008.8.26.0602 sem partes proibidas
2025-07-28 16:08:12 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 16:08:13 [INFO] - verificar_palavras_proibidas_mov - Nenhuma palavra proibida.
2025-07-28 16:08:13 [INFO] - processar_numero_autos - Processo principal 0006919-67.2008.8.26.0602 sem palavras proibidas
2025-07-28 16:08:13 [INFO] - processar_numero_autos - Processo principal 0006919-67.2008.8.26.0602 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 16:08:15 [INFO] - processar_numero_autos - Nenhum link de precatório (seletor principal) para 0006919-67.2008.8.26.0602. Tentando fallback...
2025-07-28 16:08:17 [INFO] - processar_numero_autos - Nenhum link 'Precatório' clicável para 0006919-67.2008.8.26.0602.
2025-07-28 16:08:18 [INFO] - processar_numero_autos - Processando número 63/11405: 0011156-42.2011.8.26.0602
2025-07-28 16:08:18 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:08:18 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 16:08:20 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 16:08:20 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:08:20 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:08:22 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:08:22 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:08:22 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:08:22 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:08:22 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:08:22 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:08:24 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:08:24 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:08:24 [INFO] - consultar_processo_principal - Consultando 0011156-42.2011.8.26.0602 (TJSP: 0011156422011 / Foro: 0602)
2025-07-28 16:08:26 [INFO] - consultar_processo_principal - Lista de resultados para 0011156-42.2011.8.26.0602.
2025-07-28 16:08:26 [INFO] - consultar_processo_principal - Link para 0011156-42.2011.8.26.0602 encontrado. Clicando...
2025-07-28 16:08:29 [INFO] - consultar_processo_principal - Detalhes de 0011156-42.2011.8.26.0602 carregados após clique.
2025-07-28 16:08:29 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 16:08:29 [INFO] - processar_numero_autos - Processo principal 0011156-42.2011.8.26.0602 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 16:08:29 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 16:08:29 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Isolete Bergamim (lower: isolete bergamim)
2025-07-28 16:08:29 [INFO] - processar_numero_autos - Processo principal 0011156-42.2011.8.26.0602 sem partes proibidas
2025-07-28 16:08:30 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 16:08:31 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento
2025-07-28 16:08:31 [INFO] - processar_numero_autos - Processo principal 0011156-42.2011.8.26.0602 com Palavra Proibida em Movimentações - Continuando para precatórios (filtro removido)
2025-07-28 16:08:31 [INFO] - processar_numero_autos - Processo principal 0011156-42.2011.8.26.0602 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 16:08:33 [INFO] - processar_numero_autos - Nenhum link de precatório (seletor principal) para 0011156-42.2011.8.26.0602. Tentando fallback...
2025-07-28 16:08:35 [INFO] - processar_numero_autos - Nenhum link 'Precatório' clicável para 0011156-42.2011.8.26.0602.
2025-07-28 16:08:36 [INFO] - processar_numero_autos - Processando número 64/11405: 0012943-09.2011.8.26.0602
2025-07-28 16:08:36 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:08:36 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 16:08:38 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 16:08:38 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:08:38 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:08:40 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:08:40 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:08:40 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:08:40 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:08:40 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:08:40 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:08:42 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:08:42 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:08:42 [INFO] - consultar_processo_principal - Consultando 0012943-09.2011.8.26.0602 (TJSP: 0012943092011 / Foro: 0602)
2025-07-28 16:08:44 [INFO] - consultar_processo_principal - Lista de resultados para 0012943-09.2011.8.26.0602.
2025-07-28 16:08:44 [INFO] - consultar_processo_principal - Link para 0012943-09.2011.8.26.0602 encontrado. Clicando...
2025-07-28 16:08:46 [INFO] - consultar_processo_principal - Detalhes de 0012943-09.2011.8.26.0602 carregados após clique.
2025-07-28 16:08:46 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 16:08:46 [INFO] - processar_numero_autos - Processo principal 0012943-09.2011.8.26.0602 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 16:08:46 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 16:08:46 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Solange Pires de Carvalho (lower: solange pires de carvalho)
2025-07-28 16:08:46 [INFO] - processar_numero_autos - Processo principal 0012943-09.2011.8.26.0602 sem partes proibidas
2025-07-28 16:08:47 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 16:08:48 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento
2025-07-28 16:08:48 [INFO] - processar_numero_autos - Processo principal 0012943-09.2011.8.26.0602 com Palavra Proibida em Movimentações - Continuando para precatórios (filtro removido)
2025-07-28 16:08:48 [INFO] - processar_numero_autos - Processo principal 0012943-09.2011.8.26.0602 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 16:08:48 [INFO] - processar_numero_autos - Links de precatório encontrados com seletor principal para 0012943-09.2011.8.26.0602.
2025-07-28 16:08:48 [INFO] - processar_numero_autos - 1 link(s) 'Precatório' para 0012943-09.2011.8.26.0602.
2025-07-28 16:08:50 [INFO] - processar_numero_autos - Processando link Precatório 1/1: 'Precatório - 00001'
2025-07-28 16:08:53 [INFO] - extrair_numero_precatorio_completo_da_pagina - Extraindo N° precatório para principal: 0012943-09.2011.8.26.0602
2025-07-28 16:08:53 [INFO] - extrair_numero_precatorio_completo_da_pagina - N° precatório não encontrado via span.unj-larger ou N° base divergente. Usar fallback do texto do link.
2025-07-28 16:08:53 [INFO] - processar_numero_autos - N° precatório formado via texto do link (fallback principal): 0012943-09.2011.8.26.0602 (01)
2025-07-28 16:08:53 [INFO] - obter_nome_cliente_do_precatorio - Buscando nome do cliente (Reqte)...
2025-07-28 16:08:54 [INFO] - obter_nome_cliente_do_precatorio - Nome do cliente: Solange Pires de Carvalho
2025-07-28 16:08:54 [INFO] - verificar_status_processo - Status 'finalizado' detectado: arquivado
2025-07-28 16:08:54 [INFO] - processar_numero_autos - Precatório '0012943-09.2011.8.26.0602 (01)' status 'Finalizado' - pulando
2025-07-28 16:08:54 [INFO] - processar_numero_autos - Precatório rejeitado: 0012943-09.2011.8.26.0602 (01) - Cliente: Solange Pires de Carvalho - Motivo: Status finalizado
2025-07-28 16:08:54 [INFO] - processar_numero_autos - FIM NÚMERO 64 (Tempo: 18.25s)
2025-07-28 16:08:54 [INFO] - processar_numero_autos - Processando número 65/11405: 0000670-35.2008.8.26.0268
2025-07-28 16:08:54 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:08:54 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 16:08:57 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 16:08:57 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:08:57 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:08:59 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:08:59 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:08:59 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:08:59 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:08:59 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:08:59 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:09:01 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:09:01 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:09:01 [INFO] - consultar_processo_principal - Consultando 0000670-35.2008.8.26.0268 (TJSP: 0000670352008 / Foro: 0268)
2025-07-28 16:09:04 [INFO] - consultar_processo_principal - Lista de resultados para 0000670-35.2008.8.26.0268.
2025-07-28 16:09:04 [INFO] - consultar_processo_principal - Link para 0000670-35.2008.8.26.0268 encontrado. Clicando...
2025-07-28 16:09:07 [INFO] - consultar_processo_principal - Detalhes de 0000670-35.2008.8.26.0268 carregados após clique.
2025-07-28 16:09:07 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 16:09:07 [INFO] - processar_numero_autos - Processo principal 0000670-35.2008.8.26.0268 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 16:09:07 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 16:09:07 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Fazenda Publica do Estado de São Paulo (lower: fazenda publica do estado de são paulo)
2025-07-28 16:09:07 [INFO] - verificar_partes_processo - Parte proibida (PJ) encontrada no Reqte: 'fazenda' em 'Fazenda Publica do Estado de São Paulo'
2025-07-28 16:09:07 [INFO] - processar_numero_autos - Processo principal 0000670-35.2008.8.26.0268 com Parte Proibida - Continuando para precatórios (filtro removido)
2025-07-28 16:09:08 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 16:09:09 [INFO] - verificar_palavras_proibidas_mov - Nenhuma palavra proibida.
2025-07-28 16:09:09 [INFO] - processar_numero_autos - Processo principal 0000670-35.2008.8.26.0268 sem palavras proibidas
2025-07-28 16:09:09 [INFO] - processar_numero_autos - Processo principal 0000670-35.2008.8.26.0268 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 16:09:12 [INFO] - processar_numero_autos - Nenhum link de precatório (seletor principal) para 0000670-35.2008.8.26.0268. Tentando fallback...
2025-07-28 16:09:13 [INFO] - processar_numero_autos - Nenhum link 'Precatório' clicável para 0000670-35.2008.8.26.0268.
2025-07-28 16:09:14 [INFO] - processar_numero_autos - Processando número 66/11405: 0130267-57.2007.8.26.0053
2025-07-28 16:09:14 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:09:14 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 16:09:16 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 16:09:16 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:09:16 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:09:18 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:09:18 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:09:18 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:09:18 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:09:18 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:09:18 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:09:20 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:09:20 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:09:20 [INFO] - consultar_processo_principal - Consultando 0130267-57.2007.8.26.0053 (TJSP: 0130267572007 / Foro: 0053)
2025-07-28 16:09:26 [INFO] - consultar_processo_principal - Lista de resultados para 0130267-57.2007.8.26.0053.
2025-07-28 16:09:26 [INFO] - consultar_processo_principal - Link para 0130267-57.2007.8.26.0053 encontrado. Clicando...
2025-07-28 16:09:31 [INFO] - consultar_processo_principal - Detalhes de 0130267-57.2007.8.26.0053 carregados após clique.
2025-07-28 16:09:31 [INFO] - verificar_status_processo - Status 'finalizado' (texto da página).
2025-07-28 16:09:31 [INFO] - processar_numero_autos - Processo principal 0130267-57.2007.8.26.0053 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 16:09:31 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 16:09:31 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Deosinda Orosz (lower: deosinda orosz)
2025-07-28 16:09:31 [INFO] - processar_numero_autos - Processo principal 0130267-57.2007.8.26.0053 sem partes proibidas
2025-07-28 16:09:32 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 16:09:33 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento
2025-07-28 16:09:33 [INFO] - processar_numero_autos - Processo principal 0130267-57.2007.8.26.0053 com Palavra Proibida em Movimentações - Continuando para precatórios (filtro removido)
2025-07-28 16:09:33 [INFO] - processar_numero_autos - Processo principal 0130267-57.2007.8.26.0053 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 16:09:35 [INFO] - processar_numero_autos - Nenhum link de precatório (seletor principal) para 0130267-57.2007.8.26.0053. Tentando fallback...
2025-07-28 16:09:37 [INFO] - processar_numero_autos - Nenhum link 'Precatório' clicável para 0130267-57.2007.8.26.0053.
2025-07-28 16:09:37 [INFO] - processar_numero_autos - Processando número 67/11405: 0121642-97.2008.8.26.0053
2025-07-28 16:09:37 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:09:38 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 16:09:40 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 16:09:40 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:09:40 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:09:42 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:09:42 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:09:42 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:09:42 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:09:42 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:09:42 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:09:44 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:09:44 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:09:44 [INFO] - consultar_processo_principal - Consultando 0121642-97.2008.8.26.0053 (TJSP: 0121642972008 / Foro: 0053)
2025-07-28 16:10:18 [INFO] - consultar_processo_principal - Lista de resultados para 0121642-97.2008.8.26.0053.
2025-07-28 16:10:18 [INFO] - consultar_processo_principal - Link para 0121642-97.2008.8.26.0053 encontrado. Clicando...
2025-07-28 16:10:35 [INFO] - consultar_processo_principal - Detalhes de 0121642-97.2008.8.26.0053 carregados após clique.
2025-07-28 16:10:35 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 16:10:35 [INFO] - processar_numero_autos - Processo principal 0121642-97.2008.8.26.0053 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 16:10:35 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 16:10:35 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Angela Aparecida Rovay (lower: angela aparecida rovay)
2025-07-28 16:10:35 [INFO] - processar_numero_autos - Processo principal 0121642-97.2008.8.26.0053 sem partes proibidas
2025-07-28 16:10:35 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 16:10:37 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento
2025-07-28 16:10:37 [INFO] - processar_numero_autos - Processo principal 0121642-97.2008.8.26.0053 com Palavra Proibida em Movimentações - Continuando para precatórios (filtro removido)
2025-07-28 16:10:37 [INFO] - processar_numero_autos - Processo principal 0121642-97.2008.8.26.0053 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 16:10:39 [INFO] - processar_numero_autos - Nenhum link de precatório (seletor principal) para 0121642-97.2008.8.26.0053. Tentando fallback...
2025-07-28 16:10:41 [INFO] - processar_numero_autos - Nenhum link 'Precatório' clicável para 0121642-97.2008.8.26.0053.
2025-07-28 16:10:41 [INFO] - processar_numero_autos - Processando número 68/11405: 0106251-05.2008.8.26.0053
2025-07-28 16:10:41 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:10:41 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 16:10:43 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 16:10:43 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:10:43 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:10:46 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:10:46 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:10:46 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:10:46 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:10:46 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:10:46 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:10:48 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:10:48 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:10:48 [INFO] - consultar_processo_principal - Consultando 0106251-05.2008.8.26.0053 (TJSP: 0106251052008 / Foro: 0053)
2025-07-28 16:11:18 [INFO] - consultar_processo_principal - Lista de resultados para 0106251-05.2008.8.26.0053.
2025-07-28 16:11:18 [INFO] - consultar_processo_principal - Link para 0106251-05.2008.8.26.0053 encontrado. Clicando...
2025-07-28 16:11:20 [INFO] - consultar_processo_principal - Detalhes de 0106251-05.2008.8.26.0053 carregados após clique.
2025-07-28 16:11:20 [INFO] - verificar_status_processo - Status 'finalizado' detectado: arquivado
2025-07-28 16:11:20 [INFO] - processar_numero_autos - Processo principal 0106251-05.2008.8.26.0053 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 16:11:20 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 16:11:20 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Rejane Celia Pereira dos Santos (lower: rejane celia pereira dos santos)
2025-07-28 16:11:20 [INFO] - processar_numero_autos - Processo principal 0106251-05.2008.8.26.0053 sem partes proibidas
2025-07-28 16:11:20 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 16:11:22 [INFO] - verificar_palavras_proibidas_mov - Nenhuma palavra proibida.
2025-07-28 16:11:22 [INFO] - processar_numero_autos - Processo principal 0106251-05.2008.8.26.0053 sem palavras proibidas
2025-07-28 16:11:22 [INFO] - processar_numero_autos - Processo principal 0106251-05.2008.8.26.0053 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 16:11:22 [INFO] - processar_numero_autos - Links de precatório encontrados com seletor principal para 0106251-05.2008.8.26.0053.
2025-07-28 16:11:22 [INFO] - processar_numero_autos - 1 link(s) 'Precatório' para 0106251-05.2008.8.26.0053.
2025-07-28 16:11:23 [INFO] - processar_numero_autos - Processando link Precatório 1/1: 'Precatório - 00001'
2025-07-28 16:11:30 [INFO] - extrair_numero_precatorio_completo_da_pagina - Extraindo N° precatório para principal: 0106251-05.2008.8.26.0053
2025-07-28 16:11:30 [INFO] - extrair_numero_precatorio_completo_da_pagina - N° precatório não encontrado via span.unj-larger ou N° base divergente. Usar fallback do texto do link.
2025-07-28 16:11:30 [INFO] - processar_numero_autos - N° precatório formado via texto do link (fallback principal): 0106251-05.2008.8.26.0053 (01)
2025-07-28 16:11:30 [INFO] - obter_nome_cliente_do_precatorio - Buscando nome do cliente (Reqte)...
2025-07-28 16:11:30 [INFO] - obter_nome_cliente_do_precatorio - Nome do cliente: Rejane Celia Pereira dos Santos
2025-07-28 16:11:30 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 16:11:30 [INFO] - processar_numero_autos - Precatório '0106251-05.2008.8.26.0053 (01)' status 'Finalizado' - pulando
2025-07-28 16:11:30 [INFO] - processar_numero_autos - Precatório rejeitado: 0106251-05.2008.8.26.0053 (01) - Cliente: Rejane Celia Pereira dos Santos - Motivo: Status finalizado
2025-07-28 16:11:30 [INFO] - processar_numero_autos - FIM NÚMERO 68 (Tempo: 48.79s)
2025-07-28 16:11:31 [INFO] - processar_numero_autos - Processando número 69/11405: 0135271-12.2006.8.26.0053
2025-07-28 16:11:31 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:11:31 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 16:11:33 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 16:11:33 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:11:33 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:11:35 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:11:35 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:11:35 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:11:35 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:11:35 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:11:35 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:11:37 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:11:37 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:11:37 [INFO] - consultar_processo_principal - Consultando 0135271-12.2006.8.26.0053 (TJSP: 0135271122006 / Foro: 0053)
2025-07-28 16:11:39 [INFO] - consultar_processo_principal - Lista de resultados para 0135271-12.2006.8.26.0053.
2025-07-28 16:11:39 [INFO] - consultar_processo_principal - Link para 0135271-12.2006.8.26.0053 encontrado. Clicando...
2025-07-28 16:11:41 [INFO] - consultar_processo_principal - Detalhes de 0135271-12.2006.8.26.0053 carregados após clique.
2025-07-28 16:11:41 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 16:11:41 [INFO] - processar_numero_autos - Processo principal 0135271-12.2006.8.26.0053 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 16:11:41 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 16:11:41 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Ilka Julian Holloway (lower: ilka julian holloway)
2025-07-28 16:11:41 [INFO] - processar_numero_autos - Processo principal 0135271-12.2006.8.26.0053 sem partes proibidas
2025-07-28 16:11:42 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 16:11:44 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento
2025-07-28 16:11:44 [INFO] - processar_numero_autos - Processo principal 0135271-12.2006.8.26.0053 com Palavra Proibida em Movimentações - Continuando para precatórios (filtro removido)
2025-07-28 16:11:44 [INFO] - processar_numero_autos - Processo principal 0135271-12.2006.8.26.0053 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 16:11:46 [INFO] - processar_numero_autos - Nenhum link de precatório (seletor principal) para 0135271-12.2006.8.26.0053. Tentando fallback...
2025-07-28 16:11:47 [INFO] - processar_numero_autos - Nenhum link 'Precatório' clicável para 0135271-12.2006.8.26.0053.
2025-07-28 16:11:48 [INFO] - verificar_sessao_ativa - Verificando se a sessão ainda está ativa
2025-07-28 16:11:48 [INFO] - verificar_sessao_ativa - Navegando para página de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 16:11:51 [INFO] - verificar_sessao_ativa - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:11:51 [INFO] - _detectar_sessao_expirada - Nome de usuário válido detectado: 'DENIS HENRIQUE SOUSA OLIVEIRA' - sessão ativa
2025-07-28 16:11:51 [INFO] - verificar_sessao_ativa - Sessão ativa confirmada - Usuário: DENIS HENRIQUE SOUSA OLIVEIRA
2025-07-28 16:11:51 [INFO] - processar_numero_autos - Processando número 70/11405: 0050325-11.2008.8.26.0224
2025-07-28 16:11:51 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:11:51 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:11:51 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:11:51 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:11:53 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:11:53 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:11:53 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:11:53 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:11:53 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:11:53 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:11:55 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:11:55 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:11:55 [INFO] - consultar_processo_principal - Consultando 0050325-11.2008.8.26.0224 (TJSP: 0050325112008 / Foro: 0224)
2025-07-28 16:12:32 [INFO] - consultar_processo_principal - Detalhes de 0050325-11.2008.8.26.0224 carregados diretamente.
2025-07-28 16:12:32 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 16:12:32 [INFO] - processar_numero_autos - Processo principal 0050325-11.2008.8.26.0224 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 16:12:32 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 16:12:32 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Osvaldo Aparecido Calegari (lower: osvaldo aparecido calegari)
2025-07-28 16:12:32 [INFO] - processar_numero_autos - Processo principal 0050325-11.2008.8.26.0224 sem partes proibidas
2025-07-28 16:12:33 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 16:12:34 [INFO] - verificar_palavras_proibidas_mov - Nenhuma palavra proibida.
2025-07-28 16:12:34 [INFO] - processar_numero_autos - Processo principal 0050325-11.2008.8.26.0224 sem palavras proibidas
2025-07-28 16:12:34 [INFO] - processar_numero_autos - Processo principal 0050325-11.2008.8.26.0224 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 16:12:37 [INFO] - processar_numero_autos - Nenhum link de precatório (seletor principal) para 0050325-11.2008.8.26.0224. Tentando fallback...
2025-07-28 16:12:38 [INFO] - processar_numero_autos - Nenhum link 'Precatório' clicável para 0050325-11.2008.8.26.0224.
2025-07-28 16:12:39 [INFO] - processar_numero_autos - Processando número 71/11405: 0000924-13.2011.8.26.0588
2025-07-28 16:12:39 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:12:39 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 16:12:41 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 16:12:41 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:12:41 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:12:43 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:12:43 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:12:43 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:12:43 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:12:43 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:12:43 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:12:45 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:12:45 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:12:45 [INFO] - consultar_processo_principal - Consultando 0000924-13.2011.8.26.0588 (TJSP: 0000924132011 / Foro: 0588)
2025-07-28 16:13:00 [INFO] - consultar_processo_principal - Lista de resultados para 0000924-13.2011.8.26.0588.
2025-07-28 16:13:00 [INFO] - consultar_processo_principal - Link para 0000924-13.2011.8.26.0588 encontrado. Clicando...
2025-07-28 16:13:03 [INFO] - consultar_processo_principal - Detalhes de 0000924-13.2011.8.26.0588 carregados após clique.
2025-07-28 16:13:03 [INFO] - verificar_status_processo - Status 'finalizado' detectado: extinto
2025-07-28 16:13:03 [INFO] - processar_numero_autos - Processo principal 0000924-13.2011.8.26.0588 status: finalizado - Continuando para precatórios (filtro removido)
2025-07-28 16:13:03 [INFO] - verificar_partes_processo - Verificando partes (Reqte)...
2025-07-28 16:13:03 [INFO] - verificar_partes_processo - Reqte para verificação de partes: Mara Luzia Corsi de Andrade Dias (lower: mara luzia corsi de andrade dias)
2025-07-28 16:13:03 [INFO] - processar_numero_autos - Processo principal 0000924-13.2011.8.26.0588 sem partes proibidas
2025-07-28 16:13:04 [INFO] - clicar_botao_mais_movimentacoes - Clicando 'Mais' das movimentações.
2025-07-28 16:13:05 [INFO] - verificar_palavras_proibidas_mov - Palavra proibida: mandado de levantamento
2025-07-28 16:13:05 [INFO] - processar_numero_autos - Processo principal 0000924-13.2011.8.26.0588 com Palavra Proibida em Movimentações - Continuando para precatórios (filtro removido)
2025-07-28 16:13:05 [INFO] - processar_numero_autos - Processo principal 0000924-13.2011.8.26.0588 consultado (TJSP) - Todos os filtros removidos.
2025-07-28 16:13:08 [INFO] - processar_numero_autos - Nenhum link de precatório (seletor principal) para 0000924-13.2011.8.26.0588. Tentando fallback...
2025-07-28 16:13:09 [INFO] - processar_numero_autos - Nenhum link 'Precatório' clicável para 0000924-13.2011.8.26.0588.
2025-07-28 16:13:10 [INFO] - processar_numero_autos - Processando número 72/11405: 0003216-62.2004.8.26.0637
2025-07-28 16:13:10 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:13:10 [INFO] - garantir_url_consulta - Navegando para URL de consulta: https://esaj.tjsp.jus.br/cpopg/open.do
2025-07-28 16:13:12 [INFO] - garantir_url_consulta - Navegação para URL de consulta concluída
2025-07-28 16:13:12 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:13:12 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:13:14 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:13:14 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:13:14 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:13:14 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:13:14 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:13:14 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:13:16 [INFO] - verificar_login_tjsp_especifico - Texto do elemento headerNmUsuarioLogado: 'DENIS HENRIQUE SOUSA OLIVEIRA'
2025-07-28 16:13:16 [INFO] - verificar_login_tjsp_especifico - Detectado nome de usuário válido: 'DENIS HENRIQUE SOUSA OLIVEIRA' - usuário ESTÁ logado
2025-07-28 16:13:16 [INFO] - consultar_processo_principal - Consultando 0003216-62.2004.8.26.0637 (TJSP: 0003216622004 / Foro: 0637)
2025-07-28 16:15:17 [ERROR] - consultar_processo_principal - Erro geral ao consultar 0003216-62.2004.8.26.0637: HTTPConnectionPool(host='localhost', port=60359): Read timed out. (read timeout=120)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\urllib3\connectionpool.py", line 534, in _make_request
    response = conn.getresponse()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\urllib3\connection.py", line 565, in getresponse
    httplib_response = super().getresponse()
  File "C:\Program Files\Python313\Lib\http\client.py", line 1430, in getresponse
    response.begin()
    ~~~~~~~~~~~~~~^^
  File "C:\Program Files\Python313\Lib\http\client.py", line 331, in begin
    version, status, reason = self._read_status()
                              ~~~~~~~~~~~~~~~~~^^
  File "C:\Program Files\Python313\Lib\http\client.py", line 292, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
               ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\socket.py", line 719, in readinto
    return self._sock.recv_into(b)
           ~~~~~~~~~~~~~~~~~~~~^^^
TimeoutError: timed out

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Augment\Bipre\TJSP_PDF_e2e\tjsp\ProcessadorTJSPUnificado_final.py", line 1763, in consultar_processo_principal
    self.driver.find_element(By.ID, 'botaoConsultarProcessos').click()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\selenium\webdriver\remote\webelement.py", line 120, in click
    self._execute(Command.CLICK_ELEMENT)
    ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\selenium\webdriver\remote\webelement.py", line 573, in _execute
    return self._parent.execute(command, params)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\selenium\webdriver\remote\webdriver.py", line 451, in execute
    response = cast(RemoteConnection, self.command_executor).execute(driver_command, params)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\selenium\webdriver\remote\remote_connection.py", line 404, in execute
    return self._request(command_info[0], url, body=data)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\selenium\webdriver\remote\remote_connection.py", line 428, in _request
    response = self._conn.request(method, url, body=body, headers=headers, timeout=self._client_config.timeout)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
           ~~~~~~~~~~~~~~~~~~~~~~~~^
        method, url, fields=fields, headers=headers, **urlopen_kw
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
           ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
        method, url, error=new_e, _pool=self, _stacktrace=sys.exc_info()[2]
    )
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\urllib3\util\retry.py", line 474, in increment
    raise reraise(type(error), error, _stacktrace)
          ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\urllib3\util\util.py", line 39, in reraise
    raise value
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
        conn,
    ...<10 lines>...
        **response_kw,
    )
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\urllib3\connectionpool.py", line 536, in _make_request
    self._raise_timeout(err=e, url=url, timeout_value=read_timeout)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\urllib3\connectionpool.py", line 367, in _raise_timeout
    raise ReadTimeoutError(
        self, url, f"Read timed out. (read timeout={timeout_value})"
    ) from err
urllib3.exceptions.ReadTimeoutError: HTTPConnectionPool(host='localhost', port=60359): Read timed out. (read timeout=120)
2025-07-28 16:15:20 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:17:11 [INFO] - garantir_url_consulta - Já na URL de consulta correta
2025-07-28 16:17:11 [INFO] - verificar_login_tjsp_especifico - Executando verificação específica do TJSP
2025-07-28 16:17:12 [ERROR] - garantir_url_consulta - Erro ao garantir URL de consulta: Message: tab crashed
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff72247e935+77845]
	GetHandleVerifier [0x0x7ff72247e990+77936]
	(No symbol) [0x0x7ff722239b0c]
	(No symbol) [0x0x7ff72222727b]
	(No symbol) [0x0x7ff722224eab]
	(No symbol) [0x0x7ff72222590f]
	(No symbol) [0x0x7ff72223453e]
	(No symbol) [0x0x7ff72224aca0]
	(No symbol) [0x0x7ff7222e0d01]
	(No symbol) [0x0x7ff7222b86a3]
	(No symbol) [0x0x7ff722281791]
	(No symbol) [0x0x7ff722282523]
	GetHandleVerifier [0x0x7ff72275684d+3059501]
	GetHandleVerifier [0x0x7ff722750c0d+3035885]
	GetHandleVerifier [0x0x7ff722770400+3164896]
	GetHandleVerifier [0x0x7ff722498c3e+185118]
	GetHandleVerifier [0x0x7ff7224a054f+216111]
	GetHandleVerifier [0x0x7ff7224872e4+113092]
	GetHandleVerifier [0x0x7ff722487499+113529]
	GetHandleVerifier [0x0x7ff72246e298+10616]
	BaseThreadInitThunk [0x0x7ffe0530e8d7+23]
	RtlUserThreadStart [0x0x7ffe060fc34c+44]

2025-07-28 16:17:12 [WARNING] - verificar_login_tjsp_especifico - Falha ao navegar para URL de consulta
2025-07-28 16:17:12 [INFO] - verificar_sessao_ativa - Verificando se a sessão ainda está ativa
2025-07-28 16:17:12 [ERROR] - verificar_sessao_ativa - Erro ao verificar sessão: Message: tab crashed
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff72247e935+77845]
	GetHandleVerifier [0x0x7ff72247e990+77936]
	(No symbol) [0x0x7ff722239b0c]
	(No symbol) [0x0x7ff72222727b]
	(No symbol) [0x0x7ff72222613e]
	(No symbol) [0x0x7ff722249ce1]
	(No symbol) [0x0x7ff7222e09e1]
	(No symbol) [0x0x7ff7222b86a3]
	(No symbol) [0x0x7ff722281791]
	(No symbol) [0x0x7ff722282523]
	GetHandleVerifier [0x0x7ff72275684d+3059501]
	GetHandleVerifier [0x0x7ff722750c0d+3035885]
	GetHandleVerifier [0x0x7ff722770400+3164896]
	GetHandleVerifier [0x0x7ff722498c3e+185118]
	GetHandleVerifier [0x0x7ff7224a054f+216111]
	GetHandleVerifier [0x0x7ff7224872e4+113092]
	GetHandleVerifier [0x0x7ff722487499+113529]
	GetHandleVerifier [0x0x7ff72246e298+10616]
	BaseThreadInitThunk [0x0x7ffe0530e8d7+23]
	RtlUserThreadStart [0x0x7ffe060fc34c+44]

2025-07-28 16:17:12 [WARNING] - verificar_e_renovar_sessao - Sessão expirada detectada - Renovando automaticamente
2025-07-28 16:17:12 [INFO] - _renovar_sessao_automaticamente - Iniciando renovação automática da sessão
2025-07-28 16:17:12 [ERROR] - _renovar_sessao_automaticamente - Erro na renovação automática: Message: tab crashed
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff72247e935+77845]
	GetHandleVerifier [0x0x7ff72247e990+77936]
	(No symbol) [0x0x7ff722239b0c]
	(No symbol) [0x0x7ff72222727b]
	(No symbol) [0x0x7ff722224eab]
	(No symbol) [0x0x7ff72222590f]
	(No symbol) [0x0x7ff72223453e]
	(No symbol) [0x0x7ff72224a4f1]
	(No symbol) [0x0x7ff72225165a]
	(No symbol) [0x0x7ff7222260ad]
	(No symbol) [0x0x7ff722249ce1]
	(No symbol) [0x0x7ff7222e09e1]
	(No symbol) [0x0x7ff7222b86a3]
	(No symbol) [0x0x7ff722281791]
	(No symbol) [0x0x7ff722282523]
	GetHandleVerifier [0x0x7ff72275684d+3059501]
	GetHandleVerifier [0x0x7ff722750c0d+3035885]
	GetHandleVerifier [0x0x7ff722770400+3164896]
	GetHandleVerifier [0x0x7ff722498c3e+185118]
	GetHandleVerifier [0x0x7ff7224a054f+216111]
	GetHandleVerifier [0x0x7ff7224872e4+113092]
	GetHandleVerifier [0x0x7ff722487499+113529]
	GetHandleVerifier [0x0x7ff72246e298+10616]
	BaseThreadInitThunk [0x0x7ffe0530e8d7+23]
	RtlUserThreadStart [0x0x7ffe060fc34c+44]

2025-07-28 16:17:17 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:17:17 [ERROR] - garantir_url_consulta - Erro ao garantir URL de consulta: Message: tab crashed
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff72247e935+77845]
	GetHandleVerifier [0x0x7ff72247e990+77936]
	(No symbol) [0x0x7ff722239b0c]
	(No symbol) [0x0x7ff72222727b]
	(No symbol) [0x0x7ff722224eab]
	(No symbol) [0x0x7ff72222590f]
	(No symbol) [0x0x7ff72223453e]
	(No symbol) [0x0x7ff72224a4f1]
	(No symbol) [0x0x7ff72225165a]
	(No symbol) [0x0x7ff7222260ad]
	(No symbol) [0x0x7ff722249ce1]
	(No symbol) [0x0x7ff7222e09e1]
	(No symbol) [0x0x7ff7222b86a3]
	(No symbol) [0x0x7ff722281791]
	(No symbol) [0x0x7ff722282523]
	GetHandleVerifier [0x0x7ff72275684d+3059501]
	GetHandleVerifier [0x0x7ff722750c0d+3035885]
	GetHandleVerifier [0x0x7ff722770400+3164896]
	GetHandleVerifier [0x0x7ff722498c3e+185118]
	GetHandleVerifier [0x0x7ff7224a054f+216111]
	GetHandleVerifier [0x0x7ff7224872e4+113092]
	GetHandleVerifier [0x0x7ff722487499+113529]
	GetHandleVerifier [0x0x7ff72246e298+10616]
	BaseThreadInitThunk [0x0x7ffe0530e8d7+23]
	RtlUserThreadStart [0x0x7ffe060fc34c+44]

2025-07-28 16:17:18 [INFO] - processar_numero_autos - Processando número 73/11405: 0002985-41.2010.8.26.0470
2025-07-28 16:17:18 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:17:18 [ERROR] - garantir_url_consulta - Erro ao garantir URL de consulta: Message: tab crashed
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff72247e935+77845]
	GetHandleVerifier [0x0x7ff72247e990+77936]
	(No symbol) [0x0x7ff722239b0c]
	(No symbol) [0x0x7ff72222727b]
	(No symbol) [0x0x7ff722224eab]
	(No symbol) [0x0x7ff72222590f]
	(No symbol) [0x0x7ff72223453e]
	(No symbol) [0x0x7ff72224a4f1]
	(No symbol) [0x0x7ff72225165a]
	(No symbol) [0x0x7ff7222260ad]
	(No symbol) [0x0x7ff722249ce1]
	(No symbol) [0x0x7ff7222e09e1]
	(No symbol) [0x0x7ff7222b86a3]
	(No symbol) [0x0x7ff722281791]
	(No symbol) [0x0x7ff722282523]
	GetHandleVerifier [0x0x7ff72275684d+3059501]
	GetHandleVerifier [0x0x7ff722750c0d+3035885]
	GetHandleVerifier [0x0x7ff722770400+3164896]
	GetHandleVerifier [0x0x7ff722498c3e+185118]
	GetHandleVerifier [0x0x7ff7224a054f+216111]
	GetHandleVerifier [0x0x7ff7224872e4+113092]
	GetHandleVerifier [0x0x7ff722487499+113529]
	GetHandleVerifier [0x0x7ff72246e298+10616]
	BaseThreadInitThunk [0x0x7ffe0530e8d7+23]
	RtlUserThreadStart [0x0x7ffe060fc34c+44]

2025-07-28 16:17:18 [ERROR] - processar_numero_autos - 0002985-41.2010.8.26.0470: Falha na verificação de sessão.
2025-07-28 16:17:18 [INFO] - processar_numero_autos - Processando número 74/11405: 0025039-64.2005.8.26.0053
2025-07-28 16:17:18 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:17:18 [ERROR] - garantir_url_consulta - Erro ao garantir URL de consulta: Message: tab crashed
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff72247e935+77845]
	GetHandleVerifier [0x0x7ff72247e990+77936]
	(No symbol) [0x0x7ff722239b0c]
	(No symbol) [0x0x7ff72222727b]
	(No symbol) [0x0x7ff722224eab]
	(No symbol) [0x0x7ff72222590f]
	(No symbol) [0x0x7ff72223453e]
	(No symbol) [0x0x7ff72224a4f1]
	(No symbol) [0x0x7ff72225165a]
	(No symbol) [0x0x7ff7222260ad]
	(No symbol) [0x0x7ff722249ce1]
	(No symbol) [0x0x7ff7222e09e1]
	(No symbol) [0x0x7ff7222b86a3]
	(No symbol) [0x0x7ff722281791]
	(No symbol) [0x0x7ff722282523]
	GetHandleVerifier [0x0x7ff72275684d+3059501]
	GetHandleVerifier [0x0x7ff722750c0d+3035885]
	GetHandleVerifier [0x0x7ff722770400+3164896]
	GetHandleVerifier [0x0x7ff722498c3e+185118]
	GetHandleVerifier [0x0x7ff7224a054f+216111]
	GetHandleVerifier [0x0x7ff7224872e4+113092]
	GetHandleVerifier [0x0x7ff722487499+113529]
	GetHandleVerifier [0x0x7ff72246e298+10616]
	BaseThreadInitThunk [0x0x7ffe0530e8d7+23]
	RtlUserThreadStart [0x0x7ffe060fc34c+44]

2025-07-28 16:17:18 [ERROR] - processar_numero_autos - 0025039-64.2005.8.26.0053: Falha na verificação de sessão.
2025-07-28 16:17:18 [INFO] - processar_numero_autos - Processando número 75/11405: 0018877-35.2008.8.26.0510
2025-07-28 16:17:18 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:17:18 [ERROR] - garantir_url_consulta - Erro ao garantir URL de consulta: Message: tab crashed
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff72247e935+77845]
	GetHandleVerifier [0x0x7ff72247e990+77936]
	(No symbol) [0x0x7ff722239b0c]
	(No symbol) [0x0x7ff72222727b]
	(No symbol) [0x0x7ff722224eab]
	(No symbol) [0x0x7ff72222590f]
	(No symbol) [0x0x7ff72223453e]
	(No symbol) [0x0x7ff72224a4f1]
	(No symbol) [0x0x7ff72225165a]
	(No symbol) [0x0x7ff7222260ad]
	(No symbol) [0x0x7ff722249ce1]
	(No symbol) [0x0x7ff7222e09e1]
	(No symbol) [0x0x7ff7222b86a3]
	(No symbol) [0x0x7ff722281791]
	(No symbol) [0x0x7ff722282523]
	GetHandleVerifier [0x0x7ff72275684d+3059501]
	GetHandleVerifier [0x0x7ff722750c0d+3035885]
	GetHandleVerifier [0x0x7ff722770400+3164896]
	GetHandleVerifier [0x0x7ff722498c3e+185118]
	GetHandleVerifier [0x0x7ff7224a054f+216111]
	GetHandleVerifier [0x0x7ff7224872e4+113092]
	GetHandleVerifier [0x0x7ff722487499+113529]
	GetHandleVerifier [0x0x7ff72246e298+10616]
	BaseThreadInitThunk [0x0x7ffe0530e8d7+23]
	RtlUserThreadStart [0x0x7ffe060fc34c+44]

2025-07-28 16:17:18 [ERROR] - processar_numero_autos - 0018877-35.2008.8.26.0510: Falha na verificação de sessão.
2025-07-28 16:17:18 [INFO] - processar_numero_autos - Processando número 76/11405: 0007316-65.2007.8.26.0472
2025-07-28 16:17:18 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:17:18 [ERROR] - garantir_url_consulta - Erro ao garantir URL de consulta: Message: tab crashed
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff72247e935+77845]
	GetHandleVerifier [0x0x7ff72247e990+77936]
	(No symbol) [0x0x7ff722239b0c]
	(No symbol) [0x0x7ff72222727b]
	(No symbol) [0x0x7ff722224eab]
	(No symbol) [0x0x7ff72222590f]
	(No symbol) [0x0x7ff72223453e]
	(No symbol) [0x0x7ff72224a4f1]
	(No symbol) [0x0x7ff72225165a]
	(No symbol) [0x0x7ff7222260ad]
	(No symbol) [0x0x7ff722249ce1]
	(No symbol) [0x0x7ff7222e09e1]
	(No symbol) [0x0x7ff7222b86a3]
	(No symbol) [0x0x7ff722281791]
	(No symbol) [0x0x7ff722282523]
	GetHandleVerifier [0x0x7ff72275684d+3059501]
	GetHandleVerifier [0x0x7ff722750c0d+3035885]
	GetHandleVerifier [0x0x7ff722770400+3164896]
	GetHandleVerifier [0x0x7ff722498c3e+185118]
	GetHandleVerifier [0x0x7ff7224a054f+216111]
	GetHandleVerifier [0x0x7ff7224872e4+113092]
	GetHandleVerifier [0x0x7ff722487499+113529]
	GetHandleVerifier [0x0x7ff72246e298+10616]
	BaseThreadInitThunk [0x0x7ffe0530e8d7+23]
	RtlUserThreadStart [0x0x7ffe060fc34c+44]

2025-07-28 16:17:18 [ERROR] - processar_numero_autos - 0007316-65.2007.8.26.0472: Falha na verificação de sessão.
2025-07-28 16:17:18 [INFO] - processar_numero_autos - Processando número 77/11405: 0007460-30.2006.8.26.0066
2025-07-28 16:17:18 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:17:18 [ERROR] - garantir_url_consulta - Erro ao garantir URL de consulta: Message: tab crashed
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff72247e935+77845]
	GetHandleVerifier [0x0x7ff72247e990+77936]
	(No symbol) [0x0x7ff722239b0c]
	(No symbol) [0x0x7ff72222727b]
	(No symbol) [0x0x7ff722224eab]
	(No symbol) [0x0x7ff72222590f]
	(No symbol) [0x0x7ff72223453e]
	(No symbol) [0x0x7ff72224a4f1]
	(No symbol) [0x0x7ff72225165a]
	(No symbol) [0x0x7ff7222260ad]
	(No symbol) [0x0x7ff722249ce1]
	(No symbol) [0x0x7ff7222e09e1]
	(No symbol) [0x0x7ff7222b86a3]
	(No symbol) [0x0x7ff722281791]
	(No symbol) [0x0x7ff722282523]
	GetHandleVerifier [0x0x7ff72275684d+3059501]
	GetHandleVerifier [0x0x7ff722750c0d+3035885]
	GetHandleVerifier [0x0x7ff722770400+3164896]
	GetHandleVerifier [0x0x7ff722498c3e+185118]
	GetHandleVerifier [0x0x7ff7224a054f+216111]
	GetHandleVerifier [0x0x7ff7224872e4+113092]
	GetHandleVerifier [0x0x7ff722487499+113529]
	GetHandleVerifier [0x0x7ff72246e298+10616]
	BaseThreadInitThunk [0x0x7ffe0530e8d7+23]
	RtlUserThreadStart [0x0x7ffe060fc34c+44]

2025-07-28 16:17:18 [ERROR] - processar_numero_autos - 0007460-30.2006.8.26.0066: Falha na verificação de sessão.
2025-07-28 16:17:18 [INFO] - processar_numero_autos - Processando número 78/11405: 0001386-58.2005.8.26.0368
2025-07-28 16:17:18 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:17:18 [ERROR] - garantir_url_consulta - Erro ao garantir URL de consulta: Message: tab crashed
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff72247e935+77845]
	GetHandleVerifier [0x0x7ff72247e990+77936]
	(No symbol) [0x0x7ff722239b0c]
	(No symbol) [0x0x7ff72222727b]
	(No symbol) [0x0x7ff722224eab]
	(No symbol) [0x0x7ff72222590f]
	(No symbol) [0x0x7ff72223453e]
	(No symbol) [0x0x7ff72224a4f1]
	(No symbol) [0x0x7ff72225165a]
	(No symbol) [0x0x7ff7222260ad]
	(No symbol) [0x0x7ff722249ce1]
	(No symbol) [0x0x7ff7222e09e1]
	(No symbol) [0x0x7ff7222b86a3]
	(No symbol) [0x0x7ff722281791]
	(No symbol) [0x0x7ff722282523]
	GetHandleVerifier [0x0x7ff72275684d+3059501]
	GetHandleVerifier [0x0x7ff722750c0d+3035885]
	GetHandleVerifier [0x0x7ff722770400+3164896]
	GetHandleVerifier [0x0x7ff722498c3e+185118]
	GetHandleVerifier [0x0x7ff7224a054f+216111]
	GetHandleVerifier [0x0x7ff7224872e4+113092]
	GetHandleVerifier [0x0x7ff722487499+113529]
	GetHandleVerifier [0x0x7ff72246e298+10616]
	BaseThreadInitThunk [0x0x7ffe0530e8d7+23]
	RtlUserThreadStart [0x0x7ffe060fc34c+44]

2025-07-28 16:17:18 [ERROR] - processar_numero_autos - 0001386-58.2005.8.26.0368: Falha na verificação de sessão.
2025-07-28 16:17:18 [INFO] - processar_numero_autos - Processando número 79/11405: 0003712-34.2003.8.26.0053
2025-07-28 16:17:18 [INFO] - verificar_e_renovar_sessao - Verificando e renovando sessão se necessário
2025-07-28 16:17:18 [ERROR] - garantir_url_consulta - Erro ao garantir URL de consulta: Message: tab crashed
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff72247e935+77845]
	GetHandleVerifier [0x0x7ff72247e990+77936]
	(No symbol) [0x0x7ff722239b0c]
	(No symbol) [0x0x7ff72222727b]
	(No symbol) [0x0x7ff722224eab]
	(No symbol) [0x0x7ff72222590f]
	(No symbol) [0x0x7ff72223453e]
	(No symbol) [0x0x7ff72224a4f1]
	(No symbol) [0x0x7ff72225165a]
	(No symbol) [0x0x7ff7222260ad]
	(No symbol) [0x0x7ff722249ce1]
	(No symbol) [0x0x7ff7222e09e1]
	(No symbol) [0x0x7ff7222b86a3]
	(No symbol) [0x0x7ff722281791]
	(No symbol) [0x0x7ff722282523]
	GetHandleVerifier [0x0x7ff72275684d+3059501]
	GetHandleVerifier [0x0x7ff722750c0d+3035885]
	GetHandleVerifier [0x0x7ff722770400+3164896]
	GetHandleVerifier [0x0x7ff722498c3e+185118]
	GetHandleVerifier [0x0x7ff7224a054f+216111]
	GetHandleVerifier [0x0x7ff7224872e4+113092]
	GetHandleVerifier [0x0x7ff722487499+113529]
	GetHandleVerifier [0x0x7ff72246e298+10616]
	BaseThreadInitThunk [0x0x7ffe0530e8d7+23]
	RtlUserThreadStart [0x0x7ffe060fc34c+44]

2025-07-28 16:17:18 [ERROR] - processar_numero_autos - 0003712-34.2003.8.26.0053: Falha na verificação de sessão.
2025-07-28 16:17:18 [INFO] - verificar_sessao_ativa - Verificando se a sessão ainda está ativa
2025-07-28 16:17:18 [ERROR] - verificar_sessao_ativa - Erro ao verificar sessão: Message: tab crashed
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff72247e935+77845]
	GetHandleVerifier [0x0x7ff72247e990+77936]
	(No symbol) [0x0x7ff722239b0c]
	(No symbol) [0x0x7ff72222727b]
	(No symbol) [0x0x7ff722224eab]
	(No symbol) [0x0x7ff72222590f]
	(No symbol) [0x0x7ff72223453e]
	(No symbol) [0x0x7ff72224a4f1]
	(No symbol) [0x0x7ff72225165a]
	(No symbol) [0x0x7ff7222260ad]
	(No symbol) [0x0x7ff722249ce1]
	(No symbol) [0x0x7ff7222e09e1]
	(No symbol) [0x0x7ff7222b86a3]
	(No symbol) [0x0x7ff722281791]
	(No symbol) [0x0x7ff722282523]
	GetHandleVerifier [0x0x7ff72275684d+3059501]
	GetHandleVerifier [0x0x7ff722750c0d+3035885]
	GetHandleVerifier [0x0x7ff722770400+3164896]
	GetHandleVerifier [0x0x7ff722498c3e+185118]
	GetHandleVerifier [0x0x7ff7224a054f+216111]
	GetHandleVerifier [0x0x7ff7224872e4+113092]
	GetHandleVerifier [0x0x7ff722487499+113529]
	GetHandleVerifier [0x0x7ff72246e298+10616]
	BaseThreadInitThunk [0x0x7ffe0530e8d7+23]
	RtlUserThreadStart [0x0x7ffe060fc34c+44]

2025-07-28 16:17:18 [WARNING] - verificar_sessao_periodica - Sessão expirada no processo 80 - Iniciando recuperação
2025-07-28 16:17:18 [INFO] - _recuperar_sessao_automaticamente - RECUPERAÇÃO DE SESSÃO: Iniciando renovação automática
2025-07-28 16:17:18 [ERROR] - _recuperar_sessao_automaticamente - RECUPERAÇÃO DE SESSÃO: Erro durante recuperação: Message: tab crashed
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff72247e935+77845]
	GetHandleVerifier [0x0x7ff72247e990+77936]
	(No symbol) [0x0x7ff722239b0c]
	(No symbol) [0x0x7ff72222727b]
	(No symbol) [0x0x7ff722224eab]
	(No symbol) [0x0x7ff72222590f]
	(No symbol) [0x0x7ff72223453e]
	(No symbol) [0x0x7ff72224a4f1]
	(No symbol) [0x0x7ff72225165a]
	(No symbol) [0x0x7ff7222260ad]
	(No symbol) [0x0x7ff722249ce1]
	(No symbol) [0x0x7ff7222e09e1]
	(No symbol) [0x0x7ff7222b86a3]
	(No symbol) [0x0x7ff722281791]
	(No symbol) [0x0x7ff722282523]
	GetHandleVerifier [0x0x7ff72275684d+3059501]
	GetHandleVerifier [0x0x7ff722750c0d+3035885]
	GetHandleVerifier [0x0x7ff722770400+3164896]
	GetHandleVerifier [0x0x7ff722498c3e+185118]
	GetHandleVerifier [0x0x7ff7224a054f+216111]
	GetHandleVerifier [0x0x7ff7224872e4+113092]
	GetHandleVerifier [0x0x7ff722487499+113529]
	GetHandleVerifier [0x0x7ff72246e298+10616]
	BaseThreadInitThunk [0x0x7ffe0530e8d7+23]
	RtlUserThreadStart [0x0x7ffe060fc34c+44]

2025-07-28 16:17:18 [ERROR] - verificar_sessao_periodica - Falha na recuperação automática no processo 80
2025-07-28 16:17:18 [ERROR] - executar_processamento - Processamento interrompido por falha de sessão no número 80
2025-07-28 16:17:18 [INFO] - executar_processamento - Processamento concluído - Consultados: 211, Downloads: 7, Falhas: 0, Recuperações: 0
2025-07-28 16:17:18 [INFO] - executar_processamento - Fechando navegador
