﻿"Olá pessoal, tudo joia? Aqui Luiz. Nesse"
vídeo eu vou bater um papo com vocês
"sobre os Tesk runners, tá? Um novo"
recurso que vem chegando ao NHN e que
você vai ter que configurar na sua
"instalação, tá? Vou explicar o que que"
são esses Tesk runners e como que você
"faz a configuração aqui, ó. Facinho"
"fazer, pessoal. Isso aqui só reforça o"
"que eu venho falando já há muitos anos,"
"né? Então, pelo menos desde 2020 aí que"
é importante que você faça a devida
gestão da infra das suas instalações
"NHN. O NITN, como todo software Open"
"Search, ele anda junto com a infra, as"
configurações dele junto com o uso dele.
Não tem como você usar a ferramenta e
"virar as costas paraa infra. Então, no"
"nosso curso e aqui na Promov, eu eu"
promovo isso de você ser um gestor de
"infra também. Hoje em dia, pessoal, é"
muito fácil fazer isso. Teve sim uma
"época, há há anos atrás que era mais"
"complicado gerenciar Docker, servidor,"
swarm. Realmente não era tão simples
"assim, não é mais assim hoje,"
"principalmente agora na era do GPT, né?"
Então é importante que vocês conheçam a
"sua infra, conheça a sua instalação para"
quando sair uma atualização dessa de
"evolução da ferramenta, você não seja"
pego de surpresa. Os seus clientes não
"sejam pegos de surpresa. Não é difícil,"
não tem desculpa para você poder
aprender infra. Todo dia aqui eu tenho
"novatos, pessoas que chegaram agora"
nesse universo e que conseguem colocar
de pé o primeiro servidor. Não é nem
"demorado, pessoal. Já existiu um tempo"
"que demorava um dia, dois dias. Nossa,"
"era legal até fazer, porque você gastava"
mesmo o dia todo ali para poder fazer
isso aí. Hoje você põe de pé um servidor
arrumadinho em menos de meia hora. Então
"não tem mais desculpa, tá gente? Tem que"
aprender. As ferramentas estão evoluindo
cada vez mais. E para você poder
"utilizar algo que é open search, você"
"tem que acompanhar, tem que evoluir"
"junto, tá? Então, nesse vídeo eu vou"
mostrar para vocês como que funciona. Dá
"um like no vídeo, se inscreve no canal e"
comenta que que você achou aqui desse
conteúdo sobre
"Teskuners, pessoal. Então, olha só o que"
"são os TK runners. Pega, pega comigo"
aqui a ideia. Você vai ter o Node code
"no seu Natn. Então você, quando você"
chegar aqui e for rodar um código com
"Node Code, esse código aqui ele é ele é"
executado hoje no mesmo processo do que
o seu
"workflow. Caso dê algum algum problema,"
"caso algo quebre nesse seu node code,"
você acaba quebrando todo o processo do
workflow.
"Então o NTN decidiu separar isso, né,"
"que é o que ele cham de task runners,"
que vai ser um ambiente separado paraa
execução de código do Node code. Então
"assim, eles conseguem ganhar um pouco"
mais de segurança e um pouco mais de
escalabilidade em relação ao à execução
"de código, além, é claro, desse grande"
ganho de que se algo der errado durante
"a execução do código, algo quebrou, um"
"loop infinito, não sei, alguma coisa ali"
"que deu errado com o seu código, ainda"
"mais hoje, né, que a gente usa muito GPT"
para poder escrever código pra gente e
às vezes ele ele ele gera um código lá
que não é legal.
"Então, pensando nisso, né, eles criaram"
esse conceito aqui de testers. E isso
"aqui, pessoal, vai ser obrigatório. Se"
"você abrir aqui a sua instalação do NHN,"
"vamos pegar aqui o meu editor, você vai"
"acompanhar aqui nos logs uma mensagem, ó"
"lá, ó. NHN Run runners enable. Então"
"você, ele tá informando que rodar o NHN"
em fila sem os task runners vai ser
"obrigatório no futuro. Então, a ideia,"
"que que é? os embaixadores, né? A nossa"
"função é trazer para vocês à luz, né,"
essas questões. Eu acho que já é a hora
de você vir já começar a preparar o seu
NHN para quando for obrigatório. E ele
"tá dizendo aqui, ó, vai ser habilitado"
por padrão no futuro para que você não
seja pego de surpresa. Você é um gestor
"de infra. Você que usa o opence, não tem"
"como você fechar os olhos pro seu, pra"
"sua instalação, pro servidor e falar:"
"""Não, eu vou só vou utilizar"". O sistema"
"SAS em geral é assim, o opence não. Você"
"é responsável também pela sua infra, né?"
E vocês vão ver que é super simples
"fazer, pessoal. Eu acho que tá na hora."
Não tem mais desculpa para você não
"aprender a instalar e configurar, até"
porque é um curso rápido. Você aprende
"mesmo assim, em um final de semana você"
é capaz de aprender a instalar e
"configurar o Swarm, o servidor,"
"portainer, habilitar o SSL, configurar"
"um backup, fazer tudo. Nossa, é em uma"
em um final de semana você consegue
"configurar isso daí. Então, olha só, vai"
"ser um recurso obrigatório, tá bom? E"
"como que funciona? Então,"
"pessoal, existem aqui duas variáveis de"
ambiente que vocês vão ter que colocar
no seu no seu stack. NHN runners enable
com valor true para poder habilitar.
"Hoje na gravação desse vídeo, estamos"
aqui na versão 1.90. Você aí do futuro
pode ser que já tenha habilitado isso
"daqui, né? ou não, mas o valor padrão"
hoje é false. Então hoje o NN está
obrigando ainda você a utilizar o Tesk
"Runners, mas daqui a um tempo vai ser"
obrigatório. Então mesmo que você não
"configure, ele vai habilitar pro padrão."
"A minha recomendação, e olha só,"
"pessoal, aqui ó, a minha recomendação é"
que você já configure isso e se caso
"aconteça algum bug, você reporte. Aqui"
na descrição do vídeo tem um link da
nossa comunidade para você vir aqui e
"reportar um bug. Luiz, olha, encontrei"
um bug aqui. Eu vou pedir para você
abrir uma ISO ou eu vou ajudar você a
abrir uma ISO lá no GitHub e eu
encaminho para eles o link dessa IS.
Então você vai ter onde reportar e
principalmente testar os seus workflows.
"Em teoria não vai mudar nada, mas é bom"
"testar, pessoal. Tem que testar, tem"
"como logar, tá? Se você usa o Enil em"
"produção e aqui independente do tamanho,"
"Luía, eu tenho um workflow que roda duas"
"vezes por dia. Nossa, ótimo. Se é"
importante para você rodar duas vezes
"por dia, então é uma instalação"
"importante, você tem que ter cuidado,"
"pessoal. Não é a quantidade, não é o"
"tamanho, é a importância. Quão"
importante é um workflow pro seu
"cliente, um agente que tá rodando"
"durante um lançamento, né? uma automação"
"que você fez que precisa funcionar, não"
"pode ter falha, quão importante ela é."
"Então, por isso que eu acho que é"
importante que vocês cuidem bem da
instalação. Eu acho que tá na hora de
vocês virem aqui e habilitar e testar
pelo menos para verificar tudo tá tudo
OK até que seja obrigatório. Depois que
for obrigatório não tem muito o que
fazer. Vocês têm esse tempo para poder
"se adequar, se necessário. Vou arriscar"
"aqui dizer, vou arriscar dizer que 99%"
das pessoas não vou ter vão ter nenhum
"problema. Aí a questão é, será que você"
não é esse 1%? Eu não sei. Eu não sei e
"também você não sabe. Então na dúvida,"
vamos vir aqui e vamos habilitar esse
recurso e roda os seus workflows e vê se
tá tudo certinho. Pelo menos tá pronto
para quando isso aqui se tornar
"obrigatório, tá? Infra, pessoal, é"
"prevenção, tá?"
"Então aqui, ó, eu vou habilitar ele com"
essa variável e aqui eu vou colocar o
"modo de execução dele. Então, olha só,"
"pessoal, eu vou ter dois modos de"
execução dos Tesk runners. E aqui tem
mais uma dica para vocês. Hoje você vai
ter o modo interno e o modo
"externo. Como é um recurso novo, eu eu"
particularmente acho que é interessante
você fazer uma adoção eh aos poucos dos
"recursos, principalmente mudanças assim,"
"onde você tem que mudar a infra, vai"
mudar o comportamento da ferramenta.
Então eu acho que é interessante você ir
"com menas menos sede pro pote, tá?"
"Então, olha só, pessoal, eu acho que é"
interessante configurar o modo interno
por enquanto. Daqui alguns meses eu
volto e gravo um vídeo novo com vocês.
"Gente, olha, todo mundo testou aqui na"
"comunidade, mais de 2.000 alunos. Todo"
"mundo testou, tá rodando bonitinho."
Agora é hora da gente fazer um teste
então com o modo externo. O modo externo
vai exigir um outro contêiner de NHN só
para rodar
"código. Luiz, funciona? Funciona. Eu"
testei e funciona. Mas eu penso na
compatibilidade. Eu penso em você aí que
não tem o tempo que eu tenho de testar.
Então eu acho que a adoção mais segura é
você realmente ir aos poucos. Vamos
"começar com o internal por enquanto, que"
vai rodar perfeitamente e aos poucos
"vamos aderindo aí ao modo externo, tá"
"bom? Então são dois modos, o modo"
interno e o modo externo. No modo
"interno, quando foi executar o seu"
"workflow e ele achou um node code, ele"
"vai estanciar um outro processo, vai"
"rodar o código, vai pegar o resultado e"
devolver pro seu pro seu workflow.
Basicamente é isso. Então não vai mais
rodar tudo no mesmo processo. No modo
"externo, ele vai fazer isso aí, mas num"
outro contêiner ele vai isolar ainda
mais a execução do código. Por isso que
eu acho que é interessante a gente ir aí
"devagar com isso daí, tá? Então vamos"
"pro interno primeiro, vai ficar mais"
fácil pra gente poder aderir aqui a
"ferramenta. Então pessoal, duas"
variáveis de ambiente para você poder
vir aqui e configurar. Então eu vou
"copiar aqui as duas variáveis, né?"
"Então, eu estou habilitando os runners e"
estou configurando ele pro interno.
Basicamente eu vou vir aqui nas minhas
stacks e eu vou configurar as minhas
stacks aqui. Cadê o meu editor? Eu vou
colar elas. Eu vou colar essas variáveis
aqui. Essa steack é a estec do curso e
ela já é todinha documentada no curso.
Eu explico linha por linha para vocês.
"Pessoal, eu os alunos brincam, né, que"
eu gosto de ensinar você a pescar. Eu
não vou dar o peixe na sua mão. Eu acho
"que é importante isso, porque vai ter um"
"domingão à tarde, vai ter uma reunião"
que você vai fazer e eu não vou estar
ali para poder pegar na sua mão e
ensinar vocês a fazer isso daí. Então eu
vou ensinar vocês no curso para que
"vocês conheçam o porta trêer, tenham uma"
"intimidade com o porta trainer, consigam"
rodar as aplicações sabendo o que que
vocês configuraram. Hoje 2025 não é um
"bicho sete cabeças, tá gente? Mas não é"
"mesmo, tá? Então, olha só, tô"
literalmente copiando e colando essas
"variáveis. Eu só preciso disso, pessoal,"
para poder habilitar esse recurso.
"Agora, se você acha que isso aqui é"
"difícil, imagina a hora que travar"
"alguma coisa, porque trava, pessoal, o"
"software foi feito para dar problema,"
"né? Então, acho que é importante que"
"vocês façam ali o nosso curso, né? o"
para vocês poderem aprender a instalar e
"configurar não só o Enate, mas ele tem o"
"chat útil, a Evolution, o Typebot,"
"vários sistemas, né, para vocês eh"
"instalarem e configurarem. Então, olha"
"só, vim aqui nos meus stacks e coloquei,"
"ó, só isso, pessoal, já habilitei o"
"recurso, já habilitei o task runner, já"
tá rodando o task runner no meu NHN.
"Super simples, super simples mesmo. Você"
"que é aluno, a sua stack do curso já tá"
com essa variável. Então é só você
"baixar uma a nova stack, tá lá, você vem"
"aqui, olha, para você poder adicionar"
essas duas variáveis aí no seu N. Também
"criei um comunicado lá, alertando vocês"
"sobre isso, tá bom? Então, tenho aqui o"
meu Nen rodando. Eu posso até remover já
"esses antigos aqui, né, que esses aqui"
são as versões que não estão rodando com
os Tesk runners. Posso vir aqui deixar
tudo limpinho. Isso aqui é é toque de
"quem mexe com isso aqui, né? De apagar"
"os contorninhos vermelhinho. Olha só,"
"pessoal, a hora que eu venho aqui agora,"
pode ver que sumiu aquela primeira
mensagem que aparecia informando que vai
ser obrigatório utilizar isso. Daí ficou
"mais uma mensagem, mas eu vou falar no"
finalzinho do vídeo para vocês sobre
"esses caras aqui. Só que olha só,"
"pessoal, ele vem aqui, ó, e"
"fala, é, versão do NHN, o editor está"
"acessível. Tem uma linhazinha mais aqui,"
ó. ele registrou um task runner. Então
"agora nesse contêiner, no editor e no"
"worker e também no MCP, eu vou ter"
"rodando não só o meu NHN, mas também um"
segundo processo que é um Tesk runers. É
"isso, pessoal. Se você vier aqui e for"
"rodar os seus workflows, rodam"
"normalmente. Luiz, encontrei um problema"
por algum motivo. O meu aqui não
funcionou como esperado. Aqui na
descrição tem o link do grupo do
WhatsApp para você poder entrar e a
gente conversa lá para poder resolver
"isso daí, tá bom? Você que é aluno, é só"
você buscar o suporte. Terça e quinta
tem o suporte ao vivo para você entrar
"ali, compartilhar a tela, a gente ajuda"
"você ao vivo ali, tá? Mas também tem o"
"fórum, dependendo da urgência do seu"
"problema, a nossa equipe chama você ali"
no privado e a gente acessa sua
"instalação e vai lá e arruma para você,"
"tá? Então pessoal, só isso. Fiz isso"
"aqui, rodou. Então"
"agora o meu, esse node code que eu tenho"
"aqui, ele vai ser executado lá no meu"
"test. Então ele, esse aqui rodou num"
"processo, esse no mesmo processo, esse"
aqui rodou em outro processo. É só para
"dar para vocês algumas garantias a mais,"
"um desempenho a mais. Então, nesse caso,"
depois que você veio aqui adicionou as
"variáveis, eu recomendo que você abra os"
"seus workflows e teste. Luiz, rodou,"
"cara, não deu diferença nenhuma."
Perfeito. Isso é o esperado. Mas caso
"aconteça alguma coisa, pode ser que"
"aconteça, né? Não sei como é que você"
monta também os seus workflows. Então
"você vem aqui e comenta no vídeo, entra"
"no nosso grupo, comenta no grupo ou vai"
pro nosso suporte que a gente vai poder
"ajudar vocês, tá pessoal? Essa aqui é a"
parte um.
Então testers tem que habilitar para
você se preparar porque eles serão
obrigatórios. em algum momento vai vir
uma versão e eles não avisam qual versão
que é que vai vir padrão. Então é melhor
você achar agora uma compatibilidade e
já se preparar para ela já corrigir do
que você ser pego de surpresa numa
versão futura e aí você vai ter o
"cliente te enchendo o saco, o seu"
"workflow parado, a sua infra dando"
trabalho para você e aí aquela correria
que a gente sabe e é essa correria que
eu evito ao máximo no curso para que
"vocês não precisem passar por isso, tá?"
Vou vir aqui agora com vocês na parte
dois. Se eu continuar aqui no meu
"contêiner, eu vou ter mais uma mensagem"
"ali, né? Ó, essa daqui, ó. Offload"
"manual executions. Pessoal, o que"
"acontece? Quando você tá rodando o seu,"
"montando o seu workflow, você aperta ali"
"o botãozinho de testar, certo? Ó, eu"
"aperto o botãozinho aqui, ó, que é o"
botãozinho de teste o workflow. Então
ele vai rodar o seu workflow aí no modo
teste. Acontece que essa execução em
teste ela ela ela funciona no seu
contêiner do editor. Então no fila eu
"tenho editor, o worker e o web hook, né?"
"Agora eu tenho o MCP, né? Tem um quarto"
contêiner para quem quiser configurar o
"MCP, mas vamos dizer que são três no"
modo clássico aí. Então essa execução de
teste roda no editor. Qual é o problema?
Às vezes ele pode dar um um problema ali
no seu editor ou sobrecarregar o seu
editor e aí você fica fora do ar. Vamos
lembrar que o worker ele é mais
otimizado e a chance que tem dele dar um
crash é
"menor, até porque se der um crash no seu"
"worker, você não perde a execução porque"
ela tá usando fila. Então ela fica
simplesmente parada na fila. A hora que
você voltar com o seu worker volta a
"funcionar. O problema é, se travar o"
"workflow, o editor, o seu usuário vai"
"ver que travou. Luiz, não tô abrindo"
"aqui aquele clássico, né? Não tá abrindo"
"meu editor, não tô logando NHN. Luiz,"
travou tudo aqui. Para não dar uma
"experiência ruim pro usuário, eles estão"
configurando aquela variável ali que vai
mover a execução do editor pro worker.
"Então, quando você apertar o botão"
"teste, não vai mais rodar no contêiner"
do editor. Assim que você apertar o
"botão teste, ele vai rodar no contêiner"
"do worker. É só isso, tá? Então, olha"
"só, a mensagem aqui é justamente essa,"
"né? Então ele tá falando, ele tá"
informando que rodar execuções manuais
"no modo fila, pessoal, o modo fila pro"
"NN ele chama de scelling, depende de"
"quem escreve. Tem o Q mode, que é o modo"
"fila e tem o scelling, né? Então cada um"
"lá escreve de um jeito, mas é o mesmo"
"modo fila, tá? Então ele tá falando que"
"no futuro, no futuro as execuções"
manuais serão roteadas para o worker.
Então ele tá ele tá pedindo para vocês
"vir aqui, ó, e colocar esse offload"
"manual execute uns to workers para true,"
para que você já consiga usar essa
maneira e evitar potenciais problemas no
"futuro. Pessoal, de novo, vou repetir"
"aqui, desculpa ser repetitivo. Infra é"
prevenção. Eu não quero que trave o seu
"NN, eu não quero que ele pare. Só que"
"para ele não parar, eu tenho que fazer a"
"manutenção, eu tenho que fazer a famosa"
preventiva. Então isso aqui é uma é uma
segunda mudança que vai ter na
"ferramenta. E eles estão alertando,"
"gente, olha, você que roda de verdade,"
"você que usa em produção, você que tem o"
cliente que tá rodando de verdade seu
"NN, já configura para você poder ter"
"tempo de fazer teste ou fazer adaptação,"
porque em algum momento vai ser
"mandatório isso. Imagina você acordar,"
atualizar para uma versão e aí tá parou
"tudo parado, né? E você não vai ter esse"
"tempo. É importante entender, pessoal. É"
"melhor ter esse tempo agora, quando você"
"não tem problema, do que você ter um um"
você ter um problema e você não vai ter
"tempo para resolver ele, porque o tempo"
tá contra você agora. Agora o tempo tá a
"seu favor. Então aproveita isso,"
pessoal. É isso que eu passo no curso. É
esse espírito que eu passo. O ideal é
não ter o problema. Para não ter
"problema, você tem que conhecer a"
"instalação, minimamente conhecer ela."
"Não precisa ser um expert, não precisa"
"ser um cara técnico, não precisa se"
"aprofundar, mas minimamente tem que ter"
"conhecimento. E ó, o próprio Nate tá de"
"cantando a bola. Ele tá, ele tá falando"
hoje o que eu falo desde 2020 para vocês
"aqui, gente. Vamos testar, vamos"
"homologar. Antes de você dar esse passo,"
"testa, porque é melhor dar problema"
agora que não tem ninguém vendo do que
dar um problema na hora que o cliente
vai ver.
"né? Dá um problema durante o lançamento,"
dá um problema no naquele momento que
você tá sendo testado pelo cliente.
"Então essa é a dica, tá gente? Essa"
daqui também é uma variável única que
"você vai colocar no seu stack, que é"
"essa daqui,"
ó. Eu vou vir aqui e vou colocar como
true. Offload manual execution to
"worker. Então vou vir aqui, ó, ele pede"
"para colocar ela como true, né? Então"
vou fazer o mesmo processo que eu fiz
com os testk runners. Vou editar cada
item do meu
stack aqui. Vou quebrar mais uma linha
"aqui e vou colar ela,"
"ó. Então, eu estou fazendo o que agora?"
Estou informando pro NHN que toda vez
que eu apertar o botãozinho testar ali
"do workflow, enquanto eu desenvolvendo"
"meu workflow, é pro worker rodar e não"
mais o
editor. Isso aqui é para ganha de
"performance também, para ganha de"
"segurança, né? E já que o worker tá ali,"
"vamos utilizar ele, né? De novo, tá,"
pessoal? para proteger o seu editor de
"travamentos, né? Porque pro usuário"
"final é um problema, você aí que é um"
"gestor do NHN, é um problema pro seu"
"usuário ver ali um travamento, mesmo que"
"seja uma coisa que ele mesmo causou, né?"
"Então, no pior dos casos, o worker tem"
mecanismos melhores para lidar com isso
do que o
"próprio editor, né? E no caso do worker,"
"ele reinicia sozinho, ele volta, ninguém"
"vê o editor, as pessoas vão ver. Então,"
"acho que é bom trabalhar com isso daí,"
né?
"Simplão, pessoal. Vim aqui, ó, coloquei"
em todos eles. Posso apagar aqui os
"vermelhinhos que vão aparecer, tá? E"
agora você vai fazer o quê? Você vai
"abrir os seus workflows e vai testar,"
vai clicar no botão executar ali e ver
"se tá funcionando direitinho, né? E como"
é que você sabe que tá rodando
"direitinho? Olha só, eu vou abrir aqui o"
meu editor e eu vou abrir aqui o meu
worker. Eu estou movendo a execução do
"editor pro worker, certo? Então, pode"
"ver que ele veio aqui, ó, mostrou que"
registrou um test no meu editor e
mostrou que registrou um test no meu
worker. Quando eu vier agora aqui e for
"testar o meu workflow, eu apertar esse"
"botãozinho aqui, ó, vamos ver. Ele vai"
"rodar. Ó lá, ó. Rodou. Então, ele fez"
agora o que tinha que fazer. Ele criou
"um test, rodou o node code, fechou o"
testinou o workflow. Se eu olhar
"aqui no editor, olha o que que o editor"
falou para mim. Ele ele ele enfileirou
uma
execução. Ele enfilerou essa execução
"18879. Se eu olhar no worker, tá lá o"
"worker executando. Ó, o worker começou a"
"rodar. Pessoal, essa mensagem de erro"
"aqui é que eu tô usando a versão Bet,"
tá? Mas ela não é para aparecer para
"vocês, tá bom?"
"Ó, o worker pegou essa"
"execução, starter execution, começou a"
"rodar ela. Lá, ó, finished execution,"
terminou de rodar e aí ele veio aqui e
"falou, o job foi finalizado com sucesso."
"Então, olha que legal, começou pelo"
"editor, passou pro worker, o worker"
"rodou, devolveu pro editor. Essa é a"
"mudança. Nesse vai e vem de informação,"
"é importante que você teste, é"
"importante que você homologue, que você"
"venha aqui e abra os seus sistemas, tá?"
"Luís, eu fiz pelo curso de Docker, eu"
tenho aqui um setup mais avançado. Você
"que tem o simples, você que tem um"
"avançado, todo mundo acho que deveria"
"fazer esse teste, tá? De você habilitar"
"os task runners primeiro, testar os task"
"runners, depois você vem e habilita esse"
offload do workers. para você verificar
"se tá tudo certinho, pessoal. É isso."
Você tá se preparando para uma mudança
futura do NN. É uma responsabilidade que
"você tem que ter. E, ó, importante você"
gerenciar sua infra. Quer aprender mais?
"Acessa na descrição, tem o link da do"
promovo web.com para você poder conhecer
"o nosso curso, assinar a nossa a nossa"
comunidade para você ter acesso a todos
"os nossos cursos, tá? Então esse curso"
"setup, instalação, Docker tá disponível"
"nos dois planos, tanto no Martech quanto"
no Eam Makers. Então nos dois planos
"você vai ter total a esses cursos, tá?"
Para que você tenha mais controle e você
passe menos apuros. A ferramenta tá
"evoluindo, pessoal. Tem grandes mudanças"
vindo na NN. Eu tô louco para avisar. É
"questão de semanas, tá? e também vai ter"
vai ter que mexer em variável de
"ambiente. Então, cada vez mais as"
ferramentas estão exigindo que vocês
aprendam minimamente como que elas
"funcionam, como que elas estão"
instaladas para que você possa aqui vir
"aqui e fazer a devida correção. Gostou,"
"pessoal? Dá um like no vídeo, se"
"inscreve no canal, comenta o que foi que"
"você achou desse vídeo. Ó, na descrição"
tem o link da Promov para você poder
assinar nossos cursos e também tem o
link do nosso grupo do WhatsApp e da
nossa comunidade para você poder entrar
lá e bater um papo com a gente. Valeu
"pessoal, um abraço, até mais. M."