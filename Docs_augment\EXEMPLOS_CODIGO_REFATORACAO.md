# 💻 **EXEMPLOS DE CÓDIGO - REFATORAÇÃO TJSP v8**

## 🔧 **1. SISTEMA DE CONFIGURAÇÃO CENTRALIZADO**

### **config/settings.py**
```python
import os
import yaml
from typing import Optional
from dataclasses import dataclass
from pathlib import Path

@dataclass
class TJSPConfig:
    """Configurações específicas do TJSP"""
    base_url: str = "https://esaj.tjsp.jus.br"
    consulta_url: str = "https://esaj.tjsp.jus.br/cjsg/consultaCompleta.do"
    timeout: int = 30
    max_retries: int = 3
    delay_between_requests: float = 2.0

@dataclass
class BrowserConfig:
    """Configurações do navegador"""
    browser_type: str = "chrome"
    headless: bool = False
    window_size: tuple = (1920, 1080)
    download_dir: str = "./downloads"
    profile_path: Optional[str] = None

@dataclass
class LoggingConfig:
    """Configurações de logging"""
    level: str = "INFO"
    file_path: str = "./logs/tjsp.log"
    max_file_size: int = 10485760  # 10MB
    backup_count: int = 5
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

class Settings:
    """Classe principal de configurações (Singleton Pattern)"""
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, 'initialized'):
            self.tjsp = TJSPConfig()
            self.browser = BrowserConfig()
            self.logging = LoggingConfig()
            self.initialized = True
    
    @classmethod
    def load_from_yaml(cls, config_path: str = "config.yaml") -> 'Settings':
        """Carrega configurações de arquivo YAML"""
        instance = cls()
        
        if Path(config_path).exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
            
            # Atualiza configurações com dados do YAML
            if 'tjsp' in config_data:
                for key, value in config_data['tjsp'].items():
                    setattr(instance.tjsp, key, value)
            
            if 'browser' in config_data:
                for key, value in config_data['browser'].items():
                    setattr(instance.browser, key, value)
            
            if 'logging' in config_data:
                for key, value in config_data['logging'].items():
                    setattr(instance.logging, key, value)
        
        return instance
    
    def load_from_env(self):
        """Carrega configurações de variáveis de ambiente"""
        # TJSP configs
        self.tjsp.base_url = os.getenv('TJSP_BASE_URL', self.tjsp.base_url)
        self.tjsp.timeout = int(os.getenv('TJSP_TIMEOUT', self.tjsp.timeout))
        
        # Browser configs
        self.browser.headless = os.getenv('BROWSER_HEADLESS', 'false').lower() == 'true'
        self.browser.download_dir = os.getenv('DOWNLOAD_DIR', self.browser.download_dir)
        
        # Logging configs
        self.logging.level = os.getenv('LOG_LEVEL', self.logging.level)
        self.logging.file_path = os.getenv('LOG_FILE', self.logging.file_path)
```

### **config.yaml (Exemplo)**
```yaml
tjsp:
  base_url: "https://esaj.tjsp.jus.br"
  consulta_url: "https://esaj.tjsp.jus.br/cjsg/consultaCompleta.do"
  timeout: 30
  max_retries: 3
  delay_between_requests: 2.0

browser:
  browser_type: "chrome"
  headless: false
  window_size: [1920, 1080]
  download_dir: "./downloads"
  profile_path: null

logging:
  level: "INFO"
  file_path: "./logs/tjsp.log"
  max_file_size: 10485760
  backup_count: 5
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

environment: "development"
```

---

## 📝 **2. SISTEMA DE LOGGING UNIFICADO**

### **config/logging_config.py**
```python
import logging
import logging.config
from pathlib import Path
from typing import Dict, Any

class LoggingManager:
    """Gerenciador centralizado de logging"""
    
    @staticmethod
    def setup_logging(config: LoggingConfig) -> None:
        """Configura o sistema de logging"""
        
        # Cria diretório de logs se não existir
        log_dir = Path(config.file_path).parent
        log_dir.mkdir(parents=True, exist_ok=True)
        
        logging_config = {
            'version': 1,
            'disable_existing_loggers': False,
            'formatters': {
                'detailed': {
                    'format': config.format
                },
                'simple': {
                    'format': '%(levelname)s - %(message)s'
                }
            },
            'handlers': {
                'console': {
                    'class': 'logging.StreamHandler',
                    'level': 'INFO',
                    'formatter': 'simple',
                    'stream': 'ext://sys.stdout'
                },
                'file': {
                    'class': 'logging.handlers.RotatingFileHandler',
                    'level': config.level,
                    'formatter': 'detailed',
                    'filename': config.file_path,
                    'maxBytes': config.max_file_size,
                    'backupCount': config.backup_count,
                    'encoding': 'utf-8'
                },
                'error_file': {
                    'class': 'logging.handlers.RotatingFileHandler',
                    'level': 'ERROR',
                    'formatter': 'detailed',
                    'filename': config.file_path.replace('.log', '_errors.log'),
                    'maxBytes': config.max_file_size,
                    'backupCount': config.backup_count,
                    'encoding': 'utf-8'
                }
            },
            'loggers': {
                'tjsp': {
                    'level': config.level,
                    'handlers': ['console', 'file', 'error_file'],
                    'propagate': False
                },
                'selenium': {
                    'level': 'WARNING',
                    'handlers': ['file'],
                    'propagate': False
                }
            },
            'root': {
                'level': config.level,
                'handlers': ['console', 'file']
            }
        }
        
        logging.config.dictConfig(logging_config)

def get_logger(name: str) -> logging.Logger:
    """Retorna logger configurado"""
    return logging.getLogger(f"tjsp.{name}")
```

---

## 🏭 **3. FACTORY PATTERN PARA WEBDRIVER**

### **web/browser_manager.py**
```python
from abc import ABC, abstractmethod
from selenium import webdriver
from selenium.webdriver.chrome.service import Service as ChromeService
from selenium.webdriver.firefox.service import Service as FirefoxService
from selenium.webdriver.chrome.options import Options as ChromeOptions
from selenium.webdriver.firefox.options import Options as FirefoxOptions
from webdriver_manager.chrome import ChromeDriverManager
from webdriver_manager.firefox import GeckoDriverManager
from typing import Optional
import logging

logger = logging.getLogger("tjsp.browser")

class WebDriverFactory(ABC):
    """Factory abstrata para criação de WebDrivers"""
    
    @abstractmethod
    def create_driver(self, config: BrowserConfig) -> webdriver.Remote:
        pass

class ChromeDriverFactory(WebDriverFactory):
    """Factory para Chrome WebDriver"""
    
    def create_driver(self, config: BrowserConfig) -> webdriver.Chrome:
        options = ChromeOptions()
        
        # Configurações básicas
        if config.headless:
            options.add_argument("--headless")
        
        options.add_argument(f"--window-size={config.window_size[0]},{config.window_size[1]}")
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-gpu")
        
        # Configurações de download
        prefs = {
            "download.default_directory": str(Path(config.download_dir).absolute()),
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True
        }
        options.add_experimental_option("prefs", prefs)
        
        # Perfil personalizado
        if config.profile_path:
            options.add_argument(f"--user-data-dir={config.profile_path}")
        
        # Serviço do ChromeDriver
        service = ChromeService(ChromeDriverManager().install())
        
        logger.info("Criando Chrome WebDriver")
        return webdriver.Chrome(service=service, options=options)

class FirefoxDriverFactory(WebDriverFactory):
    """Factory para Firefox WebDriver"""
    
    def create_driver(self, config: BrowserConfig) -> webdriver.Firefox:
        options = FirefoxOptions()
        
        if config.headless:
            options.add_argument("--headless")
        
        # Configurações de download
        options.set_preference("browser.download.folderList", 2)
        options.set_preference("browser.download.dir", str(Path(config.download_dir).absolute()))
        options.set_preference("browser.helperApps.neverAsk.saveToDisk", "application/pdf")
        
        service = FirefoxService(GeckoDriverManager().install())
        
        logger.info("Criando Firefox WebDriver")
        return webdriver.Firefox(service=service, options=options)

class BrowserManager:
    """Gerenciador principal de navegadores"""
    
    def __init__(self, config: BrowserConfig):
        self.config = config
        self.driver: Optional[webdriver.Remote] = None
        self._factories = {
            "chrome": ChromeDriverFactory(),
            "firefox": FirefoxDriverFactory()
        }
    
    def create_driver(self) -> webdriver.Remote:
        """Cria WebDriver baseado na configuração"""
        factory = self._factories.get(self.config.browser_type)
        if not factory:
            raise ValueError(f"Browser {self.config.browser_type} não suportado")
        
        self.driver = factory.create_driver(self.config)
        logger.info(f"WebDriver {self.config.browser_type} criado com sucesso")
        return self.driver
    
    def quit_driver(self):
        """Encerra o WebDriver"""
        if self.driver:
            self.driver.quit()
            logger.info("WebDriver encerrado")
            self.driver = None
```

---

## 🗄️ **4. REPOSITORY PATTERN PARA DADOS**

### **data/repositories.py**
```python
from abc import ABC, abstractmethod
from typing import Optional, List, Dict, Any
import json
import pandas as pd
from pathlib import Path
import logging

logger = logging.getLogger("tjsp.data")

class CheckpointRepository(ABC):
    """Repository abstrato para checkpoint"""
    
    @abstractmethod
    def save(self, data: Dict[str, Any]) -> bool:
        pass
    
    @abstractmethod
    def load(self) -> Optional[Dict[str, Any]]:
        pass
    
    @abstractmethod
    def exists(self) -> bool:
        pass

class JSONCheckpointRepository(CheckpointRepository):
    """Repository para checkpoint em JSON"""
    
    def __init__(self, file_path: str):
        self.file_path = Path(file_path)
    
    def save(self, data: Dict[str, Any]) -> bool:
        try:
            self.file_path.parent.mkdir(parents=True, exist_ok=True)
            with open(self.file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            logger.info(f"Checkpoint salvo em {self.file_path}")
            return True
        except Exception as e:
            logger.error(f"Erro ao salvar checkpoint: {e}")
            return False
    
    def load(self) -> Optional[Dict[str, Any]]:
        try:
            if self.file_path.exists():
                with open(self.file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                logger.info(f"Checkpoint carregado de {self.file_path}")
                return data
        except Exception as e:
            logger.error(f"Erro ao carregar checkpoint: {e}")
        return None
    
    def exists(self) -> bool:
        return self.file_path.exists()

class ExcelCheckpointRepository(CheckpointRepository):
    """Repository para checkpoint em Excel"""
    
    def __init__(self, file_path: str):
        self.file_path = Path(file_path)
    
    def save(self, data: Dict[str, Any]) -> bool:
        try:
            # Converte dados para DataFrame
            if 'processos' in data:
                df = pd.DataFrame(data['processos'])
                df.to_excel(self.file_path, index=False)
                logger.info(f"Checkpoint Excel salvo em {self.file_path}")
                return True
        except Exception as e:
            logger.error(f"Erro ao salvar checkpoint Excel: {e}")
            return False
    
    def load(self) -> Optional[Dict[str, Any]]:
        try:
            if self.file_path.exists():
                df = pd.read_excel(self.file_path)
                data = {
                    'processos': df.to_dict('records'),
                    'total': len(df)
                }
                logger.info(f"Checkpoint Excel carregado de {self.file_path}")
                return data
        except Exception as e:
            logger.error(f"Erro ao carregar checkpoint Excel: {e}")
        return None
    
    def exists(self) -> bool:
        return self.file_path.exists()

class RepositoryFactory:
    """Factory para criação de repositories"""
    
    @staticmethod
    def create_checkpoint_repository(file_path: str) -> CheckpointRepository:
        """Cria repository baseado na extensão do arquivo"""
        path = Path(file_path)
        
        if path.suffix.lower() == '.json':
            return JSONCheckpointRepository(file_path)
        elif path.suffix.lower() in ['.xlsx', '.xls']:
            return ExcelCheckpointRepository(file_path)
        else:
            raise ValueError(f"Formato de arquivo não suportado: {path.suffix}")
```

---

## 🎯 **5. DEPENDENCY INJECTION CONTAINER**

### **core/di_container.py**
```python
from typing import Type, TypeVar, Dict, Any, Callable
import logging

logger = logging.getLogger("tjsp.di")

T = TypeVar('T')

class DIContainer:
    """Container simples para Dependency Injection"""
    
    def __init__(self):
        self._services: Dict[Type, Any] = {}
        self._singletons: Dict[Type, Any] = {}
        self._factories: Dict[Type, Callable] = {}
    
    def register_singleton(self, interface: Type[T], implementation: Type[T]) -> None:
        """Registra um serviço como singleton"""
        self._services[interface] = implementation
        logger.debug(f"Singleton registrado: {interface.__name__} -> {implementation.__name__}")
    
    def register_transient(self, interface: Type[T], factory: Callable[[], T]) -> None:
        """Registra um serviço como transient (nova instância a cada resolução)"""
        self._factories[interface] = factory
        logger.debug(f"Transient registrado: {interface.__name__}")
    
    def register_instance(self, interface: Type[T], instance: T) -> None:
        """Registra uma instância específica"""
        self._singletons[interface] = instance
        logger.debug(f"Instância registrada: {interface.__name__}")
    
    def resolve(self, interface: Type[T]) -> T:
        """Resolve uma dependência"""
        # Verifica se já existe uma instância singleton
        if interface in self._singletons:
            return self._singletons[interface]
        
        # Verifica se é um factory transient
        if interface in self._factories:
            return self._factories[interface]()
        
        # Verifica se é um singleton registrado
        if interface in self._services:
            implementation = self._services[interface]
            instance = implementation()
            self._singletons[interface] = instance
            return instance
        
        raise ValueError(f"Serviço não registrado: {interface.__name__}")

# Container global
container = DIContainer()

def get_container() -> DIContainer:
    """Retorna o container global"""
    return container
```

---

## 🔄 **6. EXEMPLO DE USO INTEGRADO**

### **main.py**
```python
from config.settings import Settings
from config.logging_config import LoggingManager
from core.di_container import get_container
from web.browser_manager import BrowserManager
from data.repositories import RepositoryFactory
import logging

def setup_dependencies():
    """Configura todas as dependências"""
    # Carrega configurações
    settings = Settings.load_from_yaml()
    settings.load_from_env()
    
    # Configura logging
    LoggingManager.setup_logging(settings.logging)
    
    # Configura DI Container
    container = get_container()
    container.register_instance(Settings, settings)
    container.register_singleton(BrowserManager, lambda: BrowserManager(settings.browser))
    
    # Repositories
    checkpoint_repo = RepositoryFactory.create_checkpoint_repository("checkpoint.json")
    container.register_instance(CheckpointRepository, checkpoint_repo)

def main():
    """Função principal"""
    setup_dependencies()
    
    logger = logging.getLogger("tjsp.main")
    logger.info("Iniciando aplicação TJSP")
    
    # Resolve dependências
    container = get_container()
    browser_manager = container.resolve(BrowserManager)
    
    try:
        # Cria driver
        driver = browser_manager.create_driver()
        
        # Lógica principal aqui...
        logger.info("Processamento concluído com sucesso")
        
    except Exception as e:
        logger.error(f"Erro durante processamento: {e}")
    finally:
        browser_manager.quit_driver()

if __name__ == "__main__":
    main()
```

---

## 🧪 **7. SISTEMA DE TESTES UNITÁRIOS**

### **tests/test_config/test_settings.py**
```python
import pytest
import tempfile
import yaml
from pathlib import Path
from config.settings import Settings, TJSPConfig, BrowserConfig

class TestSettings:
    """Testes para sistema de configurações"""

    def test_singleton_pattern(self):
        """Testa se Settings implementa Singleton corretamente"""
        settings1 = Settings()
        settings2 = Settings()
        assert settings1 is settings2

    def test_default_values(self):
        """Testa valores padrão das configurações"""
        settings = Settings()
        assert settings.tjsp.base_url == "https://esaj.tjsp.jus.br"
        assert settings.browser.browser_type == "chrome"
        assert settings.logging.level == "INFO"

    def test_load_from_yaml(self):
        """Testa carregamento de configurações do YAML"""
        config_data = {
            'tjsp': {
                'base_url': 'https://test.tjsp.jus.br',
                'timeout': 60
            },
            'browser': {
                'headless': True,
                'browser_type': 'firefox'
            }
        }

        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config_data, f)
            config_path = f.name

        try:
            settings = Settings.load_from_yaml(config_path)
            assert settings.tjsp.base_url == 'https://test.tjsp.jus.br'
            assert settings.tjsp.timeout == 60
            assert settings.browser.headless == True
            assert settings.browser.browser_type == 'firefox'
        finally:
            Path(config_path).unlink()

### **tests/test_web/test_browser_manager.py**
```python
import pytest
from unittest.mock import Mock, patch
from web.browser_manager import BrowserManager, ChromeDriverFactory
from config.settings import BrowserConfig

class TestBrowserManager:
    """Testes para gerenciamento de navegador"""

    @pytest.fixture
    def browser_config(self):
        """Fixture com configuração de teste"""
        return BrowserConfig(
            browser_type="chrome",
            headless=True,
            download_dir="./test_downloads"
        )

    @patch('web.browser_manager.webdriver.Chrome')
    @patch('web.browser_manager.ChromeDriverManager')
    def test_create_chrome_driver(self, mock_driver_manager, mock_chrome, browser_config):
        """Testa criação do Chrome WebDriver"""
        mock_driver_manager.return_value.install.return_value = "/path/to/chromedriver"
        mock_driver = Mock()
        mock_chrome.return_value = mock_driver

        manager = BrowserManager(browser_config)
        driver = manager.create_driver()

        assert driver == mock_driver
        mock_chrome.assert_called_once()

    def test_unsupported_browser(self, browser_config):
        """Testa erro para navegador não suportado"""
        browser_config.browser_type = "safari"
        manager = BrowserManager(browser_config)

        with pytest.raises(ValueError, match="Browser safari não suportado"):
            manager.create_driver()

### **tests/test_data/test_repositories.py**
```python
import pytest
import tempfile
import json
from pathlib import Path
from data.repositories import JSONCheckpointRepository, RepositoryFactory

class TestJSONCheckpointRepository:
    """Testes para repository JSON"""

    @pytest.fixture
    def temp_file(self):
        """Fixture com arquivo temporário"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            yield f.name
        Path(f.name).unlink(missing_ok=True)

    def test_save_and_load(self, temp_file):
        """Testa salvamento e carregamento de dados"""
        repo = JSONCheckpointRepository(temp_file)
        test_data = {
            'processos': [
                {'numero': '123456', 'status': 'processado'},
                {'numero': '789012', 'status': 'pendente'}
            ],
            'total': 2
        }

        # Testa salvamento
        assert repo.save(test_data) == True
        assert repo.exists() == True

        # Testa carregamento
        loaded_data = repo.load()
        assert loaded_data == test_data

    def test_repository_factory(self):
        """Testa factory de repositories"""
        json_repo = RepositoryFactory.create_checkpoint_repository("test.json")
        assert isinstance(json_repo, JSONCheckpointRepository)

        excel_repo = RepositoryFactory.create_checkpoint_repository("test.xlsx")
        assert isinstance(excel_repo, ExcelCheckpointRepository)

        with pytest.raises(ValueError):
            RepositoryFactory.create_checkpoint_repository("test.txt")

### **tests/conftest.py**
```python
import pytest
import logging
from config.settings import Settings
from core.di_container import DIContainer

@pytest.fixture(scope="session", autouse=True)
def setup_test_logging():
    """Configura logging para testes"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

@pytest.fixture
def test_settings():
    """Fixture com configurações de teste"""
    settings = Settings()
    settings.tjsp.base_url = "https://test.tjsp.jus.br"
    settings.browser.headless = True
    settings.logging.level = "DEBUG"
    return settings

@pytest.fixture
def clean_di_container():
    """Fixture que limpa o DI container para cada teste"""
    container = DIContainer()
    yield container
    # Cleanup após o teste
    container._services.clear()
    container._singletons.clear()
    container._factories.clear()

### **pytest.ini**
```ini
[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts =
    --verbose
    --tb=short
    --cov=.
    --cov-report=html
    --cov-report=term-missing
    --cov-fail-under=80
markers =
    unit: Unit tests
    integration: Integration tests
    slow: Slow running tests
```

---

## 📊 **8. TESTES DE INTEGRAÇÃO**

### **tests/integration/test_tjsp_flow.py**
```python
import pytest
from unittest.mock import Mock, patch
from core.processor import TJSPProcessor
from config.settings import Settings

@pytest.mark.integration
class TestTJSPIntegrationFlow:
    """Testes de integração do fluxo completo"""

    @pytest.fixture
    def processor(self, test_settings):
        """Fixture com processador configurado"""
        return TJSPProcessor(test_settings)

    @patch('web.browser_manager.webdriver.Chrome')
    def test_complete_processing_flow(self, mock_chrome, processor):
        """Testa fluxo completo de processamento"""
        # Mock do WebDriver
        mock_driver = Mock()
        mock_chrome.return_value = mock_driver

        # Mock das páginas web
        mock_driver.get.return_value = None
        mock_driver.find_element.return_value = Mock()

        # Dados de teste
        test_processes = [
            {'numero': '1234567-89.2023.8.26.0001'},
            {'numero': '9876543-21.2023.8.26.0002'}
        ]

        # Executa processamento
        result = processor.process_batch(test_processes)

        # Validações
        assert result['success'] == True
        assert result['processed_count'] >= 0
        assert 'errors' in result

@pytest.mark.slow
class TestPerformance:
    """Testes de performance"""

    def test_processing_time_benchmark(self):
        """Benchmark de tempo de processamento"""
        import time

        start_time = time.time()

        # Simula processamento de 100 processos
        for i in range(100):
            # Simula operação de processamento
            time.sleep(0.01)

        end_time = time.time()
        processing_time = end_time - start_time

        # Deve processar 100 itens em menos de 5 segundos
        assert processing_time < 5.0
```

---

## 🚀 **9. SCRIPT DE MIGRAÇÃO**

### **scripts/migrate_from_legacy.py**
```python
#!/usr/bin/env python3
"""
Script para migração do sistema legado para nova arquitetura
"""

import sys
import logging
from pathlib import Path
from typing import Dict, Any
import json

# Adiciona o diretório raiz ao path
sys.path.append(str(Path(__file__).parent.parent))

from config.settings import Settings
from config.logging_config import LoggingManager
from data.repositories import RepositoryFactory

logger = logging.getLogger("tjsp.migration")

class LegacyMigrator:
    """Migrador do sistema legado"""

    def __init__(self, legacy_path: str, new_path: str):
        self.legacy_path = Path(legacy_path)
        self.new_path = Path(new_path)

    def migrate_checkpoint(self) -> bool:
        """Migra arquivo de checkpoint legado"""
        try:
            legacy_checkpoint = self.legacy_path / "checkpoint_processamento.json"

            if not legacy_checkpoint.exists():
                logger.warning("Arquivo de checkpoint legado não encontrado")
                return False

            # Carrega dados legados
            with open(legacy_checkpoint, 'r', encoding='utf-8') as f:
                legacy_data = json.load(f)

            # Converte para novo formato
            new_data = self._convert_checkpoint_format(legacy_data)

            # Salva no novo formato
            new_repo = RepositoryFactory.create_checkpoint_repository(
                str(self.new_path / "checkpoint.json")
            )

            success = new_repo.save(new_data)

            if success:
                logger.info("Checkpoint migrado com sucesso")
            else:
                logger.error("Falha na migração do checkpoint")

            return success

        except Exception as e:
            logger.error(f"Erro na migração do checkpoint: {e}")
            return False

    def _convert_checkpoint_format(self, legacy_data: Dict[str, Any]) -> Dict[str, Any]:
        """Converte formato legado para novo formato"""
        # Implementa conversão específica baseada no formato legado
        converted = {
            'version': '2.0',
            'timestamp': legacy_data.get('timestamp'),
            'processos': [],
            'statistics': {
                'total': 0,
                'processed': 0,
                'errors': 0
            }
        }

        # Converte processos
        if 'processos' in legacy_data:
            for processo in legacy_data['processos']:
                converted_processo = {
                    'numero': processo.get('numero'),
                    'status': processo.get('status', 'pendente'),
                    'data_processamento': processo.get('data_processamento'),
                    'resultado': processo.get('resultado')
                }
                converted['processos'].append(converted_processo)

        converted['statistics']['total'] = len(converted['processos'])
        converted['statistics']['processed'] = len([
            p for p in converted['processos']
            if p['status'] == 'processado'
        ])

        return converted

def main():
    """Função principal de migração"""
    if len(sys.argv) != 3:
        print("Uso: python migrate_from_legacy.py <caminho_legado> <caminho_novo>")
        sys.exit(1)

    legacy_path = sys.argv[1]
    new_path = sys.argv[2]

    # Configura logging
    settings = Settings()
    LoggingManager.setup_logging(settings.logging)

    logger.info("Iniciando migração do sistema legado")

    # Executa migração
    migrator = LegacyMigrator(legacy_path, new_path)

    success = migrator.migrate_checkpoint()

    if success:
        logger.info("Migração concluída com sucesso")
        print("✅ Migração concluída com sucesso!")
    else:
        logger.error("Falha na migração")
        print("❌ Falha na migração!")
        sys.exit(1)

if __name__ == "__main__":
    main()
```
