# 🚀 **FRAMEWORK COMPLETO N8N + EVOLUTION API**

[![Docker](https://img.shields.io/badge/Docker-20.10+-blue.svg)](https://www.docker.com/)
[![N8N](https://img.shields.io/badge/N8N-Latest-green.svg)](https://n8n.io/)
[![Evolution API](https://img.shields.io/badge/Evolution%20API-v2.2.0-orange.svg)](https://github.com/EvolutionAPI/evolution-api)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

## 📋 **VISÃO GERAL**

Framework modular e escalável para automação de workflows (N8N) com integração WhatsApp (Evolution API). Oferece instalações flexíveis desde desenvolvimento até produção empresarial.

### **🎯 Características Principais:**

- ✅ **Modular**: Instale apenas o que precisa
- ✅ **Escalável**: De desenvolvimento até produção
- ✅ **Isolado**: Bancos dedicados para cada serviço
- ✅ **Monitorado**: Stack completo de observabilidade
- ✅ **Seguro**: Configurações de segurança por padrão
- ✅ **Documentado**: Documentação técnica completa

---

## 📦 **MÓDULOS DISPONÍVEIS**

### **1. 🔧 N8N Básico**
```
Componentes: N8N + PostgreSQL + Redis
Recursos: 2GB RAM, 10GB Disk
Uso: Desenvolvimento, testes, projetos pequenos
Tempo de instalação: ~5 minutos
```

### **2. 🏢 N8N Completo**
```
Componentes: N8N + Workers + Monitoramento
Recursos: 4GB RAM, 20GB Disk
Uso: Produção, alta disponibilidade
Tempo de instalação: ~15 minutos
```

### **3. 📱 Evolution API**
```
Componentes: Evolution + PostgreSQL + Redis dedicados
Recursos: 2GB RAM, 10GB Disk
Uso: Integração WhatsApp independente
Tempo de instalação: ~10 minutos
```

### **4. 🌟 Framework Integrado**
```
Componentes: Todos os módulos + Proxy + SSL
Recursos: 8GB RAM, 50GB Disk
Uso: Solução empresarial completa
Tempo de instalação: ~30 minutos
```

---

## 🚀 **INSTALAÇÃO RÁPIDA**

### **Pré-requisitos:**
- Docker 20.10+
- Docker Compose 2.0+
- 4GB RAM (recomendado)
- 20GB espaço em disco

### **Instalação Interativa:**
```bash
# 1. Clonar repositório
git clone https://github.com/seu-usuario/n8n-evolution-framework.git
cd n8n-evolution-framework

# 2. Executar instalador
chmod +x install-framework.sh
./install-framework.sh

# 3. Seguir menu interativo
```

### **Instalação Direta por Módulo:**

#### **N8N Básico:**
```bash
chmod +x scripts/install-n8n-basic.sh
./scripts/install-n8n-basic.sh
```

#### **Evolution API:**
```bash
chmod +x scripts/install-evolution-api.sh
./scripts/install-evolution-api.sh
```

---

## 🔗 **ACESSOS APÓS INSTALAÇÃO**

### **N8N Básico:**
- **URL**: http://localhost:5678
- **Usuário**: admin
- **Senha**: admin123

### **Evolution API:**
- **URL**: http://localhost:8001
- **API Key**: evolution_api_key_123
- **Manager**: Interface web completa

### **N8N Completo (quando disponível):**
- **N8N**: http://localhost:5678
- **Grafana**: http://localhost:3000
- **Prometheus**: http://localhost:9090
- **PgAdmin**: http://localhost:5050

---

## 📁 **ESTRUTURA DO PROJETO**

```
n8n-evolution-framework/
├── 📄 README_FRAMEWORK.md              # Este arquivo
├── 📄 FRAMEWORK_INSTALACAO_COMPLETO.md # Documentação completa
├── 📄 DOCUMENTACAO_TECNICA_COMPLETA.md # Documentação técnica
├── 🔧 install-framework.sh             # Instalador interativo
├── 📁 modules/                         # Módulos individuais
│   ├── 📁 n8n-basic/                  # N8N mínimo
│   │   ├── docker-compose.yml
│   │   ├── .env
│   │   └── README.md
│   ├── 📁 n8n-complete/               # N8N completo
│   ├── 📁 evolution-api/              # Evolution standalone
│   │   ├── docker-compose.yml
│   │   ├── .env
│   │   └── README.md
│   └── 📁 integrated/                 # Framework integrado
├── 📁 scripts/                        # Scripts de instalação
│   ├── install-n8n-basic.sh
│   ├── install-evolution-api.sh
│   ├── install-n8n-complete.sh
│   └── health-check.sh
├── 📁 configs/                        # Configurações
│   ├── 📁 nginx/                      # Proxy reverso
│   ├── 📁 grafana/                    # Dashboards
│   ├── 📁 prometheus/                 # Métricas
│   └── 📁 ssl/                        # Certificados
└── 📁 docs/                           # Documentação adicional
    ├── INSTALLATION.md
    ├── CONFIGURATION.md
    ├── TROUBLESHOOTING.md
    └── API_REFERENCE.md
```

---

## 🔧 **COMANDOS ÚTEIS**

### **Gerenciamento Geral:**
```bash
# Status de todos os serviços
docker-compose ps

# Logs em tempo real
docker-compose logs -f

# Parar todos os serviços
docker-compose down

# Reiniciar serviços
docker-compose restart

# Atualizar imagens
docker-compose pull && docker-compose up -d
```

### **Backup e Restore:**
```bash
# Backup completo
./scripts/backup-all.sh

# Restore específico
./scripts/restore-n8n.sh backup-20240125.sql

# Backup apenas N8N
docker-compose exec postgres pg_dump -U n8n_user n8n > n8n-backup.sql
```

### **Monitoramento:**
```bash
# Health check completo
./scripts/health-check.sh

# Recursos do sistema
docker stats

# Logs específicos
docker-compose logs n8n
docker-compose logs evolution-api
```

---

## 🔒 **SEGURANÇA**

### **⚠️ Configurações Padrão (DESENVOLVIMENTO):**
- N8N: admin/admin123
- Evolution API Key: evolution_api_key_123
- PostgreSQL: senhas simples

### **🛡️ Para Produção:**
1. **Alterar todas as senhas**
2. **Configurar HTTPS/SSL**
3. **Restringir acesso às portas**
4. **Configurar firewall**
5. **Implementar backup automatizado**
6. **Monitorar logs de segurança**

### **🔐 Script de Segurança:**
```bash
# Gerar senhas seguras
./scripts/generate-secure-passwords.sh

# Configurar SSL
./scripts/setup-ssl.sh

# Configurar firewall
./scripts/configure-firewall.sh
```

---

## 📊 **MONITORAMENTO**

### **Métricas Disponíveis:**
- **Sistema**: CPU, RAM, Disk, Network
- **N8N**: Executions, Workflows, Performance
- **Evolution**: Instances, Messages, Connections
- **Bancos**: Connections, Queries, Performance

### **Dashboards Grafana:**
- Overview Geral
- N8N Performance
- Evolution API Metrics
- System Resources
- Database Performance

---

## 🆘 **SUPORTE E TROUBLESHOOTING**

### **Problemas Comuns:**

#### **1. Serviços não iniciam:**
```bash
# Verificar logs
docker-compose logs

# Verificar recursos
docker system df
free -h

# Reiniciar Docker
sudo systemctl restart docker
```

#### **2. N8N não conecta ao banco:**
```bash
# Verificar conectividade
docker-compose exec postgres pg_isready -U n8n_user -d n8n

# Verificar variáveis
docker-compose config
```

#### **3. Evolution API não funciona:**
```bash
# Verificar logs específicos
docker-compose logs evolution-api

# Verificar bancos dedicados
docker-compose exec postgres-evolution pg_isready
docker-compose exec redis-evolution redis-cli ping
```

### **🔍 Diagnóstico Avançado:**
```bash
# Health check completo
./scripts/health-check.sh

# Análise de performance
./scripts/performance-analysis.sh

# Verificação de segurança
./scripts/security-audit.sh
```

---

## 🤝 **CONTRIBUIÇÃO**

1. Fork o projeto
2. Crie uma branch para sua feature
3. Commit suas mudanças
4. Push para a branch
5. Abra um Pull Request

---

## 📄 **LICENÇA**

Este projeto está licenciado sob a MIT License - veja o arquivo [LICENSE](LICENSE) para detalhes.

---

## 🙏 **AGRADECIMENTOS**

- [N8N Team](https://n8n.io/) - Plataforma de automação
- [Evolution API](https://github.com/EvolutionAPI/evolution-api) - WhatsApp Integration
- [Docker](https://www.docker.com/) - Containerização
- Comunidade Open Source

---

**📞 Suporte:** [Issues](https://github.com/seu-usuario/n8n-evolution-framework/issues)
**📧 Contato:** <EMAIL>
**🌐 Website:** https://seu-website.com

---

**⭐ Se este projeto foi útil, considere dar uma estrela!**
