# 🔧 Guia de Solução de Problemas - TJSP End-to-End

## 🚨 Problemas Comuns e Soluções

### **1. <PERSON>rro: "Python não encontrado"**

**Sintomas:**
```
❌ Python não encontrado!
'python' is not recognized as an internal or external command
```

**Soluções:**
1. **Instalar Python 3.8+**
   - Baixe de: https://python.org/downloads/
   - ✅ **IMPORTANTE**: Marque "Add Python to PATH" durante instalação

2. **Verificar PATH**
   ```cmd
   # Verificar se Python está no PATH
   echo %PATH%
   
   # Adicionar manualmente (substitua pelo seu caminho)
   set PATH=%PATH%;C:\Python39;C:\Python39\Scripts
   ```

3. **Usar python3 em vez de python**
   ```bash
   python3 orquestrador_tjsp_e2e.py
   ```

---

### **2. <PERSON>rro: "Sistema não encontrado"**

**Sintomas:**
```
❌ Sistema TJSP: ❌ Não encontrado
❌ Sistema Extração: ❌ Não encontrado
```

**Soluções:**
1. **Verificar estrutura de diretórios**
   ```
   TJSP_PDF_e2e/
   ├── tjsp/
   │   └── ProcessadorTJSPUnificado_final.py
   └── extracao/
       └── src/
           └── sistema_principal.py
   ```

2. **Executar do diretório correto**
   ```cmd
   cd Bipre\TJSP_PDF_e2e
   python orquestrador_tjsp_e2e.py
   ```

3. **Verificar nomes dos arquivos**
   - Arquivo TJSP: `ProcessadorTJSPUnificado_final.py`
   - Arquivo Extração: `src/sistema_principal.py`

---

### **3. Erro: "Módulo não encontrado"**

**Sintomas:**
```
ModuleNotFoundError: No module named 'pandas'
ImportError: No module named 'selenium'
```

**Soluções:**
1. **Executar instalação automática**
   ```cmd
   INSTALAR_SISTEMA.bat
   ```

2. **Instalar manualmente**
   ```cmd
   pip install pandas openpyxl tqdm selenium webdriver-manager
   pip install PyPDF2 pdfplumber beautifulsoup4 requests
   ```

3. **Verificar ambiente virtual**
   ```cmd
   # Se usando venv
   venv\Scripts\activate
   pip install -r requirements.txt
   ```

---

### **4. Erro: "Permissão negada" ao copiar arquivos**

**Sintomas:**
```
PermissionError: [Errno 13] Permission denied
❌ Erro ao sincronizar arquivo.pdf
```

**Soluções:**
1. **Executar como Administrador**
   - Clique direito no `.bat` → "Executar como administrador"

2. **Verificar arquivos em uso**
   - Feche PDFs abertos no Adobe Reader/navegador
   - Feche Excel se estiver usando arquivos de checkpoint

3. **Verificar antivírus**
   - Adicione pasta do projeto às exceções
   - Desative proteção em tempo real temporariamente

---

### **5. Erro: "Nenhum PDF encontrado"**

**Sintomas:**
```
❌ Nenhum PDF encontrado para processar!
💡 Coloque os PDFs na pasta data/input/
```

**Soluções:**
1. **Executar download primeiro**
   - Use opção "1. Download TJSP" no menu
   - Ou execute processo completo (opção 3)

2. **Verificar diretório de downloads**
   ```
   tjsp/downloads_completos/  ← PDFs devem estar aqui
   ```

3. **Sincronizar manualmente**
   - Use opção "4. Sincronizar arquivos" no menu

---

### **6. Erro: "Falha na verificação de integridade"**

**Sintomas:**
```
❌ Falha na verificação de integridade
Hash origem ≠ Hash destino
```

**Soluções:**
1. **Verificar espaço em disco**
   ```cmd
   dir C:\ # Verificar espaço livre
   ```

2. **Executar verificação de disco**
   ```cmd
   chkdsk C: /f
   ```

3. **Usar sincronização básica**
   - Use opção "4" em vez de "5" no menu
   - Desativa verificação de integridade

---

### **7. Processo interrompido/travado**

**Sintomas:**
- Processo para de responder
- Não há progresso por muito tempo

**Soluções:**
1. **Usar Ctrl+C para interromper**
   - Checkpoint salva automaticamente
   - Pode retomar depois

2. **Verificar logs**
   ```
   logs_orquestrador/orquestrador_YYYYMMDD_HHMMSS.log
   ```

3. **Retomar processamento**
   - Execute novamente - checkpoint retoma automaticamente
   - Use opção "6. Ver status" para verificar progresso

---

### **8. Erro: "WebDriver não encontrado"**

**Sintomas:**
```
selenium.common.exceptions.WebDriverException
WebDriver executable needs to be in PATH
```

**Soluções:**
1. **Instalar webdriver-manager**
   ```cmd
   pip install webdriver-manager
   ```

2. **Baixar ChromeDriver manualmente**
   - https://chromedriver.chromium.org/
   - Colocar na pasta `tjsp/drivers/`

3. **Atualizar Chrome**
   - Versão do Chrome deve ser compatível com ChromeDriver

---

## 🔍 Diagnóstico Avançado

### **Executar Testes Completos**
```cmd
python teste_sistema_completo.py
```

### **Verificar Status Detalhado**
1. Execute o orquestrador
2. Escolha opção "6. Ver status atual"
3. Analise informações exibidas

### **Logs Detalhados**
```
logs_orquestrador/
├── orquestrador_YYYYMMDD_HHMMSS.log
├── sincronizacao_detalhada_YYYYMMDD_HHMMSS.json
└── relatorio_consolidado_YYYYMMDD_HHMMSS.json
```

### **Limpeza Completa**
```cmd
# Remover checkpoints
del checkpoint_*.json
del checkpoint_*.xlsx

# Limpar logs antigos
rmdir /s logs_orquestrador

# Reinstalar sistema
INSTALAR_SISTEMA.bat
```

---

## 📞 Suporte Adicional

### **Informações para Suporte**
Ao reportar problemas, inclua:

1. **Versão do Python**: `python --version`
2. **Sistema Operacional**: Windows 10/11
3. **Logs de erro**: Copie mensagens completas
4. **Estrutura de diretórios**: `dir /s`
5. **Último checkpoint**: `checkpoint_orquestrador.json`

### **Comandos de Diagnóstico**
```cmd
# Verificar Python e pip
python --version
pip --version

# Listar pacotes instalados
pip list

# Verificar estrutura
dir tjsp
dir extracao

# Testar importações
python -c "import pandas, selenium, PyPDF2; print('OK')"
```

### **Reset Completo**
Se nada funcionar:

1. **Backup dos dados importantes**
   ```cmd
   copy tjsp\downloads_completos\*.pdf backup\
   copy extracao\data\output\*.xlsx backup\
   ```

2. **Reinstalação limpa**
   ```cmd
   # Remover tudo exceto dados
   del *.py *.json *.bat *.md
   
   # Baixar arquivos novamente
   # Executar INSTALAR_SISTEMA.bat
   ```

---

**Última atualização**: 2025-01-28  
**Versão do sistema**: 1.0
