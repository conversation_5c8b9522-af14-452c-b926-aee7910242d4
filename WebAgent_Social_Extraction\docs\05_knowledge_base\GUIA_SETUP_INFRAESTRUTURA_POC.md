# 🐳 GUIA SETUP INFRAESTRUTURA - POC WEBAGENT

**Data:** 2025-01-24  
**Objetivo:** Setup completo da infraestrutura Docker para PoC  
**Tempo Estimado:** 15-20 minutos  

---

## 🎯 RESUMO EXECUTIVO

### 🏗️ **INFRAESTRUTURA POC**
- **PostgreSQL 15** - Database principal
- **Redis 7** - Cache e message broker
- **FastAPI** - API Gateway (desenvolvimento)
- **Celery Workers** - Processamento assíncrono
- **Monitoring** - Prometheus + Grafana (opcional)

### ⚡ **SETUP RÁPIDO**
```bash
# 1. Verificar Docker
docker --version

# 2. Subir infraestrutura essencial
docker-compose up postgres redis -d

# 3. Verificar status
docker ps
```

---

## 🔍 1. VERIFICAÇÃO DOCKER ATUAL

### ✅ **STATUS VERIFICADO**
- **Docker:** 28.1.1 ✅ Instalado
- **Docker Compose:** v2.35.1 ✅ Instalado
- **Docker Desktop:** ✅ Ativo
- **Containers ativos:** 0 (limpo)

### 🧪 **TESTE DE FUNCIONAMENTO**
```bash
# Verificar Docker daemon
docker info

# Verificar compose
docker-compose --version

# Listar containers
docker ps -a
```

---

## 📋 2. ESTRUTURA DOCKER CONFIGURADA

### 🗂️ **ARQUIVOS DOCKER**
```
WebAgent_Social_Extraction/
├── 🐳 docker-compose.yml        # Orquestração principal
├── 📁 infra/docker/             # Dockerfiles
│   ├── Dockerfile.api           # FastAPI container
│   ├── Dockerfile.worker        # Celery container
│   └── Dockerfile.monitoring    # Prometheus/Grafana
└── 📁 config/                   # Configurações
    ├── redis/redis.conf         # Redis config
    └── postgres/init.sql        # DB initialization
```

### 🔧 **SERVIÇOS DEFINIDOS**

#### **2.1 PostgreSQL Database**
```yaml
postgres:
  image: postgres:15-alpine
  container_name: webagent-postgres
  ports: ["5432:5432"]
  environment:
    POSTGRES_DB: webagent
    POSTGRES_USER: postgres
    POSTGRES_PASSWORD: postgres
  volumes:
    - postgres_data:/var/lib/postgresql/data
    - ./data/schemas:/docker-entrypoint-initdb.d
```

#### **2.2 Redis Cache**
```yaml
redis:
  image: redis:7-alpine
  container_name: webagent-redis
  ports: ["6379:6379"]
  volumes:
    - redis_data:/data
    - ./config/redis/redis.conf:/usr/local/etc/redis/redis.conf
```

#### **2.3 API Gateway**
```yaml
api:
  build:
    context: .
    dockerfile: infra/docker/Dockerfile.api
  container_name: webagent-api
  ports: ["8000:8000"]
  depends_on: [postgres, redis]
```

#### **2.4 Celery Workers**
```yaml
worker:
  build:
    context: .
    dockerfile: infra/docker/Dockerfile.worker
  container_name: webagent-worker
  depends_on: [postgres, redis]
```

---

## 🚀 3. SETUP PASSO A PASSO

### 📅 **FASE 1: INFRAESTRUTURA BÁSICA (5 min)**

#### **3.1 Verificar Pré-requisitos**
```bash
# Verificar Docker Desktop está rodando
docker info

# Verificar espaço em disco (mínimo 2GB)
docker system df
```

#### **3.2 Criar Rede Docker**
```bash
# Criar rede personalizada
docker network create webagent-network

# Verificar criação
docker network ls | findstr webagent
```

#### **3.3 Subir Database e Cache**
```bash
# Navegar para o projeto
cd WebAgent_Social_Extraction

# Subir apenas serviços essenciais
docker-compose up postgres redis -d

# Verificar status
docker ps
```

#### **3.4 Aguardar Inicialização**
```bash
# Verificar logs PostgreSQL
docker logs webagent-postgres

# Verificar logs Redis
docker logs webagent-redis

# Aguardar health checks (30-60 segundos)
docker-compose ps
```

### 📅 **FASE 2: VERIFICAÇÃO E TESTES (5 min)**

#### **3.5 Testar Conexão PostgreSQL**
```bash
# Conectar ao PostgreSQL
docker exec -it webagent-postgres psql -U postgres -d webagent

# Executar teste básico
\l  # Listar databases
\q  # Sair
```

#### **3.6 Testar Conexão Redis**
```bash
# Conectar ao Redis
docker exec -it webagent-redis redis-cli

# Executar teste básico
ping  # Deve retornar PONG
exit
```

#### **3.7 Verificar Health Checks**
```bash
# Status detalhado dos containers
docker-compose ps

# Verificar health status
docker inspect webagent-postgres | findstr Health
docker inspect webagent-redis | findstr Health
```

### 📅 **FASE 3: CONFIGURAÇÃO AVANÇADA (10 min)**

#### **3.8 Configurar Database Schema**
```bash
# Copiar schema inicial
docker cp ./data/schemas/init.sql webagent-postgres:/tmp/

# Executar schema
docker exec -it webagent-postgres psql -U postgres -d webagent -f /tmp/init.sql
```

#### **3.9 Configurar Redis**
```bash
# Verificar configuração Redis
docker exec -it webagent-redis redis-cli CONFIG GET "*"

# Testar cache básico
docker exec -it webagent-redis redis-cli SET test "Hello WebAgent"
docker exec -it webagent-redis redis-cli GET test
```

---

## 🔧 4. CONFIGURAÇÕES ESPECÍFICAS POC

### 📊 **VARIÁVEIS DE AMBIENTE**
```env
# Database
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/webagent
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=webagent
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres

# Redis
REDIS_URL=redis://localhost:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# Celery
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2
```

### 🎛️ **CONFIGURAÇÃO REDIS OTIMIZADA**
```conf
# config/redis/redis.conf
maxmemory 256mb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
appendonly yes
appendfsync everysec
```

### 🗄️ **SCHEMA DATABASE INICIAL**
```sql
-- data/schemas/init.sql
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Schema para PoC
CREATE SCHEMA IF NOT EXISTS webagent;

-- Tabela posts extraídos
CREATE TABLE webagent.posts (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    platform VARCHAR(50) NOT NULL,
    post_id VARCHAR(255) NOT NULL UNIQUE,
    url TEXT NOT NULL,
    title TEXT,
    description TEXT,
    author VARCHAR(255),
    view_count BIGINT DEFAULT 0,
    like_count BIGINT DEFAULT 0,
    comment_count BIGINT DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE,
    extracted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB,
    viral_score DECIMAL(5,2),
    analysis_data JSONB
);

-- Índices para performance
CREATE INDEX idx_posts_platform ON webagent.posts(platform);
CREATE INDEX idx_posts_viral_score ON webagent.posts(viral_score DESC);
CREATE INDEX idx_posts_created_at ON webagent.posts(created_at DESC);
CREATE INDEX idx_posts_extracted_at ON webagent.posts(extracted_at DESC);

-- Tabela para análises IA
CREATE TABLE webagent.ai_analyses (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    post_id UUID REFERENCES webagent.posts(id),
    model_name VARCHAR(100) NOT NULL,
    analysis_type VARCHAR(50) NOT NULL,
    input_tokens INTEGER,
    output_tokens INTEGER,
    cost_usd DECIMAL(10,6),
    analysis_result JSONB,
    confidence_score DECIMAL(5,2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela para métricas PoC
CREATE TABLE webagent.poc_metrics (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(15,6),
    metric_unit VARCHAR(50),
    metadata JSONB,
    recorded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

---

## 🔍 5. TROUBLESHOOTING

### ❌ **PROBLEMAS COMUNS**

#### **5.1 Docker Desktop não está rodando**
```bash
# Verificar status
docker info

# Se erro: "Cannot connect to Docker daemon"
# Solução: Abrir Docker Desktop manualmente
```

#### **5.2 Porta já em uso**
```bash
# Verificar portas ocupadas
netstat -an | findstr :5432
netstat -an | findstr :6379

# Solução: Parar serviços conflitantes ou mudar portas
```

#### **5.3 Containers não sobem**
```bash
# Verificar logs detalhados
docker-compose logs postgres
docker-compose logs redis

# Verificar recursos disponíveis
docker system df
docker system prune  # Limpar se necessário
```

#### **5.4 Health checks falhando**
```bash
# Verificar health status
docker inspect webagent-postgres | findstr Health

# Aguardar mais tempo (até 2 minutos)
# Verificar logs para erros específicos
```

### 🛠️ **COMANDOS ÚTEIS**

#### **Gerenciamento Containers**
```bash
# Parar todos os containers
docker-compose down

# Subir com rebuild
docker-compose up --build -d

# Ver logs em tempo real
docker-compose logs -f

# Limpar volumes (CUIDADO: perde dados)
docker-compose down -v
```

#### **Monitoramento**
```bash
# Status recursos
docker stats

# Espaço usado
docker system df

# Informações detalhadas
docker inspect webagent-postgres
```

---

## ✅ CHECKLIST INFRAESTRUTURA

### 🐳 **DOCKER**
- [x] Docker Desktop instalado e rodando
- [x] docker-compose.yml configurado
- [ ] **Containers PostgreSQL e Redis rodando** ⚠️
- [ ] **Health checks passando** ⚠️
- [ ] **Rede webagent-network criada** ⚠️

### 🗄️ **DATABASE**
- [ ] **PostgreSQL acessível** ⚠️
- [ ] **Schema webagent criado** ⚠️
- [ ] **Tabelas iniciais criadas** ⚠️
- [ ] **Índices configurados** ⚠️

### 🔄 **CACHE**
- [ ] **Redis acessível** ⚠️
- [ ] **Configuração otimizada** ⚠️
- [ ] **Teste básico funcionando** ⚠️

### 🔧 **CONFIGURAÇÃO**
- [ ] **Variáveis de ambiente** ⚠️
- [ ] **Arquivos de config** ⚠️
- [ ] **Volumes persistentes** ⚠️

---

## 🚀 PRÓXIMOS PASSOS

### 📅 **APÓS SETUP INFRAESTRUTURA**
1. **Configurar ambiente Python** (venv + dependências)
2. **Configurar credenciais** (.env com APIs)
3. **Testar conexões** (Python → PostgreSQL/Redis)
4. **Implementar primeiro PoC** (YouTube extraction)
5. **Executar análise Gemini** básica

---

**🎯 STATUS:** ✅ **GUIA COMPLETO - PRONTO PARA SETUP**  
**⏱️ TEMPO:** 15-20 minutos para setup completo  
**📋 PRÓXIMO:** Executar setup e configurar ambiente Python
