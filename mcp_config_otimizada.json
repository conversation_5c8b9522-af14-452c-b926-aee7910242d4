{"// CONFIGURAÇÃO MCP OTIMIZADA - Augment Code Orchestrator V5.0": "Elimina duplicação mantendo funcionalidade", "// ESTRATÉGIA": "VSCode como servidor principal, <PERSON> como cliente", "vscode_settings": {"mcpServers": {"memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "env": {"NODE_OPTIONS": "--max-old-space-size=256", "MCP_PORT": "8001"}}, "everything": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-everything"], "env": {"NODE_OPTIONS": "--max-old-space-size=256", "MCP_PORT": "8002"}}, "github": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "env": {"NODE_OPTIONS": "--max-old-space-size=256", "GITHUB_PERSONAL_ACCESS_TOKEN": "****************************************", "MCP_PORT": "8003"}}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "env": {"NODE_OPTIONS": "--max-old-space-size=256", "MCP_PORT": "8004"}}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"], "env": {"NODE_OPTIONS": "--max-old-space-size=256", "MCP_PORT": "8005"}}, "21st-dev-magic": {"command": "npx", "args": ["-y", "@21st-dev/magic@latest"], "env": {"NODE_OPTIONS": "--max-old-space-size=256", "API_KEY": "3efe2d0659b1c6e95b2140da7376afe07cd3e51ae695c6a3e9f246e150f5e1f8", "MCP_PORT": "8006"}}, "supabase": {"command": "npx", "args": ["-y", "@supabase/mcp-server-supabase@latest", "--access-token", "********************************************"], "env": {"NODE_OPTIONS": "--max-old-space-size=256", "MCP_PORT": "8007"}}, "playwright": {"command": "npx", "args": ["-y", "@playwright/mcp@latest"], "env": {"NODE_OPTIONS": "--max-old-space-size=256", "MCP_PORT": "8008"}}, "google-maps": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-google-maps"], "env": {"NODE_OPTIONS": "--max-old-space-size=256", "GOOGLE_MAPS_API_KEY": "AIzaSyDOiKX9cj85FZOFB3WyP2hUm6qoXUtj6bk", "MCP_PORT": "8009"}}, "netlify": {"command": "npx", "args": ["-y", "@netlify/mcp"], "env": {"NODE_OPTIONS": "--max-old-space-size=256", "MCP_PORT": "8010"}}, "blowback": {"command": "npx", "args": ["-y", "blowback-context"], "env": {"NODE_OPTIONS": "--max-old-space-size=256", "MCP_PORT": "8011"}}, "windows-mcp": {"command": "uv", "args": ["--directory", "C:\\Users\\<USER>\\Documents\\Augment\\windows-mcp", "run", "main.py"], "env": {"MCP_PORT": "8012"}}}}, "claude_desktop_config_DISABLED": {"// NOTA": "Claude Desktop configurado para NÃO iniciar servidores MCP", "// ESTRATÉGIA": "<PERSON> se conecta aos servidores já rodando no VSCode", "mcpServers": {}}, "configuracao_consolidada": {"total_servidores": 12, "memoria_estimada_mb": 768, "portas_utilizadas": "8001-8012", "estrategia": "VSCode como host <PERSON><PERSON><PERSON>, <PERSON> como cliente", "beneficios": ["Elimina 100% da duplicação", "Reduz consumo de RAM em 75%", "<PERSON><PERSON><PERSON> to<PERSON> as funcionalidades", "Facilita monitoramento e debug", "Evita conflitos de porta", "Cleanup automático ao fechar VSCode"]}, "servidores_por_categoria": {"core_mcp": ["memory", "everything", "sequential-thinking"], "desenvolvimento": ["github", "context7", "21st-dev-magic"], "infraestrutura": ["supabase", "playwright", "windows-mcp"], "integracao": ["google-maps", "netlify", "blowback"]}, "monitoramento": {"comando_verificacao": "Get-NetTCPConnection -LocalPort 8001,8002,8003,8004,8005,8006,8007,8008,8009,8010,8011,8012", "comando_cleanup": "Get-Process node | Where-Object {$wmi = Get-WmiObject Win32_Process -Filter \"ProcessId = $($_.Id)\"; $wmi.CommandLine -match \"mcp\" -and $wmi.CommandLine -notmatch \"8001|8002|8003|8004|8005|8006|8007|8008|8009|8010|8011|8012\"} | Stop-Process -Force", "log_location": "C:\\Users\\<USER>\\AppData\\Roaming\\Code\\logs\\mcp_servers.log"}, "implementacao": {"passo_1": "Finalizar todos os processos MCP atuais", "passo_2": "Limpar configuração Claude Desktop", "passo_3": "Aplicar nova configuração VSCode", "passo_4": "Reiniciar VSCode", "passo_5": "Verificar funcionamento", "passo_6": "<PERSON><PERSON><PERSON> (sem MCPs próprios)"}}