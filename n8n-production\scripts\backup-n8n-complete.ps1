# ===================================================================
# BACKUP COMPLETO N8N - POWERSHELL VERSION
# ===================================================================

param(
    [string]$BackupDir = "./backups",
    [int]$RetentionDays = 30
)

# Configurações
$ErrorActionPreference = "Stop"
$Timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$BackupName = "n8n_backup_$Timestamp"
$BackupPath = Join-Path $BackupDir $BackupName

# Funções de log
function Write-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Write-Header {
    param([string]$Message)
    Write-Host "[BACKUP] $Message" -ForegroundColor Magenta
}

# Banner
function Show-Banner {
    Write-Host ""
    Write-Host "=====================================================================" -ForegroundColor Cyan
    Write-Host "    🔄 BACKUP COMPLETO N8N - TODOS OS DADOS E CONFIGURAÇÕES" -ForegroundColor Cyan
    Write-Host "=====================================================================" -ForegroundColor Cyan
    Write-Host "    Backup: $BackupName" -ForegroundColor Cyan
    Write-Host "    Destino: $BackupPath" -ForegroundColor Cyan
    Write-Host "=====================================================================" -ForegroundColor Cyan
    Write-Host ""
}

# Verificar pré-requisitos
function Test-Prerequisites {
    Write-Info "Verificando pré-requisitos..."
    
    # Verificar se Docker está rodando
    try {
        docker info | Out-Null
        Write-Success "Docker está rodando"
    }
    catch {
        Write-Error "Docker não está rodando!"
        exit 1
    }
    
    # Verificar se containers estão rodando
    $runningContainers = docker-compose ps --filter "status=running" -q
    if (-not $runningContainers) {
        Write-Error "Containers N8N não estão rodando!"
        Write-Info "Execute: docker-compose up -d"
        exit 1
    }
    
    # Criar diretório de backup
    if (-not (Test-Path $BackupPath)) {
        New-Item -ItemType Directory -Path $BackupPath -Force | Out-Null
    }
    
    Write-Success "Pré-requisitos verificados!"
}

# Backup PostgreSQL
function Backup-PostgreSQL {
    Write-Header "1. BACKUP POSTGRESQL"
    
    Write-Info "Fazendo backup do banco PostgreSQL..."
    
    try {
        # Backup completo do banco
        $sqlDumpPath = Join-Path $BackupPath "postgresql_dump.sql"
        docker-compose exec -T postgres pg_dump -U "n8n_user" -d "n8n" --verbose --no-owner --no-privileges --clean --if-exists > $sqlDumpPath
        
        # Compactar backup SQL
        Compress-Archive -Path $sqlDumpPath -DestinationPath "$sqlDumpPath.zip" -Force
        Remove-Item $sqlDumpPath
        
        Write-Success "Backup PostgreSQL concluído e compactado"
        
        # Backup de metadados do banco
        $dbListPath = Join-Path $BackupPath "postgresql_databases.txt"
        $tablesPath = Join-Path $BackupPath "postgresql_tables.txt"
        
        docker-compose exec -T postgres psql -U "n8n_user" -d "n8n" -c "\l" > $dbListPath
        docker-compose exec -T postgres psql -U "n8n_user" -d "n8n" -c "\dt" > $tablesPath
        
        Write-Info "Metadados PostgreSQL salvos"
    }
    catch {
        Write-Error "Falha no backup PostgreSQL: $_"
        exit 1
    }
}

# Backup Redis
function Backup-Redis {
    Write-Header "2. BACKUP REDIS"
    
    Write-Info "Fazendo backup do Redis..."
    
    try {
        # Forçar save do Redis
        docker-compose exec -T redis redis-cli BGSAVE | Out-Null
        
        # Aguardar conclusão do save
        Start-Sleep -Seconds 5
        
        # Copiar arquivo RDB
        $rdbPath = Join-Path $BackupPath "redis_dump.rdb"
        docker-compose exec -T redis cat /data/dump.rdb > $rdbPath
        
        Write-Success "Backup Redis concluído"
        
        # Backup de informações do Redis
        $redisInfoPath = Join-Path $BackupPath "redis_info.txt"
        $redisConfigPath = Join-Path $BackupPath "redis_config.txt"
        
        docker-compose exec -T redis redis-cli INFO > $redisInfoPath
        docker-compose exec -T redis redis-cli CONFIG GET "*" > $redisConfigPath
        
        Write-Info "Informações Redis salvas"
    }
    catch {
        Write-Error "Falha no backup Redis: $_"
        exit 1
    }
}

# Backup volumes N8N
function Backup-N8NVolumes {
    Write-Header "3. BACKUP VOLUMES N8N"
    
    Write-Info "Fazendo backup dos volumes N8N..."
    
    try {
        # Criar diretório para volumes
        $volumesPath = Join-Path $BackupPath "volumes"
        New-Item -ItemType Directory -Path $volumesPath -Force | Out-Null
        
        # Backup do volume n8n_data
        Write-Info "Backup volume n8n_data..."
        $n8nDataPath = Join-Path $volumesPath "n8n_data.tar.gz"
        docker run --rm -v "n8n-production_n8n_data:/source:ro" -v "${PWD}/${BackupPath}/volumes:/backup" alpine:latest tar czf /backup/n8n_data.tar.gz -C /source .
        
        # Backup do volume postgres_data
        Write-Info "Backup volume postgres_data..."
        $postgresDataPath = Join-Path $volumesPath "postgres_data.tar.gz"
        docker run --rm -v "n8n-production_postgres_data:/source:ro" -v "${PWD}/${BackupPath}/volumes:/backup" alpine:latest tar czf /backup/postgres_data.tar.gz -C /source .
        
        # Backup do volume redis_data
        Write-Info "Backup volume redis_data..."
        $redisDataPath = Join-Path $volumesPath "redis_data.tar.gz"
        docker run --rm -v "n8n-production_redis_data:/source:ro" -v "${PWD}/${BackupPath}/volumes:/backup" alpine:latest tar czf /backup/redis_data.tar.gz -C /source .
        
        Write-Success "Todos os volumes N8N foram salvos"
    }
    catch {
        Write-Error "Falha no backup volumes: $_"
        exit 1
    }
}

# Backup configurações
function Backup-Configurations {
    Write-Header "4. BACKUP CONFIGURAÇÕES"
    
    Write-Info "Fazendo backup das configurações..."
    
    try {
        # Criar diretório para configurações
        $configsPath = Join-Path $BackupPath "configs"
        New-Item -ItemType Directory -Path $configsPath -Force | Out-Null
        
        # Backup docker-compose.yml
        if (Test-Path "docker-compose.yml") {
            Copy-Item "docker-compose.yml" $configsPath
            Write-Info "docker-compose.yml salvo"
        }
        
        # Backup .env
        if (Test-Path ".env") {
            Copy-Item ".env" $configsPath
            Write-Info ".env salvo"
        }
        
        # Backup de outros arquivos de configuração
        Get-ChildItem -Path "." -Include "*.yml", "*.yaml", "*.json", "*.conf" | ForEach-Object {
            Copy-Item $_.FullName $configsPath -ErrorAction SilentlyContinue
        }
        
        # Backup do diretório configs se existir
        if (Test-Path "configs") {
            Copy-Item "configs" $BackupPath -Recurse -Force
            Write-Info "Diretório configs salvo"
        }
        
        Write-Success "Configurações salvas"
    }
    catch {
        Write-Error "Falha no backup configurações: $_"
        exit 1
    }
}

# Backup metadados do sistema
function Backup-SystemMetadata {
    Write-Header "5. BACKUP METADADOS DO SISTEMA"
    
    Write-Info "Coletando metadados do sistema..."
    
    try {
        # Criar diretório para metadados
        $metadataPath = Join-Path $BackupPath "metadata"
        New-Item -ItemType Directory -Path $metadataPath -Force | Out-Null
        
        # Informações do Docker
        docker --version > (Join-Path $metadataPath "docker_version.txt")
        docker-compose --version > (Join-Path $metadataPath "docker_compose_version.txt")
        docker-compose ps > (Join-Path $metadataPath "containers_status.txt")
        docker images > (Join-Path $metadataPath "docker_images.txt")
        docker volume ls > (Join-Path $metadataPath "docker_volumes.txt")
        docker network ls > (Join-Path $metadataPath "docker_networks.txt")
        
        # Informações do sistema
        Get-Date > (Join-Path $metadataPath "backup_timestamp.txt")
        Get-ComputerInfo | Select-Object WindowsProductName, WindowsVersion, TotalPhysicalMemory > (Join-Path $metadataPath "system_info.txt")
        Get-WmiObject -Class Win32_LogicalDisk | Select-Object DeviceID, Size, FreeSpace > (Join-Path $metadataPath "disk_usage.txt")
        
        # Informações específicas do N8N
        try {
            docker-compose exec -T n8n n8n --version > (Join-Path $metadataPath "n8n_version.txt") 2>$null
        }
        catch {
            "N8N version not available" > (Join-Path $metadataPath "n8n_version.txt")
        }
        
        Write-Success "Metadados coletados"
    }
    catch {
        Write-Error "Falha no backup metadados: $_"
        exit 1
    }
}

# Criar arquivo de manifesto
function New-Manifest {
    Write-Header "6. CRIANDO MANIFESTO DO BACKUP"
    
    $manifestPath = Join-Path $BackupPath "BACKUP_MANIFEST.md"
    
    $manifestContent = @"
# BACKUP N8N COMPLETO

## Informações do Backup
- **Data/Hora**: $(Get-Date)
- **Backup ID**: $BackupName
- **Versão do Script**: 1.0 (PowerShell)

## Conteúdo do Backup

### 1. Banco de Dados PostgreSQL
- ``postgresql_dump.sql.zip`` - Dump completo do banco N8N
- ``postgresql_databases.txt`` - Lista de databases
- ``postgresql_tables.txt`` - Lista de tabelas

### 2. Cache Redis
- ``redis_dump.rdb`` - Dump do Redis
- ``redis_info.txt`` - Informações do Redis
- ``redis_config.txt`` - Configurações do Redis

### 3. Volumes Docker
- ``volumes/n8n_data.tar.gz`` - Volume de dados do N8N
- ``volumes/postgres_data.tar.gz`` - Volume de dados do PostgreSQL
- ``volumes/redis_data.tar.gz`` - Volume de dados do Redis

### 4. Configurações
- ``configs/docker-compose.yml`` - Configuração Docker Compose
- ``configs/.env`` - Variáveis de ambiente
- ``configs/`` - Outros arquivos de configuração

### 5. Metadados
- ``metadata/`` - Informações do sistema e versões

## Como Restaurar
``````bash
./scripts/restore-n8n-complete.sh $BackupName
``````

## Verificação de Integridade
- PostgreSQL: $(if (Test-Path (Join-Path $BackupPath "postgresql_dump.sql.zip")) { (Get-Item (Join-Path $BackupPath "postgresql_dump.sql.zip")).Length } else { "N/A" })
- Redis: $(if (Test-Path (Join-Path $BackupPath "redis_dump.rdb")) { (Get-Item (Join-Path $BackupPath "redis_dump.rdb")).Length } else { "N/A" })
- Total: $(if (Test-Path $BackupPath) { (Get-ChildItem $BackupPath -Recurse | Measure-Object -Property Length -Sum).Sum } else { "N/A" })
"@

    $manifestContent | Out-File -FilePath $manifestPath -Encoding UTF8
    Write-Success "Manifesto criado"
}

# Compactar backup final
function Compress-Backup {
    Write-Header "7. COMPACTANDO BACKUP FINAL"
    
    Write-Info "Compactando backup completo..."
    
    try {
        $zipPath = Join-Path $BackupDir "$BackupName.zip"
        Compress-Archive -Path $BackupPath -DestinationPath $zipPath -Force
        
        # Remover diretório não compactado
        Remove-Item $BackupPath -Recurse -Force
        
        Write-Success "Backup compactado: $BackupName.zip"
    }
    catch {
        Write-Error "Falha na compactação do backup: $_"
        exit 1
    }
}

# Limpeza de backups antigos
function Remove-OldBackups {
    Write-Header "8. LIMPEZA DE BACKUPS ANTIGOS"
    
    Write-Info "Removendo backups com mais de $RetentionDays dias..."
    
    try {
        $cutoffDate = (Get-Date).AddDays(-$RetentionDays)
        $oldBackups = Get-ChildItem -Path $BackupDir -Name "n8n_backup_*.zip" | Where-Object {
            (Get-Item (Join-Path $BackupDir $_)).LastWriteTime -lt $cutoffDate
        }
        
        foreach ($backup in $oldBackups) {
            Remove-Item (Join-Path $BackupDir $backup) -Force
            Write-Info "Removido: $backup"
        }
        
        $remainingBackups = (Get-ChildItem -Path $BackupDir -Name "n8n_backup_*.zip").Count
        Write-Info "Backups restantes: $remainingBackups"
    }
    catch {
        Write-Warning "Erro na limpeza de backups antigos: $_"
    }
}

# Relatório final
function Show-FinalReport {
    Write-Host ""
    Write-Host "=====================================================================" -ForegroundColor Green
    Write-Host "                    BACKUP CONCLUÍDO COM SUCESSO!" -ForegroundColor Green
    Write-Host "=====================================================================" -ForegroundColor Green
    
    $zipPath = Join-Path $BackupDir "$BackupName.zip"
    $backupSize = if (Test-Path $zipPath) { 
        [math]::Round((Get-Item $zipPath).Length / 1MB, 2).ToString() + " MB"
    } else { 
        "N/A" 
    }
    
    Write-Info "Detalhes do Backup:"
    Write-Host "  Arquivo: $BackupName.zip"
    Write-Host "  Tamanho: $backupSize"
    Write-Host "  Local: $zipPath"
    Write-Host "  Data: $(Get-Date)"
    Write-Host ""

    Write-Info "Conteudo do Backup:"
    Write-Host "  PostgreSQL (workflows, credenciais, execucoes)"
    Write-Host "  Redis (cache, filas, sessoes)"
    Write-Host "  Volumes N8N (arquivos, configuracoes)"
    Write-Host "  Configuracoes (docker-compose, .env)"
    Write-Host "  Metadados do sistema"
    Write-Host ""
    
    Write-Info "Para restaurar:"
    Write-Host "  ./scripts/restore-n8n-complete.sh $BackupName"
    Write-Host ""
    
    Write-Success "Backup N8N completo finalizado!"
}

# Função principal
function Main {
    Show-Banner
    Test-Prerequisites
    Backup-PostgreSQL
    Backup-Redis
    Backup-N8NVolumes
    Backup-Configurations
    Backup-SystemMetadata
    New-Manifest
    Compress-Backup
    Remove-OldBackups
    Show-FinalReport
}

# Executar função principal
try {
    Main
}
catch {
    Write-Error "Erro durante o backup: $_"
    exit 1
}
