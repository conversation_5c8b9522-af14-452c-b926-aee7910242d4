﻿Olá pessoal tudo joia aqui luí nesse
vídeo eu vou mostrar para vocês como que
vocês podem atualizar o seu postgis pro
PG Vector pessoal Olha o chatot
recentemente lançou a versão 4 e na
versão 4 você tem que fazer esse upgrade
do poste tigris comum para um poste
tigris com o PG Vector e eu acredito que
ao longo do tempo outras ferramentas
também vão seguir esse mesmo caminho
Então assiste o vídeo com atenção passo
a passo para que você não cometa nenhum
erro aí na hora de fazer esse upgrade
que é simples mas exige bastante atenção
dá um like no vídeo se inscreve no canal
e bora lá pessoal antes de começar aqui
vamos falar o que que é o PG Vector né
então eu tô aqui no github do PG Vector
é um projeto que ele é open sece
totalmente Open sce e basicamente o PG
Vector ele é uma uma cópia do post greis
original ele erda o post greis original
e instala a extensão PG Vector isso aqui
é importante pessoal porque ele não é
uma modificação do postgis tá Então olha
só se eu venho aqui nele então tô no
repositório dele eu tenho aqui o docker
file do PG Vector Se você olhar aqui
você pode ver que ele dá um from na
imagem Lá da imagem oficial e não faz
nada além de vir aqui ó e instalar a
extensão então ele não copia nenhum
arquivo de configuração Extra ele não
faz nada a mais no seu postgre você pode
ver aqui ó que tá tudo certinho aqui tá
do que que tem aqui na no seu no seu
post Gris Então não é uma versão
modificada tá gente é é a versão
original como o comandinho a mais
inclusive você você poderia até ir no
seu post normal a rodar Esse comando que
ele funcionaria do mesmo jeito então eu
gosto dessa imagem aqui porque ela não
mexeu em nada que não deveria tá gente
Então olha só como é que funciona aqui
pessoal para você poder fazer uma
migração correta é o seguinte o PG
Vector essa imagem do docker do PG
Vector ele é um Drop In Lá da imagem
oficial que quer dizer isso daí como
eles pegaram a mesma imagem oficial e só
Instalou a extensão eles não mexerem
mais nada eu vou simplesmente trocar a
imagem que eu tô usando do post Gris
pela imagem do PG Vector mas tem um
truque muito importante ali que eu vou
mostrar para vocês agora Olha só pessoal
eu tô aqui no meu perer eu tenho aqui um
post Gris rodando ó esse meu postgis tá
na 16 que é a versão do curso Então aqui
na promov web nosso curso atual Eu uso o
postgis 16 no comecinho da promov web
ainda era o post Gris 15 tá gente então
é só você ver se você roda o 15 ou 16
evitar usar o latest tá gente então eu
estou usando o postgis 16 aqui se eu
venho aqui no dockerhub do PG Vector
vocês vão ver aqui ó que para cada
versão do postgis ele tem uma tag Então
eu tenho uma tag post Gris 17 pro 16 pro
15 e pro 14 até pro 13 tem aqui ó 13 12
né É claro que do post Gris 12 pra
frente seria bom até você atualizar o
seu próprio post Gris Mas que que eu
tenho que fazer pessoal para você fazer
atualização sem risco é só você pegar
qual é a versão que você tá utilizando e
você usar a tag correspondente então eu
tô usando a 16 que que eu vou fazer ó se
eu abrir aqui esse link ó eu vou ver
aqui o nome completo Ó PG Vector bar PG
Vector dois pontos pg16 né então é só
você ver qual versão que você usa aí e
colocar aqui a versão do do do PG Vector
correspondente fez isso pessoal é só vir
aqui e atualizar lembra que antes de
fazer isso vocês devem fazer um backup
da VPS Vocês estão mexendo no banco de
dados é coisa séria então faz um backup
depois você vem aqui e faz esse update e
lembra também que o os sistemas poderão
cair eu vou ter aqui nessa minha VPS um
chat útil tenho nhn eu tenho Evolution
Type bot tudo isso usa esse meu post
Gris aqui então como você vai reiniciar
o sistema Pode ser que algum deles caia
junto mas é normal eles caírem porque
você Reiniciou o banco de dados tá então
faz parte aí do processo apertou ali o
Deploy o a imagem do do PG Vector tem
quantos aqui deixa eu ver dar uma
olhadinha nela essa imagem ó comprimida
ela vai ter uns 140 M geralmente são é
vezes 4 né geralmente Então vamos Vamos
chutar a uns 400 M 500 m essa imagem ela
descompactada né então não é uma imagem
grandona tá gente e pode ver ó que já
subiu aqui então eu vou ter o meu post
Gris 16 parado e eu vou ter também aqui
o meu PG Vector 16 rodando pessoal fez
esse processo clica nele vem aqui em
logs e dá uma olhadinha no log se deu
algum problema se deu alguma mensag de
erro aqui volta o seu backup se não deu
significa que o seu sist estão
funcionando e operando corretamente no
caso aqui caso você for utilizar o
chatot 4 aí sim você vai poder vir aqui
e fazer o upgrade chat 4 ele exige que
você use o PG Vector e não só o postgis
tá o ENEN pessoal ainda não mas eu acho
que com o tempo vai vai ter essa mudança
Evolution também ainda não mas com
certeza com o tempo eles podem fazer
essa mudança aí então como é um padrão
aos pouquinhos as os softwares vão
aderindo né o chatu fez aqui um update
que vai usar bastante vetor ali na no
atendimento ficou vai ficar Fantástico
em breve eu faço para vocês tô testando
em produção ainda para poder falar para
vocês como é que tá ficando o chatu 4
mas você precisa fazer esse update aqui
antes de atualizar chatot 4 então agora
você tem um postgis rodando o seu
postgis oficial rodando mais a extensão
PG Vector pessoal deu certo pode até
apagar esse daqui esse postgis que ficou
morto ali vermelhinho tá rodando dando
tudo certinho pode ver dei sorte que
nenhum sistema Caiu né então
recapitulando pessoal PG Vector aqui no
dockerhub eu vou deixar aqui na
descrição do vídeo para vocês um link da
comunidade com os links aqui tá do
sistema é só você escolher a versão que
você tá usando para cada versão do
postgis tem a versão do PG Vector e como
vocês podem verificar aqui ele usa a
imagem oficial do postgis não é uma
imagem customizada isso aqui é
importante íssimo da gente conversar
porque muita gente customiza a imagem aí
fica diferente não tem aquil aquele
resultado que você espera então é o seu
postgis normal mais a extensão PG Vector
pessoal dá um like no vídeo se inscreve
no canal quer aprender docker quer
aprender a configurar um servidor assim
faz o nosso curso de docker aqui na
descrição tem o link para vocês até mais
pessoal um abraço