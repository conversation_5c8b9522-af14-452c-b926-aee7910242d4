# 📋 DOCUMENTAÇÃO COMPLETA - INSTALAÇÃO N8N PRODUCTION

## 🏗️ ARQUITETURA DA STACK

### Serviços Implementados
- **n8n** (Aplicação Principal) - Porta: 5678
- **n8n-worker** (Processamento de Filas)
- **PostgreSQL** (Banco de Dados) - Porta: 5432
- **Redis** (Cache/Filas) - Porta: 6379
- **Traefik** (Proxy Reverso/SSL) - Portas: 80, 443, 8080
- **pgAdmin** (Interface PostgreSQL) - Porta: 5050
- **RedisInsight** (Interface Redis) - Porta: 8001
- **Prometheus** (Métricas) - Porta: 9090
- **Grafana** (Dashboards) - Porta: 3000
- **Bull Board** (Monitor de Filas) - Porta: 3002

## 🔐 CREDENCIAIS E ACESSOS

### Credenciais Padrão (ALTERE EM PRODUÇÃO!)
```
N8N:
- Usuário: admin
- Senha: admin123
- URL: http://localhost:5678

PostgreSQL:
- Usuário: n8n_user
- Senha: postgres123
- Banco: n8n
- Host: localhost:5432

Grafana:
- Usuário: admin
- Senha: grafana123
- URL: http://localhost:3000

pgAdmin:
- Email: admin@localhost
- Senha: grafana123
- URL: http://localhost:5050

RedisInsight:
- URL: http://localhost:8001

Bull Board (Monitor de Filas):
- URL: http://localhost:3002/ui

Prometheus:
- URL: http://localhost:9090

Traefik Dashboard:
- URL: http://localhost:8080
```

## 📁 ESTRUTURA DE DIRETÓRIOS

```
n8n-production/
├── config/
│   ├── grafana/          # Configurações Grafana
│   ├── prometheus/       # Configurações Prometheus
│   └── traefik/          # Configurações Traefik
├── data/                 # Dados persistentes
│   ├── n8n/             # Dados n8n
│   ├── postgres/        # Dados PostgreSQL
│   ├── redis/           # Dados Redis
│   ├── grafana/         # Dados Grafana
│   ├── prometheus/      # Dados Prometheus
│   ├── pgadmin/         # Dados pgAdmin
│   └── redisinsight/    # Dados RedisInsight
├── logs/                # Arquivos de log
│   ├── n8n/            # Logs n8n
│   └── traefik/        # Logs Traefik
├── local-files/         # Arquivos compartilhados com n8n
├── ssl/                 # Certificados SSL
├── monitoring/          # Configurações de monitoramento
├── docker-compose.yml   # Configuração principal
├── .env                 # Variáveis de ambiente
├── init-data.sh        # Script inicialização PostgreSQL
├── Dockerfile.bullboard # Dockerfile Bull Board
├── server.js           # Servidor Bull Board
└── credentials.txt     # Backup das credenciais
```

## 🚀 COMANDOS DE INSTALAÇÃO

### 1. Iniciar Stack Completa
```bash
docker-compose up -d
```

### 2. Verificar Status dos Serviços
```bash
docker-compose ps
```

### 3. Ver Logs de um Serviço
```bash
docker-compose logs -f n8n
docker-compose logs -f postgres
docker-compose logs -f redis
```

### 4. Parar Stack
```bash
docker-compose down
```

### 5. Parar e Remover Volumes (CUIDADO!)
```bash
docker-compose down -v
```

## 🔧 CONFIGURAÇÕES IMPORTANTES

### Queue Mode (Escalabilidade)
- **Habilitado**: Sim
- **Redis Host**: redis:6379
- **Workers**: 1 (pode ser escalado)
- **Health Check**: Ativo

### Logs
- **Nível**: info
- **Saída**: arquivo + console
- **Localização**: ./logs/n8n/n8n.log

### Métricas
- **Prometheus**: Habilitado
- **Endpoint**: http://localhost:5678/metrics
- **Coleta**: A cada 30 segundos

## 📊 MONITORAMENTO

### URLs de Acesso
- **n8n**: http://localhost:5678
- **Grafana**: http://localhost:3000
- **Prometheus**: http://localhost:9090
- **Bull Board**: http://localhost:3002/ui
- **pgAdmin**: http://localhost:5050
- **RedisInsight**: http://localhost:8001
- **Traefik**: http://localhost:8080

### Health Checks
Todos os serviços possuem health checks configurados:
- PostgreSQL: `pg_isready`
- Redis: `redis-cli ping`
- n8n: Depende do PostgreSQL e Redis

## 🔒 SEGURANÇA

### Variáveis Críticas
- `N8N_ENCRYPTION_KEY`: Chave de criptografia (CRÍTICA!)
- Senhas de banco de dados
- Certificados SSL

### Recomendações
1. Altere todas as senhas padrão
2. Use senhas fortes (16+ caracteres)
3. Faça backup da chave de criptografia
4. Configure SSL em produção
5. Restrinja acesso às portas

## 🔄 BACKUP E RESTORE

### Backup Essencial
```bash
# Backup dados PostgreSQL
docker-compose exec postgres pg_dump -U n8n_user n8n > backup_n8n.sql

# Backup volumes Docker
docker run --rm -v n8n-production_n8n_data:/data -v $(pwd):/backup alpine tar czf /backup/n8n_backup.tar.gz /data

# Backup credenciais
cp credentials.txt backup_credentials_$(date +%Y%m%d).txt
```

### Restore
```bash
# Restore PostgreSQL
docker-compose exec -T postgres psql -U n8n_user n8n < backup_n8n.sql
```

## 🛠️ TROUBLESHOOTING

### Problemas Comuns

1. **Serviço não inicia**
   ```bash
   docker-compose logs [nome_servico]
   ```

2. **Erro de permissão**
   ```bash
   sudo chown -R 1000:1000 data/
   ```

3. **Porta ocupada**
   ```bash
   netstat -tulpn | grep [porta]
   ```

4. **Reset completo**
   ```bash
   docker-compose down -v
   docker system prune -f
   docker-compose up -d
   ```

## 📈 ESCALABILIDADE

### Adicionar Workers
```yaml
n8n-worker-2:
  <<: *shared-n8n
  command: worker
  depends_on:
    - n8n
```

### Monitorar Performance
- Use Grafana para métricas
- Bull Board para filas
- Logs para debugging

## ⚠️ IMPORTANTE

1. **SEMPRE** faça backup da chave de criptografia
2. **NUNCA** perca o arquivo credentials.txt
3. **ALTERE** senhas padrão em produção
4. **MONITORE** logs regularmente
5. **ATUALIZE** imagens Docker periodicamente
