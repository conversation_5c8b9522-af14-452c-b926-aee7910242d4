﻿Você sabia que dá para montar um agente
de A no WhatsApp que entende o que a
pessoa tá falando e executa várias ações
"sozinho, sem pagar nada pela PI? Hoje eu"
"vou te mostrar como criar um MCP, que é"
"tipo um cérebro por trás do WhatsApp,"
usando NTN e uma API gratuita do
WhatsApp. Ele entende o que o usuário
"quer e pode responder, buscar"
"informações, executar ações e até chamar"
"outro sistema, tudo de forma automática"
"e sem programação. E o melhor, todo"
material tá disponível para download de
graça. Bora construir essa solução
juntos. Eu sou o Pedrinho da NAS e você
está no canal da comunidade ZDG.
[Música]
"Bora pra ação, galera. Nesse vídeo a"
"gente vai construir um MCP do zero,"
utilizando Nin e AP gratuita do
"WhatsApp, que é um agente de a, é uma"
camada de inteligência artificial que a
gente adiciona ali à nossa PI do
"WhatsApp, permitindo com que a gente"
controle todas as funções da API e do
"WhatsApp de maneira bem prática, sem"
"linhas de programação. Então, nós vamos"
passar comandos para inteligência
artificial e a inteligência artificial
vai consumir as funções e as ações que
existem dentro da AP e do WhatsApp. Todo
o material que a gente vai utilizar
nessa aula tá disponível para download
na página de apoio que você encontra
logo na descrição desse vídeo. E antes
"de continuar, já deixa um like nesse"
"vídeo, deixa um comentário para mim"
dizendo como você pretende aplicar essa
nova tecnologia para melhorar e otimizar
o teu atendimento e as tuas ações no
"WhatsApp. Basicamente, gente, esse fluxo"
aqui é um fluxo de MCP que já tá
"instalado no NH, tá bem? E e nós vamos"
eh construir todo esse ambiente aqui de
"maneira gratuita, sem gastar nada no"
nosso PC. Então eu vou abrir aqui o
"chat, vou perguntar paraa inteligência"
artificial quais as
ferramentas ele está preparado para
"utilizar. Então eu mando para ele, ó,"
"quais tools você tem, né? Então a"
inteligência artificial vai lá no meu
"MCP, consulta as ferramentas que estão"
"disponíveis e traz para mim. Olha, as"
ferramentas disponíveis são enviar
"mensagem, criar grupo, buscar grupos,"
buscar participantes dos grupos. E ao
final desse vídeo eu vou ensinar a você
como colocar mais funções aqui nesse
"nesse servidor, tá? Nesse MCP. Aí vamos"
"falar para ele, ó, envie uma"
mensagem eh explicando o que é uma API
de WhatsApp. Colocar aqui Evolution API
para o
número 5535. Aí eu passo um número aqui
"para ele, ó. Beleza?"
"Ó, ele vai lá, consta o agente de A,"
"roda o modelo e executa a função. Ó lá,"
"enviando a mensagem, ó. Se eu chegar"
"aqui, a mensagem foi enviada com"
"sucesso. Então, cheguei aqui, ó. Qual"
que é a mensagem? Uma PI de WhatsApp
como Evolution permite que
desenvolvedores integrem
funcionalidades. Se eu chegar aqui no
"WhatsApp, ó, ele já enviou para mim uma"
"page de WhatsApp como Evolution, permite"
com com eh com que desenvolvedores
"integrem funcionalidades. Então, a"
partir deste chat com a IA que eu estou
"rodando com a Open AI, é possível que eu"
envie várias ações que eu vou ensinar a
vocês como construir essas ações aí
"dentro do nosso servidor. Beleza? Então,"
"galera, bora pra ação, né? Eh, um vídeo"
"que a gente vai tra, eu vou trazer tudo"
detalhado do zero para que mesmo que
"você não saiba nada de programação, ao"
final desse vídeo você consiga tá
utilizando o MCP e consiga dar os teus
"primeiros passos nesse ambiente, tá? É"
legal. O que que nós vamos precisar? Nós
"precisamos de duas ferramentas básicas,"
que sem elas nós não conseguimos
"executar nada nosso PC. Então, a"
primeira delas que você vai baixar é o
chamado Docker Desktop e o segundo é o
"Node, tá? É o Docker Desktop, você"
encontra o download dele aqui no Google.
Vou deixar o link também para download
"dessas soluções, todas na página de"
"apoio. Então, entra na página do Docker"
"Desktop, tá bem? Eh, deixa ele carregar"
"pra gente e vai até aqui embaixo, ó,"
"download Docker, tá? Baixa aqui a versão"
do seu PC. Meu caso foi Windows AMD64.
"Baixou o Docker Desktop, você vai"
instalar ele no modelo padrão. Não
precisa se se preocupar com nada.
"Executa o instalador, vai dando next até"
"o final, até finalizar a instalação do"
seu Docker. E você vai abrir esse
"Docker, ele vai ser essa baleinha aqui,"
"ó. Ele não vai ter nenhum contêiner, tá"
bem? Então você executa o teu Docker
"Desktop, ele abre essa baleinha. Docker"
Desktop tá instalado. Show de bola. O
segundo programa que nós vamos instalar
"no nosso computador é o Node, tá? Então"
"você vai procurar aqui, ó, download."
"Node, tá? Eh, aqui download node, né?"
"Faz o download do Node, eh, da versão"
"aqui do seu do do seu sistema, no caso"
aqui seria o Windows. E também vou
"deixar para vocês, pessoal, eh, o link"
do Node na página de apoio. Instala o
"Node no formato padrão, executa o"
"instalador, vai dando next até o final e"
ele vai terminar de instalar. Como é que
você sabe que o Node tá instalado no teu
computador? Depois de terminar de
"instalar o Node, você vai vir aqui, ó,"
"em pesquisar e executa o cmd, tá? o"
"prompt de comando, esse carinha é preto."
"E você escreve assim, ó, node traço V,"
para saber a versão do node que está
instalado. Se ele aparecer a versão
"aqui, seja 23, 22, 21, 20, qual seja,"
quer dizer que o node está instalado.
"Então, o primeiro passo primordial pra"
"gente continuar com esse vídeo é esse,"
"instalar o Docker Desktop, instalar o"
"Node na sua máquina. Tudo pronto, vamos"
"começar, tá galera? Ó, abre o seu"
"Docker, o seu Docker Desktop. Aqui nas"
"imagens você vai encontrar, tá? Eh, o"
"NTIN, você vai fazer o pull. Então,"
"encontra Net, clica em pull. Ele vai"
"baixar o Netn para você. Eh, aguarda ele"
"baixar, né? No meu caso, já tenho a"
"imagem baixada, então foi rapidinho, mas"
algumas vezes ele demora um pouco para
"baixar. Legal. Então, faz o pull e"
"depois vai em run. Legal. Ó, eh, assim"
"que a gente for executar o run, a gente"
precisa fazer algumas
"configurações. Eh, vocês vão encontrar"
na página de apoio também esta pastinha
"chamada ZDG. Então, antes de continuar"
"com a instalação da NTN, baixa essa"
"pasta chamada ZDG, que está na página de"
"apoio da descrição desse vídeo, joga ela"
aqui no teu desktop e descompacta ela.
"Você vai descompactar, ela vai abrir"
essa pasta com esses quatro arquivos.
Inicialmente são os quatro arquivos que
a gente vai precisar para executar o
"nosso ambiente, beleza? Então, baixa a"
"pasta do ZDG, coloca na tua área de"
"trabalho, descompacta ela e você vai"
encontrar essas esses quatro arquivos.
"Beleza? Fez isso, vamos seguir aqui pra"
instalação. Qual o nome do contêiner que
"eu vou dar? Vou dar o nome para ele, de"
"N aula. Tá bem? A porta, eh, a porta"
"você pode usar a porta padrão 5678, só"
que eu vou utilizar outra porque eu já
tenho NTN que eu fiz a demonstração para
"vocês rodando na 5678. Então, vou"
escolher uma outra porta para ele. Vou
"escolher aqui a 5 e 789, tá? O volume, a"
gente precisa montar esse volume. Que
"que nós vamos fazer, ó? O volume nós"
"vamos colocar aqui, ó. Clica nesses três"
"pontinhos, beleza? navega até a tua área"
de trabalho e encontra a pasta
"ZDG. Seleciona essa pasta, beleza? E que"
que você vai colocar no valor dela? Você
"vai colocar exatamente este valor,"
"legal? Em variável de ambiente, a gente"
"seta aqui é o time zone, tá? Para ele"
poder utilizar o time zone de São Paulo.
"Beleza? Ó, dá o"
"run. Aguarda, ele vai montar para você,"
né? Você pode eh clicar aqui fora para
"fechar essa telinha. Você vai perceber,"
"ó, que ele está executando, ó. Já"
"executou NTN aula, né? Ele montou todo o"
ambiente e executou. Você pode vir aqui
"em contêiner, tá bem? Você já vai"
"encontrar NTN aula, ó, que é o contêiner"
que nós acabamos de criar. Você clica
"aqui, ó, para ele abrir pra gente, ó."
Legal. Ele abriu já pra gente aqui o
nosso NTN. Vou colocar aqui um e-mail
"qualquer para acessar, né?"
e uma
senha.
Next. Legal. Vamos escolher aqui. Vou
colocar aqui eh um SAS ou qualquer
coisa. Vou dar um get start. Acho que
"ele já foi, ó. Beleza. Dá um skip."
"Legal. Ó, ele já traz pra gente um"
modelo e aqui já meio que pré-pronto pra
"gente utilizar, que é basicamente isso"
aqui. Eu vou também eu posso baixar essa
esse flow aqui e deixar para vocês na
página de apoio e fazer download também.
Mas basicamente ele já vem com aqui meio
"pré-pronto, né? Que é o que a gente"
"utilizou. É só baixar, apagar esse"
"calendário aqui, ó, que é exatamente"
"isso aqui que a gente vai utilizar, ó."
Você pode usar esse aqui como base que
ele já vem aqui pronto para você.
"Beleza? Então, tá aqui, ó, instalado"
"etn, ó. Então, o que que a gente fez?"
"Baixou a imagem net, instalou no nosso"
"PC. Segunda coisa importante, você"
"precisa navegar aqui, ó, nesses três"
pontinhos na lateral esquerda do seu
"Net. Vira em settings, eh, dar um save"
aqui para salvar o que eu apaguei. Vamos
lá em
settings. Vai virem community nodes.
Aqui dentro nós vamos instalar o
"community node do MCP, que é esse cara"
"aqui, ó. Então, vem aqui, ó, copia. Esse"
essa descrição também tá na página de
"apoio, tá? Esse esse passo a passo. Joga"
"aqui, marca esse checkbox e dá um"
install.
"Guarda ele instalar para você,"
"tá? Instala, instala, instala. Ele vai"
mostrar aqui. A gente consegue
acompanhar os logs do
contêiner. Vamos lá. Deixa ele terminar
de instalar pra
gente. Beleza. Enquanto ele vai
"instalando aqui, a gente pode ir pros"
próximos passos. Como eu tô com bastante
"coisa rodando, ele pode estar"
"consumindo, demorando um pouco, mas tá"
"fazendo download. Tá legal, pessoal. Eh,"
deixa ele terminar de instalar o
community node. Qual que é o próximo
"passo que a gente vai fazer? Olha só,"
galera. Próximo passo é instalar o
"pacote do Evolution. Então, instalou"
"NTN, baixou o Community Node, a gente"
pode instalar Evolution. Como é que a
"gente vai instalar esse Evolution,"
"galera? Ó, naquela pastinha ZDG, vocês"
vão perceber que depois que a gente
"instalou eh o NTN no Docker, surgiram"
"vários novos arquivos, mas não precisa"
"se preocupar, é dessa forma mesmo que a"
gente precisa que ele se comporte. Aqui
nós vamos instalar o Evolution da
"seguinte forma. Clica aqui na pasta ZDG,"
clica com o botão direito nessa pasta e
abre o terminal. Que que nós vamos
fazer? Eu já deixei o Docker Compose e o
ENV prontos para executar as
configurações que a gente precisa. Tá
"bem? Então, basicamente é jogar essas"
"dois, é ter esses dois arquivos na pasta"
e aqui dentro do seu terminal você vai
"escrever docker compose up. Ó, botão é"
"contrl C para colar no terminal, botão"
"direito, tá? É do mouse e enter. Ele vai"
"montar, ó. Ó lá, tá montando pra gente,"
"ó. Se você entrar aqui, ó, dentro"
"do Ah, já instalou com Node foi"
"instalado. Beleza, ó. Se você entrar"
"dentro do seu do seu Docker Desktop,"
"você já vai encontrar agora eh aqui, ó,"
o ZDG. Qual que é o nome que eu coloquei
agora? Vamos descobrir. Será que é ZDG
que eu coloquei? É ele
"mesmo. ZDG. Deixa eu ver aqui, ó. É,"
então o nome da Evolution vai entrar
"para você como ZDG. Tudo bem, não tem"
"problema, tá? Entrou aqui o ZDG. Se você"
"vier aqui, ó, na Evolution AP e vai"
"abrir esse clicar nesse botãozinho,"
"Evolution AP e clicar aqui, ó, ele já"
vai abrir pra gente na porta 8088. Como
"é que você abre o manager,"
"tá? Você vem aqui, ó,"
"808/manager, legal? Ele abre aqui, né,"
com as instâncias. E aí você pode criar
uma instância. Que que você vai fazer?
"Você cria uma instância, pode chamar ela"
"de evolution, tá? Tipo Bailes mesmo,"
"token e salva. E aí você vem aqui, ó,"
"dentro e gera um QR code. Beleza? Então,"
"ó, ler o Qcode, ele vai aparecer para"
"você nesse formato aqui, ó, de"
"conectado, tá? Essa é outra evolution"
que eu tô rodando. Tô rodando duas
"evolutions do meu PC, tá? Que eu mostrei"
o exemplo e essa nova que nós acabamos
"de conectar. Lê o QR code, ele vai"
aparecer para você no conectado. Legal.
"Então é isso aí, ó. Já instalamos NTIN,"
instalamos a Evolution e agora nós
estamos prontos aqui para o quarto
"passo, que é fazer a instalação do"
servidor. Como é que a gente instala o
"servidor? O nosso servidor é esse index,"
que inclusive eu peguei como exemplo de
um outro vídeo de um produtor de
"conteúdo chamado Eric, Eric ou Érico,"
perdão se eu se eu tô errando seu nome
"agora, mas muito legal. O vídeo do cara"
"é top, tá? Depois vocês podem buscar aí"
"no YouTube. Eh, usei o dele como modelo"
para construir e ao final do vídeo eu
vou mostrar para vocês como construir
"novas funções. Então, esse index JS, que"
"é esse cara aqui, ó, é o servidor MCP,"
"né, que vai fazer é a nossa é o nosso"
arquivo que vai adicionar essa camada de
"inteligência artificial, permitindo com"
"que a Open AI execute comandos, né, de"
acordo com a nossa as nossas instruções
"diretamente Evolution API, tá? Como é"
que a gente faz a instalação desse
"servidor? Então, vamos voltar aqui, ó,"
"pro nosso tutorial. Vem cá, ó, nosso"
"tutorialzinho, ó. Legal, ó. Como que a"
"gente executa isso? Quarto passo, vem na"
pasta
"ZDG. Eh, você pode fechar isso aqui"
"agora, ó, que já tá, tá rodando. Ah,"
"inclusive, ele parou, ó. Fechei o"
"terminal, ele parou. Como é que você"
"executa Evolution de novo, ó? Clica"
nessa caixinha e dá o
"play. Beleza? Ele já executou de novo,"
"sem problemas. Volta na pastinha ZDG,"
"botão direito, abrir o terminal, tá?"
Como é que a gente instala agora o nosso
"MCP server ali, ó, com este comando"
"npmi? Já tá pronto, só copiar. Botão"
"direito, npmi, você aguarda. Ele vai"
"criar mais uma nova pasta aqui dentro,"
"né? O node modules, o package de locker"
"jzone. Então, já foram adicionados novos"
arquivos aqui na nossa pastinha. Beleza?
"Então, tá OK, ó. Vamos lá, ó. Já"
instalamos todo o ambiente que é
necessário pra gente executar o nosso
"MCP. Agora, quais são os últimos passos"
"e os passos mais importantes, tá? Porque"
"agora com o ambiente definido, nós"
precisamos construir aqui a o nosso
"workflow dentro de NTIN, tá? Então,"
bastante atenção pra gente começar a
"construir. Então, nós vamos utilizar"
aquele workflow que vem da com a IA já
"pronta aqui dentro, tá bem? Então, clica"
"nele. Vou dar um nome para ele aqui, ó."
MCP
Evo IA.
"AI, né? Artificial intelligence. Beleza."
E eu apaguei o nozinho que a gente tinha
aqui do nosso amigo e Google Calendar.
"Foi removido, tá? Aqui no AI Tools, o"
"que que nós vamos fazer? Ó, a primeira"
"coisa, ó, no agente de IA, nós vamos"
configurar as credenciais do Open AI.
"Então, você vai abrir aqui Open AI, você"
"vai criar uma credenciala, tá? E você"
vai passar aqui uma API que você vai
pegar lá na tua Open AI. Vamos fazer
"isso juntos agora. Eh, depois a gente,"
eu edito esse vídeo e removo informações
"importantes que talvez apareçam, não tem"
"problema. Ó, legal. O que que nós vamos"
"fazer, ó? Primeira coisa que ele vai"
"pedir pra gente, ó, ID da organização."
"Beleza? Tá aqui, ó. Vamos no Evolution,"
"coloco ele aqui, ó. Legal. E ele vai"
pedir pra gente uma chave de API. Então
você vem em API Kiss e cria uma nova
"secret. Vou colocar aqui, ó, aula nit"
acessando o default project. Create a
"secretar essa secret, tá? E vou jogar"
"aqui, ó. Beleza, ó. Salvou, validou as"
"credenciais. Então, o primeiro passo já"
"está pronto, ó. Openi model configurado,"
tá? É importante que você tenha uma
"conta eh plus aí, se eu não me engano,"
chama Plus do chatpt para você criar a
"sua chave de API, tá? Então, chave de"
"API criada, qual que é o próximo passo"
"que nós vamos executar aqui, ó? Próximo"
passo é configurar o prompt do IA.
"Então, o que que nós vamos falar para"
"ele, ó? Nós queremos dar uma instrução"
"mais detalhada para ele, ó, dizendo"
"assim, ó. Entra aqui no agente de A e"
ele já veio com uma instrução
pré-preparada do Google Calendar que
estava aqui antes. Então a gente apaga
toda essa esse prompt e adiciona o
promptó. Você é um agente eh que usa
MCP. Use e buscar tool para informações
de ferramentas e executar tool para
executar ferramentas. Tá bem? Beleza.
"Estamos pronto. Próximo passo, vamos"
configurar MCP. Esse passo é bem
"importante, tá, pessoal? Então, ó."
"Criamos agente de A, né? Configuramos"
"agente de A, eh, configuramos as nossas"
credenciais. Agora a gente vai adicionar
"memória. Ó, vou colocar uma memória de"
"50 aqui, eh, que é o número de"
"interações, né, que o modelo vai receber"
desse contexto. Você pode definir quanto
"você quiser, tá"
"bem? E por último, aqui a gente vai"
adicionar eh a primeira função do MCP.
precis dar um restart no meu Nate N pra
gente poder instalar o MCP client.
"Basicamente vim aqui no meu contêiner,"
"tá bem? Eh, pausei e reiniciei, tá? Mas"
vamos lá. Agora nós vamos adicionar o
"MCP client. Então, vem aqui, ó, no tools"
"e busca ele, ó. MCP client Tool. Beleza?"
A
"primeira, a primeira função que nós"
"vamos, vamos colocar no nosso MCP é a"
listagem das tools. Paraa gente listar
"essas tools, o primeiro passo é"
configurar as credenciais. Como é que
você configura as credenciais? Então
"você vem aqui, ó, criar nova credencial."
"No command você vai colocar node, tá?"
O argumento é basicamente a nossa pasta
que nós setamos aqui na pasta do NH na
hora que nós criamos o nosso contêiner
"Docker, né,"
barraindexjs. Beleza? Por que que a
gente usa esse caminho? Porque o nosso
"index JS, que é o nosso serviço eh do NT"
"do do MCP, ele já está na pastinha do"
"NTN que nós mapeamos. Então, a gente já"
instalou e deixou pronto o ambiente do
indexs dentro da pasta que foi mapeada
"pro NTN. Então ele tá aqui, ó, esse cara"
aqui. Então é é bem tranquilo.
Basicamente a gente vai apontar eh pro
nosso MCP dizendo que a pasta padrão do
NTN do contêiner onde está o nosso
servidor. Então index js. As variáveis
"de ambiente você clica em expression,"
"abre aqui, ó. E basicamente as nossas"
"variáveis de ambiente são essas três, ó."
"Então você vai colar as três aqui, ó."
Qual que é a instância da Evolution? A
instância que nós vamos utilizar a
"instância Evolution, ó. Beleza? Qual é"
"aqui da IPI? É esse cara aqui, ó. Tá"
"bom? E qual que é o API base, ó? Esse"
aqui é muito importante porque como a
gente tá rodando tudo aqui dentro do
nosso ambiente Docker Evolution e também
"o nosso o nosso Nate, o nosso MCP, o API"
"database, o API base, né? que é onde"
"nossa evolution está instalada, seria"
"esse cara aqui, ó, local roxo"
8088. Só que a gente vai trocar isso
"aqui, ó, para"
host eh host docker internal. Agora
deixa eu só conferir se é host Docker
interno ou host interno Docker. Vamos
descobrir. Deixa eu descobrir aí agora
que a porca torce o rabo. Vamos ver
aqui. Eu não salvei essa informação. Mas
"mas ah, vamos pegar aqui no Google."
Vamos ver se é host internal Docker ou
"Host Docker Internal. Ã, Host Docker"
Internal.
que vai a host intern. É isso aqui mesmo
"que eu preciso,"
ó. Host docker interno. Esse esse nome
"aqui, ó. Fiz certo 80 88. Beleza. Então,"
"eh, se você tá usando no ambiente em"
"produção, você vai passar aqui o endpint"
Evolution
"Evolutioni.com.br, tá? Eh, então nós não"
vamos passar o local host porque o local
"host, se eu passar local host o"
contêiner do NTN não enxerga o contêiner
"de evolution, né? Eles estão rodando em"
"contêiner separados. O Natn tá nesse,"
ZDG tá nesse que evolutioni. Então eles
"não se comunicam, tá? Eles estão"
isolados entre si. Então pra gente poder
"dizer pro NTN, ó, NTN, use a rota do"
"Evolution, a gente tem que apontar, tá?"
A R Docker Internal e a porta que eu tô
"utilizando, que é 8088, beleza? Fechou"
"aqui, ó, e salvou as credenciais."
"Beleza? Ó, primeira coisa que a gente"
vai testar aqui é a listagem de tools.
"Então, tá aqui, ó, MCP. Tá pronto, ó. E"
"eu vou dar um nome para esse aqui já, ó."
"E o nome deles vai ser, qual o nome que"
"nós deixamos? Buscar tools, que é esse"
"nome aqui, ó. Esse cara aqui, ó. Buscar"
"tools. Então, vem aqui, ó. Vamos dar um"
"nome para ele, ó. Buscar to. Buscar to,"
né? Sem s. buscar
"tools. Beleza, ó, abre o"
chat. Vamos perguntar para ele quais
tools tem.
"Ó lá, já achou, ó. Rodou legal, ó."
Ó lá. Quais tools tem? As ferramentas
"disponíveis são pau, pau, pau, pau, pau,"
"pau, pau. Beleza. Legal. Agora, que que"
a gente vai fazer? a gente vai construir
"um um MCP, é, para poder executar essas"
"tools, que basicamente é esse aqui que"
"executa o envio da mensagem, tá? Então"
vamos lá. Executar tools. Então vem MCP
"client. Eh, aqui nós vamos colocar, qual"
que é o nome que eu deixei? Executar
"Tools. Executar Tools. Beleza, deu um"
"rename. Eh, aqui nós vamos usar o"
"execute tool, executar tool. O tool"
"name, basicamente vocês vão copiar isso"
"aqui que tá na página de apoio, tá? Que"
"nós vamos buscar da IA, vamos dizer para"
"ele, ó, busque da Ia, né, o nome da tu."
Então ele vai fazer essa busca. E qual
que é o parâmetro que a gente vai
utilizar? A gente vai deixar a
inteligência artificial definir esse
parâmetro pra gente. Beleza? Define
"automático. Isso mesmo, ó. Beleza? Ó,"
"salvou. Agora, eh, a gente poderia aqui"
mandar ele enviar a mensagem. Ele vai
dar um erro porque eu não tenho número
"conectado, mas a gente pode dizer para"
"ele, ó, envie uma mensagem eh"
de
eh explicando
explicando o que é um
"MCP, um model context"
protocol para o número. Eu posso até
"deixar aqui assim, olha, ele ele vai e"
pergunta pra gente qual é o número.
Vamos ver se ele
"tá, se ele vai chegar aqui mesmo tando"
sem a conexão. Vamos ver o que que ele
vai dizer pra gente. Eu acho que ele
"estando sem a conexão, ele não vai sem o"
Qcode lido. Talvez ele não consegue não
"consiga utilizar a a tool, mas não tem"
problema. Mas que que é legal? Você pode
"dizer para ele, ó, envia a mensagem"
explicando o que é MCP. Aí ele vai falar
"assim, para qual número? Tipo aqui, aqui"
"eu já paguei meu histórico, né? Aí ele"
"vai perguntar: ""Mas para qual número"
"você quer enviar, né?"" Então é isso,"
beleza? Deixa eu dar um pause aqui.
Dessa forma você consegue construir
execução da execuções das tools. Agora é
um pouco mais além aqui e que seria eh
uma explicação mais técnica das tools.
Nesse arquivo index você tem aqui as
"ferramentas, tá? Como que você pode"
construir novas ferramentas? Então você
pode criar novas ferramentas eh usando a
própria inteligência artificial. Então
"aqui eu tenho as tools, tem um"
"arreizinho, né, que é um conjunto das"
tools que existem. Então na hora que eu
"mando ele listar as tools, eles tra ele"
"traz, ó, to de enviar mensagem, to de"
"criar grupos, to de buscar grupos"
"participantes, tá bem? Eh, aqui eu tenho"
"render para eh informar a criação, né,"
"da da do serviço. E aqui os to rends,"
"tá? Que que é, ó? Quando eu chamo envia"
"mensagem, que que ele faz, ó? ele chama"
eh a rota de envio de mensagem. Quando
"eu falo para ele, ó, criar grupo, ele"
chama a rota de criar grupo. Quando eu
"chamo buscar grupo, ele chama a rota de"
buscar grupos. Lembrando que como a
"gente tá rodando local, vocês vão"
"perceber que aqui tá sem os, tá? Só"
"HTTP, tá? Que eu tô usando o Host Docker"
Internal. Se você vai usar isso aqui em
"produção, você tem que configurar teu"
"index js para chamar https, tá? Porque"
"tá Evolution, possivelmente vai estar"
"instalada aí na eh em um em um servidor,"
"né, com Prox Reverso expondo serviço no"
HTTPS. Beleza? Se não sabe como instalar
"a Evolution PI dentro da comunidade ZDG,"
você pode agendar a instalação com o
"nosso time, tá? Você passa lá as"
informações pro nosso time e nosso time
"instala no seu servidor para você,"
"beleza? Aí você fala assim: ""Ah, tá, eu"
"quero criar novas rotas"". Que que você"
"vem? Você pode vir no próprio chatt, né?"
"dizer para ele assim, ó, chatt, eu tenho"
"esse index, ó, cadê? Vamos lá, ó. Lá"
"tenho o index JS, que é um MCP que"
consome rotas da Evolution API. Aí você
"fala assim, ó, eh, tem o envia"
"mensagem que aí você vem aqui, olha só"
"que legal, vem aqui na document."
"Inclusive, se a gente pode fazer isso no"
"cursor, né? A gente fez um uma videoaula"
"anterior com o cursor, você pode fazer"
"isso tudo direto no cursor, tá? Então é"
"tranquilo também. Abre o index js aqui,"
"ó. Vamos lá, ó. Index J. Vamos fazer no"
"cursor que vai ser mais legal, ó. Vamos"
lá. Aqui vamos achar aqui. Cadê a nossa
"pasta DG? Tá beleza. O que que eu quero,"
ó? Vamos abrir ele
"aqui. Abriu, ó. Beleza, ó. Eh, cursor."
Vamos ver
aqui. Eu não sei se ele vai tá aqui
"agora. Ah, o chat, ó. Beleza. Ó, que que"
"a gente tava falando para ele,"
"ó? Eh, tem a função. Olha que legal."
Envia
mensagem que consome a rota. Aí você vem
"aqui na rota aqui, ó. Você abre aqui o"
"teu Evolution, vem na documentação, aí"
"você vem aqui, ó, na API, ó, versão"
"dois. Qual que é o send message? Ó, que"
é o que a gente tá usando aqui. Consome
"a rota. Olha só, essa aqui,"
"ó, que consome a"
rota eh de enviar mensagem.
Crie a mesma
função. Crio uma função
similar no meu
MCP para enviar imagem. Cadê? Vamos ver
se enviar uma
mídia. Enviar
mídias usando a rota abaixo. Aí você
"fala assim para ele, ó. Aqui o sendí é"
"esse cara aqui, ó. Copiou. Tuf sende."
"Beleza, agora"
"aguarda. Já tá criando,"
"ó. Olha, o cursor é Esse cursor é"
 mesmo.
"Ó lá. Beleza. Ó, que que você vai fazer,"
ó? Já tá construindo para você. Você dá
"o accept, ó. Ele já criou enviar media,"
"ó, dizendo tudo que você precisa passar,"
"number, tal, tal, tal, né? Ó lá, já"
"adicionou o esquema de validação do Zod,"
né? Já adicionou aqui uma TU Enviar
"mídia, tá bem? E aqui o handle também,"
"ó. Envia mensagem, envia mídia, ó. Já tá"
pronto. Beleza. Vamos ver se ele já vai
"entrar aqui, ó. Quais tools tem? Vamos"
"dar um reset aqui,"
ó. Quais tools
"tem? Vamos lá. Ó lá, ó. Envia mídia,"
"ó. Legal. A partir disso, você viu como"
"é simples, né? O cursor, se não souber"
"como instalar, é na no vídeo anterior a"
"esse, a gente fez um vídeo com cursor"
"Evolution API, bem legal também, tá? Eh,"
"então é isso aí, você pode criar, não,"
"agora eu quero um send, né? Faço mesmo."
"Você vai entrar no cursor, abre o"
"cursor, fala para ele, ó, tem a função"
"de vídeo mensagem, usa rata tal, ó, né?"
"É, agora crie. Agora vamos falar para"
"ele assim, ó."
Agora crie eh uma função no meu MCP para
enviar localizações usando a rota
"abaixo. Ah, baixo. Aí você vem aqui, ó,"
no
"send aqui o formato JavaScript, beleza?"
"Cola e dá o send,"
"ó. Beleza. Ó, já tá criando, ó. Vou te"
"ajudar a criar nova função. Então, o que"
ele vai fazer? vai adicionar o esquema
no ZOD de
validação. Depois ele vai criar pra
gente o envio a
localização e vai ficar disponível nas
pra gente utilizar. Deixa ele terminar
"de escrever. A gente dá o accept aqui,"
"ó. Acept aqui do file, ó. Vou por aqui."
Vou por
aqui. Já alterou.
"Legal. Reiniciar aqui, ó, a"
"conversa. Beleza,"
"ó. Ah, pera aí. Que que ele deu aqui? Un"
anatos tá dando na
"autorizado. Ah, deve ter caído meu"
contêinerzinho. Com certeza. Tô com
"consumo excessivo aqui de memória, né?"
"Eh, mas basicamente, pessoal, vocês"
viram aqui como é simples você construir
"o teu MCP, né? E a partir do cursor, tá?"
"Tá, tá até aqui, ó. Isso mesmo, ó. Eh,"
"sem problemas. eh com o cursor, com a"
"inteligência artificial, né? Vocês"
perceberam que é bem prático você agora
criar uma camada de de inteligência
artificial para controlar tua page de
"WhatsApp, mesmo que você não saiba nada"
de programação. E se você quiser
aprofundar os teus conhecimentos em API
"de WhatsApp, eh em sistemas de"
"múltiplatimento, né, para múltiplos"
"canais, não deixe de acessar a"
comunidade ZDG. é a maior comunidade do
mundo de profissionais e pessoas
entusiastas que trabalham com APIs e
"automações para WhatsApp. Já são mais,"
"são quase 7.000 membros, tá bem? Mais de"
4 anos aí na estrada ensinando e
transformando a vida de centena de
milhares de pessoas com esse conteúdo
aqui sobre APIs e automações e sistemas
de multiatendimento para WhatsApp. Link
da comunidade tá na descrição desse
"vídeo, tá bem? Forte abraço, beijo no"
"coração de todos, fiquem com Deus."
Qualquer dúvida é só chamar. Tamos
"junto, Pedro Danas. Até a próxima."