#!/bin/bash

# Script para gerar credenciais seguras para instalação n8n
echo "🔐 Gerando credenciais seguras para n8n Production..."

# Gerar chave de criptografia n8n (32 bytes base64)
N8N_ENCRYPTION_KEY=$(openssl rand -base64 32)
echo "N8N_ENCRYPTION_KEY: $N8N_ENCRYPTION_KEY"

# Gerar senha para PostgreSQL
POSTGRES_PASSWORD=$(openssl rand -base64 16)
echo "POSTGRES_PASSWORD: $POSTGRES_PASSWORD"

# Gerar senha para usuário admin n8n
N8N_PASSWORD=$(openssl rand -base64 12)
echo "N8N_PASSWORD: $N8N_PASSWORD"

# Gerar senha para Grafana
GRAFANA_PASSWORD=$(openssl rand -base64 12)
echo "GRAFANA_PASSWORD: $GRAFANA_PASSWORD"

# Salvar em arquivo seguro
cat > credentials.txt << EOF
# ==========================================
# CREDENCIAIS N8N PRODUCTION
# Data: $(date)
# ==========================================

N8N_ENCRYPTION_KEY=$N8N_ENCRYPTION_KEY
POSTGRES_PASSWORD=$POSTGRES_PASSWORD
N8N_PASSWORD=$N8N_PASSWORD
GRAFANA_PASSWORD=$GRAFANA_PASSWORD

# ==========================================
# IMPORTANTE: 
# - Mantenha este arquivo seguro
# - A chave de criptografia é CRÍTICA
# - Faça backup das credenciais
# ==========================================
EOF

echo ""
echo "✅ Credenciais geradas e salvas em credentials.txt"
echo "⚠️  IMPORTANTE: Faça backup deste arquivo!"
