﻿Olá pessoal tudo joia aqui luí nesse
vídeo eu vou falar com vocês de um
update que saiu hoje e que é
importantíssimo o Traffic 3 pessoal eu
quero que vocês assistam esse vídeo com
bastante atenção tá assiste até o final
pega a mensagem que eu vou passar aqui
para vocês porque esse Com certeza é um
dos updates mais importantes do ano
então fica comigo até o final dá o like
comenta e pessoal se você quer concer
mais sobre setup configuração de
infraestrutura de docker acessa aqui ó
promovo app.com para você poder conhecer
os nossos cursos tem um curso gigante de
docker ali também tem um instalador caso
você queira aí ter as facilidades de um
instalador mas faz o curs docker porque
esse vídeo de hoje vai ser importante
para você entender um pouco mais sobre
esse Universo de infra e se você gosta
de grupos de WhatsApp acessa aí prom.com
bar WhatsApp e se você é aluno acessa
promove.com bdc que é ali que tem a
comunidade oficial e é ali que você vai
conseguir o suporte tá então aqui
pessoal a gente chegou o o dia do
Traffic lançar aí a versão 3 Eu uso o
Traffic desde a versão 1 né 1.7 que foi
a versão Acho que mais popular ali do
Traffic antes dele eu utilizava muito o
engex e e o Traffic pessoal ele na
versão um para versão dois a gente eu
usei a versão um ali por uns 2 3 anos né
a versão dois foi um tanto complicada a
migração Porque ela foi praticamente
100% incompatível né então a gente teve
que escrever tudo do um reescrever tudo
do um pro dois tanto do lado do Traffic
quanto do lado do serviços ali do swarm
então foi bastante bastante complicado
Além disso pessoal tem uma outra questão
também que eu falo muito com os alunos
que vocês são responsáveis pela infra tá
na versão um para versão dois e eu optei
por não utilizar o Final Zero então saiu
lá a versão 2.0 Como acabou de sair aqui
o 3.0 eu optei por não utilizar ele até
porque os releases do TRF são bem
frequentes e eu dei um espaço ali para
não atualizar pra correção de bug sempre
que sai um software versão zero pessoal
é a versão mais propensa a tbg vocês vão
ver aqui a quantidade de modificação que
teve no Traffic da versão do pra versão
3 e aconteceu a mesma coisa da versão um
para versão dois tá E ali eu aprendi né
a gente fala né a gente erra uma vez ali
eu aprendi pessoal o Traffic ele é o
sistema base da sua infra ele é com
certeza o software mais importante da
sua infra Porque sem ele você não abre
nada você não abre o pertain você não
abre nenhuma aplicação sua então se
tiver um bug se tiver um erro se tiver
algo complicado ali no Traffic acabou tá
gente você vai est em sérios problemas
Então pessoal esse vídeo aqui eu falei
com para vocês que é um vídeo importante
não tô querendo colocar medo em vocês
longe disso tá gente só quero bater um
papo real aqui como eu faço na mentoria
Como eu faço nos cursos tá e se você não
é aluno pessoal tá mais do que na hora
de você virar aluno acessa aí a promov
web para você poder conhecer mais o
nosso trabalho e basicamente pessoal o o
Traffic quando saiu da versão paraa
versão dois eh não só teve essa questão
de compatibilidade que também tem na
versão dois para TR Mas é bem pouquinho
mas tem mas aparece seram vários bugs
então na época né o htp 2 não táa
funcionando bem algumas regras de cash
não funcionavam bem então quem rodava o
WordPress com ele teve sérios problemas
e algumas rotas não funcionavam direito
pessoal por mais que ele seja muito
testado e a equipe do Traffic ela é
fantástica ele realmente é um sistema
muito muito bem feito não tem sistema
perfeito pessoal vai ter um bugzinho
outro ali ainda mais com a quantidade de
mudanças que eles fizeram essa versão
agora ele é maior do que foi a versão do
Então pessoal a minha primeira dica para
vocês é você aí que aluna da promov web
você que é ao mentorado eu não recomendo
utilizar o Traffic até a versão 3.1 tá
vamos sempre seguir essa regra por quê
Porque eu não sei o que tem ali dentro
ele é um software essencial se tiver um
bug nele pessoal não tem para onde
correr não tem para onde chorar eu quero
que vocês entendam isso e uma vez que
você fez o update o downgrade não é tão
fácil é é uma versão Major que mudou
então não foi a da 3 para 3.1 foi da 2
para TR Quando muda a versão Major que é
o primeiro número ali pessoal não é
retrocompatível tá inclusive os seus
certificados os seus SSL Então pessoal é
um negócio tem que ser muito bem pensado
a regra é vamos evitar tá bom vai ter o
cur docker agora nesse mês aqui de Mind
vai regravar o curso docker eu vou
gravar para vocês o curso já com o
Traffic
e também com uma migração do dois pro
três mas a caráter experimental ainda tá
gente eu vou recomendar para você você
aí que tá rodando em produção Evita o TR
por hora luí mas se tiver tudo bem
beleza pessoal tiver tudo bem Quando
chegar na 3.1 estamos tranquilos Tá mas
vocês rodam o sistema em produção vocês
têm que ter responsabilidade hoje
pessoal na promovo web aqui só no nosso
instalador são mais de 5000 Instalações
tá então eu tenho que ter essa
responsabilidade de chegar aqui e jogar
Real em você eu quero usar quero pro
nosso dia a dia pessoal para que para
tudo que tem na promov web hoje não vai
mudar nada o Traffic 3 não vai mudar uma
vírgula ele tem novos recursos tem muita
aplicabilidade mas pro nosso Universo de
software aqui ele não tem nenhum recurso
novo que vale a pena você ir lá e
atualizar tá então é importante ficar
muito claro isso ele evoluiu sim pro
nosso uso pessoal a gente faz o uso
muito simples dele né pro nosso uso não
mudou nada então você tem algo que já tá
há 5 anos no mercado sendo
constantemente diariamente testado daqui
a pouquinho vocês vão ver que os números
do Traffic tá não convém eu trocar ele
pela versão 3 agora hoje no lançamento
dele vamos dar tempo para ele amadurecer
rapidamente tenho certeza que eles vão
começar a lançar já as versões pefs de
de correção e eu não quero pessoal que
vocês aí que estão rodando em produção
seu Chen o seu chatot a sua Evolution
que vocês tenham problemas vocês viram
recentemente a questão de um mês atrás a
a mudança que foi pro docker 26 vocês
viram a mudança que aconteceu em relação
ao piner pessoal cada passo que eles dão
de update consequentemente vai ter algo
quebrado é questão de parar e pensar
devo atualizar a minha recomendação para
você é não tá a gente vai ter o curso no
curso já vou fazer o curso com ele para
que vocês já possam configurar ele mas
na versão 3.1 tá bom pessoal então é
muito muito importante ter isso em mente
tá Não é questão de Breaking Change
porque o Breaking Change ele é facinho
de configurar é questão de estabilidade
Vocês precisam muito mais de
estabilidade do que de novidade Vocês
precisam muito mais que as coisas
funcionem para que você tenha um final
de semana sossegado para que você tenha
um lançamento sossegado do que para ter
um recurso que você não vai usar tá
gente jogando na real para vocês aqui o
que ele tem de novidade a gente não usa
ainda tem algumas coisas ali que eu goo
Gostei a gente pode até pensar em
utilizar mais paraa frente mas se você
fizer o update hoje você não tem zero
benefício tá eu quero que você entenda
isso zero benefício Ah luí mas é um
recurso novo você não vai usar tá gente
então vamos trocar não vamos trocar o
estável pelo novo o novo eu não sei
vamos deixar a galera instalar ele vamos
deixar alguém instalar sempre tem um
afobado deixa o afobado na frente
pessoal o afobado vai descobrir esse
caminho aí se der se der errado para ele
a gente avisou mas eu quero que você que
é meu aluno que você não caia nessa
pessoal não quer dizer que é ruim não
quer dizer que tá proibido quer dizer o
quê testa vamos controlar Tá bom eu vou
subir ali o stack do Traffic 3 para
vocês tudo para quem já quiser a testar
ele mas a recomendação é não coloquem em
produção a gente não sabe como que ele
tá eu não sei como é que no dia a dia
ele tá o Traffic 2 tem 5 anos eu sei
vocês aqui que estão desde do começo
comigo vocês estão hos 2 anos utilizando
ele então vocês sabem como é que ele
funciona bem a gente não sabe do TR do
três Então pessoal vamos com calma aqui
tá dado o recado vamos continuar aqui
pessoal Então olha só aqui Ele conta um
pouco né que surgiu em 2015 eu comecei a
usar o TRF em 2016 e e ali na versão 1.7
e Olha só pessoal o Traffic ele se
tornou um dos gateways mais implantados
do mundo com TRS bilhões de downloads e
mais de 750 colaboradores É muita gente
Pessoal é é muita implementação ele tá
classificado no top 15 do docker HUB e
tem 47.000 estrelas no github muita
coisa pessoal lá o Trafic 1 fo em 2016
né a versão mais popular da série 1 foi
a 1.7 eu tenho até hoje pessoal como
curiosidade eu devo ter uns uns 15
servidores que tem o Traffic 1.7 ainda
gerando SSL sist são sistemas legados
que funcionam perfeitamente tá e o
Traffic 2
veio 3 anos depois né veio em 2019 ali e
5 anos agora pessoal 5 anos de Traffic 2
muito testado colocado na batalha
pessoal Ele é bem testado bem estável
mesmo tá aí hoje saiu Então o a versão 3
de novo pessoal eu não sou contra
evolução tá eu sei que alguém vai
comentar aí o pessoal sempre comenta mas
eu não sou contra evolução eu sou contra
a gente sair correndo queimar a largada
da evolução deixa pessoal deixa que o TR
seja testado deixa que ele seja entado
pela galera deixa que as isos apareçam
no github que a equipe do Traffic saiba
no em cenários reais como que ele tá ele
tá ele tá quase um ano no beta o TRF 3
pessoal tem um bom tempo que tá ali já
disponível no no repositório não digo um
ano Mas já tá um bom tempo no Beta ali
passou por alfa beta e RC né agora saiu
ele ficou Acho que uns 2 TR meses como
reliz candidate então já tá sendo
testado Mas vamos deixar pessoal ele
sair tá bom vamos deixar ele sair vamos
deixar o TRF conhecer um pouco do mundo
real tá Para que as pessoas possam
testar ele e uma vez que ele foi
minimamente testado em produção no mundo
real já a versão oficial a versão 3.0
mesmo sem ser Beta Alfa relas candidate
vamos deixar ele dar um tempo para você
poder ter um sistema estável lembrando
pessoal embaixo do Trafic é só o docker
você tem o seu servidor Você tem o
docker e o Traffic se ele tiver com
problema pessoal tá tudo com problema
não tem tem não tem o que fazer tá então
de novo vamos ficar bem atento aí tá bom
E aqui pessoal ele é uma versão enorme e
olha só ele tem suporte para coisas bem
interessantes o asm né o asm eu vou
fazer um vídeo mais paraa frente para
vocês sobre isso mas você consegue rodar
código no navegador é um é um mecanismo
de execução muito rápido muito rápido e
que vem revolucionando muitas áreas
pessoal muita gente tá usando aí com o
asm open telemetry né para você poder
fazer uma observabilidade dele aqui vai
ficar legal colocar o Traffic para quem
usa Prometeus para quem gosta de de
analisar a quantidade de requisições
tudo né o Traffic 2 pessoal ele teve
dentro dele há alguns anos atrás o
CoPilot né então a gente tinha dentro do
do Trafic um CoPilot né que era um
dashboard que mostrava o servidor se ele
tava saudável número de requisições
número de erros depois eles abandonaram
o projeto né Mas é interessante observar
que eles estão dando adotando o open
telemetry que é uma tecnologia que tá
muito hypada aí né Tem s suporte para
kubernetes também tá e o spi ffe que eu
tô por fora do que que é isso daqui tá
depois eu vou observar um pouco mais
sobre isso pessoal aqui é importante que
ó foram 200 P request pessoal é muita
coisa muita coisa foi mexida e de novo
pessoal é bom é bom pro software ter
essa evolução tá você aí tem que ficar
atento só observando o que que vai dar
Ah vamos vamos observar vamos ver vamos
pom para rodar aqui E é assim que
funciona pessoal mas não caiam logo de
cara no Traffic 3 tá Então pessoal a
lista de possibilidades é tão grande que
eles vão fazer aqui uma série de blogs
tá e eu vou acompanhar essa série de
blogs eu também vou trazer conteúdo para
vocês em cima das séries que eles forem
montar tá bom E aqui pessoal nesse
primeiro artigo deles aqui eles falam
sobre a a migração a migração pessoal do
Traffic 2 por TR ela é tranquila tá de
novo né na versão um pra versão dois foi
totalmente compatível foi um Break
Change Total mesmo a gente tem que
reescrever ver tudo na versão dois você
não precisa mudar nada do lado da
aplicação você só precisa mudar no
Traffic mesmo alguns parâmetros no
Traffic 3 que são diferentes do Trafic 2
no curso de docker eu vou abordar essa
migração tá e eu vou disponibilizar
tanto o stack do três quanto pro stack
do dois tá então importante ter isso aí
em mente tá
eh uma nova versão principal é sempre
algo aguardado e realmente pessoal novo
design novos recursos melhor experiência
do usuário Tá mas a contraparte
geralmente é aspecto da migração tá
então fic atento aí quem quiser se
arriscar você é livre para se arriscar
tá gente mas sempre tem alguém que quer
se arriscar eu não quero que você se
arrisque se você perguntar para mim a
minha resposta vai ser não para você tá
gente vamos deixar passar um mês dois
meses para ele assentar para ver ali
quem quer quem quer se arriscar poder
correr os riscos porque eu não sei você
aí pessoal mas eu não quero correr risco
não tá o meu TRF Tá bonitinho aqui ó
time que tá ganhando no mexo eu vou sim
com certeza usar o três é em daqui algum
algum tempo falei para vocês vamos
testar Vamos colocar todas as aplicações
para rodar vamos rodar em caso real
vamos ver como é que ele funciona no
swarm com várias máquinas com uma
máquina só no swarm solo roteando
internamente ali tem tanta coisa que a
gente tem que ver no Traffic pessoal pra
gente poder falar assim olha já passou
lembrando o c o Traffic 2 tem 5 anos
pessoal já testei ele demais o três não
tá por isso que eu não recomendo para
vocês logo de cara tá bom Então pessoal
a regra aqui né aqui ele fala um pouco
sobre isso daí né Ele fala sobre um
pouco sobre o processo de update ele
fala que em 90% dos casos de uso você
não vai ter que mexer em muita coisa Mas
tem sim pra gente que usa docker essa
questão o único recurso aqui pessoal que
eu acho legal é que ele vai ter
nativamente hp3 né se você usa Cloud
flare por exemplo você já tem através do
Cloud flare então eu não preciso Traffic
me oferecer isso de novo tá gente aqui
no curso eu mostro para vocês o cloud
flare tem um módulo de cloud flare nessa
nova nessa nova atualização do C docker
vai ter muita coisa de cloud flare ali o
cloud flir já faz isso pra gente então
eu não preciso atualizar o Traffic para
ter isso eu já tenho isso com o Traffic
2 usando Cloud flare tá então para quem
quiser para quem fica muito ansioso aí
por causa de recurso você consegue esses
recursos em outro lugar tá bom E aqui
pessoal eh é importante entender que no
curso que a gente vai fazer agora nossa
atualização eu vou falar mais sobre o
Traffic eu aguardei aqui né a chegada do
três quer dizer eu vou fazer o curso em
cima do três não tem porque eu fazer o
curso em cima do dois tá quem tá usando
dois continua usando dois quem for
começar um novo projeto vamos vamos
começar com três Tá eu vou fazer
conteúdo do dois também para vocês tá
porque é o que eu vou considerar estável
mas eu não sei quando é que vai sair aí
o 3.1 tá pode ser pessoal que daqui a
duas semanas saia daqui uma semana saia
aí beleza eu só não recomendo 3.0 tá
pessoal essa que a questão é isso tem
que ficar bem claro para vocês aqui tá E
aqui pessoal Ele fala também para você
poder emigrar aos poucos tá o que é
difícil muitos de vocês aí usam no mesmo
servidor todos os aplicativos qual que é
o problema vocês estão todos sobre o
mesmo Traffic né então não tem como
vocês migrarem uma parte migrarem outra
vai ter que migrar todo do mundo então
você vai ter que testar chatwoot web
socket do chatwoot nhn Web socket do nhn
base Row web socket do base Row
Evolution né você vai ter que testar
tudo isso Type bot como é que tudo
funciona para poder falar assim ó tá
realmente assim a mudança foi realmente
transparente não não teve problema de
novo pessoal vamos dar tempo a gente vai
ter esse teste tá vou fazer com vocês
esses testes aí para que vocês aprendam
também a fazer tá então acho que é
importante de novo pessoal se você não é
aluno é agora pessoal Entra aí
promove.com para você poder aproveitar o
nosso curso novo vai sair no final do
mês tá então é a hora de você aproveitar
você pode entrar agora fazer o curso
atual tá para você já pegar o curso
atual já é bem completo esse novo vai
ser mais completo ainda mas pelo menos
você já entra no novo sabendo alguma
coisa então não perde tempo pessoal
acessa aí pro mov.com para poder
conhecer o nossos cursos tá E aqui
pessoal Quero mostrar para vocês fazer
um uma outra consideração que é a
seguinte se você tá aqui e você é um
usuário do nosso instalador no nosso
instalador até sair a versão
3.1 eu vou forçar ali a versão 2.11 tá
então todos vocês que estão assistindo
aqui quem criou um servidor até hoje tá
com a versão latest a partir da data de
hoje pessoal a versão Lage vai ser a
versão TR tá então se você criar um
Traffic novo hoje e usar o latest ele
vai ser ele vai est ali
como como como a versão TR então para
ficar no ambiente mais estável tem que
fixar um Lab ali fixar uma imagem que é
o
2.11 aqui pessoal no nosso instalador se
você vier aqui no nosso instalador você
vai rodar aqui ó promova web vai
aparecer para você aqui as opções tá ó
Então luí eu preciso mexer não precisa
mexer pessoal mas se você quiser fazer
faz tá ó promovo web server 2 p Traffic
2 p update esse comandinho aqui pessoal
ele vai rodar para você o update para
que seja baixado ali o seu stack já com
a versão
E 2.11 se eu rodar esse comandinho aqui
ó head Traffic vocês vão ver que ele tá
rodando ali a latest ó então eu tô
usando a imagem latest até ontem a
imagem latest era dois era versão 2.11 a
partir de hoje a imagem l é a 3.0 então
assim que você rodar esse comandinho
promova Web
Server Traffic
update ele vai se comunicar com o nosso
servidor eu vou baixar para você aqui
atualizar o seu o seu contêiner aqui ou
seu serviço do Traffic com a versão 2.11
Então vamos olhar aqui ó lá versão 2.11
tá então dessa maneira aqui você vai
continuar rodando na versão 2.11 assim
que sair um update assim que sair a
versão 3.1 e ao longo dos próximos do
meses que eu juntar aqui vou montar um
grupo de estudos aqui pra gente poder
fazer esse teste aí vamos testar com as
aplicações são várias aplicações pessoal
são vários tipos de teste que tem que
ser feito ali aí sim luí batemos o
martelo pessoal ó aqui no grupinho de
estudo e você que é aluno pode
participar Tá eu vou criar um canal ali
no discord só para quem é alun chado do
Traffic 3 pra gente poder fazer nosso
nossos estudos ali tá pessoal batemos
martelo agora sim tá tudo arrumadinho
testamos funcionou Tá certo de demos um
tempo para ele poder sair correções aí
aí eu vou vir no instalador e eu vou
colocar a versão TRS para vocês Tá mas
por enquanto eu tenho que ter essa
responsabilidade de trazer para vocês o
que é estável tá gente nem sempre o novo
é o que é estável O que é estável Hoje é
a versão 2.11 do Traffic tá uma vez que
você rodou Esse comando Esse comando
promov Web Server Traffic update o nosso
sistem já vai fazer para você todo o
download ali tá então já vai fazer o
download já vai atualizar o serviço para
você tá então vai ficar tudo certinho
para você ali rodando tá pessoal então
dessa maneira aqui vocês vão ter um
Traffic estável rodando Luiz eu fiz pelo
curs de docker você tem o vs code eu
mostrei para vocês como é que vocês
instalam o vs code é só você editar o
seu stack e fazer atualização Acabei de
subir também lá no nosso curso de de
docker o o stack já com o Traffic 2.1 11
para você também ter caso você tenha
feito o seu servidor pelo curs de docker
Luiz quero usar o três pessoal vamos
aguardar o momento certo no nosso
treinamento que daí eu vou falar para
vocês sobre a migração tudo Tá tudo que
mudou eu vou começar a minha rotina de
testes então de novo Pessoal vocês estão
aqui para fazer a mentoria vocês estão
aqui para que eu possa guiar vocês por
um caminho melhor para que vocês não ten
que ficar se preocupando e descobrindo
as coisas eu tô aqui para ajudar vocês e
nesse momento a ajuda que tô dando para
vocês é fiquem no dois pon 11 tá não
atualizem agora vamos segurar a
ansiedade pelo menos até saí pessoal a a
turma do Trafic é rápida vamos esperar
pelo menos um ou dois updates vai luí
não quero esperar até o 3.1 beleza mas
espera até os as primeiras correções tá
pessoal acho que é muito importante
vocês também terem essa noção vocês
também terem ess essas novidades ninguém
aqui precisa correr não tem nenhum
recurso vocês viram os recursos aqui não
tem nenhum recurso aqui na versão apesar
de ter bastante recurso legal que o nhn
precisa que o shatu precisa que o Bas
Roll precisa nenhum pessoal então não
tem porque agora você você mudar aqui
tem nenhum bug na versão dois que impede
você de usar a versão dois então vamos
aprender pessoal é um ótimo momento pra
gente poder bater esse papo de ó vamos
usar o estável vamos usar o estável
vamos deixar ele ficar ele sair um
pouquinho ele amadurecer um pouquinho
mais ali no mundo real e aí ó a gente
vem e atualiza ele tá então eu vou
seguir essa regra tá pessoal espero que
vocês entendam que é a minha
responsabilidade é essa de trazer a real
para vocês mesmo sabendo que às vezes
não é o que vocês querem Tá mas eu tenho
essa essa responsabilidade Até porque eu
sofri na pele a mudança do um pro dois
Eu lembro que mesmo mudando mesmo
migrando houveram alguns problemas ali
que atrapalhou bastante a minha empresa
naquela época e eu tenho esse
compromisso com vocês de trazer para
vocês Compartilhar esse conhecimento tá
gente até mesmo essas questões de de
problema de erro eu tenho compartilhar
com vocês Tá pessoal agradecer vocês por
participar aqui desse vídeo qual queer
dúvida pessoal você aí que é aluno
acessa aqui ó promovo app.com bar
discord vai ter um canal lá Traffic para
você que é aluno ali eu vou te eu vou
centralizar todo o nosso bate-papo sobre
o Traffic ali para tirar todas as
dúvidas do Traffic ali Tá eu já tô
acompanhando ele ali há um bom tempo vai
ter o Traffic 3 no nosso curso só vamos
aguardar o momento certo tá pessoal o
curso começa já na semana que vem tá
então já na semana que vem vocês vão
poder me acompanhar ali para acompanhar
o andamento das coisas tá para eu
publicar esse curso no final do mês e
vocês vão adorar esse curso tenho
certeza pessoal que ó se eu já ten aluno
que já mudou de vida aluno que tá
vendendo bem aluno que dorme tranquilo
porque a infra tá boa nesse curso eu
caprichei mais ainda tá eu posso até
dizer que já é já consegui chegar
pessoal já é o quarto curso de docker né
o quarta turma de docker eu consegui
chegar ali no no curso definitivo de
docker swarm tá pessoal é um trabalho
longo que eu tenho é o curso CS que eu
mais gosto de fazer né o curso que eu
mais gosto de montar e olha esse tá
Fantástico Então você aí que ainda tá
batendo cabeça luí batendo cabeça ainda
cara eu ainda tô ralando aqui me meus
aplicativos não não instalam Ainda sou
dependente de instalador de terceiro não
tenho suporte não tem garantia não tem
nada pessoal é hora de você ter controle
da sua infra a sua empresa depende disso
o seu cliente depende disso Depois leva
processo não sabe por quê Porque Caiu
porque perdeu porque invadiram é assim
pessoal o mercado ele não tem dó não não
tem coração tá então acessa aí
promove.com se inscreve faz lá nosso
curso de Doc tem certeza que vai mudar a
sua vida um abraço pessoal até o próximo
vídeo