# 🔐 CR<PERSON><PERSON><PERSON><PERSON>IS COMPLETAS - N8N PRODUCTION ENVIRONMENT

## 🎯 ACESSOS PRINCIPAIS

### N8N - Automação de Workflows
- **URL:** http://localhost:5678
- **Usuário:** admin
- **Senha:** admin123
- **Descrição:** Interface principal para criar e gerenciar workflows

### N8N - CREDENCIAIS REAIS DO USUÁRIO
- **Email:** <EMAIL>
- **Nome:** MontSam
- **Sobrenome:** IA
- **Senha:** R5w2h4m9!s
- **License Key:** 90574678-0463-4101-8994-e84c643368a5
- **API Key:** ey3hGcC1Q17iUz1lMiIsInR5cCI6IkpXVCJ9.eyJ2aWl0OIIyT1I2ZjgGDSYSRMLTR1ZWMlOTk1OC1qOVv+rjF1Mj8zOTI1CJpcSMtOIJuOWd1LCJhdWdOIJhdWJsaWtYXBpIiwiaWFOIjoxNzM0OzMGMSMTQrQ.vOOqixXhKJXwCIv6bGukYZ-aBzEuU_xNj3SuM-KuaM

### Grafana - Dashboard de Monitoramento
- **URL:** http://localhost:3000
- **Usuário:** admin
- **Senha:** grafana123
- **Descrição:** Dashboards e métricas de performance

### PgAdmin - Administração PostgreSQL
- **URL:** http://localhost:5050
- **Email:** <EMAIL>
- **Senha:** pgadmin123
- **Master Key:** R5w2h4m9!s
- **Descrição:** Interface web para gerenciar banco PostgreSQL

### Configuração Servidor PostgreSQL no PgAdmin
- **Server Name:** N8N-Production
- **Host:** postgres
- **Port:** 5432
- **Database:** n8n
- **Username:** n8n_user
- **Password:** postgres123

## 🔧 FERRAMENTAS DE MONITORAMENTO

### Prometheus - Coleta de Métricas
- **URL:** http://localhost:9090
- **Acesso:** Direto (sem autenticação)
- **Descrição:** Sistema de monitoramento e alertas

### RedisInsight - Administração Redis
- **URL:** http://localhost:8001
- **Acesso:** Direto (sem autenticação)
- **Descrição:** Interface para visualizar dados Redis

### Bull Board - Monitoramento de Filas
- **URL:** http://localhost:3002
- **Acesso:** Direto (sem autenticação)
- **Descrição:** Dashboard para monitorar filas de jobs

## 💾 CREDENCIAIS DE BANCO DE DADOS

### PostgreSQL
- **Host:** localhost
- **Porta:** 5432
- **Database:** n8n
- **Usuário:** n8n_user
- **Senha:** postgres123

### Redis
- **Host:** localhost
- **Porta:** 6379
- **Senha:** Não configurada

## 🔑 CONFIGURAÇÕES INTERNAS

### N8N Encryption Key
```
YourEncryptionKeyHere123456789012345678901234567890
```

### Timezone
```
America/Sao_Paulo
```

## � COMMUNITY NODES CONFIGURADOS

### Nodes Habilitados
- **n8n-nodes-mcp** - Model Context Protocol integration
- **n8n-nodes-evolution-api** - Evolution API WhatsApp integration

### Como Instalar Novos Community Nodes
1. Acesse N8N: http://localhost:5678
2. Vá em Settings > Community Nodes
3. Digite o nome do pacote (ex: n8n-nodes-telegram)
4. Clique em Install

### Pacotes Recomendados
- `n8n-nodes-telegram` - Telegram Bot
- `n8n-nodes-discord` - Discord integration
- `n8n-nodes-google-sheets` - Google Sheets avançado
- `n8n-nodes-openai` - OpenAI GPT integration

## 📁 ACESSO A ARQUIVOS LOCAIS

### Diretórios Mapeados
- **Documentos:** `/host-documents` (somente leitura)
- **Downloads:** `/host-downloads` (leitura/escrita)
- **Temp:** `/host-temp` (leitura/escrita)
- **Arquivos N8N:** `/files` (leitura/escrita)

### Como Usar nos Workflows
1. Use o node "Read/Write Files"
2. Especifique o caminho: `/host-downloads/arquivo.txt`
3. O N8N terá acesso aos seus arquivos locais

## 🌐 CONFIGURAÇÃO DE TÚNEL

### Ngrok (Recomendado)
1. Instale ngrok: https://ngrok.com/download
2. Configure seu token: `ngrok authtoken SEU_TOKEN`
3. Execute: `setup-tunnel.bat`
4. Use a URL gerada nos webhooks

### Alternativas
- **Cloudflare Tunnel** (gratuito)
- **LocalTunnel** (simples)
- **Serveo** (sem cadastro)

## �📋 COMANDOS ÚTEIS

### Iniciar Ambiente
```bash
start.bat
# ou
docker-compose up -d
```

### Parar Ambiente
```bash
stop.bat
# ou
docker-compose down
```

### Ver Logs
```bash
docker-compose logs -f [serviço]
# Exemplos:
docker-compose logs -f n8n
docker-compose logs -f postgres
docker-compose logs -f grafana
```

### Status dos Serviços
```bash
docker-compose ps
```

### Backup do Banco
```bash
docker-compose exec postgres pg_dump -U n8n_user n8n > backup.sql
```

## � **EVOLUTION API - WHATSAPP INTEGRATION**

### **🔗 Acesso Web:**
- **URL:** http://localhost:8001
- **Manager:** Evolution Manager Interface
- **API Key:** evolution_api_key_123

### **🔧 Configuração:**
- **Versão:** v2.2.0 (atendai/evolution-api)
- **PostgreSQL Dedicado:** postgres-evolution
- **Redis Dedicado:** redis-evolution
- **Porta:** 8001

### **📊 Banco de Dados Dedicado:**
- **Host:** postgres-evolution
- **Database:** evolution
- **User:** evolution_user
- **Password:** evolution123
- **Port:** 5432

### **🔄 Cache Dedicado:**
- **Host:** redis-evolution
- **Port:** 6379
- **Database:** 0

### **📝 Endpoints Principais:**
- **Manager:** http://localhost:8001
- **Health Check:** http://localhost:8001/manager/health
- **API Base:** http://localhost:8001/api
- **Webhook:** Configurável via interface

### **🚀 Como Usar:**
1. Acesse http://localhost:8001
2. Configure uma nova instância WhatsApp
3. Escaneie o QR Code com seu WhatsApp
4. Use a API para enviar/receber mensagens
5. Integre com N8N para automações

---

## � **SISTEMA DE BACKUP COMPLETO**

### **🔧 Scripts de Backup:**
- **Backup Completo:** `./scripts/backup-n8n-complete.sh`
- **Restore Completo:** `./scripts/restore-n8n-complete.sh nome_backup`
- **Configurar Automação:** `./scripts/setup-backup-automation.sh`
- **Gerenciar Backups:** `./scripts/manage-backups.sh`

### **📁 Diretório de Backups:**
- **Local:** `./backups/`
- **Formato:** `n8n_backup_YYYYMMDD_HHMMSS.tar.gz`
- **Retenção:** 30 dias (configurável)

### **🔄 Conteúdo do Backup:**
- ✅ **PostgreSQL** (workflows, credenciais, execuções)
- ✅ **Redis** (cache, filas, sessões)
- ✅ **Volumes N8N** (arquivos, configurações)
- ✅ **Configurações** (docker-compose, .env)
- ✅ **Metadados** (versões, sistema)

### **⏰ Backup Automático:**
- **Agendamento:** Configurável via cron
- **Opções:** Diário, 6h, Semanal, Personalizado
- **Logs:** `./backups/backup.log`
- **Limpeza:** Automática de backups antigos

### **🚀 Como Usar:**
```bash
# Backup manual agora
./scripts/backup-n8n-complete.sh

# Configurar backup automático
./scripts/setup-backup-automation.sh

# Gerenciar backups existentes
./scripts/manage-backups.sh

# Restaurar backup específico
./scripts/restore-n8n-complete.sh nome_do_backup
```

### **📊 Verificação de Integridade:**
- **Health Check:** `./scripts/health-check-complete.sh`
- **Verificar Backup:** Opção no menu de gerenciamento
- **Logs de Backup:** Monitoramento automático
- **Alertas:** Configuráveis para falhas

---

## ��🚨 IMPORTANTE - SEGURANÇA

⚠️ **ESTAS SÃO CREDENCIAIS DE DESENVOLVIMENTO!**

Para produção real:
1. Altere todas as senhas
2. Configure HTTPS
3. Restrinja acesso às portas
4. Configure firewall
5. Use senhas complexas
6. Configure backup automático

## 📞 SUPORTE

Se algo não funcionar:
1. Verifique se Docker Desktop está rodando
2. Execute: `docker-compose logs`
3. Reinicie: `docker-compose restart [serviço]`
4. Recrie: `docker-compose down && docker-compose up -d`

---
**Ambiente criado e configurado com sucesso! 🎉**
