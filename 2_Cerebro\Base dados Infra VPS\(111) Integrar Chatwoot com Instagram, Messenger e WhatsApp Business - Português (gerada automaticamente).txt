﻿E aí pessoal beleza Luiz aqui nesse
vídeo eu quero bater um papo com vocês
sobre uma ferramenta chamada notifica me
Hub que vai tirar um peso enorme das
costas de vocês de facilitar a
integração do chat w do nhn com as
principais apis de de conversação Como
por exemplo o Instagram o Messenger o
WhatsApp oficial e diversas outras
ferramentas que você pode colocar no seu
chat no seu nhn né e PR me ajudar nessa
tarefa aqui eu tô com o Lucas o Lucas
que é parceiro também aqui da promov web
O Lucas vai gravar também conteúdo
comigo aqui do makers né O Lucas também
trabalha como agência consultor E aí
Lucas beleza como é que tá o carnaval aí
tá tranquilo aqui né Estamos aqui no
setup carnaval Carnaval para quem tá com
a vida ganha né Lucas tem que trabalhar
cara mesmo É isso
aí pessoal basicamente é assim Lucas o
<PERSON> acho que pro Lucas também é
novidade que a gente vai apresentar aqui
né mas basicamente os seus clientes eles
querem adicionar canais né Lucas acho
que a maior dificuldade hoje da galera é
lidar com isso porque a Evolution ela
resolveu muito bem pra gente um problema
ela conseguiu chegar lá e resolver a
questão do WhatsApp não oficial s n
então eu consigo ter ali facinho e
facinho mesmo instala Evolution ou usa a
Evolution Cloud né e esan o CR code Tá
funcionando então a Evolution deu um ela
deu um passo muito grande e permitiu que
a galera andasse muito também pra frente
né então eu acho que hoje Lucas é
assunto pacificado a relação de apis não
oficiais o que não é o caso das apis
oficiais tanto do WhatsApp quanto do
Instagram e da meta e lá do do Facebook
Messenger né ali já é uma outra pegada
ali já é um um outro problema né Lucas
sim então eu acho que existe espaço
Lucas existe bastante espaço pra gente
poder falar de ferramenta assim né de
ferramentas que é o caso aqui do
notifica me Hub que Vai facilitar vai
fazer esse meio de campo entre a minha
conexão com a com a meta seja no
WhatsApp no na na WhatsApp Cloud ou no
Messenger de uma maneira muito mais
simples assim como é com a Evolution eu
achei legal Lucas porque se eu eu tava
olhando aqui né montando aqui o vídeo eu
fiquei falei cara é é é mais ou menos
isso é como se fosse a Evolution aí dos
das apis oficiais né porque ela traz
para mim ela ela tira toda aquela
complexidade da api oficial e joga para
mim aqui de uma maneira muito mais
simples né E também tem aquela questão
Lucas os clientes eles querem o tempo
todo cara canal você você que é agência
você sabe você chega lá e fala assim ó
vamos pôr no WhatsApp Vamos mas hoje em
dia para pensar né Lucas hoje em dia
quem atende o público geral o público
final atende no Instagram e no no
Messenger também não fica restrita a
WhatsApp né e o WhatsApp é bom mas ele
tem os limites Então acho que legal da
gente ter aí uma uma alternativa vamos
dizer assim né porque desafoga um
pouquinho o WhatsApp né Então aquela
campanha que faz no Facebook uma
campanha que faz ali na no no no
Instagram dilui um pouco aquela galera
que ficaria só no WhatsApp né sim eu
acho sensacional né essa capacidade
multi chenel né Eh de você poder atender
em vários canais e não ficar só no
WhatsApp até porque a gente sabe que
existem públicos diferentes público
determinado público tá no WhatsApp um
determinado público tá no Instagram e e
também tem sim para quem não acredita
tem um público grande também no Facebook
né É o o brasileiro gosta do Facebook né
Acho que o povo vamos Vamos chutar Lucas
Vamos chutar Não tenho número disso daí
data venha Total mas eu acho que assim a
nossa geração para cima é mais Facebook
mais velho eu acho a galera mais nova é
mais o Instagram e o WhatsApp Apesar que
os mais velhos também acaba usando
WhatsApp mas vamos pensar em rede social
mesmo assim Acho que as tias do Zap tão
no tão no no Facebook né e os mais novos
estão mais no Instagram Então dependendo
do público que o cliente atende você tem
que ter os dois né você tem que estar no
no WhatsApp no Facebook e no no
Instagram né isso aí Lucas era um
problemão cara que é você gerenciar essa
conexão só na powertic a minha
experiência de 10 anos na powertic nos
últimos quase 4 anos aqui na Pr web é
que a galera Manda muito bem Lucas no no
workflow muito bom no n os cara consegue
bolar os bot sem importa lá um
fluxo as pessoas conseguem fazer a coisa
acontecer só que esbarra nesse problema
que eu venho esbarrando há muitos anos
que é manter a conexão ativa e saudável
não adianta Lucas o meu Nate el Tá ok
Você fez o curso aqui ó deixou o su arme
bonitão banco gerenciado escalável tudo
certinho chega na hora expira o token
chega na hora cai a conexão a meta Muda
alguma coisa lá e você tá cheio de
cliente aqui para poder atender e
vou ter que ler documentação vou ter que
descobrir que Campo que foi que mudou
para eu poder vir aqui e ajustar E aí
Lucas você já tá Deus quiser aí né Com
seus 10 15 20 clientes e aí tem que
mudar em todos então acho que o maior
problema hoje é manter essa essa
integração uniforme que é o que a gente
faz com Evolution né a Evolution Tem Uma
tranquilidade de que o WhatsApp faz as
mudanças dele mas para mim continua
igual a api sim né quando eu acesso
direto a api eu não tenho essa garantia
né então e eu tô mais propenso ali a
algum tipo de falha Então eu acho que o
o o notifica me Hub é legal cara porque
ele vai trazer para mim essa essa
maturidade de manter o negócio estável e
se tiver uma uma mudança é função deles
acompanhar a mudança não é a função
minha acompanhar isso e eu como dono do
negócio e você aí como dono do negócio
também vocês ficam mais tranquilo Nossa
função é desenvolver ela a minha função
é montar o chatbot entrevistar o cliente
atender o cliente é fazer o cara
conseguir ter sucesso ali a agora esses
detalhes menores Lucas Eu
particularmente não quero abraçar e aí
vem outro ponto né Lucas a gente estava
conversando também esses dias né falando
sobre isso a galera abraça muita coisa
pessoal olha só tem eu tem um vídeo ali
que eu falo sobre isso na no nosso no
nosso YouTube eu falo assim ó eu não
consigo trocar Lucas trocar o
NN alternativa NN é muito cara é
inviável eu não tenho uma Open sce e a
altura do então o cara com os acertos e
defeitos dele as qualidades defeitos eu
vou ter que ficar com ele o mesmo
acontece com o docker então eu vou ter
que aprender o docker para poder fazer
minha infra eu vou ter que aprender o
nhn E agora tem alguns níveis de
ferramentas são mais trocáveis a própria
Evolution é uma é trocável eu tenho a un
api que é boa eu tenho a da Connect que
é boa eu tenho agora o Wats 1 lá que é
boa Você vai ter várias ferramentas Open
competem com ela então ali já é um
negócio mais trocável e esse negócio de
conexão aqui cara ele ele mim tá nessa
categoria aí não é o Core do meu negócio
ento não vou gastar tempo cara eu não
vou ter Lucas uma uma um colaborador
aqui para ficar olhando conexão de de
WhatsApp só que ao mesmo tempo eu
preciso dela né Eu preciso da conexão
porque senão eu não converso não recebo
aí aí que entra a ideia de terceirizar
né Lucas a gente fala muito muito sobre
isso sobre infra também né de quando
usar um banco gerenciado quando que eu o
que que eu posso abraçar e deixar do meu
lado o que que eu posso colocar para
alguém poder fazer por mim né e eu acho
cara que esse esquema tema de conexão
com api é um gargalo gigante que quem tá
começando tem não só quem tá começando
Mas quem tá escalando também já tá numa
tá numa subidinha sabe Lucas de cada
cliente cada semana é um ou dois
clientes que aparecem ali aquele chatbot
básico começa vender no chat útil só por
cara sair do WhatsApp web e ir para um
lugar que vai ter uma resposta salva
duas três pessoas analisando Talvez um
botzin ali verificando alguma coisa
montando a base de conhecimento vai
vendendo aos poucos né você não sai
vendendo já negócio completo aos poucos
eu acho que para esses caras aí cara
fica mais interessante ainda usar aqui o
o o notifica me Hub né então é um
problemão que eu acho que tem hoje cara
que a galera tem que lidar né que é ter
esse tempo de acompanhar isso daí e
correr esse risco né Luca é um risco né
cara assim eu não sei você Lu eu tô cada
vez com menos tempo de ficar atrás de
api atrás de de conexão né cara a gente
tá cada vez mais focado em outras coisas
aí né sim eu eu acho legal porque assim
basicamente todo mundo que tá começando
não vou generalizar começa full open
source eh eu pelo menos assim foi comigo
e aí com o tempo você começa a perceber
que você não tem tempo para fazer tudo
então tudo que for viável você
terceirizar né como você falou ali um
banco gerenciado essas conexões com os
aplicativos que demandam eh
conhecimentos técnicos específicos para
cada uma das apis partir do momento que
você usa exatamente monitoramento a
partir do momento que você usa uma uma
API como o notifica você tá
terceirizando essa preocupação das
atualizações das Ferramentas de tudo o
mais e focando no seu Core né no caso se
você vai trabalhar com agente g ou
chatot e deixando e segmentando tudo
isso para outros né porque o tempo ele
ele é
escasso
É ISO e é isso porque eu não tenho todo
esse tempo e mesmo se eu tivesse Luas eu
prefiro arrumar outros clientes do que
eu gastar meu tempo com isso né então p
que vocês vão vão passar por algum
momento né Lucas de início é legal de
início eu entendo o cara querer ficar no
full Open se né Lucas falou ou ou ir ir
atrás de solução eh artesanal vamos
dizer assim né Lucas de eu ir lá e
configurar tudo eu lá fazer tudo eu
entendo às vezes o cara não tem ainda
essa essa expertise né de de entender
que isso aqui cara ele vai pagar um
valorzinho muito bom para esse cara aqui
para ele ele te dar uma abstração
gigante em cima das coisas cois né e e
tira esse gargalo né Lucas que eu acho
que é um grande é um grande gargalo que
a galera tem eu vejo eu vejo na Prom web
no fórum vej os alunos também isso daí
eles precisam integrar mais coisas
porque de novo né pessoal o WhatsApp é
muito chamativo mas só o WhatsApp vira
um problema porque o multicanal tá aí
você pega aqui o notifica me ele é capaz
de integrar e-mail telegram WhatsApp o
oficial o Messenger o Instagram e tem
Mercado Livre tem YouTube cara
comentário do YouTube Eu posso jogar no
meu no meu chat útil comentários do
YouTube né então fica muito mais prático
para mim para trabalhar assim porque é o
chat útil ele é uma é um sistema muito
simples de gerenciar muito amigável para
gerenciar o usuário final se dá muito
bem nele né E você consegue montar nele
ali uma centralzinha de entendimento
cara de respeito né Lucas além de p
automação que eu vou mostrar hoje ainda
aqui só um só um um tira gosto de como
que você pode automatizar o chat w
também né Então pessoal dá um like no
vídeo se inscreve aqui Assiste esse
vídeo comenta que que você achou tá e
Antes de mostrar Lucas deixa eu carregar
aqui a tela do do nosso querido notifica
me
Hub Então a gente tem aqui né o site
deles pessoal aqui tem um ponto
importantíssimo já que eu preciso falar
para vocês tá eles fizeram um cupom pra
gente e eu não não é cupom de afiliado é
só cupom de desconto mesmo tá então aqui
no link aqui na descrição vai ter um
link para vocês para que vocês possam
acessar a plataforma e criar a sua
assinatura com desconto e é um Descontão
Lucas Descontão eu não vou colocar o
desconto aqui no vídeo para não datar o
vídeo porque ele pode dar mais desconto
menos desconto isso aí pode mudar com o
tempo então aqui na descrição você vai
encontrar o link para você entrar se
cadastrar lá com esse cupom da promov
web e lembrar tá gente não é um afiliado
tá eu não faço afiliado com ele todo
desconto tudo que tem ali é de desconto
para vocês e fica um valor baixíssimo
Lucas então aqui Lucas que que é legal
cara deles né o seu caso por exemplo
você você atende não só um cliente você
tem vários clientes Conforme você for
criando conta com eles eles vão te
facilitando então você pode ter uma
conta de revenda com eles né ele ainda
não faz algum tipo de White Label não é
nesse sentido é mais revenda na F de
negócio quanto que você vai pagar para
ele como que ele pode cobrar de você
depois essas integrações então é muito
prático cara assim muito legal mesmo o
sistema dele então aqui pessoal ó aqui
na descrição tem o link para vocês aí do
notifica me Hub E aí você cai aqui Lucas
no painelzinho deles cara eu achei muito
legal é um painel limpo é uma tela só eu
gosto de ver realmente assim cara é não
tem não tem não tem para onde ir não tem
onde errar né não dá nem Lucas para
montar um curso disso um vídeo é o
suficiente não é um curso é um vídeo
olha que o pessoal monta hein Ah sim sim
mas assim é um negócio assim é um vídeo
mesmo explicando para economizar o tempo
da galera é que você falou Eu posso
ficar aqui enrolar os caras aqui né Tem
tem essa também pessoal vou fazer um
exemplo aqui Lucas eu vou conectar meu
Facebook aqui tá Então olha só eu tenho
api oficial do WhatsApp para utilizar
por aqui pessoal o notifica me Hub ele é
parceiro oficial como service prov V da
Meta Então por aqui você consegue criar
uma conta no WhatsApp Business Cloud
para você que não é tão experiente tá
dando seus primeiros passos é um caminho
muito bom muito bom né Lucas porque você
não você não precisa se preocupar com a
complexidade do parar da meta e tem uma
galera que trava nesse ponto Então vem
aqui cara faz por aqui por aqui você
configura tudo põe ali o número que você
quer eles da notifica aqui faz todo o
processo para você pelo painel aqui
então é mais prático eu acho que quem tá
começando Lucas tem que buscar uns
caminhos mais fáceis porque senão não
acaba nunca não sei se você você já
percebeu isso na comunidade mesmo tem
Lucas a galera assim que parece que não
acaba nunca né cara porque fica travado
nessa parte mais técnica né sim Nossa eh
eu eu falo por mim mesmo eu já demorei
alguns dias para fazer configurações lá
do appm até entender tudo e você sabe
que eh imagina adicionou outro cliente
se você não tiver esse processo bem
mapeado passo a passo você vai se
enrolar de novo e você tendo a uma
plataforma como a notifica me Hub para
você dar alguns cliques e já tá ok já
poder receber requisições Já poder
responder requisições isso daí é uma
monor roda e aqui né Lucas por exemplo
né pessoal eu V ter o WhatsApp oficial
Facebook Instagram telegram SMS e-mail
Mercado Livre e o YouTube em breve eles
vão colocar mais integrações o Google
Business eu sou louco também Luc eu
sempre Recebo mensagem cara e eu vejo
depois de três meses essa mensagem aqui
porque não sei onde ela vai cara essa
mensagem que na própria pesquisa Então
imagina Car uma lanchonete um carrinho
de lanche algum lugar ali que precisa
que tá tá no Google Business né no
Google local Business cara seria muito
legal ter isso daí né LinkedIn também é
uma coisa que eu queria muito ter então
tem tem as integrações aqui pessoal ó
bem bem facinho e elas funcionam iguais
qualquer um que você for conectar Lucas
vai ser o mesmo processo eu vou conectar
aqui o Facebook pessoal não vai abrir
aess telinha para vocês que eu tô
gravando uma AB do Chrome Então
Infelizmente o o stream ar aqui ele não
pega a tela né Lucas mas apareceu uma
talinha normal para mim do Facebook de
de conexão eu vou conectar aqui eu vou
autorizar ele a acessar a página da
promov web é bom você olhar tudo você
olhar todo o passo a passo não faltou
nenhuma permissão Zinha ali para você
poder receber as
mensagens apertei três botões de ok ele
conectou tá conectado o meu o meu
WhatsApp tá gente então é legal porque
eu consigo Lucas fazer um sistema aqui
agora de jogar isso daqui ou numa NN e é
legal que consiga montar algum tipo de
automação ou jogar no chat ut e ele tem
integração com os dois se você vem aqui
ó em editar você vai vai poder colocar
aqui o Web Hook né ó Então essas
notificações inclusive deixa que você pô
dois que é muito comum né você querer
integrar enviar para algum lugar depois
vai para outro né então já quebrou o
galho aqui já né de eu poder ter mais de
um web Hook então você que não vai usar
o chatot você pode vir aqui ó em cria a
sua integração encaminha para onde você
quiser aqui a requisição para quem vai
usar o chat ú eu vou ter uma telinha
específica dele ó configurar chat ú e de
novo configuração pessoal pessoal nos
moldes da Evolution tem aqui Um shat de
teste esse aqui Lucas é o chat que eu
fiz no curso recente Inclusive eu
publiquei lá as aulas para vocês né de
escalabilidade do chat útil 10 aulas
sobre Como que você pode montar um chat
útil mais mais escalável né Então olha
só a URL perguntar o RL do chat ú no meu
caso aqui é o chat api então lá no curso
eu ensinei vocês a a ter um editor a ter
o side Kick e também a ter só api que é
mais escalado Lucas vou poder ter um
chat só recebendo várias aquisições e
replicando as aquisições para poder ter
uma alta disponibilidade né a conta
então a conta eu vou vir aqui na
engrenagem account settings a minha
conta é número um eu vou vir aqui vou p
a conta número um token da conta vou no
meu perfil e eu vou copiar aqui o token
da minha conta ó tá aqui copiar o token
e eu vou colar ali pessoal isso aqui é o
mesmo processo que vocês fazem na
Evolution para poder conectar Evolution
aqui no chat útil e esse e o notifica me
Hub ele vai usar a mesma ideia de assim
que eu apertar o confirmar ali ele vai
lá criar para mim a caixa de entrada
então vou clicar em gerar D primeiro
aqui ó gerou um ID de caixa para mim
aqui eu vou clicar em confirmar
ó lá o que que ele fez ô Lucas ele foi
lá no chat ut e criou para mim aqui ó
uma caixa de entrada mesmo processo da
Evolution se eu for observar essa caixa
Lucas eu vou vir aqui na caixa vou vir
aqui em Box configurações ele já cria o
i Hook de volta lá para para mim ó então
ele ele vai se comunicar com o chat ut
ele já criou a URL que vai devolver para
mim lá pro pro notificam então a
integração de chegar no chat ut e sair
do chat ut já tá ok né inclusive se eu
vier aqui agora na promov web mandar um
oi pessoal já percebi aqui e é bom é bom
até falar Lucas eu não sei se é um bug
ou se é um comportamento normal mas às
vezes a primeira mensagem assim que eu
configuro a caixa ela não chega da
segunda deantes nunca falhou mas a
primeira às vezes não chega depois eu V
com o Stefan isso daqui se é alguma
coisa do meu lado aqui que que é mas à
vezes eu mando um oi esse Oi ele não ele
não chega aí eu mando de novo Oi Aí ele
chega só a primeira vez só quando você
configura a caixa de entrada talvez
algum Cash né Lucas Pode ser algum Cash
até mesmo no chat útil aqui né porque
que que é legal ô Lucas aqui ó mandei o
oi certo mandei um oi lá e pingou o oi
no meu chat útil aqui ó tá aqui o oi que
eu mandei ó Então tá chegando certinho
né só porque eu falei que às vezes não
dá e agora deu mas eu posso vir aqui ó
eu posso acompanhar pelo painel tem essa
telinha de logs aqui ó atividade recente
ó legal aqui o que eu mandei ó se eu
fosse receber um web Hook eu receberia
Lucas esse web Hook aqui do notifica e
não o do a do do Facebook isso aqui é
muito legal pessoal porque padroniza
para você o payload o nosso curso Lucas
ele começa com normalização de dados né
um dos primeiros modulos do curso eu eu
eu pegar essa loucura que vem da
internet e eu deixar numa maneira que eu
consigo entender né e que fica mais
fácil para trabalhar no fluxo é é isso
aqui cara porque Olha só pessoal vai vir
notificação da Meta do Instagram do
Facebook vai vir do Mercado Livre vai
vir do telegram vai vir de e-mail o
notifica me vai criar um payload padrão
para mim e vai encaminhar pro meu nhn
caso eu configure um web Hook ali sempre
no mesmo padrão Lucas Então olha só vem
aqui ó tipo mensagem ó que legal cara o
ID da mensagem quando que essa mensagem
foi enviada ou recebida ó lá o canal
canal um canal Facebook é uma mensagem
de entrada tá vendo Então eu tenho de
entrada ou seja a pessoa falou comigo e
eu tenho a de saída que é quando eu
respondo pra pessoa fá Qual é a mensagem
em si o ID de quem mandou né como é um
Facebook é um ID da conta da pessoa se
fosse o WhatsApp seria o telefone dela e
por aí vai né ó ó lá ó o canal né que é
um canal Facebook o nome da pessoa então
até para você pegar o nome que é legal ó
nome primeiro nome segundo nome é a
pessoa ó lá ela mandou um texto oi tá
vendo cara é isso aqui Lucas o que você
receber de Instagram do que for você vai
receber isso daqui para você que vai
montar um Lake é legal você que quer
montar algum tipo de histórico para
depois poder consultar e fazer sumário é
legal né mas no nosso caso aqui Lucas eu
não recebi esse web Hook né Eu
encaminhei lá pro pro chat ut então o
chat útil recebeu webook para mim
processou a mensagem e ela tá aqui na
tela né então eu poderia responder
normalmente aqui agora as mensagens pelo
chat u e elas vão sem encaminhadas lá
pro nosso pro nosso Facebook né então eu
vim aqui ó respondi um tudo bem Ó lá Ó
recebi aqui ó ele mandou mandei um oi e
recebi um tudo bem da PR web tá vendo ó
Então pessoal funciona 100% só fica
atento na hora que você configura a
caixa o primeiro oi que você manda É
isso aí Lucas que às vezes eh eu não sei
se é do lado do chat ú aqui mas dá uma
engasgadas tá gente é normal isso daí
quem sabe qu tempo eles não arrumam né
mas uma vez que criou a caixa não dá
mais roda normal ô Lucas tem alguma
pergunta aqui Lucas desse processo aqui
de criar caixa de vida de volta tá
tranquilo não o que o que eu achei bem
legal assim até para destacar é a
capacidade de unificar o payload né O
Jeison de todos os tipos de entrada né
independente de eu ter Facebook
Instagram até Futuramente eu tenho
certeza que eles vão botar aí tiktok é o
próprio YouTube Mercado Livre imagina
quando você e padroniza essa esse
payload você consegue trabalhar de
maneira mais fácil Chanel né você tem
que fazer ou seja até ontem vamos dizer
assim né De certa forma a maioria fazia
o quê fazia um fluxo para cada tipo de
entrada porque o payload era diferente e
agora só sim deixava um fluxo com montão
de if E aí ficava aquele monstro né sim
não agora agora tá simplificando muito
mais né enfim ô Lucas eu vou conectar o
Instagram aqui ó manda um oi para mim só
pra gente poder ver como é que chega do
Instagram deixa eu conectar aqui deixa
só conectar aqui
primeiro pessoal mesma coisinha tá ó Dei
dois cliques conectei o meu Instagram
aqui Prontinho Deixa eu só configurar
aqui o chat ut mesmo esquema né Lucas só
então vou pegar o URL do meu chat ut
Toda vez que você for adicionar uma ca
de entrada é esse processo pessoal e é
muito fácil tá a minha conta é um deixa
eu pegar aqui o meu
token você faz em menos de um minuto
Lucas você configura uma conta mesmo
esquema da Evolution eu gostei pessoal e
de novo tá gente eu não tenho nenhum
tipo de relação comercial com eles né
Nenhum eles me deram um cupom de
desconto já fiquei feliz porque daí como
eu ia fazer vídeo ficou Ah vou fazer o
vídeo então você não dá um descontinho
aí eu sempre peço sabe Lucas vai que
cola né
colou desconto entendeu porque isso aqui
pessoal muda muda muito para vocês
porque permite criar uma automação que
eu vi que a galera quer muito criar que
é o Instagram e o Facebook porque de
novo né Lucas o WhatsApp tá resolvido
cara a Evolution resolveu pra gente o
WhatsApp sim né não preciso de outra
pelo menos por enquanto não preciso de
outra eu tenho a oficial que eu posso
diretamente no chat ú colocar ela beleza
eu tenho a não oficial com a a Evolution
e agora eu tenho aqui ó todas essas
outras que eu vou poder colocar e seguir
a mesma regra né de de de uso né pessoal
dá uma olhadinha aqui na descrição vocês
vão ver o valor que é vocês vão então
vocês vão entender por que eu fiquei
animado de colocar isso daqui tá não é o
valor que tá no site deles o valor o
valor lá é cliente final o valor o valor
aqui do link é um valor especial para
quem assistiu ví promov web e diversos
outros criadores de conteúdo também
estão também estão utilizando porque
realmente pessoal é uma coisa que vira o
jogo Lucas isso aqui vira o jogo cara
muito legal mesmo deixa eu olhar aqui o
meu chat ú Manda um oi para mim Lucas
por favor ó Então agora eu tenho aqui ó
notifica me esse aqui é o meu Instagram
e eu tem aqui o meu o meu ó lá ó Lucas
liima ppro Aí sim hein cara ha lá recebi
aqui ó deixa eu responder o famoso
Opa respondi o famoso Opa chegou aqui e
aqui ó se eu olhar no log então Lucas se
eu vem aqui no log da Ó lá se eu vem
aqui no log da dele consigo acompanhar a
mensagem de saída o out tá vendo ó eu
respondi opa então out no Instagram e se
eu vier aqui baixo ó vai ter o Win né ó
vai ter o send ali ó que o Lucas Lima
mandou para mim então fica muito mais
fácil Lucas de não conseguia se eu fosse
receber no web Hook eu tava feliz já
cara se eu fosse receber por Web Hook
mas vindo direto pro pro chat vira vira
mais o jogo ainda né isso aqui realmente
cara é um negócio de de tirar o chapéu
só que aí Lucas não para aí quero fazer
um bônus pro pessoal aqui Um item a mais
pessoal aqui na descrição logo abaixo do
link ali do do desconto para vocês eu
deixei um link de uma página para que
vocês possam baixar esse meu poque aqui
né inclusive o po look é um terma que eu
vou usar muito agora a gente vai usar
muito no e makers também isso daqui né
até é bom é bom aproveitar esse vídeo e
explicar pra galera que é assim pessoal
o Pock é uma prova de conceito então às
vezes Lucas eu vou eu vou mostrar pro
Lucas alguma coisa aqui eu vim aqui e
mostrei ó Lucas dá para conectar aqui o
Instagram Facebook dois três cliques
você dá tá lá no seu chat ult Tá tudo
funcionando maravilhosamente bem tá tá
lindo aqui aí o Lucas fala assim beleza
luí mas tem como colocar uma i aí vou
falar assim ó tem só que é ruim Lucas
porque nessa conversa nossa tá muito na
imaginação é uma conversa na na
engenharia de sof cara a gente trabalha
muito disso de fazer a pessoa enxergar
não fazer ela imaginar porque eu
imaginar cara a pessoa viaja ou ela
viaja ou ela não consegue ter uma
criatividade suficiente de concretizar
aquilo na cabeça dela então eu prefiro
vir e mostrar pra pessoa não vou falar
não vou fazer ela pensar eu vou mostrar
vou fazer ela ver o que eu tô eu tô
informando né então eu vim aqui ó eu
mostrei para vocês ó gente conectei
vocês viram que com dois trê três
cliques o meu chat útil tá recebendo
agora Instagram e Facebook por um valor
que nem ninguém ninguém chega perto
ninguém né então isso para mim já é já é
uma coisa legal e eu falei pô vou gravar
um vídeo pro pessoal porque eu já achei
interessante isso daqui sabe Luc é uma
coisa que muda vocês já tem Bot prontos
você já tem seus clientes agora vim aqui
ó e fazer um upsell né fazer mais uma
venda aí de colocar mais canais para ele
só que daí Luc o que acontece o próprio
chatot ele tem uma um esquema nele aqui
ó nas integrações de poder enviar web
Hook e o que que é esse meu poque aqui ó
eu poder receber um web Hook do chat ut
passar essa pergunta que a pessoa fez
essa conversa pela ia armazenar a
memória dessa nossa conversa e o e o a
ia poder vir aqui ó ir lá no chat u e
poder responder só que daí entra Lucas a
facilidade disso então Olha só pessoal
eu não estou indo lá na na notifica me
Hub e e pegando o Web Hook deles não tô
por que que eu não tô fazendo isso Lucas
porque eu ainda teria que criar um Para
eles um para oficial um para um no api e
um pro para
Evolution que que você falou cada cada
um seria um um fluxo diferente isso e
cada um é um payload diferente então
eu eu tô fazendo o seguinte pessoal a
Evolution sabe conversar com chat ut a
Uno api conversa com chat ut a notifica
conversa com chatu a api oficial
conversa com chat ut então eu tô
delegando para eles conversarem com
chatot o chatot vai pegar essas
mensagens salvar no banco e eu vou usar
o Web Hook do chatot E aí Lucas eu
melhoro mais o processo ainda cara
porque independente de que canal que
veio para mim de novo vai vir sempre o
mesmo payload o mesmo payload Sim né
então aqui n se web Hook vai cair sempre
o mesmo tipo de mensagem essa aqui é a
mágica né essa então eu posso vir aqui
pessoal copiar por exemplo esse payload
aqui né e colar no chat eu vou vir aqui
no web Hook novo web Hook eu colo aqui a
a a URL né do da minha po aqui ó colei
aqui minha a ur da minha poc eu vou usar
aqui ó mensagem criada então qualquer
mensagem criada no chat ut em qualquer
caixa de entrada vai chamar aqui esse
web Hook Então vou criar ele aqui se
você puder Lucas mandar um oi de novo
para
mim eu vou aproveitar vou responder aqui
também ó vou mandar um bom
dia que que acontece Lucas nesse caso
aqui né ó vamos vamos olhar aqui a a as
execuções vai dar algum erro rodou
certinho cara pelo menos Diz ele que
rodou certinho aqui ó ó lá pessoal eu
mandei um bom dia aqui do meu Facebook
certo Lucas Ó lá sim e pode ver ó que já
veio um bom dia como posso ajudá-lo hoje
já viio automático quem respondeu isso
aí foi aá se eu for olhar aqui pessoal
no meu chat ut eu vou ter aqui as
conversas né ó ó lá o Lucas mandou para
mim um um oi de novo e agora quem
respondeu foi aá eu mandei aquela aquele
Bom dia quem respondeu foi aá então isso
aqui pessoal essa prova de conceito esse
poque é só para eu poder apresentar para
um cliente que o negócio funciona e
vocês viram que eu só fiz um web Hook
Lucas eu vim aqui no web Hook eu apontei
esse web Hook lá para aquele pro meu
chat pro meu nhn eu estou numa conta
certo a conta agência cinca todas as
caixas de entrada dessa conta agência C
qualquer mensagem que chegar ali vai
pingar aqui para mim vai vai chegar aqui
o que que eu faço aqui eu vou filtrar eu
quero tudo pode serer que não Lucas o
que que eu não quero aqui Lucas eu não
quero por exemplo ó mensagens de saída
Então vamos lá pessoal entrada é quando
o Lucas mandou uma mensagem para mim vai
vir web Hook quando eu responder pro
Lucas também vai vir um web Hook que é
isso que eu não quero que é o outgoing
Então eu só vou eu só vou eu vou filtrar
eu só quero mensagens de entrada
mensagens que as pessoas mandam para mim
e eu não quero mensagens que ten uma
pessoa atribuída eu só quero mensagens
que não tem ninguém atribuído para ela
ainda que é o famoso gente B né Lucas eu
vou deixa o bot respondendo se eu chegar
aqui no meu chat ó eu venho aqui no meu
chatot eu olho aqui a mensagem do Lucas
por exemplo vem aqui no Lucas olha aqui
e clico nesse botão ó atribuir para mim
se eu atribuir para mim eu não passo
mais esse
filtro então o bot não vai não vai mais
responder a mensagem para mim o bot só
vai responder a hora que eu for lá e
tirar aquele aquela opção de atribuição
Então tá atribuído o bot não responde
não atribuo o bot responde feito isso
Lucas eu venho aqui o que eu preciso
saber pessoal isso isso aqui Luc é
interessante do chat como uma ferramenta
de integração nesse sentido né se eu vi
aqui por exemplo
ó Eu vou ter esse payload aqui o que que
vem no payload para mim o ID da conta eu
preciso saber o id da conta para poder
chamar de volta eu preciso saber qual
que é a conversa Lucas Poder chamar API
de volta então eu US esses dois aqui na
URL do chat ut ó que é o o account o
conversation tá vendo aqui ó pode ver
que at ele até formou aqui para mim a a
URL né Ó então eu teno o ID da conversa
que é esse ID aqui ó Então essa conversa
com você é a 10 aquela outra minha a
minha mensagem ó ela é a nove tá vendo ó
Então eu tenho que saber que eu tô na
conta um na conversa nove eu faço uma
requisição para pro end Point messages e
eu respondo lá eu pego o que o bot
respondeu aqui é o output do meu do meu
ia e eu mando tá lá cara é só isso Lucas
então Independente de qual caixa mandou
a a a pergunta para cá eu vou conseguir
extrair de que caixa que é e na hora que
eu responder eu respondo para aquela
caixa específica luí eu posso tirar
então Um item ali pode pode vir eu posso
vir no filtro falar filtro eu não quero
a o inbox 3 eu não quero o inbox 9
Beleza entendeu Lucas então vai chegar
tudo aqui eu faço uma barreira do que
que eu quero o que que eu não quero o
que eu quiser ele continua o agente
processa e ó que legal Lucas Eu salvei
na memória do agente o ID da conversa
ó então o que que é legal Lucas esse ID
de essa conversa minha aqui 10 por
exemplo que tô com você ó essa conversa
tá na memória do ag gente se eu vier
aqui resolver e iniciar uma outra
conversa o que que vai acontecer Lucas
ele vai zerar para mim lá a min o meu
histórico também então começamos de novo
do zero
ende po Você pode falar luí eu não quero
isso eu quero que independente vamos
supor que você não vai não vai usar o r
aqui vai pôr um supa base por exemplo
que vai armazenar um histórico
persistente de conversa Luiz Eu não eu
quero que independente da da conversa
aqui no chat u eu quero que qualquer
mensagem com esse contato aqui ó Lucas
Lima pro seja o histórico dele então é
só vir aqui cara e mudar ao invés de eu
pegar aqui do meu do meu node no meu
node set ali por exemplo no meu input
aqui ó esse daqui ó qu eu pegar o
o o ID da conversa eu posso pegar o ID
do contato o Sender ID ali então
independente da onde o Lucas falar
comigo eu mantenho um histórico com ele
isso aí Lucas é o multicanal com memória
multicanal sim né então se você der um
oi para mim no telegram e me der um oi
no no e fizer uma pergunta para mim no
Facebook eu vou manter o contexto da
história porque eu estou falando com o
Lucas independente do canal é claro que
para essa situação Lucas você tem que
fazer uma certa gestão né uma governança
aí dos seus contatos Então tem que vir
no chat útil mesclar os contatos que eu
identifiquei que são as mesmas as mesm a
mesma pessoa em canais diferentes Então
esse aqui é o Lucas no Facebook esse
aqui é o Lucas no telegram esse aqui é o
Lucas no e-mail e eu junto tudo num
Lucas só aí eu consigo Lucas
independente da onde você falar comigo
eu continuo eu continuo a converso com
você né se eu usar o seu ID de contato
como chave de memória Então pessoal essa
poc ela serve para isso Lucas para poder
a gente mostrar às vezes pro cliente ou
até mesmo para você poder defender um
ponto às vezes numa reunião se o nhn
serve ou não porque você precisa então
aqui ó eu consigo defender o quê
defender que qualquer mensagem que chega
na conta do cliente do chat ut eu
consigo pôr o mesmo bote para responder
todas então economizei tempo e dinheiro
aqui concorda Concordo eu consigo
defender que eu consigo filtrar o que eu
quero responder ou não então você tem
uma um humano atendendo eu consigo não o
bot não responder eu consigo defender
isso né eu também consigo colocar um
agente ó e esse é meu agente Lucas nem
promt não tem tá porque ele é um poque
Mas eu posso vir aqui e colocar todo um
contexto para ele um promt legalzinho do
serviço da empresa eh mas eu eu só tô
mostrando que eu consigo ter um agente E
que esse agente tem uma memória daquela
conversa ou daquele contato você define
aí e eu consigo também mostrar que eu
consigo responder ao automaticamente pro
chatu Independente de qual foi a caixa
que mandou mensagem para mim o sistema
não vai falar assim olha eu recebi da
Caixa cim eu respondi para oito não
existe isso né ele vai literalmente na
automação recebeu a mensagem responder
para aquele mesmo inbox que que
perguntou para ele né então eu consigo
provar vários pontos Lucas nessa prova
de conceito de que sim o chat ú pode ser
uma ferramenta centralizadora de
conversa o nhn pode ser a ferramenta que
vai trabalhar muito melhor essa conversa
e vai passar por a gente eu poderia vir
aqui Lu no comecinho cara e transcrever
um áudio o chat ú vai salvar o áudio
para mim no S3 e vai encaminhar para mim
a URL do áudio é só eu abrir ela e
passar para transcrever então poderia
até mostrar pro cliente que até via voz
seja no Instagram no Messenger ou no
WhatsApp eu consigo transcrever os
áudios e passar na I para I poder
entender o que foi que a pessoa eh
conversou ali eh perguntou ali né então
a ideia de Pop pessoal é você não montar
o final eu não esse aqui não é um
produto para vender pro cliente é só
para defender as ideias é prova de
conceito Lucas é só para eu poder faler
assim gente olha Estou provando para
você que esse kit aqui ó notifica me Hub
chatot e nhn é capaz de ajudar você a a
centralizar as conversas imagina Lucas
eu poder pegar um e-mail cara eu posso
pegar um e-mail eu posso criar um bot
esse meu bot aqui eu posso filtrar para
que nesse Bot não venha e-mail certo não
quero e-mail aqui mas eu posso ir no no
criar um outro bot e falar assim ó e
esse vai só vai passar e-mail e ele vai
responder vai vai responder o e-mail
para mim vai rascunhar o e-mail sei lá
vai poder fazer algum tipo de de análise
do email criar um ticket Então você
começa Lucas a criar pequenas provas de
conceito para poder defender as ideias
com os clientes e que na prática você aí
que é técnico Você viu que funciona
então o que para um é uma prova de
conceito paraa outra pro outro já é já é
uma oportunidade de negócio né ó pessoal
esse fluxo Zinho bem simples tá aqui
para dar Note para vocês tá para vocês
poderem baixar Esse link é só você ver
aqui no link da descrição do vídeo esse
item e funciona Lucas simplesmente
funciona cara você pode mandar mensagem
aqui eu posso começar a responder por I
enfim você começa a criar uma série de
automações aqui né desde fazer o chatbot
quanto a gente poder armazenar isso aí
né Tá armazenar no banco do chat ú eu
posso criar uma automação que vai ler o
banco do chat ut pegar as conversas do
dia do banco do chat ut montar um resumo
e alimentar um rag meu com com as
principais perguntas tem tanta coisa eu
posso fazer aqui dá pr gente passar o
dia inteiro aqui eh an analisando né
Lucas mas olha pessoal a janela de
possibilidade que abre de eu poder vir
aqui e pô uma Evolution se eu vier aqui
por Evolution agora a Evolution também
vai vai funcionar o bot com ela se eu
colocar um no API aqui também vai
funcionar com ele então independente
Lucas Da onde tá vindo a informação cara
veio pro chat ut ele padronizou para mim
encaminhou pro nhn e agora eu converso
nhn chat ut e não mais nhn e qualquer
outra integração isso aí cara ó matou a
pé Lucas Eu acho que isso aí é o chat
shot virou um Hub Além de que já era
ótima essa ferramenta acabou virando um
Hub e não só um Hub agora um Hub até
para nós que estamos trabalhando no
background de todas essas operações que
a gente tem feito mente de uma forma que
vai centralizar e facilitar muito né ou
seja um payload Unificado para usar um
fluxo para fazer todos os atendimentos
uma possibilidade de ter uma memória
única multicanal Cara isso aí vai
explodir a cabeça de muita gente porque
hoje você vai ter essa possibilidade de
até o próprio chatot disponível pro
cliente até para ele usar para outros
canais né fora os canais com i com
chatbot até pros canais ali talvez até
internos da própria empresa ou seja você
tá agregando um valor absurdo né pro
cliente não posso pegar essas mensagens
Lucas e salvar no CRM quem usa hubspot
por exemplo quem usa algum outro sistema
que permite que você salve logs de
conversa né o hubspot tem essa api é
muito legal porque a hora que eu abro a
ficha do cliente eu consigo ver até as
conversas que eu tive em outro lugar
então o que eu falei no Instagram com
ele tá salvo lá na na na linha do tempo
dele sim n isso aí vai abrindo o leque
porque o cliente quer ver isso fal Oi
isso sairia do do chat ú pro HS Spot ou
ou pelo NN tanto faz né do NN aqui aqui
eu respondo esse node aqui eu respondo
pro pro chat útil né pro cliente poder
receber o próximo node aqui eu já podia
mandar pro hubspot Ou seja você ia botar
um in um in um in seu e um Wi dele né do
do do cliente né para poder ter a
diferenciação lá né você vai ter aqui os
engagement aqui que eles chamam de
engagement né ó poderia vir aqui e criar
um engagement nele e já era ó lá
sensacional meeting tesque e t também a
API de conversation do hubspot que não
tem no n não tá não tem no node mas tem
na api então isso aí para qualquer CRM
cara qualquer CRM você pode criar uma
nota no contato uma anotação nele você
poderia salvar toda essa conversa ou
melhor né Lucas eu poderia no final do
dia 11:59 meia-noite eu faço um loop no
meu banco fal ass ó quero todo mundo que
falou comigo ah luí hoje foram 50
pessoas falaram com você então vou pegar
o 50 um por um ler todo o histórico de
conversa gerar um resumo igual o pessoal
faz no grupo ali gera um resumo daquilo
ali e salvo no CRM olha no dia tal de de
Março o Lucas esse foi o nosso assunto
com o Lucas Beleza não precisa aliz
salvar um por um acho que é muito beleza
cara faz um resumo Então faz um resumo e
salva o resumo entendeu mas você tem
você você consegue olhar a ficha de um
cliente olhar um um perfil e saber
quanto tempo F que ele fala com você
Quanto tempo F que ele não fala com você
eh horários do dia que ele mais fala
dias do mês que ele mais fala é muito
previsível Lucas tem cliente que muito
muito previsível eu trabalho com CRM
Cara eu fiz meu TCC em cima de CRM então
assim a gente consegue olhar tanta coisa
cara da da da pessoa e e e criar blocos
de contatos que se comportam do mesmo
jeito fica muito mais fácil para você
poder prever então tem gente que te
chama no suporte no começo do mês por
quê Porque é só no começo do mês que ele
usa Talvez o resto do mês aquela pessoa
não olha mais para cara do não olha para
cara do chatot mas todo começo de mês
ele tem que Executar a tarefinha ali né
Então você já começa a observar isso né
esse cara sempre aparece começo de nunca
no meio nem no final outros mais de
manhã do que à tarde né você vai você
vai planejar uma uma manutenção Qual é o
melhor horário né você consegue Através
disso aí saber Ó de acordo com as
conversas que eu vou recebendo aqui
consigo saber que 11 horas da noite
ninguém mais fala comigo então vou
planejar vou vou agendar para esse
horário tudo isso tudo isso você vende
pro cliente né Eu eu tenho o NN E aí
entra Lucas a vantagem e o curso vou
bater muito nisso de você ser bom em
automação antes de ser bom em chatbot
porque o chatbot vai resolver um 1 ter
do problema os outros 2 teros é
automação cara que é o que que que é o
qu é a parte de você receber informação
processar ela senão você não consegue
trabalhar com chatbot se você não fizer
isso eu não consigo trabalhar com
chatbot Lu se não souber transcrever um
áudio você não souber manipular a
memória salvar
uma Oi vai ficar muito limitado e também
a parte aquela parte de você poder uma
vez que você fez isso eu poder criar
mais mais ações em cima desse histórico
porque hoje cara o que mais vale
qualquer empresa é o histórico da
conversa cara ali você vê como que o
cliente tá se sentindo ali você percebe
Quais são os buracos do produto o que
que eles querem o que que o que que gera
dúvida cara tudo isso aí Tem muita área
que você pode vender para uma empresa só
só de histórico de bate-papo com as
pessoas tem muito informação ali que
você pode extrair para ela né a legal
cara Eu Oi o que eu acho legal disso daí
que você tá falando é é que assim tem
tem vou dar o meu caso aqui né Tem muito
cliente que pede auditoria de conversa
então dá para você montar com isso aí
uma auditoria um que nem você falou O
próprio resumo trazer um feedback pro
pro pro empresário dono daquela empresa
falando o seguinte Olha tá estamos com
deficiência em tal área estamos com
desafio em tal área aqui da conversação
e tal dúvida não tá sendo respondida
paraos nossos clientes cara isso é
sensacional é e você vai e isso aí Lucas
é com Automação que você faz é isso que
a galera não entende eles focam muito no
ia ia e faz gente aa é uma parte ali sim
porque o nhn ele é uma ferramenta de
automação com suporte aa né então você
tem que saber a automação né para você
poder depois recuperar esse too de
conversa manipular uma sessão salvar num
banco tem muita coisa para fazer aqui
cara que assim que a ia vai processar e
que você pode utilizar para outras
coisas ou o contrário né as automações
normais que você que você já faz e que
você pode p i no meio ela melhora mais
ainda sim né você falou analisar
sentimento por exemplo de a cada hora
olhar o o histórico de conversa e
verificar se tá tudo bem as pessoas
estão Ok no suporte eh enviando e
recebendo mensagens ou ou é um um um dia
ou uma tarde ou uma manhã que o clima tá
mais tenso tudo isso cara tudo isso
assim a gente tem que é bom a empresa
precisa saber disso né Lucas quem quem
atende cara precisa saber disso daí né
Então pessoal inúmeras possibilidades
agora com a notifica me vai ficar mais
fácil para vocês incluírem na sua no seu
kit de de oferta o Facebook e o
Instagram né porque a Evolution Você já
faz a un AP Você já faz a ap oficial
Quem quiser fazer diretamente faz também
eu acho legal fazer por aqui você que
não é muito experiente porque vai ser
muito mais fácil para você fazer por
aqui e o cliente que pedir para você uma
ap oficial por aqui ó você faz ô Lucas
falando sério cara uma hora se você
pegar uma hora cria no clickup ali ó uma
hora aperta o play lá para para
registrar a hora em uma hora você
configura o chat ú faz um botzin simplão
você se prepara legal para uma reunião
cara com tudo conectado você fala assim
ó ô cliente fazal o seguinte manda
mensagem lá para você ver manda lá uma
dúvida Cara isso aí Lucas ó é fechar
contrato cara você você ganha pessoa de
fazer isso daí né por isso que eu falo
pessoal crie provinhas de conceito deixa
no seu NN porque vir M Você vai precisar
delas elas vão ajudar demais vocês no
dia a dia né Lucas para você poder
provar o conceito poder poder provar pro
cliente que é possível fazer aquilo que
ele quer fazer né E depois você
incrementa depois você começa a
desenvolver realmente né Lucas o que o
que que ele quer ali né Fazer o que que
ele tá pedindo para fazer mas tudo
começa com a prova de conceito né então
faz o faz o download aqui para vocês tá
tá aqui na descrição também tem na
descrição do vídeo o link para vocês
poderem se inscrever que notifica me
ganhar um desconto ali para quem aluna
promov web reforçar tá gente não sou
afiliado dele não tem nenhum tipo de
vinco comercial com ele mas eu realmente
acho que esse cara aqui ele vai até vou
ver se eu consigo marcar uma live com
eles Lucas bater um papo com eles Isso
aqui vai realmente cara ajudar muito
vocês no dia a dia de vocês aí enviando
pro chat útil do chat útil poen fica um
caminho muito mais fácil para poder
processar as informações e trabalhar ali
com a automação de chatbot né Lucas sim
sensacional cara
assim explodiu até a minha cabeça né
porque isso vai poupar muito tempo ali
um dia ou dois a menos de trabalho e com
a entrega que vai gerar muito mais valor
com muito mais possibilidades né porque
por exemplo como é que a gente pausa aa
antes lá do from me né no caso ali da da
Evolution mas hoje agora se você
designar um atendente lá no chatwood
como você mostrou já tá resolvido então
assim eh lá eu tinha que configurar um
tempo lá no RS né e agora não agora o
tempo fica de acordo com o real né se a
partir daquele momento o colaborador
daquela empresa entender que já não
precisa mais a humano falar ele
desabilita aquela atribuição de pessoa e
deixa sem atribuição a partir daquele
momento aí automaticamente vai responder
sensacional até pro operador é mais
fácil de entender isso daí né falar ó se
tá com você a conversa o bot não
responde ele só vai responder as as
conversas não tá com ninguém assim que
alguém assumir e se você não quiser mais
você devolve lá pro pro não atribuído
você tira de você a conversa que aí o
bot volta a responder com ele o bot tem
memória tudo certinho ele vai saber o
que que que tava conversando se a pessoa
começar hoje voltar amanhã ele sabe o
que que tava falando ainda então é mais
é mais prático né Lucas é bem mais
prático né e e só para explodir um pouco
mais a cabeça imagina botar um subnode
na ia de atribuição para ia entender que
tem que atribuir para alguém já vai
jogar automaticamente o negócio olha
olha só como Explode a cabeça né
chamando ferramenta agora ali né por
isso que por isso que eu falei pro
pessoal que o cara ele resolve o
problema eu consigo vir aqui cara e
criar uma ferramentinha de acordo acordo
com a minha conversa no chatbot eu venho
aqui chama uma ferramenta que só vai
atribuir pro Lucas e não pro Luiz essa
conversa sim entendeu não então ou
melhor né Lucas melhor ainda cara eu já
posso pegar esse web Hook já vem para
mim o seu ID de contato eu posso pôr uma
tag por exemplo aqui uma tag no contato
no chat e informar que você é o gerente
de contas desse cara aqui então toda vez
que ess cara mandar um oi que que eu vou
fazer eu vou vir aqui ó vou falar Opa
você pode ver que no we vem essa essa
opção de de tags dele aqui né vou falar
Opa esse cara aqui é do Luca aqui ó
Labes ó que tá vazio lá que não tem
nenhuma Lab associado mas vamos supor
que tem a lebo Lucas eu já posso vir
automaticamente aqui falar não esse cara
não passa pelo bote esse cara eu já vou
encaminhar esse cara pro Lucas Sim
entendeu então até Car cliente né Hã já
é um cliente já tá atendendo quem que
vai botar I né o cliente não passa pelo
bote o cliente eu vou atender com com
pessoas mesmo ali né vou fazer uma
filinha eu posso até responder olha Já
encaminhei a conversa pro Lucas tá só
vou aguardar aquele Aguarda um minutinho
só ele poder vir atender você já cara
você você faz tanta coisa aqui Lucas que
você pode fazer né e super simples cara
super simples de trabalhar né Então
pessoal a dica para vocês aí dá um like
no vídeo se inscrev no canal tá e
comenta que que você achou e compartilha
esse vídeo também na na nas comunidades
que Vocês participam porque isso aqui
Lucas é um trabalho que não só eu tá mas
vários outros influenciadores aí estão
trabalhando em cima do notifica me rub
porque a gente enxergou pessoal enou
amos possibilidades ali a minha função e
a do Lucas também né Lucas é trazer para
vocês as possibilidades como que vocês
vão utilizar eu não sei mas que dá para
fazer muita coisa dá né Lucas dá um
negócio muito completo muito legal mesmo
cara sim tem que tomar cuidado né porque
tá D muito ideia não tem que tomar
cuidado porque dá muita ideia dá muita
ideia e e eu falei para você a galera
ainda não pegou as manhas a h que a
galera pegar as manã cara que tem muito
tem muito dado aqui que pode virar
informação pode virar estatística o jogo
começa a virar cara ainda tá no começo
né Lucas é muito no começo ainda né mas
eu acho que o caminho vai ser esse viu ô
Lucas Obrigadão viu cara usei seu tempo
no carnaval e tirei você do bloquinho o
bloquinho do colchão aqui em casa é o
bloquinho do colchão tá todo mundo
dormindo n a gente faz a gente aproveita
para poder descansar né sim mas obrigado
viu Valeu que agradeço o convite Luiz
estamos junto show de bola pessoal
Obrigadão hein tchau tchau