# WebAgent Social Extraction Platform - Docker Compose
# Development environment setup

version: '3.8'

services:
  # ===== DATABASE =====
  postgres:
    image: postgres:15-alpine
    container_name: webagent-postgres
    environment:
      POSTGRES_DB: webagent
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./data/schemas:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - webagent-network

  # ===== CACHE & MESSAGE BROKER =====
  redis:
    image: redis:7-alpine
    container_name: webagent-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./config/redis/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - webagent-network

  # ===== API GATEWAY =====
  api:
    build:
      context: .
      dockerfile: infra/docker/Dockerfile.api
    container_name: webagent-api
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=********************************************/webagent
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    env_file:
      - .env
    volumes:
      - ./src:/app/src
      - ./config:/app/config
      - ./data:/app/data
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - webagent-network
    restart: unless-stopped

  # ===== CELERY WORKER =====
  worker:
    build:
      context: .
      dockerfile: infra/docker/Dockerfile.worker
    container_name: webagent-worker
    environment:
      - DATABASE_URL=********************************************/webagent
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    env_file:
      - .env
    volumes:
      - ./src:/app/src
      - ./config:/app/config
      - ./data:/app/data
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - webagent-network
    restart: unless-stopped

  # ===== CELERY BEAT SCHEDULER =====
  beat:
    build:
      context: .
      dockerfile: infra/docker/Dockerfile.worker
    container_name: webagent-beat
    command: celery -A src.workers.main beat --loglevel=info
    environment:
      - DATABASE_URL=********************************************/webagent
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    env_file:
      - .env
    volumes:
      - ./src:/app/src
      - ./config:/app/config
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - webagent-network
    restart: unless-stopped

  # ===== CELERY MONITORING (FLOWER) =====
  flower:
    build:
      context: .
      dockerfile: infra/docker/Dockerfile.worker
    container_name: webagent-flower
    command: celery -A src.workers.main flower --port=5555
    ports:
      - "5555:5555"
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    env_file:
      - .env
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - webagent-network
    restart: unless-stopped

  # ===== FRONTEND (REACT) =====
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: webagent-frontend
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8000
    volumes:
      - ./frontend/src:/app/src
      - ./frontend/public:/app/public
    depends_on:
      - api
    networks:
      - webagent-network
    restart: unless-stopped

  # ===== MONITORING =====
  prometheus:
    image: prom/prometheus:latest
    container_name: webagent-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./infra/monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    networks:
      - webagent-network
    restart: unless-stopped

  grafana:
    image: grafana/grafana:latest
    container_name: webagent-grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./infra/monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./infra/monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    networks:
      - webagent-network
    restart: unless-stopped

  # ===== NGINX REVERSE PROXY =====
  nginx:
    image: nginx:alpine
    container_name: webagent-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./infra/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./infra/nginx/ssl:/etc/nginx/ssl
    depends_on:
      - api
      - frontend
    networks:
      - webagent-network
    restart: unless-stopped

# ===== VOLUMES =====
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

# ===== NETWORKS =====
networks:
  webagent-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
