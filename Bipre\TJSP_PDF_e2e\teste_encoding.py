#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Teste de Encoding para Validar Correção
Testa se o subprocess consegue executar scripts com emojis Unicode
"""

import os
import sys
import subprocess
from pathlib import Path

def testar_encoding_subprocess():
    """Testa se o subprocess consegue executar com emojis"""
    print("🧪 Testando encoding do subprocess...")
    
    # Criar script de teste temporário
    script_teste = Path("teste_emoji_temp.py")
    
    conteudo_teste = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
print("🚀 Teste de emoji no subprocess")
print("✅ Se você vê este emoji, o encoding está funcionando!")
print("📊 Teste concluído com sucesso")
'''
    
    try:
        # Criar arquivo de teste
        script_teste.write_text(conteudo_teste, encoding='utf-8')
        
        # Testar sem configuração de ambiente (deve falhar)
        print("\n1️⃣ Teste SEM configuração de ambiente:")
        try:
            result1 = subprocess.run(
                [sys.executable, str(script_teste)],
                capture_output=True,
                text=True,
                encoding='utf-8',
                timeout=10
            )
            if result1.returncode == 0:
                print("✅ Sucesso (inesperado)")
                print(f"Saída: {result1.stdout}")
            else:
                print("❌ Falhou como esperado")
                print(f"Erro: {result1.stderr}")
        except Exception as e:
            print(f"❌ Erro: {e}")
        
        # Testar COM configuração de ambiente (deve funcionar)
        print("\n2️⃣ Teste COM configuração de ambiente UTF-8:")
        try:
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'
            env['PYTHONLEGACYWINDOWSSTDIO'] = 'utf-8'
            env['PYTHONUTF8'] = '1'
            
            result2 = subprocess.run(
                [sys.executable, str(script_teste)],
                capture_output=True,
                text=True,
                encoding='utf-8',
                env=env,
                timeout=10
            )
            if result2.returncode == 0:
                print("✅ Sucesso!")
                print(f"Saída: {result2.stdout}")
            else:
                print("❌ Falhou")
                print(f"Erro: {result2.stderr}")
        except Exception as e:
            print(f"❌ Erro: {e}")
            
    finally:
        # Limpar arquivo de teste
        if script_teste.exists():
            script_teste.unlink()
            
    print("\n🎯 Teste de encoding concluído!")

def testar_orquestrador_corrigido():
    """Testa se o orquestrador corrigido funciona"""
    print("\n🚀 Testando orquestrador corrigido...")
    
    try:
        # Importar orquestrador
        from orquestrador_tjsp_e2e import OrquestradorTJSP
        
        # Criar instância
        orquestrador = OrquestradorTJSP()
        
        # Testar verificação de sistemas
        tjsp_ok, extracao_ok = orquestrador.verificar_sistemas()
        
        print(f"📊 Sistema TJSP: {'✅' if tjsp_ok else '❌'}")
        print(f"📊 Sistema Extração: {'✅' if extracao_ok else '❌'}")
        
        if tjsp_ok and extracao_ok:
            print("✅ Orquestrador carregado com sucesso!")
            print("💡 Agora você pode testar as opções 1, 2 ou 3 do menu")
        else:
            print("⚠️ Alguns sistemas não foram encontrados")
            
    except Exception as e:
        print(f"❌ Erro ao testar orquestrador: {e}")

if __name__ == "__main__":
    print("🔧 TESTE DE CORREÇÃO DE ENCODING")
    print("=" * 50)
    
    testar_encoding_subprocess()
    testar_orquestrador_corrigido()
    
    print("\n" + "=" * 50)
    print("📋 RESUMO:")
    print("• Se o teste 2️⃣ passou, a correção está funcionando")
    print("• Agora você pode executar o orquestrador normalmente")
    print("• Use EXECUTAR_ORQUESTRADOR.bat ou python orquestrador_tjsp_e2e.py")
