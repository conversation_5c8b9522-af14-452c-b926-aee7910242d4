# WebAgent Social Extraction Platform - Makefile
# Automation commands for development, testing, and deployment

.PHONY: help install install-dev setup clean test test-unit test-integration test-e2e test-performance lint format type-check security-check build run run-dev run-worker run-tests docker-build docker-run docker-stop deploy-local deploy-staging deploy-prod backup restore monitoring logs

# Default target
help: ## Show this help message
	@echo "WebAgent Social Extraction Platform - Available Commands:"
	@echo ""
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

# ===== INSTALLATION & SETUP =====
install: ## Install production dependencies
	pip install -r requirements.txt

install-dev: ## Install development dependencies
	pip install -r requirements.txt
	pip install -e ".[dev,test,docs,monitoring,media]"
	pre-commit install

setup: ## Complete project setup
	@echo "🚀 Setting up WebAgent Social Extraction Platform..."
	python -m venv venv
	@echo "📦 Installing dependencies..."
	$(MAKE) install-dev
	@echo "📋 Copying environment template..."
	cp .env.example .env
	@echo "🐳 Starting Docker services..."
	docker-compose up -d redis postgres
	@echo "🗄️ Running database migrations..."
	$(MAKE) migrate
	@echo "✅ Setup complete! Edit .env file with your API keys."

clean: ## Clean up build artifacts and cache
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	find . -type d -name ".pytest_cache" -exec rm -rf {} +
	find . -type d -name ".coverage" -delete
	rm -rf build/ dist/ htmlcov/

# ===== TESTING =====
test: ## Run all tests
	pytest tests/ -v --cov=src --cov-report=term-missing

test-unit: ## Run unit tests only
	pytest tests/unit/ -v -m "not slow"

test-integration: ## Run integration tests
	pytest tests/integration/ -v

test-e2e: ## Run end-to-end tests
	pytest tests/e2e/ -v

test-performance: ## Run performance tests
	pytest tests/performance/ -v -m "performance"

test-poc: ## Run PoC validation tests
	cd poc && python -m pytest . -v

# ===== CODE QUALITY =====
lint: ## Run linting checks
	flake8 src tests
	black --check src tests
	isort --check-only src tests

format: ## Format code
	black src tests
	isort src tests

type-check: ## Run type checking
	mypy src

security-check: ## Run security checks
	bandit -r src/
	safety check

quality: lint type-check security-check ## Run all quality checks

# ===== DEVELOPMENT =====
run: ## Run the API server
	uvicorn src.api.main:app --host 0.0.0.0 --port 8000

run-dev: ## Run the API server in development mode
	uvicorn src.api.main:app --host 0.0.0.0 --port 8000 --reload

run-worker: ## Run Celery worker
	celery -A src.workers.main worker --loglevel=info

run-beat: ## Run Celery beat scheduler
	celery -A src.workers.main beat --loglevel=info

run-flower: ## Run Celery monitoring (Flower)
	celery -A src.workers.main flower --port=5555

run-all: ## Run all services (API + Worker + Beat)
	@echo "🚀 Starting all services..."
	docker-compose up -d
	@echo "✅ All services started!"

# ===== DATABASE =====
migrate: ## Run database migrations
	alembic upgrade head

migrate-create: ## Create new migration
	@read -p "Migration name: " name; \
	alembic revision --autogenerate -m "$$name"

migrate-rollback: ## Rollback last migration
	alembic downgrade -1

db-reset: ## Reset database (WARNING: destroys all data)
	@echo "⚠️  WARNING: This will destroy all data!"
	@read -p "Are you sure? (y/N): " confirm; \
	if [ "$$confirm" = "y" ] || [ "$$confirm" = "Y" ]; then \
		alembic downgrade base; \
		alembic upgrade head; \
		echo "✅ Database reset complete"; \
	else \
		echo "❌ Database reset cancelled"; \
	fi

# ===== DOCKER =====
docker-build: ## Build Docker images
	docker-compose build

docker-run: ## Run with Docker Compose
	docker-compose up -d

docker-stop: ## Stop Docker services
	docker-compose down

docker-logs: ## View Docker logs
	docker-compose logs -f

docker-clean: ## Clean Docker resources
	docker-compose down -v --remove-orphans
	docker system prune -f

# ===== DEPLOYMENT =====
deploy-local: ## Deploy locally
	@echo "🚀 Deploying locally..."
	$(MAKE) docker-build
	$(MAKE) docker-run
	@echo "✅ Local deployment complete!"

deploy-staging: ## Deploy to staging
	@echo "🚀 Deploying to staging..."
	# Add staging deployment commands here
	@echo "✅ Staging deployment complete!"

deploy-prod: ## Deploy to production
	@echo "🚀 Deploying to production..."
	# Add production deployment commands here
	@echo "✅ Production deployment complete!"

# ===== MONITORING =====
monitoring: ## Start monitoring stack
	docker-compose -f infra/monitoring/docker-compose.monitoring.yml up -d

logs: ## View application logs
	docker-compose logs -f api worker

metrics: ## View metrics dashboard
	@echo "📊 Opening metrics dashboard..."
	@echo "Prometheus: http://localhost:9090"
	@echo "Grafana: http://localhost:3000"

# ===== BACKUP & RESTORE =====
backup: ## Create database backup
	@echo "💾 Creating database backup..."
	python scripts/database/backup.py
	@echo "✅ Backup complete!"

restore: ## Restore database from backup
	@echo "🔄 Restoring database..."
	@read -p "Backup file path: " backup_file; \
	python scripts/database/restore.py "$$backup_file"
	@echo "✅ Restore complete!"

# ===== DOCUMENTATION =====
docs: ## Build documentation
	mkdocs build

docs-serve: ## Serve documentation locally
	mkdocs serve

docs-deploy: ## Deploy documentation
	mkdocs gh-deploy

# ===== PoC SPECIFIC =====
poc-youtube: ## Run YouTube extraction PoC
	cd poc/youtube_extraction && python youtube_poc.py

poc-gemini: ## Run Gemini analysis PoC
	cd poc/gemini_analysis && python gemini_poc.py

poc-performance: ## Run performance benchmarks
	cd poc/performance_tests && python benchmark.py

poc-cost: ## Run cost analysis
	cd poc/cost_analysis && python cost_calculator.py

poc-all: ## Run all PoC tests
	$(MAKE) poc-youtube
	$(MAKE) poc-gemini
	$(MAKE) poc-performance
	$(MAKE) poc-cost

# ===== UTILITIES =====
shell: ## Open Python shell with app context
	python -c "from src.api.main import app; import IPython; IPython.embed()"

requirements: ## Update requirements.txt
	pip-compile requirements.in

check-env: ## Check environment variables
	python scripts/utilities/check_env.py

health-check: ## Check system health
	python scripts/monitoring/health_check.py

# ===== CI/CD =====
ci-test: ## Run CI test suite
	$(MAKE) quality
	$(MAKE) test
	$(MAKE) security-check

ci-build: ## Build for CI
	$(MAKE) docker-build

ci-deploy: ## Deploy from CI
	@echo "🚀 CI Deployment..."
	# Add CI deployment logic here

# ===== DEVELOPMENT HELPERS =====
reset-dev: ## Reset development environment
	$(MAKE) docker-stop
	$(MAKE) clean
	$(MAKE) docker-clean
	$(MAKE) setup

update-deps: ## Update all dependencies
	pip install --upgrade pip
	pip install --upgrade -r requirements.txt

generate-secret: ## Generate new secret key
	python -c "import secrets; print(f'SECRET_KEY={secrets.token_urlsafe(32)}')"
