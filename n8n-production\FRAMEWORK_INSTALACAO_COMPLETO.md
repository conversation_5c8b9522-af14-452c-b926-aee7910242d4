# 🚀 **FRAMEWORK COMPLETO DE INSTALAÇÃO N8N + EVOLUTION API**

## 📋 **ÍNDICE**

1. [<PERSON><PERSON><PERSON> Geral](#visão-geral)
2. [Módulo 1: N8N Básico](#módulo-1-n8n-básico)
3. [Módulo 2: N8N Completo](#módulo-2-n8n-completo)
4. [Módulo 3: Evolution API](#módulo-3-evolution-api)
5. [Módulo 4: Framework Integrado](#módulo-4-framework-integrado)
6. [Configurações Avançadas](#configurações-avançadas)
7. [Troubleshooting](#troubleshooting)

---

## 🎯 **VISÃO GERAL**

Este framework oferece instalações modulares e escaláveis:

### **📦 Módulos Disponíveis:**

| Módulo | Descrição | Componentes | Uso Recomendado |
|--------|-----------|-------------|------------------|
| **N8N Básico** | Instalação mínima | N8N + PostgreSQL + Redis | Desenvolvimento/Testes |
| **N8N Completo** | Instalação com monitoramento | N8N + DB + Monitoring Stack | Produção |
| **Evolution API** | WhatsApp API standalone | Evolution + PostgreSQL + Redis dedicados | Integração WhatsApp |
| **Framework Integrado** | Solução completa | Todos os componentes | Produção Completa |

### **🏗️ Arquitetura Modular:**

```
Framework/
├── modules/
│   ├── n8n-basic/          # N8N mínimo
│   ├── n8n-complete/       # N8N + monitoramento
│   ├── evolution-api/      # Evolution standalone
│   └── integrated/         # Solução completa
├── configs/
│   ├── environments/       # Variáveis de ambiente
│   ├── nginx/             # Configurações proxy
│   └── monitoring/        # Configs monitoramento
├── scripts/
│   ├── install/           # Scripts de instalação
│   ├── backup/            # Scripts de backup
│   └── maintenance/       # Scripts de manutenção
└── docs/
    ├── technical/         # Documentação técnica
    ├── guides/           # Guias de uso
    └── troubleshooting/  # Resolução de problemas
```

---

## 🔧 **MÓDULO 1: N8N BÁSICO**

### **📋 Componentes:**
- N8N (Workflow Engine)
- PostgreSQL (Database)
- Redis (Cache/Queue)

### **🎯 Características:**
- ✅ Instalação rápida (< 5 minutos)
- ✅ Recursos mínimos (2GB RAM)
- ✅ Ideal para desenvolvimento
- ✅ Sem monitoramento

### **📁 Estrutura de Arquivos:**

```
modules/n8n-basic/
├── docker-compose.yml
├── .env
├── install.sh
├── README.md
└── configs/
    ├── n8n/
    └── postgres/
```

### **🚀 Instalação Rápida:**

```bash
# 1. Criar diretório
mkdir n8n-basic && cd n8n-basic

# 2. Baixar arquivos
curl -O https://raw.githubusercontent.com/seu-repo/n8n-basic/docker-compose.yml
curl -O https://raw.githubusercontent.com/seu-repo/n8n-basic/.env

# 3. Configurar variáveis
cp .env.example .env
nano .env

# 4. Iniciar
docker-compose up -d

# 5. Verificar
docker-compose ps
```

### **⚙️ Configuração docker-compose.yml:**

```yaml
version: '3.8'

services:
  postgres:
    image: postgres:16-alpine
    container_name: n8n-postgres
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - n8n-network

  redis:
    image: redis:7-alpine
    container_name: n8n-redis
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - n8n-network

  n8n:
    image: docker.n8n.io/n8nio/n8n:latest
    container_name: n8n-app
    ports:
      - "${N8N_PORT}:5678"
    environment:
      DB_TYPE: postgresdb
      DB_POSTGRESDB_HOST: postgres
      DB_POSTGRESDB_PORT: 5432
      DB_POSTGRESDB_DATABASE: ${POSTGRES_DB}
      DB_POSTGRESDB_USER: ${POSTGRES_USER}
      DB_POSTGRESDB_PASSWORD: ${POSTGRES_PASSWORD}
      QUEUE_BULL_REDIS_HOST: redis
      QUEUE_BULL_REDIS_PORT: 6379
      N8N_BASIC_AUTH_ACTIVE: true
      N8N_BASIC_AUTH_USER: ${N8N_BASIC_AUTH_USER}
      N8N_BASIC_AUTH_PASSWORD: ${N8N_BASIC_AUTH_PASSWORD}
      WEBHOOK_URL: ${WEBHOOK_URL}
      GENERIC_TIMEZONE: ${TIMEZONE}
    volumes:
      - n8n_data:/home/<USER>/.n8n
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - n8n-network

volumes:
  postgres_data:
  redis_data:
  n8n_data:

networks:
  n8n-network:
    driver: bridge
```

### **🔐 Arquivo .env:**

```env
# === N8N BÁSICO - CONFIGURAÇÕES ===

# Database
POSTGRES_DB=n8n
POSTGRES_USER=n8n_user
POSTGRES_PASSWORD=n8n_password_123

# N8N
N8N_PORT=5678
N8N_BASIC_AUTH_USER=admin
N8N_BASIC_AUTH_PASSWORD=admin123
WEBHOOK_URL=http://localhost:5678
TIMEZONE=America/Sao_Paulo

# Recursos
POSTGRES_MAX_CONNECTIONS=100
REDIS_MAXMEMORY=256mb
```

---

## 🔍 **MÓDULO 2: N8N COMPLETO**

### **📋 Componentes:**
- N8N (Main + Worker)
- PostgreSQL (Database)
- Redis (Cache/Queue)
- Grafana (Monitoring)
- Prometheus (Metrics)
- PgAdmin (DB Management)
- Redis Commander (Redis Management)
- Bull Board (Queue Management)

### **🎯 Características:**
- ✅ Monitoramento completo
- ✅ Escalabilidade (workers)
- ✅ Dashboards visuais
- ✅ Gestão de filas
- ✅ Backup automatizado

### **📁 Estrutura de Arquivos:**

```
modules/n8n-complete/
├── docker-compose.yml
├── .env
├── install.sh
├── README.md
├── configs/
│   ├── grafana/
│   │   ├── dashboards/
│   │   └── provisioning/
│   ├── prometheus/
│   │   └── prometheus.yml
│   ├── nginx/
│   │   └── nginx.conf
│   └── backup/
│       └── backup-script.sh
└── scripts/
    ├── setup.sh
    ├── backup.sh
    └── restore.sh
```

### **🚀 Instalação Completa:**

```bash
# 1. Clonar repositório
git clone https://github.com/seu-repo/n8n-complete.git
cd n8n-complete

# 2. Executar script de instalação
chmod +x install.sh
./install.sh

# 3. Configurar monitoramento
./scripts/setup-monitoring.sh

# 4. Verificar instalação
docker-compose ps
./scripts/health-check.sh
```

### **⚙️ Docker Compose N8N Completo:**

```yaml
version: '3.8'

services:
  # === DATABASE ===
  postgres:
    image: postgres:16-alpine
    container_name: n8n-postgres-complete
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./configs/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "${POSTGRES_PORT}:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - n8n-network

  # === CACHE/QUEUE ===
  redis:
    image: redis:7-alpine
    container_name: n8n-redis-complete
    command: redis-server --appendonly yes --maxmemory ${REDIS_MAXMEMORY} --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    ports:
      - "${REDIS_PORT}:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - n8n-network

  # === N8N MAIN ===
  n8n:
    image: docker.n8n.io/n8nio/n8n:latest
    container_name: n8n-main
    ports:
      - "${N8N_PORT}:5678"
    environment:
      DB_TYPE: postgresdb
      DB_POSTGRESDB_HOST: postgres
      DB_POSTGRESDB_PORT: 5432
      DB_POSTGRESDB_DATABASE: ${POSTGRES_DB}
      DB_POSTGRESDB_USER: ${POSTGRES_USER}
      DB_POSTGRESDB_PASSWORD: ${POSTGRES_PASSWORD}
      QUEUE_BULL_REDIS_HOST: redis
      QUEUE_BULL_REDIS_PORT: 6379
      EXECUTIONS_MODE: queue
      QUEUE_BULL_REDIS_DB: 2
      N8N_BASIC_AUTH_ACTIVE: true
      N8N_BASIC_AUTH_USER: ${N8N_BASIC_AUTH_USER}
      N8N_BASIC_AUTH_PASSWORD: ${N8N_BASIC_AUTH_PASSWORD}
      WEBHOOK_URL: ${WEBHOOK_URL}
      GENERIC_TIMEZONE: ${TIMEZONE}
      N8N_METRICS: true
      N8N_LOG_LEVEL: info
    volumes:
      - n8n_data:/home/<USER>/.n8n
      - ./configs/n8n:/etc/n8n
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - n8n-network

  # === N8N WORKER ===
  n8n-worker:
    image: docker.n8n.io/n8nio/n8n:latest
    container_name: n8n-worker
    command: worker
    environment:
      DB_TYPE: postgresdb
      DB_POSTGRESDB_HOST: postgres
      DB_POSTGRESDB_PORT: 5432
      DB_POSTGRESDB_DATABASE: ${POSTGRES_DB}
      DB_POSTGRESDB_USER: ${POSTGRES_USER}
      DB_POSTGRESDB_PASSWORD: ${POSTGRES_PASSWORD}
      QUEUE_BULL_REDIS_HOST: redis
      QUEUE_BULL_REDIS_PORT: 6379
      QUEUE_BULL_REDIS_DB: 2
      GENERIC_TIMEZONE: ${TIMEZONE}
      N8N_LOG_LEVEL: info
    volumes:
      - n8n_data:/home/<USER>/.n8n
      - ./configs/n8n:/etc/n8n
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    deploy:
      replicas: 2
    networks:
      - n8n-network

  # === MONITORING ===
  prometheus:
    image: prom/prometheus:latest
    container_name: n8n-prometheus
    ports:
      - "${PROMETHEUS_PORT}:9090"
    volumes:
      - ./configs/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - n8n-network

  grafana:
    image: grafana/grafana:latest
    container_name: n8n-grafana
    ports:
      - "${GRAFANA_PORT}:3000"
    environment:
      GF_SECURITY_ADMIN_USER: ${GRAFANA_USER}
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD}
      GF_INSTALL_PLUGINS: grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - grafana_data:/var/lib/grafana
      - ./configs/grafana/provisioning:/etc/grafana/provisioning
      - ./configs/grafana/dashboards:/var/lib/grafana/dashboards
    depends_on:
      - prometheus
    networks:
      - n8n-network

  # === MANAGEMENT TOOLS ===
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: n8n-pgadmin
    ports:
      - "${PGADMIN_PORT}:80"
    environment:
      PGADMIN_DEFAULT_EMAIL: ${PGADMIN_EMAIL}
      PGADMIN_DEFAULT_PASSWORD: ${PGADMIN_PASSWORD}
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      - postgres
    networks:
      - n8n-network

  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: n8n-redis-commander
    ports:
      - "${REDIS_COMMANDER_PORT}:8081"
    environment:
      REDIS_HOSTS: local:redis:6379
    depends_on:
      - redis
    networks:
      - n8n-network

  bull-board:
    build:
      context: ./configs/bull-board
      dockerfile: Dockerfile
    container_name: n8n-bull-board
    ports:
      - "${BULL_BOARD_PORT}:3002"
    environment:
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_DB: 2
    depends_on:
      - redis
    networks:
      - n8n-network

volumes:
  postgres_data:
  redis_data:
  n8n_data:
  grafana_data:
  prometheus_data:
  pgadmin_data:

networks:
  n8n-network:
    driver: bridge
```

---

## 📱 **MÓDULO 3: EVOLUTION API**

### **📋 Componentes:**
- Evolution API (WhatsApp Integration)
- PostgreSQL Dedicado (Database)
- Redis Dedicado (Cache)

### **🎯 Características:**
- ✅ Instalação independente
- ✅ Bancos dedicados
- ✅ Isolamento completo
- ✅ Fácil integração

### **📁 Estrutura de Arquivos:**

```
modules/evolution-api/
├── docker-compose.yml
├── .env
├── install.sh
├── README.md
├── configs/
│   ├── evolution/
│   │   └── config.yml
│   ├── postgres/
│   │   └── init.sql
│   └── nginx/
│       └── evolution.conf
└── scripts/
    ├── setup-instance.sh
    ├── backup.sh
    └── health-check.sh
```

### **🚀 Instalação Evolution API:**

```bash
# 1. Criar diretório
mkdir evolution-api && cd evolution-api

# 2. Baixar configurações
curl -O https://raw.githubusercontent.com/seu-repo/evolution-api/docker-compose.yml
curl -O https://raw.githubusercontent.com/seu-repo/evolution-api/.env

# 3. Configurar
cp .env.example .env
nano .env

# 4. Instalar
docker-compose up -d

# 5. Configurar primeira instância
./scripts/setup-instance.sh
```

### **⚙️ Docker Compose Evolution API:**

```yaml
version: '3.8'

services:
  # === POSTGRESQL DEDICADO ===
  postgres-evolution:
    image: postgres:15-alpine
    container_name: evolution-postgres
    environment:
      POSTGRES_DB: ${EVOLUTION_POSTGRES_DB}
      POSTGRES_USER: ${EVOLUTION_POSTGRES_USER}
      POSTGRES_PASSWORD: ${EVOLUTION_POSTGRES_PASSWORD}
    volumes:
      - postgres_evolution_data:/var/lib/postgresql/data
      - ./configs/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "${EVOLUTION_POSTGRES_PORT}:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${EVOLUTION_POSTGRES_USER} -d ${EVOLUTION_POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - evolution-network

  # === REDIS DEDICADO ===
  redis-evolution:
    image: redis:7-alpine
    container_name: evolution-redis
    command: redis-server --appendonly yes --maxmemory ${EVOLUTION_REDIS_MAXMEMORY}
    volumes:
      - redis_evolution_data:/data
    ports:
      - "${EVOLUTION_REDIS_PORT}:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - evolution-network

  # === EVOLUTION API ===
  evolution-api:
    image: atendai/evolution-api:v2.2.0
    container_name: evolution-api
    ports:
      - "${EVOLUTION_PORT}:8080"
    environment:
      # Database
      DATABASE_PROVIDER: postgresql
      DATABASE_CONNECTION_URI: postgresql://${EVOLUTION_POSTGRES_USER}:${EVOLUTION_POSTGRES_PASSWORD}@postgres-evolution:5432/${EVOLUTION_POSTGRES_DB}?schema=public

      # Redis
      REDIS_URI: redis://redis-evolution:6379

      # API Configuration
      API_KEY: ${EVOLUTION_API_KEY}
      JWT_SECRET: ${EVOLUTION_JWT_SECRET}

      # Server
      SERVER_TYPE: http
      SERVER_PORT: 8080

      # Logs
      LOG_LEVEL: info
      LOG_COLOR: true

      # Instance
      DEL_INSTANCE: false

      # Webhook
      WEBHOOK_GLOBAL_URL: ${EVOLUTION_WEBHOOK_URL}
      WEBHOOK_GLOBAL_ENABLED: true

      # QR Code
      QRCODE_LIMIT: 10

      # Authentication
      AUTHENTICATION_TYPE: apikey
      AUTHENTICATION_API_KEY: ${EVOLUTION_API_KEY}

      # Timezone
      TZ: ${TIMEZONE}
    volumes:
      - evolution_instances:/evolution/instances
      - ./configs/evolution:/evolution/config
    depends_on:
      postgres-evolution:
        condition: service_healthy
      redis-evolution:
        condition: service_healthy
    networks:
      - evolution-network

volumes:
  postgres_evolution_data:
  redis_evolution_data:
  evolution_instances:

networks:
  evolution-network:
    driver: bridge
```

### **🔐 Arquivo .env Evolution:**

```env
# === EVOLUTION API - CONFIGURAÇÕES ===

# PostgreSQL Dedicado
EVOLUTION_POSTGRES_DB=evolution
EVOLUTION_POSTGRES_USER=evolution_user
EVOLUTION_POSTGRES_PASSWORD=evolution123
EVOLUTION_POSTGRES_PORT=5433

# Redis Dedicado
EVOLUTION_REDIS_PORT=6380
EVOLUTION_REDIS_MAXMEMORY=512mb

# Evolution API
EVOLUTION_PORT=8001
EVOLUTION_API_KEY=evolution_api_key_123
EVOLUTION_JWT_SECRET=evolution_jwt_secret_456
EVOLUTION_WEBHOOK_URL=http://localhost:8001/webhook

# Timezone
TIMEZONE=America/Sao_Paulo

# Logs
LOG_LEVEL=info
```

---

## 🔗 **MÓDULO 4: FRAMEWORK INTEGRADO**

### **📋 Componentes:**
- N8N Completo (Main + Worker)
- Evolution API
- Bancos Dedicados (PostgreSQL x2, Redis x2)
- Stack de Monitoramento
- Ferramentas de Gestão
- Proxy Reverso (Nginx)

### **🎯 Características:**
- ✅ Solução completa
- ✅ Isolamento de serviços
- ✅ Monitoramento unificado
- ✅ SSL/TLS configurado
- ✅ Backup automatizado

### **📁 Estrutura Integrada:**

```
framework-integrado/
├── docker-compose.yml          # Compose principal
├── .env                        # Variáveis globais
├── install.sh                  # Script de instalação
├── README.md                   # Documentação
├── configs/
│   ├── nginx/
│   │   ├── nginx.conf
│   │   ├── n8n.conf
│   │   └── evolution.conf
│   ├── ssl/
│   │   ├── generate-certs.sh
│   │   └── certs/
│   ├── grafana/
│   │   ├── dashboards/
│   │   └── provisioning/
│   ├── prometheus/
│   │   └── prometheus.yml
│   └── backup/
│       ├── backup-all.sh
│       └── restore-all.sh
├── scripts/
│   ├── install/
│   │   ├── install-basic.sh
│   │   ├── install-complete.sh
│   │   ├── install-evolution.sh
│   │   └── install-integrated.sh
│   ├── maintenance/
│   │   ├── update-all.sh
│   │   ├── backup-all.sh
│   │   └── health-check.sh
│   └── utils/
│       ├── generate-passwords.sh
│       ├── setup-ssl.sh
│       └── configure-firewall.sh
└── docs/
    ├── INSTALLATION.md
    ├── CONFIGURATION.md
    ├── TROUBLESHOOTING.md
    └── API_REFERENCE.md
```

### **🚀 Instalação Framework Integrado:**

```bash
# 1. Clonar repositório completo
git clone https://github.com/seu-repo/n8n-evolution-framework.git
cd n8n-evolution-framework

# 2. Executar instalação interativa
chmod +x install.sh
./install.sh

# 3. Seguir wizard de configuração
# - Escolher módulos
# - Configurar domínios
# - Gerar certificados SSL
# - Configurar backups

# 4. Verificar instalação
./scripts/health-check.sh
```
