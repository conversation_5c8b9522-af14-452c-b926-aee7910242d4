﻿pessoal então Começando aqui o nosso
curso da Evolution api 2 tá nesse curso
aqui eu quero passar com vocês é um
curso sobre setup praticamente né nesse
primeiro momento Prim primeiro módulo do
curso e a aula hoje é sobre a Evolution
em si o que que é Evolution como que ela
tá que tamanho que ela tá porque a
Evolution cresceu eem membros da família
ali de sistemas que vocês vão ter que
aprender a lidar a cuidar também então é
uma aula mais teórica mas eu queria
muito mesmo que vocês prestar muita
atenção nessa aula pega um caderninho aí
ó vai anotando tá bom pessoal para que
vocês fiquem eh para que a gente fique
sincronizado até onde da Pake Evolution
Quais são os pontos de atenção que vocês
têm que ter para Evolution porque no
curso aqui a gente vai instalar ela mas
eu queria muito que vocês não só
seguissem a instalação mas que vocês
planejem pensassem um pouco mais na na
instalação também tá então a ideia aqui
bater um papo com vocês sobre essas
essas mudanças da Evolution tá e aqui
pessoal seguinte então Evolution a gente
pode dividir ela em três partes né
praticamente três partes a aplicação em
si tá a infraestrutura dela e as
integrações dela então é engraçado
porque eu acompanhei desde o começo
Evolution desde os primeiros dias de
Evolution praticamente aí né e e você vê
aonde ela chegou onde ela tá um ano
depois praticamente pouco mais de um ano
depois é de bater bater palma pro daves
ali e agradecer muito mesmo ele por
dedicar tanto a ferramenta né e trazer
ela nesse ponto aqui tá gente então
falando um pouco sobre a Evolution
pessoal a Evolution ela tá disponível no
github então se você for
no no do github da Evolution api você
vai encontrar todinho o código fonte
aqui então é interessante o seguinte a
Evolution ela é open sece a gente F luí
só porque o código tá aqui ela ela é
open não o nhn o código tá aqui ele não
é open sce mas a licença da Evolution
sim é open sce você pode vir aqui você
pode fazer um fork pode ver ó que a
Evolution tem 575 Forks uma cópia sua
que você tem para que você possa ter a
sua versão modificar ou só mesmo fazer
uma cópia para garantir Eh vamos lembrar
sempre né pessoal e nesse mercado aqui
do WhatsApp vamos lembrar sempre da BS
que em um belo momento Acordamos uma
segunda-feira sem sem abios no ar né
então foram lá e apagaram falam ó não
tem mais acabou acabou a brincadeira
eh continuamos tendo a bos Graças aos
Forks né então é a maneira que você tem
de de se precaver aí até de de ter a sua
versão da do sistema a evol pessoal ela
vai oferecer para vocês
o a imagem dela no docker Hub através do
perfil do atenda então lá no lá no
dockerhub não é Evolution api bar
Evolution API como é aqui no github é o
atenda I tá então fica atenda I Barra
Evolution api e é importante falar para
vocês o seguinte sobre o versionamento
que é esse ponto aqui ó isso aqui
pessoal e qualquer software que você for
utilizar qualquer um o nen o maltico
chatot WordPress o mais simples que for
o Reds qualquer um vocês são os
responsáveis pela estabilidade da
ferramenta é importante entender pessoal
que na quando a gente fala de
estabilidade eu tô falando em fazer a
coisa funcionar não só estar funcionando
mas ela ela funcionar bem Então pessoal
como qualquer outro software existente
nesse planeta é propenso Aé um bug é
propenso a ter uma compatibilidade por
mais que seja devidamente testado por
mais que o David também faz o trabalho
dele de ficar ali Testando a ferramenta
a comunidade testando ferramenta Pode
ser que algum bug apare é comum tá gente
não existe software sem bug quando você
faz lá a sua faculdade Deia de software
gente entende muito isso boa parte do
nosso trabalho é é evitar o bug mas ele
vai acontecer em algum momento que que
não pode acontecer é você ser emocionado
e você sair atualizando o sistema só
porque S uma versão nova a gente viu né
a versão 2.10 ali ó sai numa sexta-feira
a gente até brinca né Tem um jargão
muito famoso no mundo da programação e
que também é do mundo de vs que é no
Deploy on friday não faça Deploy na
sexta-feira por pessoal porque se der
problema você vai ter que vir no sábado
no domingo resolver né então quando você
parte para essa área do Open sece quando
você fala assim ó Luiz eu estou disposto
a minha agência a minha empresa o meu
cliente está disposto a utilizar o open
sece tudo isso pessoal tem que ser
levado em conta né a partir desse
momento você é o responsável Então
pessoal aquela regra máxima do futebol
time que tá ganhando a gente não mexe
estou aqui usando a versão 2.9
2.0.9 Tá ok para você pessoal fica com
ela que que você tem que fazer fazer um
ambiente de teste nesse ambiente de
teste você faz um update nesse ambiente
de teste você simula o uso da ferramenta
o ideal mesmo eu que sou do perfil um
pouco mais medroso de fazer update de de
sistema tem uma frase né pessoal que
fala que cachorro que é mordido por
cobra tem medo de barbante né entendeu
eu tive 8 anos uma Empresa SAS né Então
realmente a gente Segurou o máximo
possível eu ainda seguro o máximo
possível qualquer tipo de upgrade porque
eu prefiro o cliente feliz com pouco do
que eu tentar oferecer mais para ele e
aí eu vou ficar triste porque eu que vou
ter que sofrer depois arrumando as
coisas resolvendo problemas que não
tinha motivo nenhum para est
ali Então pessoal quando sai uma versão
nova de qualquer sistema e vocês vão ver
que a Evolution ela ela envolve diversos
sistemas vocês têm que ficar muito
atento lembrar que o open sce quem tá
nesse universo Open sece você monta Lego
você tem vários botões igual aqui ó você
tem vários botões ali ó um você apertou
errado meu irmão parou tudo então vocês
vão ver que a Evolution tem não só
Evolution mas tem um banco de dados tem
um Cash tem um Storage tem ferramentas
Integradas a ela é um ecossistema
Qualquer mudança qualquer variação nesse
ecossistema tende a parar o tudo então
pessoal não é colocar medo em vocês não
tá É que eu queria que desde a primeira
aula do curso vocês tivessem essa noção
você não precisa de um novo recurso não
tem motivo para você atualizar você
precisa de um novo recurso você primeiro
vai testar ele Luiz eu preciso lá da
Integração com Cent que eu sugeri para
ele eu quero muito utilizar inclusive já
estou utilizando mas nesse caso eu
precisava mesmo eu queria mesmo uma
noção daqui a pouquinho eu vou explicar
PR vocês o que que faz isso nesse caso
pessoal separa põe de lado uma
instalação Zinha a parte que você tem
aqui aquele número telefone que você não
usa você f Ah se der problema não deru
problema em nada demais não roda vê se
simula envia imagem envia áudio envia o
áudio vê se o áudio foi como áudio vê
que a imagem foi como como imagem vê se
o texto formatou certinho recebe os web
Hook vê se o Web Hook veio certinho para
você manda lá para ia Vê se a ia
respondeu certinho para você fez tudo
luí ó testei cara fiz uma minha
simulação aqui ó inclusive pessoal
recomendo você que quer ser quer andar
um nível à frente dos outros aí tem um
ambiente mesmo de teste uma uma
instalação completa de teste você vai
pagar 10 no VPS para você poder ter isso
daí mas ali é um lugar que você sabe
muito bem que ele é feito para teste tem
diversos workflows no HN que é para
teste com os nodos que você mais usa uma
Evolution que envia todo tipo de
mensagem um workflow pessoal um botão
que você aperta você envia um áudio você
envia um texto para você mesmo só para
ver se se veio certo ah Luiz Nessa
versão aqui o áudio não aparece no chat
ut você vai saber disso depois que você
pôs na mão do cliente você tem que saber
disso antes para não colocar isso na mão
do cliente Essa é é ali que a gente
divide a galera quem trabalha mesmo e
quer ter uma estabilidade quer ter um
crescimento e quer dormir em paz do cara
pessoal que é bombeiro só paga fogo o
dia inteirinho não pode dormir não pode
ter final de semana não tem feriado não
tem Sábado à Noite não faz nada porque
qualquer momento alguém vai aparecer ali
por quê Porque sexta-feira à noite o
marval tá fazendo update de sistema
Então pessoal ficar atento eu queria que
desde o começo vocês ficassem já com
esse alerta porque eu sou eu sou muito
rigoroso no no até no suporte nos
comentários nas AAS cara não é para
fazer esses dias aparece o rapaz Luiz
atualizei pra versão Beta falei por qu
Por que que você foi pra versão Beta né
ass você tem que entender que é um
negócio que não tá pronto você tem que
esperar o ruim dali você não tem que
esperar que vai dar bom você tem que
esperar que vai dar ruim porque não tá
pronto
né pessoal então acho que é importante a
gente desde o comecinho colocar esses
pingos no zos porque olha do suporte ali
pessoal tranquilamente 50% dos problemas
que eu tenho de suporte São pessoas que
foram atualizar alo que elas não
precisavam atualizar elas não escolheram
o melhor momento para atualizar e elas
não tinham a menor ideia do que tava
fazendo esse aí pessoal ó é o assim
metade do suporte C A Sarinha fica ali
eu fico com c Sarinha também ali metade
é isso né a gente resolvendo problema
que não era para não era para existir né
então é um dia de tarde é no fim do dia
é no final de semana é no feriado que
você quer fazer isso aí com a chance de
dar problema e você atrapalha o seu
final de semana atrapalha o seu feriado
o cliente fica irritado né tem esses
dias mesmo rapaz falou luí é véspera de
lançamento falei cara é vésper de
lançamento você não mexe no servidor
você você põ um Santos pedito em cima
dele e reza todo dia Santos pedito pelo
amor de Deus as causas impossíveis lá
para não para não travar você não tem
que atualizar as coisas na Vesta do
lançamento você tem que usar estável o
máximo possível Depois que passar o
lançamento Você vai testar e pode ser
que você nem atualize de acordo com que
deu no seu teste Então pessoal a partir
de agora a Evolution ela vai pedir para
você isso porque vocês vão ver que é um
lego ali são várias conexões várias
ferramentas inclusas nesse ecossistema
de automação para WhatsApp e que vocês
têm que ter um controle É lucrativo
demais pessoal é interessante demais
possibilidades a gente fala parece parce
que até jargão mas é infinito mesmo o
que você consegue fazer ali pessoal cada
dia S um tutorial novo no YouTube cada
dia alguém tem uma ideia nova é
realmente um mercado é um Ocean Azul
Você tá navegando ali ó maravilhosamente
não tem uma ondinha para atrapalhar você
só que existe essa responsabilidade de
você gerenciar bem a ferramenta tá gente
então acho que é importante a gente ter
esse papo com vocês aqui tá Então sempre
que vocês forem fazer algum tipo ah luí
eu queria atualizar pensa pensa para
vocês é melhor pro cliente tem vantagem
se não tem pessoal ou se você não
conseguiu achar uma boa vantagem não
atualiza não quer dizer que você não tem
que nunca utilizar aquela versão quer
dizer que você tem que pensar duas três
vezes e algo tem que te convencer a
fazer aquilo porque pode ser que tem um
problema ali tá bom então acho que é
importante a gente ter essa noção
pessoal desde o começo porque tudo vai
ser assim todo ferramentas são assim
pessoal então saindo um pouco da
aplicação aqui eu só queria falar sobre
isso com vocês mesmo né que o código Tá
disponível no github isso aqui pessoal é
muito bom muito bom porque quebra muita
objeção Tem gente que fala assim ah mas
a pessoa tá vendo que tá passando no meu
WhatsApp ele tá vendo o que que eu tô
mandando aliha porque manda pessoal ele
vai olhar o aparelho inteiro concorda
Então realmente quem tem essa dúvida
quem tem essa precaução não tá errado a
vantagem é você aí pode vistoriar o
código se é um cliente um pouco mais
rigoroso você pode dizer olha os seus
deves podem homologar o código cansei
pessoal de vender instalação do mtic que
eu só consegui vender com a instalação
principalmente pro povo de fintec aí
porque os dvis lá tinha alguém lá que
vinha pro código e homologa o código ele
falava olha rastreamos aqui olhamos tudo
realmente ninguém envia nada para lugar
nenhum ali tá então isso é um ponto
muito M positivo muito positivo né E o
versionamento pessoal de novo tá para
ser repetitivo mesmo que isso aqui é um
tipo de coisa que não dá para acontecer
não dá para aceitar mais vocês não estão
mais no nível do usuário né Vocês estão
no nível do devops aqui de quem mantém
quem mantém pessoal não pode ser Júnior
de de atualizar o negócio dessa maneira
e falar assim ó luí Putz cara quebrou
mas quebrou na sexta-feira à tarde
quebrou no no sábado à noite quebrou
quebrou 10 minutos depois que saiu
estranho né
Por que que não quebrou ontem né Que que
foi quebrar agora Então pessoal ficar um
pouco atento tá
eh eu gosto muito de pensar assim né
Vocês TM a oportunidade eh ainda mais
vocês quem é mais novo né tem a
oportunidade de ter um negócio muito bom
pela frente muito próp na frente mas vai
exigir de vocês isso daqui vai exigir de
vocês que vocês tenham cuidado eu sou um
exemplo disso né com Tod hum dado né
pessoal 8 anos de powertic gerenciamos
mais de 400 mtic né e a gente tinha
muito muito pouco problema por quê
Porque a gente era precavido eu não ia
no no vida louca o modo vida louca
pessoal nem todo mundo gosta tá então é
é bom até você alinhar isso até com o
seu cliente olha cliente seguinte vai
aparecer uma mensagem de update que tem
sistema que mostra né mensagem de update
mas eu Primeiro vou homologar a versão
primeiro eu vou fazer a minha bateria de
teste aí sim eu vou pôr para você
pessoal Isso aí é um item essa curadoria
que você faz é um item que você põe no
contrato você cobra por isso
porque você oferece para aquele cliente
aquela aquela estabilidade aquela
segurança de que você está testando algo
antes de Aqua ali pra mão dele porque
ele não tem esse conhecimento mas você
tem então é uma maneira até que você tem
de de monetizar aí tá E aí pessoal a
gente cai aqui no
ponto principal eu acho que é a
infraestrutura da Evolution Aqui é onde
a gente teve o maior número de mudanças
e não são não são poucas mudanças tá
pessoal e e eu queria conversar um pouco
mais com vocês aí por isso que eu pedi
para vocês né Pega um papelzinho aí ó
anota qualquer dúvida que você ten aí e
leva lá pra comunidade para poder bater
um papo sobre isso seguinte pessoal a
Evolution na versão 1 em teoria ela era
um sistema que ela por si só se mantinha
então eu tinha lá um único contêiner eu
poderia ter um único contêiner ali
dentro Eu não precisava de um banco de
dados eu não precisava de Cash eu ia I
salvar os arquivos ali mesmo então meio
que eu conseguia rodar e até mesmo assim
as primeiras versões da Evolution e até
antes de vir a versão docker dele era
literalmente só Evolution ela ela por si
só se mantinha isso é bom isso é ruim
isso é bom pessoal porque quanto menor o
sistema é é melhor para lidar com ele né
Isso é ruim porque você dá muitos papéis
muitas responsabilidades para um sistema
só então até pro Davidson poder evoluir
a ferramenta poder crescer a ferramenta
poder fazer ela ela evoluir mesmo né
perdão um trocadilho aí do Evolution né
fazer Evolution evoluir
e precisou pessoal quebrar parte hoje em
dia o padrão de software que você você
vai trabalhar quem aqui quer ser
programador quem aqui quer trabalhar com
devops quem quer trabalhar com ti nessa
área de de ti você vai trabalhar com
microsserviços são pequenos sistemas que
fazem um papel específico Então vai ser
comum você ter um sistema que é o banco
um sistema que é o Cash um sistema que é
o Storage o outro que faz o envio de
mensagens um outro que recebe um outro
que faz fila se você for olhar no mundo
Open sce o mundo Open sce pessoal Por
incrível que pareça Ele chegou muito
tarde Nessa onda o mercado corporativo
abraçou isso aí há muitos e muitos e
muitos anos atrás né de você trabalhar
com sistemas desacoplados né mas no open
sce pessoal por tempo por escassez por
falta de mão de ó obra por uma série de
fatores demorou para chegar Mas vamos
olhar o chat já é assim o chatu já tem
lá o um não é mais um monolito ele tem
um sistema que é o web e tem um sistema
que é o backend Type bot mesma coisa tem
o builder e o viewer um sistema é o
editor que você gerencia o outro é o que
vai rodar o bot em produção o Defy mas
ainda o Defy é cheio de serviço vamos
pegar o nhn worker e
editor e web Hook Então você já começa a
reparar que o open search tá indo por
esse caminho também de quebrar a
aplicação em partes é bom porque fica
muito mais fácil para evoluir o sistema
é ruim pra gente aqui para nós é ruim
porque é mais coisas para gerenciar
quanto mais coisa você tem mais coisas
pode dar problema né gente então por
isso que eu falo para vocês que é bom é
bom estudar é bom vocês acompanharem
essa evolução aí porque é um é um rumo
do mercado não sou eu que isso não é a
gente quer isso daí quem fez isso aí éo
mercado e ele que dita música A gente só
dança ela né pessoal então é assim que
funciona na Evolution hoje pessoal vocês
vão ter aqui ó como obrigatório é bom
frisar isso aqui ó tem que ter a
Evolution em si eu tenho que ter um
redis e eu tenho que ter um
postgis Então são três sistemas que são
obrigatórios de novo sem o rs e Sem o
postgis sem Evolution tá gente só que
qual que é o problema aqui na hora que
você for planejar a sua infra e aqui no
curso pessoal eu vou fazer essa mudança
ao longo do tempo aqui porque de novo né
o mercado dita regra né eu tenho que
entender como é que como é que que
caminho que as ferramentas estão tão
tendo para poder trazer para vocês essas
mudanças também tá então vamos pegar
aqui ó a Evolution ela precisa de um
postgis para salvar os dados dela e ela
precisa de um Heads para poder fazer
cche desses dados que geralmente estão
no post gre e por que isso pessoal
porque pra aplicação ficar o tempo
inteiro pedindo informação pro banco ela
acaba sobrecarregando o banco só para
fazer uma querinha pegar um campo só
específico Então você põe um cche no
meio né para fazer o qu como é que
funciona geralmente funciona essas
integrações De Cash pegar o exemplo da
Evolution outros frameworks faz a mesma
coisa
a Evolution Pede uma informação ela pede
essa informação pra camada de Cash a
camada de Cash ela vai falar assim Opa
eu tenho essa informação aqui ela já
devolve para Evolution então o caminho
foi muito mais curto se a Evolution
fizer isso aí 10 20 30 vezes por segundo
Ok mecanismo de Cash é para aguentar uma
carga gigantesca mesmo vamos pegar o RS
o RS na versão 7 pessoal ele aguenta por
padrão uma instalação simples dele
10.000 conexões concorrentes para você
ter uma noção do que que é o mecanismo
de
Cash o problema tá no banco banco já não
é assim banco já é outra história né
tanto é que quando você tem uma
aplicação um um um uso que vai fazer um
uso muito grande de banco geralmente
você põe um prox no meio o my Kell tem
lá o prox dele o post grd tem o prox
dele também no caso aqui pessoal o Cash
esse Cash ele acaba sendo uma espécie de
prox pro banco também porque porque se
evolution pedi uma informação e o Cash
não tem aí o Cash vai no banco e pega
pegou essa informação ele salva no cash
e devolva Evolution na próxima chamada
Opa me dá de novo Aqua informação lá ah
tá tá aqui comigo já Então pessoal é uma
economia que tem é uma sequência enorme
de requisições pro Cash para que tenha
uma sequência menor de requisições pro
banco Esse é o sonho de todo programador
Esse é o sonho de todo mundo fazer a
coisa acontecer assim né para que para
que você dependa o mínimo possível do
seu banco ser em tempo real pra leitura
PR escrita PR atualização não tem como
ser mas pelo menos pra leitura seria o
ideal então a Evolution ela usa dessa
maneira aí então aqui pessoal ao invés
de ficar o tempo inteiro chamando post
gree ele vai ficar o tempo inteiro
chamando rs por isso que vocês têm que
ter um cuidado muito grande com esses
dois aqui o que que eu vou sugerir no
curso para vocês a partir de agora tá o
que que eu vou passar para para vocês
como como ideia para que quando eu for
montar um stack des dessa formação aqui
ó formação de automação de WhatsApp a
gente consiga ter recursos específicos
para algumas
ferramentas dependendo da carga de uso
então o que que eu quero dizer com isso
você vai ter uma Evolution simples que
que é uma Evolution simples é uma
Instância conectada numa Evolution para
um cliente tá só para aquele cliente ali
e que não é assim luí cara o cara recebe
mensagem sei lá 10 15 por minuto Esse é
um uso grande mas aquele cara no meu
caso aqui pessoal eu recebo o qu 20 30
mensagens por dia né no no no
atendimento aqui não é um uso grande
nesse cenário pessoal tudo bem eu ter um
Heads só para servir todo mundo porque é
muito pouca carga mesmo muito pouca
carga agora se no começo você já pensou
assim ó luí é o seguinte cara o cliente
ele vai experimentar a ferramenta se ele
bate o
martelo eu precisar vai ser uma carga
grande de trabalho Talvez uma Evolution
minha tenha 10 instâncias 15 instâncias
por exemplo Talvez eu tenha qu C cai de
entrada no
chat aí pessal eu já recomendo que vocês
coloquem um redis para cada um Luiz quer
dizer que o Reds não aguenta não é isso
pessoal é uma questão estratégica não é
técnica se esse R dis para para todo
mundo entendeu então não é que ele não
aguenta é que eu vou usar uma outra
frase que também é muito batida que é
assim ó não coloque todos os ovos na
mesma cesta né minha avó falava muito
isso para mim quando eu era criança quer
dizer o qu se esse R dis paraar para o
meu NN para o meu shat wot para o Type
bot para o Defy para Evolution de
um
Heads Então se é uma operação simples
uma operação bem simples Eu acho Ok ter
um head Só se você for usar um
instalador qualquer um que seja você vai
ter um head só instalador é feito para
você ter uma uma Instância só das
Ferramentas na
máquina a gente par desse princípio
então ali pessoal eu acho Ok ter um head
só Luiz eu fazer um setup aqui cara eu V
três clientes nesse meu setup dois
clientes diferentes empresas diferentes
no mesmo servidor aí pessoal cada um com
seu Heads tá porque eu não quero que o
cliente a quebre o rs e o cliente B
fique fora do ar não pode acontecer isso
então não é uma questão técnica é uma
questão estratégica vai ter um cliente
só um sistema só e não é muito carregado
beleza ão um R só Luiz carregou bastante
é um sistema que é um cliente só mas ele
vai tacar o terror então o RS para cada
um Luiz é um servidor peguei aqui um
dedicado na contáb peguei um dedicado na
rner peguei uma máquina boa aqui uma
máquina muito boa mesmo eu vou colocar
cinco clientes diferentes lá dentro aí
pessoal aí é RS para cada um mesmo assim
sem a menor chance de compartilhar isso
daí tá então que fique bem claro pessoal
não é uma questão técnica tá Não é
questão de Ah o Reds não aguenta é que
estrategicamente não é viável e vocês
vão ter que pensar muito
estrategicamente na Evolution agora
porque são muitos muitos pontos para
ligar tá e a mesma regra pessoal eu
colocaria aqui pro post G né então você
vai lidar com post GR Olha só o post gre
ele vai ser o banco da Evolution do
chatot do Type bot do Defy do
nhn assim você vai colocando vai
colocando ferramento lá dentro ele
aguenta pessoal você pode ter certeza
que ele aguenta aguenta aguenta muito
bem no setup padrão do curso nosso aqui
eh tem lá a opção que você pode colocar
até 200 conexões simultâneas no mesmo
post Gris a ressalva que eu faço para
vocês não é não é se ele aguenta é que
estrategicamente compensa
estrategicamente convé com a mesma regra
do Reds se esse post gri para por
qualquer motivo todo mundo para então eu
não posso que um derrube todos não posso
que um ovo podre empodere toda minha
cesta né então é important isso pessoal
é estratégia vocês vão ter que pensar né
por isso que ali na na comunidade tem lá
o fórum tem o bate-papo né Agora toda
quinta-feira a gente vai bater um papo
disso com vocês vai ser um papo
estratégico não é um suport é um papo PR
gente bater papo para poder marar na
cabeça de vocês esses tópicos até que
vocês assimilem bem isso daí pensar
estrategicamente tá tudo que for fácil
pessoal na frente quebra é assim sempre
foi assim tudo vai ser assim vocês tem
que ir pro caminho que é mais seguro
mais estável para que dê o mesmo
trabalho para você né e a gente ganha
dinheiro em cima disso enquanto menos a
gente trabalha né pessoal então aqui
pessoal ó pensar estrategicamente aqui
no curso a gente vai usar um só porque é
um cenário simples que eu vou mostrar
para vocês mas ó luí quero montar uma
Evolution SAS beleza um Heads só para
Evolution um post gree só para Evolution
luí eu vou ter um servidor uma máquina
dedicada um para cara também se você tem
recurso pessoal se tem máquina é
disponível Separa mais as ferramentas
fica melhor você fica mais independente
tá agora se você tem menos recursos a
máquina menor pouca memória pouca CPU aí
tudo bem você aglomerar mais você
compartilhar mais um RS um post Gris
entreas ferramentas a regra básica é
essa é uma situação pequena usa um é uma
situação que que é grande ou pode ser
grande Usa usa a mais é bem simples
pessoal bem simples e banco não pode ser
replicado lá no curso de docker vocês
aprenderam a replicar sistema o banco
não pode ser replicado então eu tenho
que realmente ter instalações
Independentes tá é bem simples A
regrinha tá gente mas tenha isso em
mente quando for pegar um cliente na
conversa lá lá no no briefing do cliente
já pergunta Fulano José você acha que
vai você acha que tem bastante mensagem
no seu Whatsapp deixa dar uma olhadinha
aí você já viu lá que o cara tem 100 200
mensagens por dia pessoal Ó você já fala
Opa eu já vou pegar uma máquinazinha com
um pouco mais de memória porque eu vou
usar um eu vou separar os Heads aqui
cada um vai ter o seu tá então acho que
é importante Pessoal vocês pensarem um
pouco mais aqui gastar um pouco mais de
tempo aqui porque vai ser um um tempo
muito bem investido não é não diria nem
gasto vai ser um tempo muito bem
investido tá E aqui pessoal a gente
fecha o que é obrigatório então é
obrigatório ter um Heads É obrigatório
ter um post gree e esse cara aqui já
entra o que é opcional eu pus como
opcional pessoal porque ele realmente
vamos dizer assim eu vou até colocar ele
para baixo aqui ó eu Fica tranquilo que
eu vou compartilhar o mapa com vocês tá
esse mapa mental com vocês aqui mas é
assim pessoal ele é
opcional aqui porque realmente eu posso
viver sem usar Evolution perfeitamente e
nunca na minha vida utilizar um web Hook
utilizar nada desse tipo ou um Rabbit ou
uma fila tudo bem eu eu trabalhar assim
né já no universo do NN a gente tem que
ter esse RT pessoal ele tá ele tá aqui
muito mais pelo NN que também tá aqui do
que pela Evolution Mas você pode
conectar um Rabbit na Evolution então se
você precisa por exemplo conectar o
WhatsApp para poder buscar estatística
maravilhoso pessoal pela Evolution você
recebe ali pelo habbit ou pelo webhook
quem tá online quem quem escreveu quem
entrou quem saiu de um grupo quem entrou
num grupo você sabe tudo se a pessoa
mandou o áudio se a pessoa respondeu se
a pessoa mandou uma mensagem nova você
sabe tudo se a pessoa falou primeira vez
com
você então tudo isso pode virar
estatística você põe numa fila para
poder processar essa informação depois
Então nesse cenário eu acho Ok utilizar
o habbit tá então só que o habbit
pessoal ele já é um sistema um pouco
diferente desses caras aqui né porque
ele já é bruto mesmo o Rabbit aguenta
uma carga enorme gigante de trabalho tá
então e não precisaria ter um rabit Para
cada tá um rabit Só dá conta de
funcionar até porque dentro do habbit
você tem um tem um esquema de virtual
hosts né Você pode dentro dele
logicamente de maneira lógica quebrar a
instalação em partes né então aqui
pessoal ó poderia ter um rit só então no
curso a gente vai usar eu tenho que
mostrar para vocês como é que funciona
mas já adiant pessoal que ele é bem
opicional mesmo é melhor você pensar bem
antes de instalar ele com Evolution por
exemplo porque pode ser que você não use
e já peguei inúmeros casos de gente aqui
com problema no servidor ocupando espaço
porque tá usando rit tá alimentando o
rit mas não usa para nada então não é
porque tem opção que você vai utilizar
você tem que pensar será que eu preciso
preciso então eu vou instalar não
preciso a ferramenta funciona
normalmente sem ele tá e tem isso aqui
pessoal é novo né isso aqui entrou na
última versão que é o Centre que é o
seguinte pessoal toda a aplicação Toda
Toda qualquer aplicação é é claro que no
open sece é mais difícil tá mas no mundo
corporativo é 100% dos casos você
precisa observar a aplicação Então até
Então nesse nosso Open search de novo
ele peca muito nisso mas até então eu
observo o servidor então no curso que
tem um curso de monitoramento você
observa o servidor você ol CPU memória
disco rede você fica ali monitorando o
servidor só que isso aí pessoal é é pro
cara da infra cara da infra tem que
saber essa casa que eu que eu montei tá
suportando as pessoas que moram aqui
então esse cara é assim ele ele ele
gerencia infra tá o Centre gerencia a
aplicação é como se fosse um médico
monitorando as pessoas que moram naquela
casa então o cara da inf tem que
monitorar o prédio a casa e o médico tem
que vir aqui monitorar as pessoas as
pessoas tem que viver bem e e a casa tem
que comportar elas então com Centre eu
consigo saber né os erros da aplicação
ele envia lá para painel dele que tem um
painel maravilhoso com ia a conta free
dele atende muito bem a gente Mas é para
uma situação pessoal para quem realmente
quer acompanhar fazer um pente Fino na
aplicação Então sempre que der um erro
ele vai registrar o erro no se entre
sempre a Evolution qualquer tipo de
exceção que der ali ele vai salvar lá
para quem quer isso daqui para todo
mundo não é só para quem quer fazer esse
grau de acompanhamento quem quer ter
esse nível de acompanhamento da
aplicação Será que gerou erro gerou erro
por quê gerou erro em qual linha gerou
erro em qual mensagem gerou erro em qual
hora será que foi um erro na aplicação
ou for um erro numa integração Será que
não for o WhatsApp tá fora do ar erro
pessoal tem tudo isso a gente chama de
Tracey né junto do erro tem o Tracey o
Tracey é o ambiente inteiro dele ali
então assim pessoal com a com o Cent eu
consigo ter essa noção de tudo que gerar
de exceção tudo que gerar de erro na
aplicação Então não é Um item para 90%
das pessoas se preocuparem mas se você é
daquele fala assim luí a minha presa a
minha agência a minha
ência ela trabalha exclusivamente com
Evolution o meu dia cara é Enviar
mensagem de WhatsApp o meu dia é montar
bot o meu dia é trabalhar nesse mercado
aí eu já acho que compensa você investir
um pouco mais de tempo aprender isso
daqui no curso agora a gente vai
configurar né mas nesse ao longo desse
curso S vou configurar mas tem muita
coisa para você aprender ali Por que
pessoal porque ajuda você entender
melhor a aplicação e também entender
melhor o erro eh é um erro da Evolution
ou é um erro da minha infra ou é um erro
da maneira que eu uso então o erro
pessoal no mundo programação ele ensina
muito a gente a gente dá muito valor pro
erro tanto é que existe todas essas
ferramentas aí que trabalham
exclusivamente analisando erro né porque
através do erro que eu cresço que eu
vejo que é um bug para poder reportar um
bug ou se eu vejo se o bug é do lado de
cá né Será que foi um bug que eu causei
Então pessoal de novo pra grande maioria
não precisa se preocupar com Centre não
precisa tá mas entendo que agora tem eu
queria pessoal que todos tivessem o n
também tem um Cent só que é interno não
é uma uma variável de ambiente que eu
mudo e configuro para mim deveria nossa
como como queria ter um cent no nend
também mas se toda aplicação fizesse
isso daqui ia ajudar muita gente muito
muito muito muita gente porque eu
poderia ter esse log de erro né sem ter
que ir no portainer olhá o logo di da
aplicação lá no painer que é horrível de
fazer mas eu ten um painel para poder
acompanhar ou ver estatísticas eu poder
observar o código Quais são os
parâmetros que naquela mensagem o que
que tinha ali que deu problema é um
estouro de memória eu esperei eu esperei
x de de memória e veio mais e faltou
memória tem uma série de fatores pessoal
que ajuda o programador a entender isso
daqui então isso aqui ó é muito
bem-vindo muito bem-vindo mesmo tá Então
pessoal ó recapitulando vive bem sem o
Rabbit tá você só vai rodar o Rabbit se
você precisar mesmo tá e de adianta para
você que a maioria das pessoas não
precisa do Rabbit e também tem um Cent
que vai ajudar você a a acompanhar e
problemas na aplicação a gente até
brinca né no mundo desenvolvimento se tá
dando certo não olha né a gente brinca
né o Center também pode monitorar a
aplicação rodando normalmente mas
ficaria muito caro rodar ele assim
geralmente você quer saber dos dos
problemas eu quero saber quando é o
problema quero registrar o problema data
hora quem foi que fez aquilo eu uso aqui
pessoal do na promov web né laravel
quando ele envia para mim um erro para
lá ele mostra tudo quem é para logado
Qual foi data e hora que fez isso
navegador da pessoa ele pega alumas
informações e fala assim ó porque pode
ser que seja um problema no Chrome e não
no Firefox então isso aí tudo isso aí
pessoal são informações que eu uso para
poder validar a ferramenta para poder
testar a ferramenta Nossa cara Será que
se eu fizer esse ajuste aqui não resolve
Então é legal pessoal é é é um sistema
muito muito muito bem-vindo aqui para
para Evolution e por fim pessoal nessa
parte aqui de infra eu vou ter aqui o o
recomendado que é assim ó isso aqui o
obrigatório eu não tenho Evolution tá o
recomendado pessoal é assim você quer
dormir bem você quer viver bem você quer
falar assim pô luí eu quero ficar
sossegado cara eu quero evitar boa parte
dos problemas esses dois entram na
jogada que é o seguinte pessoal eu vou
ter então um proxy e um Storage o prox
pessoal a gente vai usar aqui o Web
share né ele tem um papel de ser um
intermediador entre a sua aplicação e o
envio da mensagem Então imagina o
seguinte pessoal eh eu tenho aqui a
minha Evolution rodando no digital Ocean
ou na rner lá nos Estados
Unidos eu posso pegar essa conexão eu
poderia rodar no Brasil eu poderia só
que nem todos os provedores oferecem
máquina do Brasil já é um problema mesmo
se eles oferecessem a máquina do Brasil
a rede que vai est é uma rede de
provedor então ainda assim é um problema
que que o prox faz esses caras eles
compram aluga casa põe modem eles alugam
sala comercial e enche de de roteador
ali quer fazer o qu para que você possa
a sua Evolution lá nos Estados Unidos
vai se comunicar com esse prox no Brasil
por exemplo e esse proxy essa máquina é
vai fazer a chamada da P pro WhatsApp
então o que chega no WhatsApp não é a
origem lá da rner lá do digita Auchan o
que chega pro WhatsApp é a origem do
proxy então o WhatsApp não consegue
saber que tem esse cara aqui que tá
chamando ele ele só enxerga esse prox
chamando ele então é como se fosse na
sua casa alguém chamando a
api então o prox pessoal ele é
fundamental para uma série uma série de
sistemas uma série de sistemas usam prox
é muito comum o uso dessa ferramenta né
mas Pro universo do WhatsApp faz mais
sentido ainda tá E lembrando né do
chapeuzinho de alumínio que a gente
brinca né que tem aquela questão do IP
do ISP tudo isso daí mas mesmo assim
mesmo se não existisse Eu ainda acho que
é bom você usar um prox até para ter um
controle maior também de das suas
requisições tudo né lembrando né pessoal
que na Evolution caso você use ela sem
api oficial ela é uma API não oficial
então o uso dela na não é oficial então
eu acabo usando esses mecanismos aqui
como como proxy para tentar disfarçar
mais ainda o uso dela tá então eu
colocaria pessoal e aqui no curso a
gente vai configurar mais PR frente um
proxy como recomendado usar sempre que
possível um prox com Evolution a gente
vai criar conta lá no webs V mostrar PR
vocês como é que conecta é um negócio
que você faz em dois minutos mas eu
quero que vocês entenda a importância
dele por que tem que ter ele tá gente
porque ele faz ess essa esse ele é esse
intermediário Então não é a Evolution
direto pro WhatsApp é Evolution vindo
pro proxy do proxy vai pro WhatsApp
tá E aqui pessoal tem o que eu acho que
é um dos pontos mais críticos de todos
que é o Storage eh aqui no curso a gente
vai usar o Minion Mas eu também vou
mostrar para vocês vou gravar uma aula
extra mostrando para vocês o back Blaze
eu ia até mostrar o digital watch spaces
mas eu acho que o back Blaze vai suprir
mais para vocês a necessidade mas
qualquer um desses storages que são
compatíveis com S3 funciona que é o
seguinte pessoal a Evolution o chatu a
Evolution o Defy e o Type bot eles têm
uma característica eles são sistemas que
se expandem
constantemente então cada dia de uso do
chatot são mais mensagens
armazenadas consequentemente você vá
usando mais disco você v usando mais do
storage da sua da sua VPS então quanto
mais se conversa no chat ut mais log
mais mensagens são salvas mais espaço do
banco de dados usa na Evolution pessoal
a mesma coisa agora na Evolution você
pode salvar Praticamente tudo no banco
de dados dela né quando a gente for
configurar ela eu vou passar linha por
linha com vocês para vocês verem com
mais atenção isso daí mas que que é
importante entender pessoal agora mais
do que nunca tem que monitorar o disco
porque eu vou ter aí Evolution que pode
encher o disco eu vou ter ali o chat que
pode encher o disco eu vou ter o dy que
pode encher o disco eu vou ter o Minion
que pode encher o disco o Minio Na
verdade ele vai encher o disco né então
é importante isso pessoal mais do que
nunca ficou importante para vocês
monitorarem o disco tá e a ideia do
storage é que você e o legal dele né é
que você pode utilizar um serviço
externo para fazer isso que é a ideia
aqui do back Blazer por exemplo o
digital Faces ou Amazon S3 por quê
Porque daí como eu sei que isso aqui é
um armazenamento infinito praticamente
né Eu só vou literalmente armazenar
itens nele ele tende só a expandir o
chat útil pessoal A não ser que você
fique apagando mensagem não vejo muito
sentido nisso mas pode ser que seja esse
o seu objetivo ele ele só vai crescer o
Defy mesma coisa vai vai gerar logo vai
gerar histórico Type bot todo mundo que
fala com você no Type bot ele salva o
registro um monte de registro no banco
de dados para cada uma das respostas da
de cada um Nozinho de bot que tem lá
então pessoal às vezes eu vejo os alunos
meio assim nossa Luiz acabou o espaço em
disco
eh 3 meses depois se meses depois
pessoal é normal vai acontecer isso com
o tempo inteiro cada versão nova do
celular que sai sai com cada vez mais
espaço porque a gente é um acumulador
esses sistemas pessoal eles são 100%
acumuladores 100% acumuladores então é
normal você se se preparar você se
precaver para faltar espaço né então são
estratégias que ao longo do curso eu
passo para vocês mas aqui de início O
Que Eu Vou sugerir para vocês fazerem né
e é a sugestão mesmo é se possível nem
uso o Minion nem perde seu tempo com ele
vai pro backblaze vai pro digital watch
Space o custo Lá é muito pequeno muito
pequeno não uma hora que você fica
mexendo por mês no Minio paga e Nossa
dois duas três contas desse cara aqui
então a dica que eu dar para vocês ao
longo do curso é históri de pessoal que
é um negócio que só tende a crescer
terceiriza terceiriza porque é é fração
de centavo de dólar que você paga o giga
é baratinho e na VPS você vai ter que
ficar monitorando mantendo verificando e
você fica meu Deus do céu vou ter que
fazer um upgrade o cliente não quer
pagar o upgrade e a máquina tá acabando
o disco é é um inferno pessoal isso aqui
realmente o disco é o COC canhar de
Aquiles de VPS porque ela limita para
você memória nó dá um jeito CPU não D um
jeito disco não tem como dar jeito não
tem então é um ponto importante de de se
observar ao longo do curso quando a
gente for configurar as ferramentas
então aqui por exemplo Ó você pode pagar
uma conta só e p ali o chatul Type bot
Defy nhn e a Evolution todo mundo
salvando no mesmo Bucket você vai pagar
ali centavos por mês isso isso se for
muito
assim então de novo né pessoal tem o
Minion eu vou mostrar o min a gente vai
usar o Minion mas entenda o problema no
m no mínio não é técnico tá é
estratégico também aqui eu pago muito
pouco e viro as costas e esqueço aqui eu
não posso virar as costas aqui eu tenho
que olhar o tempo inteiro é aqui que tá
o custo é você ter que olhar o tempo
inteiro tá gente então se você pode
pagar pouquinho para não olhar é melhor
do que se você você não pagar nada ter
ficar olhando a regra a regra básica é
essa né
então aqui pessoal eu tenho essa
estrutura da Evolution né Essa estrutura
dela como um todo para vocês verem como
é que ela funciona né então eu vou ter
aqui o Reds o post Gris um proxy um
Storage um Rabbit um Cent né numa
instalação normal talvez eu não tenha o
Rabbit Talvez não tenha o Cent Mas pode
ver ó que eu tenho várias ferramentinhas
aqui ó que são extremamente necessárias
para poder rodar ferramenta Então aquela
Evolution Simplon que a gente conhecia
acabou tá gente e aqui por fim pessoal
antes da gente finalizar aqui a gravação
dessa aula são as integrações né porque
agora qu para vocês a Evolution ela não
roda sozinha a Evolution sozinha pessoal
não faz sentido eu preciso de algo
acionando ela ela é um intermediário ela
não é o fim né então eu tenho WhatsApp
eu tenho Evolution e eu tenho as minhas
aplicações então aqui pessoal vai entrar
mais uma pancada de ferramenta para você
monitorar um chat ut para você poder
monitorar um chat ú tem um banco post
Gris tem um Heads e tem dois serviços eu
vou ter um Type bot que vai ter um banco
de dados postgis e vai ter outros dois
serviços eu vou ter um Defy que vai ter
o Defy que são dois três serviços e vou
ter mais uns três ou quatro contêiners
rodando junto com ele então aqui pessoal
cada cada integração que você for
colocar aqui do chat Type bot Defy você
vai vai acarretar para você mais
gerenciamento mais complexidade na sua
infra então quando você for negociar uma
Evolution quando você for oferecer um
contrato oferecer uma proposta para um
cliente vai ser necessário Pessoal vocês
colocarem manutenção nela e eu arrisco
Dizer para vocês pelo pelo que eu ouv né
a gente tá aí quase dois meses usando
Evolution 2 e 15 enal 15al seria um
valor bom inicial para você poder olhar
uma Evolution olhar aqui os componentes
olhar disco olhar memória olhar CPU
olhar log olhar se tá tudo funcionando
quinz analmente
Então abre né eu pus aqui né no modelo
de negócio né ó abre vaga para
manutenção né de você ser um
especialista em manter as coisas
funcionando você a sua função é
consertar sistemas e aqui pessoal
calculo por cima são 12 ou 13 serviços
que vocês vão vão ter que lidar num
servidor ou dois servidores para ter
tudo isso rodando Então não é mais uma
uma uma integração simples não é mais
uma brincadeira simples o negócio ficou
complexo pessoal e ficou bem complexo tá
aqui eu coloquei pessoal para vocês
também um pouco de modelo de negócio que
é o seguinte eu V vou falar um pouco no
curso com vocês sobre isso também como
que vocês podem oferecer serviços de
Evolution serviços pro cliente final
como que vocês podem oferecer manutenção
de Evolution para quem hospeda e
gerencia a Evolution e também pessoal
nesse modelo que tá aqui da Evolution
hoje essa Evolution 2 aqui eu acho que é
a Evolution mais Sá de
todas dá para ter um SAS legal com a
Evolution hoje você oferecendo som
somente api já dá ainda mais com a opção
de prox aqui ó e o Storage dá para você
manter um SAS legal mesmo de Evolution
quero ver quem que vai ser o primeiro a
montar isso daí né um primeiro um grande
SAS aí de Evolution SAS SAS mesmo aquele
SAS que você entra logo e tá tá lá o
usuário sempre para você poder utilizar
né porque é um mercado que
ó desse modo que tá aqui pessoal a
estrutura dela a infra dela o o o
projeto dela tá perfeito para virar um
SAS tá Evolution Então eu acho que
chegou na hora de começar a surgir esses
micr SAS né que oferecem api que
oferecem sistemas menores né mais mais
especializados né no caso da Evolution
então ficou legal mas aqui na área de
serviço e de manutenção pessoal tem
muito muita muita coisa pra gente poder
fazer aqui e que eu tenho certeza que
vocês vão ser muito criativos aí na hora
de de lid daí né Na hora de vocês
fazerem as coisas acontecerem aqui com
evolu tá gente então e antes antes de
fechar aqui o vídeo tá das integrações a
gente vai cobrir todas no curso chatwood
Type bot Open Lembrando que o único que
é externo aqui é o openi todos os outros
também são aut hospedados e também você
vai ser o responsável por lidar então de
novo pessoal deu um problema na
Evolution atrapalha o Defy deu um
problema no Type bot atrapalha o menu
seu da Evolution deu um problema no chat
útil atrapalha tudo então é importante
entender pessoal que agora nesse cenário
nesse setup que a Evolution chegou agora
e vocês vão ter muito muito muito mais
trabalho mesmo para poder lidar e isso é
bom isso não é ruim pessoal isso é bom
porque a Evolution deu um passo muito
grande pra frente né ela mudou de
prateleira pessoal mudou de prateleira
em consequência vocês que trabalham com
ela tem que também acompanhar a
prateleira vocês não podem ficar para
trás o mercado é assim pessoal o mercado
dita a regra o nhn muda Defy muda tava
todo mundo muito feliz com o flowise até
que chegou a Defy deixou todo mundo do
flowise de calça curta né e assim vai o
n daqui a pouco tem outro sistema que
vem e cresce também então é
importantíssimo tá gente importantíssimo
vocês enxergarem isso como oportunidade
enxergarem isso como Nossa Luiz legal
cara eu não tinha pensado que eu vou ter
tudo isso para manter então aqui na
minha propos que eu mando PR os clientes
eu vou colocar lá um uma uma uma Um item
lá de no orçamento que é a manutenção
recorrente uma recorrência a de
manutenção da ferramenta pessoal o cara
vai ter ia Type bot Defy chatu Ele tem
que te pagar manutenção disso n é
possível né porque vai exigir manutenção
isso aqui não funciona sozinho não se
engane tá E também assim vocês vão ver
no curso que é muito fácil instalar o
problema é manter o problema é é
conectar e fazer as conexões funcionarem
é vocês fazerem o monitoramento das
Ferramentas tá porque vai exigir pessoal
não tem como não não exigir tá então o
objetivo da aula aqui é só passar com
vocês mesmos eh mesmo essa questão da
Evolution os os principais pontos dela
os módulos dela as possibilidades dela e
deixa bem claro para vocês aqui já na
primeira aula no primeiro vídeo do curso
tá que vocês não vão poder virar para
trás e falar assim ah deixei rodando lá
kolus pessoal não vai colar mais isso
daí vocês vão ter que gerenciar do mesmo
mod que Gerencia um nhn do mesmo mod que
já gerenciava um chatot uma um Type bot
ali né o dfy também é bem chatinho de
gerenciar
então ficou mais complexo eu gosto né A
minha área então eu fico muito feliz de
ter possibilidade de gravar curso disso
mas eu sei que para vocês aí que tem 10
15 clientes é uma dor de cabeça muito
grande gerenciar isso aí e fico alerta
para vocês deixarem sempre no contrato
de vocês sempre essa cláusula falando
assim olha vai ter manutenção recorrente
mesmo que o cliente cancele com você ele
vai ter que achar outro para manter para
fazer manutenção pessoal porque não dá
para colocar isso aí pro cliente
oferecer para ele como algo que se
sustenta ele não se sustenta ele precisa
de acompanhamento pessoal obrigado pela
aula aí hein valeu até aula