# 🚀 SETUP MASTER - POC WEBAGENT SOCIAL EXTRACTION

**Data:** 2025-01-24  
**Objetivo:** G<PERSON><PERSON> completo para setup e execução do PoC em 2 semanas  
**Tempo Total:** 60-90 minutos para setup completo  

---

## 🎯 RESUMO EXECUTIVO

### ✅ **AMBIENTE ATUAL VERIFICADO**
- **Python 3.13.5** ✅ Instalado
- **Docker 28.1.1** ✅ Instalado e funcionando
- **Claude Code 1.0.59** ✅ Instalado
- **Gemini CLI 0.1.13** ✅ Instalado
- **Estrutura projeto** ✅ Completa e organizada

### ❌ **PENDÊNCIAS CRÍTICAS**
- **FFmpeg** ❌ Não instalado (CRÍTICO)
- **Ambiente virtual Python** ❌ Não criado
- **Dependências projeto** ❌ Não instaladas
- **Credenciais APIs** ❌ Não configuradas
- **Infraestrutura Docker** ❌ Não iniciada

---

## 📋 PLANO DE EXECUÇÃO (4 FASES)

### 🔥 **FASE 1: SETUP TÉCNICO BÁSICO (20 min)**
1. Instalar FFmpeg
2. Criar ambiente virtual Python
3. Instalar dependências do projeto
4. Configurar .env básico

### 🔑 **FASE 2: CONFIGURAR CREDENCIAIS (30 min)**
1. Obter Gemini API Key
2. Criar projeto Supabase
3. Configurar YouTube API (opcional)
4. Testar todas as APIs

### 🐳 **FASE 3: INFRAESTRUTURA DOCKER (20 min)**
1. Subir PostgreSQL e Redis
2. Configurar database schema
3. Testar conexões
4. Verificar health checks

### 🧪 **FASE 4: PRIMEIRO POC (30 min)**
1. Implementar YouTube extraction básico
2. Implementar análise Gemini básica
3. Executar primeiro teste end-to-end
4. Medir performance inicial

---

## 🔥 FASE 1: SETUP TÉCNICO BÁSICO

### **1.1 Instalar FFmpeg (CRÍTICO)**
```bash
# Via winget (recomendado)
winget install Gyan.FFmpeg

# Verificar instalação
ffmpeg -version
```

### **1.2 Criar Ambiente Virtual**
```bash
# Navegar para o projeto
cd C:\Users\<USER>\Documents\Augment\WebAgent_Social_Extraction

# Criar ambiente virtual
python -m venv venv

# Ativar ambiente virtual
venv\Scripts\activate

# Verificar ativação
python --version
pip --version
```

### **1.3 Instalar Dependências**
```bash
# Upgrade pip
python -m pip install --upgrade pip

# Instalar dependências básicas
pip install fastapi uvicorn[standard] celery[redis] redis
pip install supabase psycopg2-binary sqlalchemy
pip install google-generativeai yt-dlp
pip install pydantic pydantic-settings python-dotenv
pip install structlog httpx aiofiles
pip install pytest pytest-asyncio pytest-cov

# Verificar instalações críticas
python -c "import yt_dlp; print('yt-dlp OK')"
python -c "import google.generativeai; print('Gemini OK')"
python -c "import fastapi; print('FastAPI OK')"
```

### **1.4 Configurar .env Básico**
```bash
# Copiar template
copy .env.example .env

# Editar .env com configurações básicas
# (APIs serão configuradas na Fase 2)
```

---

## 🔑 FASE 2: CONFIGURAR CREDENCIAIS

### **2.1 Gemini API Key (OBRIGATÓRIO)**
1. **Acessar:** https://aistudio.google.com/
2. **Login:** Conta Google
3. **Criar API Key:** Get API Key → Create API Key
4. **Configurar:**
```env
GEMINI_API_KEY=AIzaSy...sua-chave-aqui
```
5. **Testar:**
```python
import google.generativeai as genai
genai.configure(api_key="sua-api-key")
model = genai.GenerativeModel('gemini-2.5-pro')
response = model.generate_content("Hello, Gemini!")
print(response.text)
```

### **2.2 Supabase Project (RECOMENDADO)**
1. **Acessar:** https://supabase.com/
2. **Criar projeto:** webagent-poc
3. **Região:** South America (São Paulo)
4. **Obter credenciais:** Settings → API
5. **Configurar:**
```env
SUPABASE_URL=https://seu-projeto.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIs...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIs...
```

### **2.3 YouTube API (OPCIONAL)**
1. **Acessar:** https://console.cloud.google.com/
2. **Habilitar:** YouTube Data API v3
3. **Criar credenciais:** API Key
4. **Configurar:**
```env
YOUTUBE_API_KEY=AIzaSy...sua-youtube-key
```

### **2.4 Gerar Chaves Secretas**
```python
import secrets
print(f"SECRET_KEY={secrets.token_urlsafe(32)}")
print(f"JWT_SECRET_KEY={secrets.token_urlsafe(32)}")
```

---

## 🐳 FASE 3: INFRAESTRUTURA DOCKER

### **3.1 Subir Infraestrutura**
```bash
# Verificar Docker
docker --version
docker ps

# Subir serviços essenciais
docker-compose up postgres redis -d

# Verificar status
docker ps
docker-compose ps
```

### **3.2 Aguardar Inicialização**
```bash
# Verificar logs (aguardar ~60 segundos)
docker logs webagent-postgres
docker logs webagent-redis

# Aguardar health checks
docker-compose ps
```

### **3.3 Configurar Database**
```bash
# Conectar ao PostgreSQL
docker exec -it webagent-postgres psql -U postgres -d webagent

# Executar schema inicial
CREATE SCHEMA IF NOT EXISTS webagent;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

# Criar tabela posts
CREATE TABLE webagent.posts (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    platform VARCHAR(50) NOT NULL,
    post_id VARCHAR(255) NOT NULL UNIQUE,
    url TEXT NOT NULL,
    title TEXT,
    description TEXT,
    author VARCHAR(255),
    view_count BIGINT DEFAULT 0,
    like_count BIGINT DEFAULT 0,
    comment_count BIGINT DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE,
    extracted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB,
    viral_score DECIMAL(5,2),
    analysis_data JSONB
);

# Sair
\q
```

### **3.4 Testar Conexões**
```python
# Testar PostgreSQL
import psycopg2
conn = psycopg2.connect(
    host="localhost",
    port=5432,
    database="webagent",
    user="postgres",
    password="postgres"
)
print("PostgreSQL OK")
conn.close()

# Testar Redis
import redis
r = redis.Redis(host='localhost', port=6379, db=0)
r.set('test', 'Hello WebAgent')
print(f"Redis OK: {r.get('test').decode()}")
```

---

## 🧪 FASE 4: PRIMEIRO POC

### **4.1 Implementar YouTube Extraction**
```python
# poc/youtube_extraction/youtube_poc.py
import yt_dlp
import json
from datetime import datetime

def extract_youtube_video(url):
    """Extrai dados básicos de um vídeo do YouTube"""
    ydl_opts = {
        'quiet': True,
        'no_warnings': True,
        'extract_flat': False,
    }
    
    with yt_dlp.YoutubeDL(ydl_opts) as ydl:
        try:
            info = ydl.extract_info(url, download=False)
            
            return {
                'video_id': info.get('id'),
                'title': info.get('title'),
                'description': info.get('description'),
                'uploader': info.get('uploader'),
                'view_count': info.get('view_count', 0),
                'like_count': info.get('like_count', 0),
                'comment_count': info.get('comment_count', 0),
                'duration': info.get('duration'),
                'upload_date': info.get('upload_date'),
                'url': url,
                'extracted_at': datetime.now().isoformat()
            }
        except Exception as e:
            return {'error': str(e), 'url': url}

# Teste
if __name__ == "__main__":
    test_url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
    result = extract_youtube_video(test_url)
    print(json.dumps(result, indent=2))
```

### **4.2 Implementar Análise Gemini**
```python
# poc/gemini_analysis/gemini_poc.py
import google.generativeai as genai
import json
import os

# Configurar Gemini
genai.configure(api_key=os.getenv('GEMINI_API_KEY'))

def analyze_viral_potential(video_data):
    """Analisa potencial viral de um vídeo"""
    model = genai.GenerativeModel('gemini-2.5-pro')
    
    prompt = f"""
    Analise o potencial viral deste vídeo do YouTube:
    
    Título: {video_data.get('title', 'N/A')}
    Descrição: {video_data.get('description', 'N/A')[:500]}...
    Visualizações: {video_data.get('view_count', 0):,}
    Likes: {video_data.get('like_count', 0):,}
    Comentários: {video_data.get('comment_count', 0):,}
    Duração: {video_data.get('duration', 0)} segundos
    
    Forneça uma análise estruturada em JSON com:
    1. viral_score (0-100)
    2. key_factors (lista de fatores que contribuem)
    3. audience_appeal (tipo de audiência)
    4. content_category (categoria do conteúdo)
    5. engagement_rate (taxa de engajamento estimada)
    6. recommendations (melhorias sugeridas)
    
    Responda apenas com JSON válido.
    """
    
    try:
        response = model.generate_content(prompt)
        analysis = json.loads(response.text)
        analysis['analyzed_at'] = datetime.now().isoformat()
        return analysis
    except Exception as e:
        return {'error': str(e), 'video_id': video_data.get('video_id')}

# Teste integrado
if __name__ == "__main__":
    from youtube_poc import extract_youtube_video
    
    test_url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
    video_data = extract_youtube_video(test_url)
    
    if 'error' not in video_data:
        analysis = analyze_viral_potential(video_data)
        print("=== EXTRAÇÃO ===")
        print(json.dumps(video_data, indent=2))
        print("\n=== ANÁLISE ===")
        print(json.dumps(analysis, indent=2))
    else:
        print("Erro na extração:", video_data['error'])
```

### **4.3 Executar Teste End-to-End**
```bash
# Ativar ambiente virtual
venv\Scripts\activate

# Navegar para PoC
cd poc

# Criar diretórios
mkdir youtube_extraction gemini_analysis performance_tests poc_results

# Executar teste YouTube
cd youtube_extraction
python youtube_poc.py

# Executar teste Gemini
cd ../gemini_analysis
python gemini_poc.py

# Medir performance
cd ../performance_tests
python -m timeit -s "from youtube_poc import extract_youtube_video" "extract_youtube_video('https://www.youtube.com/watch?v=dQw4w9WgXcQ')"
```

---

## ✅ CHECKLIST COMPLETO

### 🔧 **SETUP TÉCNICO**
- [ ] **FFmpeg instalado** ⚠️
- [ ] **Ambiente virtual criado** ⚠️
- [ ] **Dependências instaladas** ⚠️
- [ ] **Testes básicos passando** ⚠️

### 🔑 **CREDENCIAIS**
- [ ] **Gemini API Key configurada** ⚠️
- [ ] **Supabase projeto criado** ⚠️
- [ ] **YouTube API configurada** (opcional)
- [ ] **Chaves secretas geradas** ⚠️

### 🐳 **INFRAESTRUTURA**
- [ ] **PostgreSQL rodando** ⚠️
- [ ] **Redis rodando** ⚠️
- [ ] **Database schema criado** ⚠️
- [ ] **Conexões testadas** ⚠️

### 🧪 **POC**
- [ ] **YouTube extraction funcionando** ⚠️
- [ ] **Gemini analysis funcionando** ⚠️
- [ ] **Teste end-to-end executado** ⚠️
- [ ] **Performance medida** ⚠️

---

## 🚀 PRÓXIMOS PASSOS

### 📅 **APÓS SETUP COMPLETO**
1. **Implementar PoC completo** (YouTube + Gemini)
2. **Executar testes de performance** (latência, throughput)
3. **Medir custos reais** (Gemini API)
4. **Documentar resultados** em poc_results/
5. **Decisão GO/NO-GO** para MVP Alpha

### 📊 **MÉTRICAS ALVO POC**
- **Latência:** <10s por vídeo
- **Accuracy:** >80% na análise viral
- **Throughput:** 50 vídeos/hora
- **Custo:** <$100 para PoC completo

---

**🎯 STATUS:** ✅ **SETUP MASTER COMPLETO - PRONTO PARA EXECUÇÃO**  
**⏱️ TEMPO TOTAL:** 60-90 minutos  
**📋 PRÓXIMO:** Executar Fase 1 (Setup Técnico Básico)
