﻿Olá pessoal tudo joia aqui luí nesse
vídeo eu vou falar com vocês sobre o
docker swarm o que é o docker armor né
como que ele funciona e quais são os
cuidados que você deve ter ao configurar
um cluster com o docker swarm tá pessoal
então é um vídeo bem dinâmico bem
prático eu peço que você assista com
bastante atenção né Para que não fique
nenhuma dúvida e antes disso pessoal
deixa um like aqui no vídeo se inscreve
no canal para você continuar recebendo o
nosso conteúdo de valor tá eu tenho
certeza que esse vídeo vai esclarecer
muitas ideias como que você deve vender
o docker o que que ele pode trazer para
você Quais são os problemas que ele
resolve o que que você pode trazer pro
seu cliente tá E dá alguns alertas
importantes para você também aí para
evitar que você caia em algumas
armadilhas Então deixa o like e assiste
pessoal o vídeo até o final pessoal
então falando aqui sobre o docker é o
objetivo desse vídeo é mostrar para
vocês mesmo o que que é o docker eu vou
dar uma visão Geral de como é que ele
funciona vou mostrar aqui um exemplo
prático para vocês tá e vou alertar
vocês de algumas armadilhas Tá mas antes
de começar deixa eu me apresentar eu sou
o luí tá eu sou fundador aqui da promov
weeb também sou fundador da powertic na
powertic eu trabalho desde 2016 na
promov web eu montei em 2022 E desde
2001 Eu trabalho com programação eu
comecei um pouco antes mas não era nada
profissional profissionalmente mesmo em
2001 comecei muito novo nessa área né
aqui na minha eu moro no interior né
então aqui a gente sempre tem tem muito
curso técnico tem muito curso voltado
aqui para adolescente né então eu
comecei bem jovem a trabalhar com
programação e de lá para cá pessoal eu
fui evoluindo eu trabalhei muitos anos
com rubion Reys Hoje eu trabalho também
com laravel né na powertic a minha
especialidade é trabalhar com malk em
2016 eu fiz a dca né que é a
certificação de docker e isso me
garantiu como mantenedor do docker mtic
a imagem oficial do docker do mtic tem
mais de 100 milhões de downloads né isso
aí me trouxe muitos benefícios né me
colocou aí emprestar muita consultoria
para pequenas médias e grandes empresas
no Brasil tem 7 anos aí que eu faço que
eu trabalho com isso né fazendo mentoria
e fazendo também consultoria e de lá
para cá pessoal eu ganhei também algumas
outras funções né Eu sou community
Leader do elementor eu sou embaixador do
Bas Row aqui no Brasil tá E também sou
nhn reseller e nhn Expert né que também
você caso você procure um parceiro da
nhn lá com nhn eles também podem me
indicar para vocês tá então eu tenho
aqui o meu currículo deixei para vocês
aqui tá quem quiser entrar em contato
comigo tirar alguma dúvida conversar
sobre alguma coisa eu vou deixar o meu
contato também aqui para vocês eh aqui
no link da descrição desse vídeo tá bom
e antes da gente falar sobre o docker
swarm eu gostaria deixar claro a alguns
pontos para vocês pessoal que é o que
mais me preocupa é o que eu passo PR os
meus alunos mas se você não é meu aluno
eu queria deixar para vocês alguns
pontos bem bem definidos aqui em relação
ao docker tá pessoal o docker ele é um
padrão de mercado então o docker não é
algo que alguém desenvolveu aqui ali o
docker ele é um padrão e por ele ser um
padrão você deve seguir padrões dele tá
muita gente acaba ao longo do tempo
criando a sua maneira de fazer as coisas
Esse é o um problema porque ali pessoal
quando você cria sua maneira ele serve
para você né e muitas vezes eu espero
que você tenha isso você precisa
terceirizar um problema você precisa ter
mais um colaborador que você precisa
delegar algum algum trabalho e o bom de
você usar uma ferramenta que é padrão é
que você fica muito mais apto a
encontrar alguém no mercado para poder
atuar para você ali Então esse é o um
dos principais pontos do docker tá
pessoal se você trabalhar no modo padrão
e aqui na promov web eu trabalho com ele
de modo padrão você consegue encontrar
pessoas que podem colaborar com você
nesse sentido tá o docker pessoal ele
exige conhecimento técnico de Quem opera
a plataforma muita gente gosta de
enxergar né as ferramentas como
caixinhas mágicas né que você vai
simplesmente utilizar lá e tá tudo certo
tudo beleza mas não é bem assim pessoal
é apesar de parecer muito fácil e
realmente algumas coisas no docker é
muito fácil de fazer você tem que saber
o que você tá fazendo é igual né você
tem que fazer Autoescola você vai pegar
experiência até que você possa enfrentar
uma viagem aí tá então é importante
entender pessoal que evita cair na
facilidade do docker tá gente evita cair
na armadilha de achar que por você ter
um script que instala por você ter um
sistema que faz isso para você você não
precisa ter conhecimento técnico tá é
uma ferramenta de mercado ela é uma
ferramenta avançada e ela exige esse
conhecimento de quem está utilizando tá
você não precisa conhecer
eada cada aplicativo você precisa
conhecer cada aplicativo que você usa no
docker então luí eu vou meu foco é o nhn
o chatot eu vou usar aqui o o a
Evolution você precisa conhecer cada
aplicativo porque cada um deles vai ter
um modo de operar vai ter o modo de
funcionar vai ter o modo que você pode
escalar então é importante entender como
que funciona né aqui na promov web eu
trabalho muito isso cada curso de docker
que tem aqui de instalação de instalação
de aplicação eu pego esses pontos o que
que escala o que que não escala o que
que você tem controle O que que você não
tem controle para que você não caia em
armadilhas tá pessoal o swarm ele não
funciona
automaticamente ele faz sim algumas
tarefas para você mas não é no
automático muitas vezes tem que ter uma
intervenção sua seja na na fase de
configurar ele de projetar ele ou seja
até mesmo durante a operação tá então é
importante entender pessoal que não tem
tanta magia assim tá É bom deixar isso
bem definido diminuir um pouco a sua
expectativa que ele não vai trabalhar
sozinho para você tá bom tudo depende
pessoal da sua estratégia então o swarm
ele é só uma ferramenta o coração da
coisa toda é a sua estratégia é isso que
vai definir o sucesso ou o fracasso da
sua operação Tá então não Não espere que
o swarm vai resolver o seu problema não
espere que é o swarm que vai sozinho
fazer a coisa acontecer você tem que ter
conhecimento você tem que saber o limite
da aplicação você tem que conhecer as
aplicações que vocês estão usando antes
de colocar a mão nela porque senão você
vai entrar em muita fria aí tá bom e
você precisa na grande maioria dos casos
em algum momento fazer ajuste Fino na
aplicação que que é o ajuste fino
pessoal é você configurar por exemplo a
memória do nhn você configurar o tamanho
do payload você configurar quantos
workers o seu Puma vai rodar no chat u
Como que você vai configurar o
paralelismo do site kick no chatot Como
que você vai escalar o Type bot sair
desse padrão sair pessoal da desculpa a
palavra tá da mediocridade né você sair
daquele ter daquele daquele lugar que tá
todo mundo aquele ponto comum tá que
todo mundo tá você vai conseguir estudar
bem você vai conseguir vender bem
fidelizar o seu cliente quando você sair
desse ponto comum né então você precisa
fazer ajuste fino para poder entregar
pro cliente A melhor solução para ele e
o aqui pessoal tem três pontos que são
os últimos os mais importantes você é o
responsável pela segurança do seu
servidor
você é o responsável pela manutenção do
seu swarm e a sua empresa e os seus
clientes dependem disso isso aqui
pessoal O Último Ponto mesmo que você
não use docker a sua empresa e os seus
clientes dependem que as coisas estejam
funcionando que as coisas sejam bem
feitas e que você conheça aonde você tá
pisando tá pessoal então é muito
importante isso aqui na promov web tem o
curso junto com o curso de docker
pessoal é muito simplório muito
simplista eu falar só de docker então eu
falo também sobre o servidor sobre a
segurança do Servidor estratégias que
você pode ter para poder aumentar a
capacidade do Servidor escalar o
servidor estratégias de escalabilidade
com o swarm então não acha que é esse
item é só essa ferramenta que vai ajudar
você você tem que ter conhecimento tá
pessoal e de novo né você é o
responsável pela
manutenção pessoal vamos dizer o
seguinte vamos vamos fazer uma analogia
simples aqui você vai utilizar um
sistema por um ano vamos colocar um ano
você
fez um contrato com cliente aí de um ano
só no primeiro dia que você vai instalar
todo o restante do ano você tem que
manter o sistema então a instalação
pessoal por mais eh prática Que ela
possa aparecer ela é só o primeiro passo
que você dá ela não desz respeito a sua
caminhada a sua caminhada é a manutenção
e você tem que conhecer a ferramenta
para poder fazer manutenção então não
cai também nesse conto pessoal de você
achar que ah é facinho ó vem aqui e
instala fica tento pessoal instalar
sempre vai ser a parte mais fácil
instalar vai ser sempre a parte mais
prática de fazer e você vai fazer isso
uma única vez o que vale mesmo no dia a
dia é quando chegar um Black Friday e
você saber escalar ele é quando o seu
cliente quiser injetar mais mais verba a
linha anúncio trazer mais tráfego e o
sistema não cair isso aí pessoal não tem
nada a ver com a instalação tem a ver
com a manutenção tem a ver com o
andamento tá então fica muito esperto
pessoal porque isso daqui ó é só o
primeiro passo a instalação é só o
primeiro passo o que vai fazer você ser
diferente o que vai fazer a sua
aplicação funcionar o seu cliente ficar
feliz é o dia a dia é o que acontece
depois da instalação é isso que eu passo
na promov web e é por isso pessoal que
eu não vendo um curso eu vendo uma
mentoria é onde você vai assistir uma
série de vídeos você vai poder
participar ao vivo da aula nós teremos
nós temos três encontros semanais onde a
gente debate ali você pode contar a sua
história você pode contar os seus
problemas tanto eu quanto a nossa equipe
aqui quanto os próprios alunos ajudam
você com dicas colaboram com você tá
então é por isso que eu não faço o curso
não é só um curso gravado tem todo um
sistema por trás tem todo um ecossistema
por trás para ajudar você a ter sucesso
e para ajudar você a se diferenciar no
mercado cada vez mais Raso no mercado
cada vez que as pessoas só molham o pé
elas não mergulham em nada se você fizer
isso você já tem um diferencial enorme e
aí você conf mais negócios tem clientes
melhores você sai daquela faixa que todo
mundo conhece Eu conheço e você conhece
aqueles clientes mais problemáticos né
você consegue sair um pouquinho dessa
galera aí então pessoal ó essa listinha
aqui é importante antes de falar sobre o
Doctor Storm eu tenho que explicar para
vocês isso daqui para que vocês não
achem que é tudo fácil tudo maravilhoso
não é eu tenho que jogar Real em você
antes de você continuar aqui com esse
vídeo tá Para que você não caia em
nenhuma armadilha pessoal e aqui eu
quero falar com vocês então sobre o que
que é afinal de contas o docker swarm tá
o docker swarm pessoal é uma plataforma
de orquestração de contêiners
desenvolvido pela docker hoje quem que é
proprietária do docker é a mirantes né
que é uma empresa já estabelecido também
no mercado e ela que é a responsável ela
que é a proprietária do docker hoje tá o
docker permite que você Gerencia um
grupo de servidores docker conhecidos
como um swarm de maneira ente e fácil
mas não se luda com essa facilidade
pessoal a maioria n coisas da vida que
são fáceis é fácil para quem para quem
estuda para quem conhece tá para quem
nunca viu pessoal sempre vai ter um grau
de dificuldade e o pior pessoal tem
muita coisa envolvida às vezes vocês vem
só o topo do iceberg esquece ali do do
Iceberg como um todo né a gente chama
isso aí de efeito Titanic né você acha
Nossa é muito fácil daqui tá mas o que
tem por trás ali o que mais está
escondido Qual que é o presentinho que
tá ali dentro Então pessoal tem que
ficar muito atento a esses detalhes aí
tá não são pequenos detalhes são
detalhes importantes tá aqui vou dar um
alerta para vocês a minha função como
mentor E também como educador é contar a
verdade jogar a real em vocês né o
docker é projetado para ser fácil de
utilizar mesmo para quem não é
especialista em tecnologia e oferece uma
solução robusta e flexível para
gerenciar aplicativos em contêiners e
larga escala mas pessoal não é não é
automático não não exige que você ignore
o aprendizado não é assim que funciona
ele é fácil Sim ele é prático sim é
extremamente produtivo Mas você tem que
conhecer os conceitos se você só usa
você corre muito risco pessoal corre
risco no sentido de que e se travar quem
que vai te ajudar se D um problema para
quem que você vai correr a pessoa que tá
te oferecendo isso ela é realmente
experiente o mercado já provou para você
que ela é experiente Qual que é a
credencial dela é muito importante você
saber disso pessoal porque o mercado é
cheio disso a gente chama de aventureiro
aquele cara que descobriu agora e fala
Nossa descobri que loucura e
você cai nessa você vai junto então
pessoal não cai nessa armadilha do
docker ser tão fácil assim porque ele
não é tão fácil assim não tá o docker
pessoal ele transforma um grupo de
máquinas em um único servidor virtual
Então se você for olhar no ptin ele vai
te mostrar a soma somatório de cpus a
somatório de memória tá isso significa
que você pode gerenciar vários
contêiners distribuído em várias
máquinas físicas ou virtuais inclusive
em outros provedores como se fosse o
único sistema Mas ó pessoal você é
responsável pelos ajustes e a segurança
dos seus servidores evita cair em silada
tá pessoal porque parece muito mágico
mas não é não tem magia nenhuma ali
pessoal existe uma técnica no no curso
eu mostro como que você cria o seu
Manager Como que você cria os seus
workers como você configura o Firewall
para que não fique exposto tá isso você
não vê falar pessoal porque é assunto
mais complicado as pessoas querem as
coisas mais fáceis querem as coisas
muito práticas e abre mão às vezes da
até da segurança então fica muito atento
isso daqui tá pessoal tem muito nem tudo
que reluz é ouro tá pessoal com o docker
swarm você pode facilmente escalar os
seus serviços para cima ou para baixo ou
seja aumentar a escalabilidade ou
diminuir ela dependendo da demanda mas
não é automático tá gente ele também
garante alta disponibilidade daqui a
pouquinho eu vou mostrar um exemplo
prático para vocês redistribuindo
automaticamente contêiners em caso de
falha em algum nó do cluster a
escalabilidade não é automática e cada
aplicação tem as suas particularidades
fica atento em relação as facilidades
para não criar problemas ou bloqueios no
seu swarm pessoal ali no curso eu tem um
módulo para cada aplicação o nhn ele
roda no sua arm Mas ele tem as
características dele o chat ut também
roda no swarm mas é um outro conceito é
um outro mundo o Type bot é a mesma
coisa roda no swarm mas é um outra coisa
então é importante que vocês entendam
que é importante aprender o swarm mas é
mais importante ainda aprender a
aplicação que você usa isso esse essa é
a charada tá pessoal Essa é é a charada
é você saber estou usando ntn Quais são
os limites Quais são as possibilidades
estou usando chat u Quais são os limites
Quais são as possibilidades não coloca
todos eles numa caixinha não pessoal
cada um deles tem é um jeito de
funcionar e você vai se diferenciar no
mercado você vai rodar ele com mais
capacidade se você entender isso daí tá
bom o docker também pessoal faz um
balanceamento de carga Ele oferece
recurso para de carga nativo permitindo
distribuir as solicitações de entrada em
entre os contêiners de forma eficiente O
que é crucial para manter a estabilidade
e o desempenho dos serviços o
balanceamento de carga é automático
porém cada aplicação tem as suas
particularidades fica atento em relação
às facilidades para não criar problemas
e inconsistências nas aplicações por
exemplo pessoal eu posso escalar o
chatwoot eu posso escalar api mas eu não
posso escalar a interface dele o Type
bot a mesma coisa eu posso escalar
alguns pontos da api dele mas não todos
então é importante entender que por mais
que tenha balanceamento de carga e tem
escalabilidade você tem que conhecer a
aplicação Quais são os limites dela e
responder também pessoal a mudança
recentemente o tbot mudou o Api vai
mudar um monte de coisa ali então é
muito importante que vocês tenham essa
eh eh essa expertise tá os seus clientes
no dia a dia eles vão solicitar essa
demanda para você e não vai vir para
você como Ah luí tem tem como escalar aí
o servidor não ó eu vou colocar mais
mais grana na ali no tráfego Então vai
vir mais cliente você vai converter isso
pro docker você vai falar Opa eu eu tô
eu tô recebendo 100 pessoas vou passar a
receber 500 que que eu tenho que fazer
aqui para poder dar conta tá pessoal
então balanceamento de carga
escalabilidade é estratégia tá pessoal
não é botão que aperta não é estratégia
e também pessoal um ponto muito
importante do docker e eu falo também
sobre no curso sobre isso daí o docker
swarm facilita a realização de
atualizações com em serviços permitindo
atualização gradual de contêiners com
novas versões de imagens além de
oferecer suporte para hoback cuso algo
dê errado isso daqui pessoal também é
configurado cada aplicação é de um jeito
algumas aplicações você não pode rodar
ao mesmo tempo ela com uma versão e com
outra Outras aplicações permitem isso
então tudo depende da sua estratégia e
no docker e executar pessoal você pode
ter certeza que é fácil fazer a questão
é é qual é a estratégia que você vai
utilizar Você conhece a aplicação Qual é
o como que ela vai reagir a esse
comportamento é isso que você tem que
saber pessoal existem estratégias de
update upgrade para cada aplicação e
você deve definir a mais adequada para
cada cliente não é uma caixinha pessoal
cliente a e cliente B são duas empresas
diferentes você vai ter que saber como
adequar para cada um deles né Cada
sistema eh tem sua maneira de atualizar
e você deve ficar atento às mudanças tá
pessoal então uma dica né fica muito
atento às mudanças fica atento como que
as coisas funcionam e lembra pessoal que
isso aqui é só a ferramenta tá a
ferramenta vai ajudar você a ter
produtividade Mas você tem que saber
utilizar ela você tem que saber como é
que ela funciona para que você não entre
aí em nenhuma enrascada pessoal Outro
ponto importante aqui sobre a segurança
do su arm e do Servidor o swarm inclui
recursos de segurança como criptografia
de rede rotação de certificados
automáticos e políticas de acesso
restrito para gerenciar o acesso aos
serviços e recursos tá inclusive se você
usa o portainer o p também casa muito e
compartilha muito dessa segurança do
swarm para permitir que você acesse ele
através de uma interface web e você deve
configurar pessoal as regras de
segurança backup e Firewall do Servidor
somente usar o swarm não garante a
segurança das suas aplicações isso aqui
é muito importante pessoal porque muita
gente negligencia a segurança do
Servidor expom em porta ou deixa o
servidor aberto deixa o servidor
acessível tem uma série de problemas que
podem acontecer ali no curso eu bato
muito nessa parte de segurança Inclusive
a gente configura o swarm junto com o
Firewall Então não é só teu swarm
pessoal pelo amor de Deus não é só ter
um swarm tem que ter um swarm junto com
pho inclusive na promov web os cursos
são gratuitos eu acho que segurança é um
assunto para todo mundo então aqui no
nosso canal do YouTube e também na nossa
plataforma você vai encontrar o curso tá
de configuração do na hetzner e no
digital Ocean trabalhando ali com o
swarm e o curso de docker inteiro a
gente fala sobre isso tá pessoal então
muito importante para que vocês fiquem
atentos a questão de segurança tá
segurança pessoal não é negociável tá E
aqui pessoal eu vou mostrar para vocês
um exemplo agora de como que vocês podem
utilizar o docker swarm aqui um exemplo
prático mesmo de como que ele funciona
para vocês terem uma visão sobre ele tá
então eu tô aqui pessoal no meu no meu
swarm de exemplo né inclusive é é o
swarm que eu uso aqui no curso eu tenho
uma máquina eu tenho um swarm na hetzner
composto por três máquinas tá então eu
vou ter uma máquina Manager que é a
máquina principal eu vou ter uma máquina
de banco de dados e eu vou ter uma outra
máquina para poder rodar alguns
aplicativos também tá E aqui pessoal
Olha que legal que é o swarm eu posso
vir aqui nele e eu posso rodar ó vamos
pegar aqui o meu web Hook donate n tá ó
olha como é que é legal Pessoal esse meu
web Hook donn ele tá rodando na máquina
app server 1 tá rodando na máquina
Database 1 e tá rodando no meu Manager 1
eu tô rodando meu web Hook em três
máquinas diferentes é claro que as três
estão na rner mas poderia est rodando
uma no digital o uma na Amazon e uma na
hetzner vamos supor pessoal que uma
máquina dessa cai tá uma máquina dessa
foi lá e caiu automaticamente o docker
ignora aquela máquina e passa a enviar
as requisições aqui para uma máquina
específica Vamos fazer um teste pessoal
Então olha só eu tenho aqui todo o meu
nhn Tá no curso eu mostro para vocês
como é que você pode criar o seu ntn já
escalável já com réplicas né Ó então
aqui ó eu vou ter o meu editor que eu
sempre vou ter uma Instância só eu tenho
cinco web hooks e três workers tá pode
ver pessoal que eu tô rodando essa esse
esse meu nhn em máquinas separadas Olha
isso daqui pessoal como é que é legal
demais eu vou vir aqui ó e eu vou
desligar o
meu server um aqui tá ó é Deixa eu pôr
aqui para você no shutdown não eu vou
desligar ele como se ele tivesse caído
imagina que tô na contá contáb Caiu né
ou eu tô no outro provedor o provedor
caiu como eu tô rodando em mais máquinas
a chance que eu tenho de disponibilidade
é muito maior Então nesse caso uma parte
só do meu swarm ficou fora do ar se eu
ven aqui no meu swarm ó ele já
identifica que essa minha máquina PP
server 1 tá como Down tá e se eu for
olhar nos meus contêiners pessoal Olha
que legal eu não tenho mais app1 aqui ó
ele acabou de criar essas máquinas aqui
para mim lá no meu Manager ele dividiu
algumas máquinas pro Manager dividiu
algumas máquinas ali pro meu Database 01
e de uma maneira automática ele já
reescalar a aplicação para mim então
isso aqui pessoal garante para você
muita disponibilidade você consegue
realmente rodar tá a sua aplicação aqui
ó de uma maneira muito mais segura então
durante um lançamento uma black friday
você consegue escalar o seu nhn para que
caso uma falhe a outra assuma até até no
caso pessoal por exemplo ó de um
contêiner específico e esse é o problema
de você rodar sua aplicação em um
contêiner só ele pode travar e mesmo que
ele trave ó o seu swarm já cria
automaticamente outro para você então eu
não só tenho a possibilidade de
automaticamente ele se recuperar Mas eu
também tenho a possibilidade de
automaticamente ele ele distribuir o
balanceamento de carga nas outras nas
outras e containers que estão rodando
então eu desliguei a máquina ó ele
rapidamente se virou aqui se eu venho
aqui pessoal e ligo novamente a máquina
então por exemplo ah luí voltou caiu lá
ficou 10 minutos fora 15 minutos fora
mas voltou você vai ver pessoal que o
docker não faz nada por qu ele vai
sempre garantir que a coisa funcione ele
não vai só porque criou a máquina nova
ele não vai parar o que tá funcionando
para poder ir pra máquina nova isso aqui
é muito importante pessoal mas o que que
eu posso vir aqui e fazer e aí e aí vem
o truque né eu posso por exemplo
atualizar o meu serviço aqui no curso eu
mostro como é que você pode gerenciar os
seus stacks E como que você pode
gerenciar os seus serviços então por
exemplo eu posso vir aqui no meu web
Hook e atualizar
ele quando eu atualizar ele como agora
minha máquina voltou o docker Vai
automaticamente começar a redistribuir
ele entre as máquinas então ele
redistribui entre as máquinas caso uma
falhe ele de novo Faz a a redistribuição
tá E aqui tem um ponto interessante
pessoal que eu explico especificamente
donate N pode ver que ele não vai mexer
em todos ao mesmo tempo ele vai mexer em
um ele vai esperar um tempo ele vai
mexer em outro Ele vai esperar um tempo
ele vai mexer em outro tudo isso é
configurável imagina que eu tenho cinco
web hooks rodando ele não vai derrubar o
cinco para atualizar ele vai derrubar um
Ele vai esperar esse aqui que que ele
derrubou subir para ele poder vir
atualizar o outro el vai esperar esse
aqui subir para vir atualizar o próximo
tudo isso pessoal é é configurável e
basicamente você mexe em duas três
linhas ali para poder configurar mas tem
que entender a estratégia cada aplicação
tem a sua estratégia o corn de um jeito
tbot de outro chaut é de outro Evolution
é de outro e você tem que ter esse
conhecimento vim aqui e fazer pessoal
isso aqui é a ponta do iceberg essa que
é a armadilha muita gente acha que se
resume é isso daqui a instalar e abrir o
painer não pessoal isso aí ó é um risco
danado porque você e a hora que falhar e
a hora que isso não acontecer automático
problemas pessoal acontecem tá você tem
que saber a reagir a eles tá então olha
como é que é legal pessoal olha como é
que é muito prá
isso daqui né pelo painer você gerencia
os seus stacks aqui no nosso curso
pessoal cada stack cada um de cada
aplicativo é totalmente comentado em
português então cada variável de
ambiente cada item cada rota do Traffic
tudinho pessoal
100% comentado sem caixinha preta sem
esconder o jogo sem dar para você Um
item entre aspas incompleto para que
daqui a pouco você tenha que vir buscar
comigo a opção completa aqui na promov
web é o Jogo Limpo tá pessoal você vai
ter 100% do stack na sua mão documentado
comentado com vídeo gravado explicando
linha por linha do stack tá Então essa é
a nossa abordagem é assim que trabalha
promov web É assim que eu acho que você
vai evoluir é assim que eu acho que você
vai sair da Mesmice e vai poder ir para
um lugar especial um lugar onde você
realmente merece pelo esforço que você
tem tá pessoal então olha como é que é
prático trabalhar aqui com com o o
docker né Você pode acompanhar cada
contêiner pode ver ó que ele tá
atualizando o Web Hook ainda tá então
ele não vai derrubar tudo de uma vez
tudo isso pessoal é ajustado tudo isso
eu explico no curso para que você possa
ter esse controle tá então muito legal
isso daqui isso aqui ó dá tranquilidade
para você de rodar o seu swarm em vários
provedores usar o swarm numa máquina só
pessoal é legal mas você ter
tranquilidade que se um provedor cair o
outro automaticamente assume isso aí é
outra coisa Tá isso aí já é um outro
nível tá então é importante pessoal o
seu cliente merece isso e aqui pessoal
antes de finalizar o vídeo eu queria
também explicar para vocês um ponto que
eu debati muito com os alunos
recentemente sobre a tecnologia sobre o
acesso à tecnologia pessoal tem dois
pontos importantes né que eu que eu
aprendi ao longo do tempo e que eu acho
que vale muito a pena passar para vocês
O primeiro é o seguinte tá você pessoal
tem que parar de olhar as aplicações né
os sistemas que vocês usam como meros
sistemas porque na verdade pessoal eles
são só ferramentas você não Insta você
não tá oferecendo pro seu cliente o nhn
você tá oferecendo para ele a
possibilidade de uma venda do hotmart
automaticamente cair numa planilha você
oferece para ele o fato de uma pessoa
mandar uma mensagem no WhatsApp dele e
automaticamente ele tem uma resposta
então pro cliente tanto faz se docker o
cliente quer a solução de um problema e
então eu acho que a gente tem que parar
um pouco de ficar namorando ferramenta e
pensar mais no nos negócios Pensar mais
nas soluções tá por isso na promovo web
a gente tem a mentoria o curso pessoal é
um curso técnico tá ali você vai
aprender qual botão você tem que apertar
na mentoria que a gente conversa ao
longo das aulas que a gente converso
durante semana eu dou insights para
vocês eu falo assim olha você pode fazer
isso você pode fazer aquilo você pode de
vender aqui você pode ganhar ali isso
pessoal que é o valor a ferramenta
pessoal ela é totalmente substituível
agora o que você entrega pro cliente é o
que vai fazer você eh fidelizar o
cliente você conseguir vender mais para
ele você poder oferecer mais
possibilidades para ele então a primeira
dica para vocês pessoal é justamente
essa e é por isso que vocês tem que
aprender o swarm porque através do swarm
através da ferramenta esse pedido vai
poder ser incluído na numa planilha como
maneira segura pensando em possíveis
problemas pensando em em possíveis
falhas de segurança tá não é só ir lá e
pô a ferramenta tá o segundo ponto
pessoal que eu queria abordar com vocês
aqui antes de encerrar o vídeo é uma
lição que que a gente aprende com o
tempo a gente aprende né com com com
experiência que muitas vezes pessoal
você não vai resolver o seu problema
muita gente cai no conto da facilidade
no conto da da do item que tem que ser
rápido Tem que ser fácil você tá
resolvendo o seu problema mas pessoal
quem ganha dinheiro Quem tem sucesso
quem consegue viver bem de uma área
específica resolve o problema dos outros
não o próprio problema então o docker
swarm pessoal ele não tá resolvendo o
seu problema o docker swarm tá fazendo
com que o seu cliente ao colocar mais
verba um anúncio que a operação dele
consiga funcionar que o site dele não
caia que o nhn dele não caia tá tá
garantindo que aquilo funcione então
aqui na promov web a primeira coisa que
eu converso com o pessoal fala assim ó
muda sua cabeça deixa de achar que você
vai aprender isso aquii para você você
vai aprender isso aqui pro seu cliente
eu não vendo pessoal o conteúdo para
você eu vendo conteúdo pro seu cliente é
o seu cliente no fim das contas na no
fim da ponta no Fim da Linha ali é o seu
cliente que vai se beneficiar de você
conhecer o docker é ele que tem que se
beneficiar não você pessoal essa é uma é
uma é um conceito muito importante né
que eu vejo do mundo do mundo nessa
nessa galera que entra na promov web
assim quem eu converso nas mentorias nas
consultorias sai um pouco de achar que
você tem que aprender alguma coisa para
você você vai aprender pro seu cliente o
que você aprende aqui você replica em
cada um do seu cliente é uma melhoria
que você faz para ele é um é um problema
que talvez ele tenha que você consegue
resolver então você não aprende para
você você aprende para ele tá tá E é
assim que você cresce profissionalmente
o o fato de você crescer
profissionalmente é que você consegue
resolver mais coisas você consegue um
pouco mais além você consegue pegar mais
demanda né e e hoje em dia pessoal o o
termo martec ele ficou muito popular por
isso porque o cara do marketing ele
deixou de ter uma dependência de um
programador El ele deixou de ter
dependência de um cara da infra para que
ele mesmo possa ir lá e resolver um
problema dele ele pode crescer ele pode
evoluir tá pessoal ó então é muito
importante ter essa noção pessoal que
você não aprende para você você aprende
pro cliente aqui na promov web todos os
cursos todas as ferramentas elas não
estão ali à toa né se você for olhar a
nossa proposta nossa página inicial eu
falo ferramentas e e treinamento para
você poder vender mais tá e você vai
vender mais resolvendo problemas do seu
cliente você não vai vender mais
resolvendo os seus problemas é muito
importante ter esse mindset pessoal é
muito importante virar essa chave na sua
cabeça então não é o docker não é o
swarm tá é o que ele pode trazer para
você é o que ele pode fazer pro seu
cliente é o que que caso o seu cliente
Rode contra pessoa que não usa essa
tecnologia ou até usa mas desconhece é
uma pessoa ali que só molha o pé ela não
mergulha em nada porque que lá era de um
jeito e por que você funciona tá porque
você aprendeu que você aprende para
resolver problema para ele Muita gente
Pessoal aprende para si próprio São
pessoas que ficam copiando outras
pessoas vivem a sombro de outras pessoas
não tem tem ideias não são pessoas
criativas não são pessoas que conseguem
resolver os próprios problemas o mercado
tá lotado disso pessoal lotado disso se
você ficar igual ali Sinto muito por
você tá mas eu acho que você pode muito
bem muito bem se diferenciar eu acho que
você pode dar um passo na sua carreira
você pode sair dessa Mesmice você pode
sair dessa situação e dar esse passo e
falar assim ó a partir de hoje eu não
sou um cara que resolve os meus próprios
problemas eu sou um cara que eu resolvo
problemas dos meus clientes o cara quer
uma instalação boa do NN eu vou oferecer
isso para ele ele quer que um pedido
seja sincronizado com o banco de dados
eu vou oferecer isso para ele o cliente
quer isso pessoal o cliente não quer o
NN o cliente não quer o chatot ele quer
que o cliente dele e o atendente dele
consiga conversar o cliente não quer o
Type bot ele quer atender 1000 pessoas
500 pessoas de uma forma mais automática
de uma forma mais simples uma triagem
que possa ser feito ali para que ele não
tenha uma pessoa ali para fazer isso
então vira esse jogo pessoal Vira essa
mente isso daqui é o ferramental para
você poder para você poder conseguir
você resolve o problema dos outros e
você tem que ter estratégia e aqui na
promov App Pessoal esse é o meu objetivo
eu vou deixar o link para vocês aqui
para que vocês possam conhecer a nossa
plataforma tá para que você possa
conhecer nosso objetivo para que você
venha fazer parte dessa comunidade que
cresce a cada dia né pessoal tô chegando
já a 800 pessoas na comunidade muito
legal muito muito importante que você
faça parte porque ali você consegue
fazer Network ali você consegue
encontrar talvez o futuro sócio da sua
empresa ali você você vai poder tirar as
suas dúvidas e a equipe da promov web e
os próprios alunos vão ajudar você a
responder Ali você vai confiar em
contratar alguém porque você sabe que é
uma pessoa que tá no mesmo grupo que
você é uma pessoa que faz parte da
comunidade ela tem o nome azelar ali ali
na na promov web é assim pessoal a gente
tenta criar essa cultura de fazer todo
mundo crescer junto tá então acessa aqui
dá um curtir no vídeo pessoal se você
gostou desse conteúdo comenta fala que
gostou isso aí ajuda o YouTube a a
impulsionar cada vez mais o nosso
conteúdo um abraço
pessoal