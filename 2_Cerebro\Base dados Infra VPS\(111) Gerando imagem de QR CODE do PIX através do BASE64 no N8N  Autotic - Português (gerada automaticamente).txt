﻿fala automatizador tudo certo bom nessa
aula aqui agora eu quero já trabalhar um
pouquinho aí com a parte prática né de
cenários reais aí do dia a dia com base
nos noes né que a gente já vem
explorando no intensivão nhn tá e dessa
vez é para mostrar para você um exemplo
prático de como você pode gerar aí o k
code né do do pix né você que trabalha
aí com get de pagamento né E você faz
todas as automações ali pelo ntn você eh
tá querendo saber como né gerar o q Code
em pix eu vou te mostrar nesse vídeo
aqui como se faz tá e a gente vai
utilizar o nosso node eh MOV binary data
né Ou seja aquele node que a gente e
mostrou exemplos né de como converter
Jon para binar e de binário PR Jon dessa
vez agora a gente vai usar ele para
poder eh gerar um binário mas por meio
de um de um código base 64 tá que
geralmente é o formato de código que vem
e o QR Code do pix né dessas plataformas
aí de pagamentos tá bom E então focando
aqui agora na tela do nosso computador
aqui com o nosso ntn aberto vamos fazer
o seguinte a gente vai criar aqui
primeiro e um um web Hook tá que vai ser
responsável por
jogar esse q code na tela tá então assim
eu não só vou só vou mostrar como você
gera esse code mas também vou te dar uma
ideia aqui de como você pode apresentar
ele para o seu usuário né para o seu
cliente enfim por meio da própria url de
web Hook do ntn Beleza então eu vou
adicionar aqui node web Hook tá colocar
aqui web
Hook
E aí aqui eu já posso colocar um nome
mais amigável né Posso botar QR Code Ok
vou deixar em get mesmo tá
eh e aqui em responde eu vou marcar
usando um node web Hook eh respond web
Hook né que é o o node que vai
determinar o que que vai ser retornado
nesse meu web Hook né quando eu acessar
o RL o que que vai aparecer vai aparecer
uma imagem vai
aparecer aliás um binário vai aparecer
um texto vai aparecer o eu determinar o
que que vai acontecer tá noo aqui eu
vedir que seja retornado um binário Tá
eu vou mostrar um pouquinho mais lá na
frente que que vai acontecer então aqui
você marca para ele retornar o que o meu
respondent web Hook vai entregar tá só
isso aí aqui eu deixo webook mesmo tá
próximo passo
a gente vai colocar aqui dois node sets
tá onde o primeiro eu vou colocar o
código base 64 né que geralmente é o
código que vem e da api né do seu getway
de
pagamento o pix né ele vai vir no
formato base 64 tá então vou colocar
aqui o nome da constante de base 64
mesmo beleza
E aí aqui no valor eu vou colocar aqui
um exemplo de código
e base 64 de um pix tá bot aqui ó vou
pesquisar aqui no
googlecode base 64 E aí você pode
acessar esse site aqui ó que é o
Web tokit online.com tá ele tem um
gerador aqui de QR Code tá você pode
clicar aqui para gerar beleza é claro é
um R code aqui fictício
e você pode apertar f12 no seu navegador
e inspecionar aqui esse q code para
poder obter aqui o o base 64 dele tá
então por exemplo quando eu clico aqui ó
ele já me joga aqui para uma nova aba e
eu posso copiar esse base4 aqui inteiro
beleza
E então já vou colar aqui
ó e ó vou
executar
Ah não esqueci que ele tá aqui
no ele tá com com web
Hook Só se eu acessar aqui a URL para
ele
poder
executar vamos fazer o seguinte deixa eu
salvar deixa eu
atualizar Vamos pro
parte ó já vou copiar
aqui
bom primeiro eu vou botar aqui para
responder imediatamente só para eu
conseguir ir gerando os output né para
você ver o que que
acontece Beleza então eu tenho aqui ó a
o base 64 completo tá E aí nesse meu
segundo node aqui
de S eu vou de fato pegar somente o
código do CR code tá sem o mim Type né e
outras informações ali de base
64 aí eu crio uma constante aqui chamado
qrcode e referencio esse meu campo aqui
base4 tá E aí que que a gente vai fazer
aqui a já vai aplicar né um JavaScript
aqui para poder tratar esses dados aqui
e para que o nosso node move binary data
possa conseguir gerar o binário ali do
base 64 eu tenho que informar apenas o
próprio código tá é isso aqui ó que vem
depois do base me4 Então você vai ter lá
o data dois pontos né o mim Type É emage
bar png E aí em seguida vou ter aqui ó
base 64 aí vou ter vírgula E aí vou ter
esse código aqui aqui essa hash gigante
aqui ó isso aqui de fato é o código né
que compõe aí o q code Então eu tenho
que pegar somente esse código e aí como
eu faço isso aqui eu posso utilizar o
método split passando aqui um
delimitador no caso delimitador seria o
quê base 64 vírgula né ou seja daqui
para trás seria o índice zero do Array
né porque o split ele vai pegar ali uma
string e vai dividir em pedal cada
pedaço é um índice Ok sendo que como eu
usando eu vou usar o delimitador base 64
o primeiro pedaço vai ser isso aqui ó tá
e o segundo pedaço que é depois do do
base 64 vai ser de fato o código do pix
Beleza então aqui ó na referência eu dou
um ponto split abro e fecho parênteses
tá E aqui dentro eu
coloco aspas né e a eu formar o quê Qual
é o delimitador base 64 vírgula beleza e
aí como eu quero pegar a segunda parte
eu vou informar índice um assim ó Ok
porque o índice zero ele pega a primeira
parte da divisão o índice um é a segunda
parte da divisão então se você for
reparar ó ele pega de fato somente o
código do pix aqui beleza então aqui eu
já tenho o meu código
pix ó já formatado mesmo agora sim a
gente vem coloca aqui um MOV binary data
né Eh desmarca essa opção aqui né para
não setar todas a os dados mas somente o
que a gente quer no caso seria o quê o o
campo CR code tá então vou botar aqui CR
code é esse campo aqui que eu quero
fazer a conversão né desse valor e a o
campo de destino eu posso deixar data
mesmo que é o nome padrão né quando gera
um binário E aí Aqui é onde entra o pulo
do gato tá muitos alunos acabam
enrolando aqui porque não sab Quais são
os parâmetros corretos que tem que
colocar para conseguir de fato gerar um
binário de uma base 64 tá então a
primeira coisa que tem que fazer é aqui
eh aqui em em mold né que é o modo você
tem que colocar Jon Tu binary tá aí como
eu falei desmarca essa opção aqui para
converter todos os dados e foca somente
no que você quer que é o qrcode ok que é
o campo q code campo de destino deixa a
data mesmo tá E agora aqui em apption
você vai selecionar as seguintes opções
eh data is base 64 ou seja dizendo que e
essa informação que está sendo recebida
aqui esse input é uma base 64 Beleza
segunda opção eh file name tá você tem
que dar um nome pro arquivo que vai ser
gerado no caso como eu quero que gere
uma imagem eu posso botar aqui ó CR
cod.png beleza Esse vai ser o nome do do
arquivo e por último tem que colocar o
mim Type né Para Dizer Que tipo de
arquivo é esse que vai ser gerado ali o
binário então eu vou colocar aqui que é
do tipo
image
Barra
pnj Beleza então se você for reparar
esse aqui ó data is base 64 e e e mês de
png é basicamente
é aquela informação que vem lá no meu
base 64 puro né ó repara que aqui ó ele
vem o mimi Type lembra que eu mostrei ó
mimi Type aqui e aqui vem dizendo que
ele é base M4 Então eu estou dizendo
aqui para esse node essas informações ó
ele é um base 64 e o mim Type dele é
esse aqui ó tá o que eu coloquei aqui e
aí eu só tô aqui informando um nome para
o binário né porque o binário tem que
ter um nome de arquivo no caso será
code.png beleza aí aqui eu posso
renomear né Posso botar aqui ó gera
o Gera a imagem
do Code
beleza
E aí por fim eu já posso de fato colocar
aqui um
responde web Hook
tá onde eu vou determinar aqui o binário
que vai ser gerado aqui seja entregue na
URL do meu web Hook então
quando o seu cliente né for acessar aqui
a URL ao invés de vir essa mensagem aqui
né workflow foi startado que é a
mensagem padrão ele vai exibir o QR Code
aqui na tela tá então é até uma forma aí
de você expor esse q code aí pro seu
cliente para ele poder ler né Eh usando
a própria URL do do Web Hook Ou seja
você não precisa ficar hospedando a
imagem do qrcode num num servidor e
depois pegar o RL lá daquele servidor
não você já Aproveita a estrutura aqui
do ntn Beleza então Então é eu vou
voltar aqui pro meu web Hook aqui em
responde né eu vou marcar essa opção que
é responde to web Hook node ok dizendo
que o que vai ser retornado aqui é o que
vai ser entregue aqui pelo meu respond
web Hook beleza e aí aqui eu vou
determinar o seguinte ó a resposta eu
quero do tipo binário certo e aqui eu
vou deixar para ele escolher
automaticamente né ou seja ele vai pegar
o binário que que vai vir aqui do meu
move binary data tá somente isso aqui
beleza então vou
salvar vamos ver se já pega aqui logo de
primeira né vou executar aqui ó ele já
entrou no modo de escuta tá vou copiar o
RL e vou tentar acessar aqui
agora pronto ó funcionou perfeitamente
Ok ou seja o meu Hook tornou aqui a
imagem do qrcode que outra hora estava
em base 104 que é o formato que vem aí
das apis do get de pagamento Então é
assim que você eh consegue automatizar
aí né a apresentação do CR code para os
seus clientes usando aí sua api do do
seu g de pagamento Tá bom então é isso
aí Espero que você tenha gostado forte
abraço e até o próximo
tutorial