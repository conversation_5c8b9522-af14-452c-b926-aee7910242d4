# MCP Emergency Cleanup - Augment Code Orchestrator V5.0
# Finaliza TODOS os processos MCP órfãos e duplicados IMEDIATAMENTE

Write-Host "=== MCP EMERGENCY CLEANUP ===" -ForegroundColor Red
Write-Host "ATENÇÃO: Este script irá finalizar TODOS os processos MCP!" -ForegroundColor Yellow
Write-Host "Pressione CTRL+C nos próximos 5 segundos para cancelar..." -ForegroundColor Yellow

for ($i = 5; $i -gt 0; $i--) {
    Write-Host "$i..." -ForegroundColor Red
    Start-Sleep 1
}

Write-Host "`nIniciando limpeza emergencial..." -ForegroundColor Cyan

# Função para finalizar processo com segurança
function Stop-ProcessSafely($processId, $processName) {
    try {
        Stop-Process -Id $processId -Force -ErrorAction Stop
        Write-Host "✓ Finalizado: $processName (PID: $processId)" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "✗ Erro ao finalizar: $processName (PID: $processId)" -ForegroundColor Red
        return $false
    }
}

# 1. FINALIZAR TODOS OS PROCESSOS NODE.JS MCP
Write-Host "`n1. FINALIZANDO PROCESSOS NODE.JS MCP" -ForegroundColor Cyan

$nodeProcesses = Get-WmiObject Win32_Process | Where-Object {
    $_.Name -eq "node.exe" -and 
    ($_.CommandLine -match "mcp" -or 
     $_.CommandLine -match "@modelcontextprotocol" -or
     $_.CommandLine -match "@21st-dev" -or
     $_.CommandLine -match "@supabase" -or
     $_.CommandLine -match "@upstash" -or
     $_.CommandLine -match "@playwright" -or
     $_.CommandLine -match "blowback-context" -or
     $_.CommandLine -match "@netlify")
}

$killedNode = 0
$savedMemoryNode = 0

foreach ($process in $nodeProcesses) {
    try {
        $proc = Get-Process -Id $process.ProcessId -ErrorAction SilentlyContinue
        if ($proc) {
            $memUsage = [math]::Round($proc.WorkingSet / 1MB, 2)
            $savedMemoryNode += $memUsage
            
            if (Stop-ProcessSafely -processId $process.ProcessId -processName "Node.js MCP") {
                $killedNode++
            }
        }
    }
    catch {
        # Processo já finalizado
    }
}

Write-Host "Node.js MCP finalizados: $killedNode processos ($savedMemoryNode MB)" -ForegroundColor Green

# 2. FINALIZAR PROCESSOS PYTHON MCP DUPLICADOS
Write-Host "`n2. FINALIZANDO PROCESSOS PYTHON MCP DUPLICADOS" -ForegroundColor Cyan

$pythonProcesses = Get-WmiObject Win32_Process | Where-Object {
    $_.Name -eq "python.exe" -and $_.CommandLine -match "main.py" -and $_.CommandLine -match "windows-mcp"
}

$killedPython = 0
$savedMemoryPython = 0

# Manter apenas 1 processo Python MCP (o primeiro)
if ($pythonProcesses.Count -gt 1) {
    for ($i = 1; $i -lt $pythonProcesses.Count; $i++) {
        try {
            $proc = Get-Process -Id $pythonProcesses[$i].ProcessId -ErrorAction SilentlyContinue
            if ($proc) {
                $memUsage = [math]::Round($proc.WorkingSet / 1MB, 2)
                $savedMemoryPython += $memUsage
                
                if (Stop-ProcessSafely -processId $pythonProcesses[$i].ProcessId -processName "Python MCP") {
                    $killedPython++
                }
            }
        }
        catch {
            # Processo já finalizado
        }
    }
}

Write-Host "Python MCP finalizados: $killedPython processos ($savedMemoryPython MB)" -ForegroundColor Green

# 3. FINALIZAR PROCESSOS UV ÓRFÃOS
Write-Host "`n3. FINALIZANDO PROCESSOS UV ÓRFÃOS" -ForegroundColor Cyan

$uvProcesses = Get-Process uv -ErrorAction SilentlyContinue
$killedUV = 0
$savedMemoryUV = 0

foreach ($process in $uvProcesses) {
    $memUsage = [math]::Round($process.WorkingSet / 1MB, 2)
    $savedMemoryUV += $memUsage
    
    if (Stop-ProcessSafely -processId $process.Id -processName "UV") {
        $killedUV++
    }
}

Write-Host "UV finalizados: $killedUV processos ($savedMemoryUV MB)" -ForegroundColor Green

# 4. FINALIZAR PROCESSOS NPX ÓRFÃOS
Write-Host "`n4. FINALIZANDO PROCESSOS NPX ÓRFÃOS" -ForegroundColor Cyan

$npxProcesses = Get-WmiObject Win32_Process | Where-Object {
    $_.Name -eq "node.exe" -and $_.CommandLine -match "npx-cli.js"
}

$killedNPX = 0
$savedMemoryNPX = 0

foreach ($process in $npxProcesses) {
    try {
        $proc = Get-Process -Id $process.ProcessId -ErrorAction SilentlyContinue
        if ($proc) {
            $memUsage = [math]::Round($proc.WorkingSet / 1MB, 2)
            $savedMemoryNPX += $memUsage
            
            if (Stop-ProcessSafely -processId $process.ProcessId -processName "NPX") {
                $killedNPX++
            }
        }
    }
    catch {
        # Processo já finalizado
    }
}

Write-Host "NPX finalizados: $killedNPX processos ($savedMemoryNPX MB)" -ForegroundColor Green

# 5. LIMPEZA DE CACHE NPM
Write-Host "`n5. LIMPANDO CACHE NPM" -ForegroundColor Cyan

try {
    npm cache clean --force 2>$null
    Write-Host "✓ Cache NPM limpo" -ForegroundColor Green
}
catch {
    Write-Host "⚠ Erro ao limpar cache NPM" -ForegroundColor Yellow
}

# 6. VERIFICAÇÃO FINAL
Write-Host "`n6. VERIFICAÇÃO FINAL" -ForegroundColor Cyan

Start-Sleep 2

$nodeAfter = (Get-Process node -ErrorAction SilentlyContinue).Count
$pythonAfter = (Get-Process python -ErrorAction SilentlyContinue).Count
$claudeAfter = (Get-Process claude -ErrorAction SilentlyContinue).Count

Write-Host "Processos restantes:" -ForegroundColor White
Write-Host "  Node.js: $nodeAfter" -ForegroundColor $(if ($nodeAfter -lt 10) { "Green" } else { "Yellow" })
Write-Host "  Python: $pythonAfter" -ForegroundColor $(if ($pythonAfter -lt 5) { "Green" } else { "Yellow" })
Write-Host "  Claude: $claudeAfter" -ForegroundColor $(if ($claudeAfter -lt 5) { "Green" } else { "Yellow" })

# 7. RELATÓRIO FINAL
Write-Host "`n=== RELATÓRIO DE LIMPEZA ===" -ForegroundColor Cyan
Write-Host "Processos finalizados:" -ForegroundColor White
Write-Host "  Node.js MCP: $killedNode" -ForegroundColor Green
Write-Host "  Python MCP: $killedPython" -ForegroundColor Green
Write-Host "  UV: $killedUV" -ForegroundColor Green
Write-Host "  NPX: $killedNPX" -ForegroundColor Green

$totalKilled = $killedNode + $killedPython + $killedUV + $killedNPX
$totalMemory = $savedMemoryNode + $savedMemoryPython + $savedMemoryUV + $savedMemoryNPX

Write-Host "`nTotal:" -ForegroundColor White
Write-Host "  Processos: $totalKilled" -ForegroundColor Green
Write-Host "  Memória liberada: $([math]::Round($totalMemory, 2)) MB" -ForegroundColor Green

# 8. RECOMENDAÇÕES
Write-Host "`n=== RECOMENDAÇÕES ===" -ForegroundColor Cyan
Write-Host "1. Reinicie VSCode e Claude Desktop" -ForegroundColor Yellow
Write-Host "2. Configure MCP apenas no VSCode (não no Claude)" -ForegroundColor Yellow
Write-Host "3. Considere instalar o MCP Daemon Centralizado:" -ForegroundColor Yellow
Write-Host "   .\mcp_daemon_installer.ps1 -Install" -ForegroundColor White
Write-Host "4. Execute este script semanalmente para manutenção" -ForegroundColor Yellow

if ($totalKilled -gt 20) {
    Write-Host "`n⚠ ATENÇÃO: Muitos processos foram finalizados!" -ForegroundColor Red
    Write-Host "Considere implementar o MCP Daemon Centralizado para evitar este problema." -ForegroundColor Yellow
}

Write-Host "`n✅ LIMPEZA EMERGENCIAL CONCLUÍDA!" -ForegroundColor Green
Write-Host "Sistema otimizado e pronto para uso." -ForegroundColor Cyan
