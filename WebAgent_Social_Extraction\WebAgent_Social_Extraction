# WebAgent Social Extraction Platform - Dependencies
# Version: 2.0 (PoC Phase)
# Last Updated: 2025-01-24

# ===== CORE FRAMEWORK =====
fastapi==0.104.1
uvicorn[standard]==0.24.0
celery[redis]==5.3.4
redis==5.0.1

# ===== DATABASE & ORM =====
supabase==2.3.0
psycopg2-binary==2.9.9
sqlalchemy==2.0.23
alembic==1.13.1

# ===== AI & ML =====
google-generativeai==0.3.2
openai==1.6.1
anthropic==0.8.1
opencv-python==********
pillow==10.1.0
numpy==1.25.2

# ===== SOCIAL MEDIA EXTRACTION =====
yt-dlp==2023.12.30
instaloader==4.10.3
tweepy==4.14.0
requests==2.31.0
beautifulsoup4==4.12.2
selenium==4.16.0

# ===== DATA PROCESSING =====
pandas==2.1.4
pydantic==2.5.2
pydantic-settings==2.1.0
python-multipart==0.0.6
python-jose[cryptography]==3.3.0

# ===== ASYNC & CONCURRENCY =====
asyncio==3.4.3
aiohttp==3.9.1
aiofiles==23.2.0
httpx==0.25.2

# ===== MONITORING & LOGGING =====
prometheus-client==0.19.0
structlog==23.2.0
sentry-sdk[fastapi]==1.39.2

# ===== TESTING =====
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-mock==3.12.0
httpx==0.25.2
factory-boy==3.3.0

# ===== DEVELOPMENT TOOLS =====
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1
pre-commit==3.6.0

# ===== SECURITY =====
python-dotenv==1.0.0
cryptography==41.0.8
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# ===== UTILITIES =====
click==8.1.7
rich==13.7.0
typer==0.9.0
schedule==1.2.0
python-dateutil==2.8.2

# ===== MEDIA PROCESSING =====
ffmpeg-python==0.2.0
moviepy==1.0.3
librosa==0.10.1

# ===== CACHING =====
redis==5.0.1
diskcache==5.6.3

# ===== CONFIGURATION =====
pyyaml==6.0.1
toml==0.10.2
configparser==6.0.0

# ===== DEPLOYMENT =====
gunicorn==21.2.0
docker==6.1.3
kubernetes==28.1.0

# ===== DOCUMENTATION =====
mkdocs==1.5.3
mkdocs-material==9.4.8
mkdocs-mermaid2-plugin==1.1.1
