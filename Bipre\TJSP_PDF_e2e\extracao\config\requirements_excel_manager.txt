# Dependências para ExcelFileManager Enterprise
# Sistema de proteção contra file locking em Excel

# Dependências principais já existentes
pandas>=1.5.0
openpyxl>=3.0.0
PyMuPDF>=1.20.0

# Novas dependências para ExcelFileManager
psutil>=5.9.0          # Detecção e controle de processos
pathlib>=1.0.0         # Manipulação de caminhos (built-in Python 3.4+)

# Dependências condicionais para Windows
pywin32>=300; sys_platform == "win32"     # COM objects e Windows API

# Dependências opcionais para funcionalidades avançadas
xlsxwriter>=3.0.0      # Engine alternativo para Excel
tqdm>=4.64.0           # Progress bars (se já não estiver instalado)

# Dependências de logging e debugging
colorlog>=6.7.0        # Logging colorido (opcional)

# Para desenvolvimento e testes
pytest>=7.0.0          # Testes unitários
pytest-cov>=4.0.0     # Coverage de testes
