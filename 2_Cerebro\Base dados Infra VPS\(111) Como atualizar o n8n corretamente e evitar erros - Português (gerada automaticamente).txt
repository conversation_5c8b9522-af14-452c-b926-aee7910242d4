﻿pessoal Olha só esse daqui é um corte do
nosso Hangout de segunda-feira então
aqui na descrição tem o link para você
poder ver o episódio completo e também
tem um link do Luma para você se
inscrever e poder participar do nosso
próximo Hangout bom vídeo é é o seguinte
o pessoal ele tem um sistema de
barramento então um coner se comunica
com o outro então o seu worker se
comunica com o seu web Hook que se
comunica com seu editor eles trabalham
em conjunto
ali é comum é comum né edon a gente
pegar a situação que o bonitão na pressa
sabe sabe a pressa é Rafael deve ter
cliente que é assim ele acorda atrasado
ele tudo que ele faz era para ele ter
feito pelo menos duas horas atrás né E
aí ele vai L atualiza só o editor ele
atualiza o editor ele abre porque ele
atualiza o editor ele viu que rodou as
migrations ele abre o editor tá lá a
versão nova beleza deixou para trás o
Hook worker E aí pessoal começa a dar
conflito começa a dar muito conflito né
você tem que rodar o nhn em versões
iguais só que eu não posso atualizar os
três ao mesmo tempo Não façam isso quem
aqui sabe por que eu não posso atualizar
os três ao mesmo tempo vamos ver tem que
fazer aquela musiquinha do lembra do do
relógio que tinha quem que sabe porque
que não pode atualizar os três ao mesmo
tempo eu sei mas vou deixar o pessoal
dizer aí a Rafael
sabe Welton Elton sabe não é possível
Elton Elton vai me salvar aqui tá
viajando
né acordou é o seguinte pessoal o en
Chen ele vai ele vai rodar a migration
Só que os três nodes os três contêiners
são capazes de rodar a migration Então
qual que é o erro da galera é ir lá e
atualizar os três de uma vez o problema
ali o que que é é que a a migration ela
é uma transação eu acho um erro isso daí
mas eu não sou ninguém na fila do pão
falar nada PR os outros né eu não faria
assim então o que que acontece Olha só
olha só a ra Olha só Rafaele você
Reiniciou o coner do editor vamos supor
que a sua igreja demora um minutinho
para rodar Você atualizou ele ele
começou a fazer uma série de mudanças no
banco como se atualizou os três ao mesmo
tempo questão de segundo o Web Hook já
subiu também
como ele faz tudo numa transação só para
essa sessão de conexão com o banco do
editor as mudanças estão lá PR sessão de
pra conexão do Web Hook as mudanças não
estão lá ainda porque eu não terminei a
transação isso a transação ela só é
efetivada quando d o comit nela Então eu
vou ter o editor mexendo no banco eu vou
ter o Web Hook mexendo no banco e eu vou
ter o work mexendo no banco para cada um
deles ninguém mais está mexendo no banco
só ele e aí que embaralha tudo pessoal
às vezes perde o workflow às vezes às
vezes bagun à vez duplica as coisas
acontece um monte de problema é duplica
execução ele mexe um montão de coisa é
muito comum a gente pegar e em
consultoria assim aqueles aqueles n que
estão lá tudo Ked fica tudo kwid que que
foi aquele kwid hoje existiu sim um
tempo que era um bug do NN eles
arrumaram issso aí no final do ano
passado hoje se você atualizar e ver um
k de lá foi porque você não não ele não
conseguiu fazer o processo que deveria
fazer de Matar tudo terminar o serviço
terminar o processo terminar o workflow
E aí atualizar o
sistema geralmente geralmente a gente
pega os os workflows os NH quando estão
assim depois de um update Então qual que
é o ideal que que é o ideal fazer
Pessoal vocês usam o docker por isso que
é importante entender o docker o docker
Quando você vai num
serviço a gente usa o swarm porque o
swarm controla serviço o serviço
controla o contêiner então eu vou no
serviço vocês não vão lá em Services ou
vocês vão no stack para atualizar alguém
aqui viu uma aula eu i no contêiner e
fala assim não pessoal Apaga lá o
contêiner não vamos no serviço seleciona
o serviço e aperto o update ensino vocês
assim a fazer ou você vai no stack muda
a no stack e aperta o botão lá que vai
chamar o update do serviço o serviço
pessoal do docker vai fazer o qu o
service ele vai pro contêiner e vai
mandar um sinal k um um sign thermal que
que é o sig thermal quem aqui tem PC
antigo ou já teve um PC antigo a gente
não Aperta o botão do PC antigo e ele
começa a desligar as coisas acho que
esse PC que trá também assim você aperta
ele ele recebe um comando lá e o Windows
fica assim ó finalizando o processo não
fica lá terminando atualização
de meia hora e desliga é a mesma coisa
esse do serviço ele vai mandar um
comando pro pro docker e falar assim ó
docker quero desligar você o NN vai
falar assim opa pera aí pera aí que eu
tô terminando de executar um workflow
aqui falta dois nozin para terminar
ele se eu vou no contêiner e eu mato e
falta dois Nozinho aquela execução do n
fica eternamente como Ked ele não vai
pegar aquilo ali e continuar depois se
eu vou no serviço e mando atualizar o
serviço ele manda um sinal pro contêiner
o contêiner fala assim docker olha eu
vou Ô nen eu vou desligar você ele fala
Espera que eu vou terminar Ele termina
de rodar os workflows que ele tá rodando
E aí ele e aí e aí sim ele morre vocês
podem perceber que às vezes demor uns
segundinhos desde a hora que vocês
apertam o atualizar até a hora que ele
vai lá e realmente mata o contêiner tem
um delay ali esse delay ele tá
finalizando de rodar algum workflow tá
tá terminando de de executar o que ele
começou a executar então ele vai
terminar tudo que ele pegou e não pega
mais nada quando é assim pessoal
atualização roda limpa quando o cara vai
lá e faz aquele aquele kit pega os
contêiner mata os contêiner aí não tem
aí não tem 100 signo ali ali ali é um
kill completo ali ele matou no meio
processo é como se ele te puxasse da da
tomada o PC então ele ele não salvou ele
não terminou não deu tempo de fazer nada
né Então esse é um esse é um é um
problema
ou quando a galera vai lá e reinicia
servidor eu cansei de ver o povo
reiniciar servidor para poder atualizar
NN ele reinicia o servidor depois
atualiza NN eu falei cara não faz isso
não tem necessidade entendeu não então
assim basicamente pessoal o que que é o
ideal é você ir no serviço apertar o
serviço e matar eu aconselho vocês para
quem puder fazer da maneira mais limpa
possível atualiza primeiro o editor
espera o editor subir olhou no log lá e
colocou lá nhn subiu na porta
5678 aceitando conexões escreveu isso
pessoal aí vai faz no web Hook espera o
Web Hook atualizar tudo o Web Hook deu a
mesma mensagem aguardando webhooks na
porta 56 78 vai no worker e atualiza o
worker por último tá então basicamente
pessoal é isso se você fizer assim a
chance de dar problema é menor não quer
dizer que não existe porque pode ser que
tenha um bug ali no Meio do processo mas
é assim que deveria ser o update Então
quando você faz um update assim pessoal
você já elimina problema e às vezes não
é problema na versão é problema de
update porque ficou coisa para trás não
terminou as coisas tem gente que é mais
criterioso e gosta de ser o modo
Apocalipse Total assim mesmo assim ó que
é que é o modo segurança pura assim sabe
SA o modo segurança o cara vai atualizar
o ele põe capacete aquele epi que chama
né ele põe epi põe o ó fal assim agora
eu vou que vocês precis estourar aqui
não pega em mim entendeu não que que
seria né o o o ideal no mundo imaginário
é eu ir lá Rafael eu matar o serviço do
Web Hook eu vou eu vou no serviço eu
removo o serviço web Hook ele vai parar
de rodar mesmo eu vou lá removo o worker
então o worker vai rodar o que tem que
rodar e vai vai morrer e não existe mais
o worker rodando
só tem o editor rodando aí eu vou lá e
atualizo o
editor espero terminar de atualizar o
editor aí eu Volo no stack e lanço de
novo o stack do we Hook lanço de novo o
stack do worker esse seria o mundo ideal
mas eu sei que vocês não ten um minuto
para fazer isso vocês vão ter no máximo
30 segundos Então vai no serviço
mesmo que já já é já é um bom caminho