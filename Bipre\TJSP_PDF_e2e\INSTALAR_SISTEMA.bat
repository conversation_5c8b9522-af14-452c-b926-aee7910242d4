@echo off
chcp 65001 >nul
title Instalação Sistema TJSP End-to-End v1.0

echo.
echo ================================================================
echo 🚀 INSTALAÇÃO SISTEMA TJSP END-TO-END v1.0
echo ================================================================
echo.
echo 📋 Este script irá:
echo    • Verificar dependências do Python
echo    • Instalar bibliotecas necessárias
echo    • Configurar estrutura de diretórios
echo    • Executar testes de validação
echo.

:: Verificar se Python está instalado
echo 🔧 Verificando Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python não encontrado!
    echo.
    echo 💡 INSTRUÇÕES DE INSTALAÇÃO:
    echo    1. Baixe Python 3.8+ de: https://python.org/downloads/
    echo    2. Durante instalação, marque "Add Python to PATH"
    echo    3. Execute este script novamente
    echo.
    pause
    exit /b 1
)

python --version
echo ✅ Python encontrado!
echo.

:: Verificar pip
echo 🔧 Verificando pip...
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ pip não encontrado!
    echo 💡 Instalando pip...
    python -m ensurepip --upgrade
)
echo ✅ pip disponível!
echo.

:: Instalar dependências básicas
echo 📦 Instalando dependências básicas...
pip install --upgrade pip
pip install pandas openpyxl tqdm pathlib

:: Verificar dependências específicas do TJSP
echo 📦 Verificando dependências do sistema TJSP...
if exist "tjsp\requirements.txt" (
    echo 📄 Instalando dependências do TJSP...
    pip install -r tjsp\requirements.txt
) else (
    echo ⚠️ Arquivo requirements.txt do TJSP não encontrado
    echo 💡 Instalando dependências comuns...
    pip install selenium webdriver-manager beautifulsoup4 requests
)

:: Verificar dependências do sistema de extração
echo 📦 Verificando dependências do sistema de extração...
if exist "extracao\config\requirements.txt" (
    echo 📄 Instalando dependências da extração...
    pip install -r extracao\config\requirements.txt
) else if exist "extracao\requirements.txt" (
    echo 📄 Instalando dependências da extração...
    pip install -r extracao\requirements.txt
) else (
    echo ⚠️ Arquivo requirements.txt da extração não encontrado
    echo 💡 Instalando dependências comuns...
    pip install PyPDF2 pdfplumber tabula-py camelot-py
)

:: Criar estrutura de diretórios
echo 📁 Criando estrutura de diretórios...
if not exist "tjsp\downloads_completos\" mkdir "tjsp\downloads_completos"
if not exist "extracao\data\input\" mkdir "extracao\data\input"
if not exist "extracao\data\output\" mkdir "extracao\data\output"
if not exist "logs_orquestrador\" mkdir "logs_orquestrador"
if not exist "backup_sincronizacao\" mkdir "backup_sincronizacao"

echo ✅ Estrutura de diretórios criada!
echo.

:: Executar testes de validação
echo 🧪 Executando testes de validação...
python teste_sistema_completo.py

if errorlevel 1 (
    echo.
    echo ⚠️ Alguns testes falharam!
    echo 💡 Verifique os erros acima e tente novamente
    echo.
) else (
    echo.
    echo 🎉 INSTALAÇÃO CONCLUÍDA COM SUCESSO!
    echo.
    echo 📋 PRÓXIMOS PASSOS:
    echo    1. Execute: EXECUTAR_ORQUESTRADOR.bat
    echo    2. Escolha opção 3 para processo completo
    echo    3. Verifique resultados em extracao\data\output\
    echo.
    echo 📚 DOCUMENTAÇÃO:
    echo    • README_ORQUESTRADOR.md - Guia completo
    echo    • logs_orquestrador\ - Logs detalhados
    echo.
)

echo 📊 Instalação finalizada!
pause
