#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
TJSP Orquestrador End-to-End v1.0
Sistema que conecta automaticamente o processo de download e extração do TJSP

Funcionalidades:
- Execução coordenada de download e extração
- Sincronização automática de arquivos
- Checkpoint unificado
- Logging consolidado
- Interface de usuário intuitiva
- Relatório final consolidado

Autor: Sistema Augment
Data: 2025-01-28
"""

import os
import sys
import time
import json
import shutil
import logging
import subprocess
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Tuple

# Importar módulo de sincronização avançada
try:
    from sincronizador_arquivos import SincronizadorArquivos
    SINCRONIZADOR_DISPONIVEL = True
except ImportError:
    SINCRONIZADOR_DISPONIVEL = False

# Configurações de diretórios
SCRIPT_DIR = Path(__file__).parent
TJSP_DIR = SCRIPT_DIR / "tjsp"
EXTRACAO_DIR = SCRIPT_DIR / "extracao"
DOWNLOADS_DIR = TJSP_DIR / "downloads_completos"
INPUT_EXTRACAO_DIR = EXTRACAO_DIR / "data" / "input"
OUTPUT_EXTRACAO_DIR = EXTRACAO_DIR / "data" / "output"
LOGS_DIR = SCRIPT_DIR / "logs_orquestrador"

class OrquestradorTJSP:
    """Orquestrador principal do sistema TJSP End-to-End"""
    
    def __init__(self):
        self.setup_logging()
        self.setup_directories()
        self.checkpoint_file = SCRIPT_DIR / "checkpoint_orquestrador.json"
        self.checkpoint_data = self.load_checkpoint()
        
    def setup_logging(self):
        """Configura sistema de logging consolidado"""
        LOGS_DIR.mkdir(exist_ok=True)
        
        # Configurar logging principal
        log_file = LOGS_DIR / f"orquestrador_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        self.logger = logging.getLogger(__name__)
        self.logger.info("🚀 Orquestrador TJSP End-to-End iniciado")
        
    def setup_directories(self):
        """Cria estrutura de diretórios necessária"""
        directories = [
            LOGS_DIR,
            INPUT_EXTRACAO_DIR,
            OUTPUT_EXTRACAO_DIR,
            DOWNLOADS_DIR
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            
        self.logger.info("📁 Estrutura de diretórios verificada")
        
    def load_checkpoint(self) -> Dict:
        """Carrega checkpoint do orquestrador"""
        if self.checkpoint_file.exists():
            try:
                with open(self.checkpoint_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                self.logger.warning(f"Erro ao carregar checkpoint: {e}")
                
        return {
            "inicio_processamento": datetime.now().isoformat(),
            "ultima_atualizacao": datetime.now().isoformat(),
            "etapas_concluidas": [],
            "downloads_sincronizados": [],
            "arquivos_extraidos": [],
            "estatisticas": {
                "total_downloads": 0,
                "total_sincronizados": 0,
                "total_extraidos": 0,
                "tempo_total": 0
            }
        }
        
    def save_checkpoint(self):
        """Salva checkpoint atual"""
        self.checkpoint_data["ultima_atualizacao"] = datetime.now().isoformat()
        
        try:
            with open(self.checkpoint_file, 'w', encoding='utf-8') as f:
                json.dump(self.checkpoint_data, f, ensure_ascii=False, indent=2)
            self.logger.debug("💾 Checkpoint salvo")
        except Exception as e:
            self.logger.error(f"Erro ao salvar checkpoint: {e}")
            
    def verificar_sistemas(self) -> Tuple[bool, bool]:
        """Verifica se os sistemas estão disponíveis"""
        tjsp_disponivel = (TJSP_DIR / "ProcessadorTJSPUnificado_final.py").exists()
        extracao_disponivel = (EXTRACAO_DIR / "src" / "sistema_principal.py").exists()
        
        self.logger.info(f"📊 Sistema TJSP: {'✅ Disponível' if tjsp_disponivel else '❌ Não encontrado'}")
        self.logger.info(f"📊 Sistema Extração: {'✅ Disponível' if extracao_disponivel else '❌ Não encontrado'}")
        
        return tjsp_disponivel, extracao_disponivel
        
    def executar_download_tjsp(self, numero_inicial: Optional[int] = None) -> bool:
        """Executa o processo de download do TJSP"""
        self.logger.info("🔄 Iniciando processo de download TJSP...")

        try:
            # Mudar para diretório TJSP
            os.chdir(TJSP_DIR)

            # Preparar comando
            cmd = [sys.executable, "ProcessadorTJSPUnificado_final.py"]

            # Configurar ambiente com UTF-8 para resolver problemas de encoding
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'
            env['PYTHONLEGACYWINDOWSSTDIO'] = 'utf-8'
            env['PYTHONUTF8'] = '1'

            # Preparar input automático para o ProcessadorTJSP
            input_data = ""
            if numero_inicial:
                input_data = f"{numero_inicial}\n"
            else:
                input_data = "\n"  # ENTER para padrão

            self.logger.info(f"🚀 Executando: {' '.join(cmd)}")
            if numero_inicial:
                self.logger.info(f"� Número inicial configurado: {numero_inicial}")
            else:
                self.logger.info("� Usando configuração padrão (ENTER)")

            self.logger.info("⏳ Aguarde... ProcessadorTJSP em execução")

            # Executar processo com input automático e timeout
            timeout_segundos = 1800  # 30 minutos
            self.logger.info(f"⏰ Timeout configurado: {timeout_segundos//60} minutos")

            try:
                result = subprocess.run(
                    cmd,
                    input=input_data,
                    env=env,
                    text=True,
                    encoding='utf-8',
                    capture_output=True,
                    timeout=timeout_segundos
                )
            except subprocess.TimeoutExpired:
                self.logger.warning(f"⏰ Timeout atingido ({timeout_segundos//60} min) - Prosseguindo para extração")
                self.logger.info("📊 Downloads parciais serão processados")
                # Marcar como concluído parcialmente para prosseguir
                self.checkpoint_data["etapas_concluidas"].append("download_tjsp_parcial")
                self.save_checkpoint()
                return True

            # Exibir saída do ProcessadorTJSP de forma organizada
            if result.stdout:
                print("\n" + "="*60)
                print("📋 SAÍDA DO PROCESSADOR TJSP:")
                print("="*60)
                print(result.stdout)
                print("="*60 + "\n")

            if result.stderr:
                print("\n" + "="*60)
                print("⚠️ AVISOS/ERROS DO PROCESSADOR TJSP:")
                print("="*60)
                print(result.stderr)
                print("="*60 + "\n")

            if result.returncode == 0:
                self.logger.info("✅ Download TJSP concluído com sucesso")
                self.checkpoint_data["etapas_concluidas"].append("download_tjsp")
                self.save_checkpoint()
                return True
            else:
                self.logger.error(f"❌ Erro no download TJSP: {result.stderr}")
                return False

        except Exception as e:
            self.logger.error(f"❌ Erro ao executar download TJSP: {e}")
            return False
        finally:
            # Voltar ao diretório original
            os.chdir(SCRIPT_DIR)
            
    def sincronizar_arquivos(self, usar_sincronizador_avancado: bool = True) -> int:
        """Sincroniza PDFs do download para extração"""
        self.logger.info("🔄 Sincronizando arquivos...")

        if not DOWNLOADS_DIR.exists():
            self.logger.warning("📁 Diretório de downloads não encontrado")
            return 0

        # Usar sincronizador avançado se disponível
        if usar_sincronizador_avancado and SINCRONIZADOR_DISPONIVEL:
            return self._sincronizar_com_modulo_avancado()
        else:
            return self._sincronizar_basico()

    def _sincronizar_com_modulo_avancado(self) -> int:
        """Sincronização usando módulo avançado"""
        self.logger.info("🚀 Usando sincronizador avançado...")

        # Criar diretório de backup
        backup_dir = SCRIPT_DIR / "backup_sincronizacao"

        # Inicializar sincronizador
        sincronizador = SincronizadorArquivos(
            origem=DOWNLOADS_DIR,
            destino=INPUT_EXTRACAO_DIR,
            backup_dir=backup_dir
        )

        # Executar sincronização
        resultados = sincronizador.sincronizar_todos(forcar_sobrescrita=False)

        # Atualizar checkpoint com novos arquivos
        for detalhe in resultados["detalhes"]:
            if detalhe["status"] == "sucesso" and detalhe["acao"] in ["copiado", "sobrescrito"]:
                if detalhe["arquivo"] not in self.checkpoint_data["downloads_sincronizados"]:
                    self.checkpoint_data["downloads_sincronizados"].append(detalhe["arquivo"])

        # Salvar relatório detalhado
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        relatorio_file = LOGS_DIR / f"sincronizacao_detalhada_{timestamp}.json"
        sincronizador.gerar_relatorio_detalhado(resultados, relatorio_file)

        # Limpeza de backups antigos (30 dias)
        sincronizador.limpar_arquivos_antigos(30)

        self.checkpoint_data["estatisticas"]["total_sincronizados"] = len(self.checkpoint_data["downloads_sincronizados"])
        self.save_checkpoint()

        return resultados["copiados"] + resultados["sobrescritos"]

    def _sincronizar_basico(self) -> int:
        """Sincronização básica (fallback)"""
        self.logger.info("📄 Usando sincronização básica...")

        # Listar PDFs no diretório de downloads
        pdfs_download = list(DOWNLOADS_DIR.glob("*.pdf"))
        pdfs_sincronizados = 0

        for pdf_path in pdfs_download:
            try:
                # Verificar se já foi sincronizado
                if pdf_path.name in self.checkpoint_data["downloads_sincronizados"]:
                    continue

                # Copiar para diretório de extração
                destino = INPUT_EXTRACAO_DIR / pdf_path.name
                shutil.copy2(pdf_path, destino)

                # Registrar sincronização
                self.checkpoint_data["downloads_sincronizados"].append(pdf_path.name)
                pdfs_sincronizados += 1

                self.logger.info(f"📄 Sincronizado: {pdf_path.name}")

            except Exception as e:
                self.logger.error(f"❌ Erro ao sincronizar {pdf_path.name}: {e}")

        self.checkpoint_data["estatisticas"]["total_sincronizados"] = len(self.checkpoint_data["downloads_sincronizados"])
        self.save_checkpoint()

        self.logger.info(f"✅ Sincronização concluída: {pdfs_sincronizados} novos arquivos")
        return pdfs_sincronizados
        
    def executar_extracao(self) -> bool:
        """Executa o processo de extração"""
        self.logger.info("🔄 Iniciando processo de extração...")

        try:
            # Mudar para diretório de extração
            os.chdir(EXTRACAO_DIR)

            # Executar sistema de extração
            cmd = [sys.executable, "src/sistema_principal.py"]

            # Configurar ambiente com UTF-8 para resolver problemas de encoding
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'
            env['PYTHONLEGACYWINDOWSSTDIO'] = 'utf-8'
            env['PYTHONUTF8'] = '1'

            self.logger.info(f"🚀 Executando: {' '.join(cmd)}")
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                encoding='utf-8',
                env=env
            )

            if result.returncode == 0:
                self.logger.info("✅ Extração concluída com sucesso")
                self.checkpoint_data["etapas_concluidas"].append("extracao")
                self.save_checkpoint()
                return True
            else:
                self.logger.error(f"❌ Erro na extração: {result.stderr}")
                return False

        except Exception as e:
            self.logger.error(f"❌ Erro ao executar extração: {e}")
            return False
        finally:
            # Voltar ao diretório original
            os.chdir(SCRIPT_DIR)
            
    def gerar_relatorio_consolidado(self):
        """Gera relatório final consolidado"""
        self.logger.info("📊 Gerando relatório consolidado...")
        
        relatorio = {
            "timestamp": datetime.now().isoformat(),
            "resumo_execucao": {
                "etapas_concluidas": self.checkpoint_data["etapas_concluidas"],
                "total_downloads_sincronizados": len(self.checkpoint_data["downloads_sincronizados"]),
                "arquivos_processados": self.checkpoint_data["downloads_sincronizados"]
            },
            "diretorios": {
                "downloads": str(DOWNLOADS_DIR),
                "input_extracao": str(INPUT_EXTRACAO_DIR),
                "output_extracao": str(OUTPUT_EXTRACAO_DIR)
            }
        }
        
        # Salvar relatório
        relatorio_file = LOGS_DIR / f"relatorio_consolidado_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(relatorio_file, 'w', encoding='utf-8') as f:
            json.dump(relatorio, f, ensure_ascii=False, indent=2)
            
        self.logger.info(f"📋 Relatório salvo: {relatorio_file}")
        return relatorio_file

    def _executar_limpeza(self):
        """Executa limpeza de arquivos antigos"""
        if not SINCRONIZADOR_DISPONIVEL:
            print("❌ Sincronizador avançado não disponível para limpeza")
            return

        try:
            dias = input("Dias para manter backups (padrão 30): ").strip()
            dias = int(dias) if dias.isdigit() else 30

            backup_dir = SCRIPT_DIR / "backup_sincronizacao"
            if not backup_dir.exists():
                print("📁 Nenhum diretório de backup encontrado")
                return

            sincronizador = SincronizadorArquivos(
                origem=DOWNLOADS_DIR,
                destino=INPUT_EXTRACAO_DIR,
                backup_dir=backup_dir
            )

            removidos = sincronizador.limpar_arquivos_antigos(dias)
            print(f"🧹 Removidos {removidos} arquivos de backup antigos")

        except Exception as e:
            self.logger.error(f"Erro na limpeza: {e}")
            print(f"❌ Erro na limpeza: {e}")

    def mostrar_menu_principal(self):
        """Exibe menu principal do orquestrador"""
        print("\n" + "="*60)
        print("🚀 TJSP ORQUESTRADOR END-TO-END v1.0")
        print("="*60)
        print("1. 📥 Executar apenas Download (TJSP)")
        print("2. 📄 Executar apenas Extração")
        print("3. 🔄 Executar processo completo (Download + Extração)")
        print("4. 🔄 Sincronizar arquivos (Downloads → Extração)")
        if SINCRONIZADOR_DISPONIVEL:
            print("5. 🚀 Sincronização avançada (com backup e verificação)")
        print("6. 📊 Gerar relatório consolidado")
        print("7. 📋 Ver status atual")
        print("8. 🧹 Limpeza de arquivos antigos")
        print("9. 🚪 Sair")
        print("="*60)
        if SINCRONIZADOR_DISPONIVEL:
            print("💡 Sincronizador avançado disponível!")
        else:
            print("⚠️ Sincronizador avançado não disponível (modo básico)")

    def mostrar_status(self):
        """Mostra status atual do sistema"""
        print("\n📊 STATUS ATUAL DO SISTEMA")
        print("-" * 40)

        # Verificar sistemas
        tjsp_ok, extracao_ok = self.verificar_sistemas()

        # Contar arquivos
        downloads_count = len(list(DOWNLOADS_DIR.glob("*.pdf"))) if DOWNLOADS_DIR.exists() else 0
        input_count = len(list(INPUT_EXTRACAO_DIR.glob("*.pdf"))) if INPUT_EXTRACAO_DIR.exists() else 0
        output_files = len(list(OUTPUT_EXTRACAO_DIR.glob("*.xlsx"))) if OUTPUT_EXTRACAO_DIR.exists() else 0

        print(f"📁 PDFs em downloads: {downloads_count}")
        print(f"📁 PDFs para extração: {input_count}")
        print(f"📁 Arquivos de saída: {output_files}")
        print(f"🔄 Etapas concluídas: {', '.join(self.checkpoint_data['etapas_concluidas']) if self.checkpoint_data['etapas_concluidas'] else 'Nenhuma'}")
        print(f"📊 Total sincronizados: {len(self.checkpoint_data['downloads_sincronizados'])}")

    def executar_processo_completo(self, numero_inicial: Optional[int] = None) -> bool:
        """Executa o processo completo end-to-end"""
        self.logger.info("🚀 Iniciando processo completo end-to-end")
        inicio = time.time()

        try:
            # 1. Verificar sistemas
            tjsp_ok, extracao_ok = self.verificar_sistemas()
            if not tjsp_ok or not extracao_ok:
                self.logger.error("❌ Sistemas não disponíveis")
                return False

            # 2. Executar download
            download_concluido = any(etapa in self.checkpoint_data["etapas_concluidas"]
                                   for etapa in ["download_tjsp", "download_tjsp_parcial"])

            if not download_concluido:
                self.logger.info("📥 Etapa 1/3: Download TJSP")
                if not self.executar_download_tjsp(numero_inicial):
                    return False
            else:
                self.logger.info("✅ Download já concluído anteriormente")

            # 3. Sincronizar arquivos
            self.logger.info("🔄 Etapa 2/3: Sincronização de arquivos")
            arquivos_sincronizados = self.sincronizar_arquivos()
            self.logger.info(f"📊 Resultado sincronização: {arquivos_sincronizados} novos arquivos")

            # 4. Executar extração
            # Verificar se há arquivos para extrair
            arquivos_input = len(list(INPUT_EXTRACAO_DIR.glob("*.pdf"))) if INPUT_EXTRACAO_DIR.exists() else 0

            if arquivos_sincronizados > 0 or "extracao" not in self.checkpoint_data["etapas_concluidas"]:
                if arquivos_input > 0:
                    self.logger.info(f"📄 Etapa 3/3: Extração de dados ({arquivos_input} arquivos)")
                    if not self.executar_extracao():
                        return False
                else:
                    self.logger.warning("⚠️ Nenhum arquivo encontrado para extração")
            else:
                self.logger.info("✅ Extração já concluída anteriormente")

            # 5. Gerar relatório final
            self.gerar_relatorio_consolidado()

            # Calcular tempo total
            tempo_total = time.time() - inicio
            self.checkpoint_data["estatisticas"]["tempo_total"] = tempo_total
            self.save_checkpoint()

            self.logger.info(f"🎉 Processo completo concluído em {tempo_total:.2f} segundos")
            return True

        except Exception as e:
            self.logger.error(f"❌ Erro no processo completo: {e}")
            return False

    def executar_interface(self):
        """Executa interface principal do orquestrador"""
        while True:
            try:
                self.mostrar_menu_principal()
                opcao = input("\n👉 Escolha uma opção: ").strip()

                if opcao == "1":
                    print("\n📥 Executando Download TJSP...")
                    numero = input("Número inicial (Enter para padrão): ").strip()
                    numero_inicial = int(numero) if numero.isdigit() else None
                    self.executar_download_tjsp(numero_inicial)

                elif opcao == "2":
                    print("\n📄 Executando Extração...")
                    self.executar_extracao()

                elif opcao == "3":
                    print("\n🔄 Executando processo completo...")
                    numero = input("Número inicial para download (Enter para padrão): ").strip()
                    numero_inicial = int(numero) if numero.isdigit() else None
                    self.executar_processo_completo(numero_inicial)

                elif opcao == "4":
                    print("\n🔄 Sincronizando arquivos (modo básico)...")
                    self.sincronizar_arquivos(usar_sincronizador_avancado=False)

                elif opcao == "5" and SINCRONIZADOR_DISPONIVEL:
                    print("\n🚀 Sincronização avançada...")
                    self.sincronizar_arquivos(usar_sincronizador_avancado=True)

                elif opcao == "6":
                    print("\n📊 Gerando relatório...")
                    self.gerar_relatorio_consolidado()

                elif opcao == "7":
                    self.mostrar_status()

                elif opcao == "8":
                    print("\n🧹 Limpeza de arquivos antigos...")
                    self._executar_limpeza()

                elif opcao == "9":
                    print("\n👋 Encerrando orquestrador...")
                    break

                else:
                    print("\n❌ Opção inválida!")

                input("\n⏸️ Pressione Enter para continuar...")

            except KeyboardInterrupt:
                print("\n\n⚠️ Processo interrompido pelo usuário")
                break
            except Exception as e:
                self.logger.error(f"❌ Erro na interface: {e}")
                input("\n⏸️ Pressione Enter para continuar...")


def main():
    """Função principal"""
    try:
        orquestrador = OrquestradorTJSP()
        orquestrador.executar_interface()
    except Exception as e:
        print(f"❌ Erro crítico: {e}")
        return False
    return True


if __name__ == "__main__":
    main()
