﻿"Olá pessoal, tudo joia? Aqui Luiz. Nesse"
vídeo eu quero bater um papo com vocês
sobre esse errinho aqui que tá
"aparecendo no MCP do NHN, tá gente?"
"Então olha só, eu tenho aqui um exemplo."
"Então quando você roda, ele dá uma"
mensagem que não é possível conectar ao
servidor MCP. Esse erro não aparece em
"todas as instalações, mas detectamos"
aqui o possível problema. Eu já tenho
aqui a primeira sugestão para vocês pra
gente poder resolver isso daí. Esse post
"aqui, pessoal, nesse post eu deixei para"
"vocês aqui uma descrição, tá, do erro,"
que basicamente é desabilitar o suporte
a GZIP no seu trafic. Então aqui eu
criei uma regrinha para vocês que vai
pegar só as
URLs/MCP e desabilitar o GZIP só nelas.
O restante do seu NHN roda normal. Eu
não preciso interferir em toda a
"aplicação só para desabilitar o GZIP, tá"
gente? Eu consigo isolar o problema ou
isolar ali o roteamento do trafic. Então
é só vocês copiarem esse esse essa rota
"que tá aqui. Então nesse caso aqui, ó,"
essa rota é do editor. Então vocês vão
acessar aqui o seu stack do editor do
"seu NHN e vai colar aqui, pessoal. Ó,"
vocês vão colar aqui e vão deixar
"alinhadinho, tá gente? Deixa alinhadinho"
o que tá aqui. Depois você vai vir na
stack do web hook e colar essa essa
"regra de baixo aqui, ó. Stack do web"
hook. Copia ela e cola aqui. Você vai
"mudar aqui no editor o endereço, tá? Ó."
"Então, esse endereço aqui você vai"
colocar o endereço do seu editor do NHN.
E lá na web hook você vai colar aqui
"aquele textinho, aquela regra do web"
hook e vai colar aqui. O resto deixa
"normal, não mexe. Então aqui eu só tô"
isolando o que for barra mcp. Eu vou
"desabilitar o gzip. Você vem aqui,"
aperta a opção pro services e atualiza o
seu web hook. Aperta aqui a opção
provices e atualiza o seu editor. Espera
ele carregar e quando você for testar a
"sua stack, ela já vai funcionar, tá"
"gente? Eu deixei aqui aberto, tá? Esse"
"daqui eu, como eu sou embaixador da"
"ferramenta, eu tenho que trazer para"
vocês eh correções de erro também. Eu
deixei aberto esse post para você. Então
"você que não é aluna promov, é só clicar"
aqui no link para você poder acessar de
"novo. Pessoal, isso aqui é a nossa"
primeira tentativa de corrigir o erro.
Eu fiz bastante teste aqui e deu certo.
Aqui na comunidade a galera já tá usando
assim e tem dado resultado. Mas caso
"você encontre o algum problema ainda,"
usa aqui a parte de comentário. Vamos
conversar aqui sobre isso. Pelo menos a
gente consegue chegar numa solução
"junto, tá? Mas isso aqui na maioria, em"
todos que eu fiz aqui resolver. Eu acho
que a maioria de vocês que estão
enfrentando aqui esse problema do
"Nat é possível conectar, você vai"
"conseguir vir aqui e resolver, tá?"
"Gente, importante só de lembrar, tá? Se"
"eu vier aqui, por exemplo, ó, e não"
"tiver com o meu workflow ativo, o meu"
"servidor ativo, não vai funcionar."
"Então, por exemplo, vai dar inclusive"
"dar a mesma mensagem, tá? Então, se eu"
"vi agora aqui, por exemplo, for bater um"
"papo aqui com a gente, ele dá uma"
"mensagem lá, não foi possível conectar"
no MCP. Por quê? Porque eu desabilitei o
workflow. Você vai ter essa mesma
mensagem se você tiver com o GZIP
"habilitado. Então, se você recebe essa"
"mensagem, verifica se o workflow tá"
ativo e verifica se você lançou
corretamente esses labels aqui que eu
"pus para você, tá? Gente, se tiver"
"alguma dúvida, é só comentar aqui, tá?"
"Para poder a gente ajuda vocês aí, tá, a"
"resolver esse problema. Pessoal, hoje,"
"especificamente, eu vou estar um pouco"
fora aqui. O suporte tá reduzido. Eu tô
indo lá para São Paulo para apresentar o
evento do Supas que vai ter o Launch
"Week 14, mas de tarde eu consigo"
"conectar aqui e bater um papo com vocês,"
caso vocês não consigam ter algum
"resultado, mas eu acho que vocês"
conseguem fazer isso daqui. É só copiar
"essa regra, colar aqui no seu editor."
"Você vem aqui, ó, até onde estão as"
"regras do trafic e cola aqui embaixo, ó."
Aqui acabra a regra padrão que tem ali
no sistema. É só você colar essa parte
"de baixo, ó. Vou até fazer para vocês"
"verem aqui, ficar mais claro, ó. Então,"
vou copiar essa
"regra e eu vou colar. Quando você colar,"
ela vem sem a entação. É só você
"selecionar e apertar o tab aqui, ó."
Deixa tudo alinhadinho. Muda o endereço
pro endereço do seu editor e aperta o
deploy. E faz a mesma coisa com Web
"Hook. São regras diferentes, pessoal,"
porque o nome do serviço aqui é
"diferente. Então, por isso que não é a"
"mesma regra pros dois. É a mesma regra,"
só muda o nome do serviço. Então deixei
"mastigadinho aqui para evitar erro, tá?"
"Vem aqui, ó, copia o do web hook, cola"
"lá embaixo nas regrinhas do web hook,"
"tá? Aqui, ó. Vocês vão colar aqui o de"
"vocês não vai ter nada, vocês vão colar"
"aqui, tá? Ó, vai ficar assim e vocês"
vêem e adicionam. É para funcionar
"certinho, tá, pessoal? Nos testes que eu"
"fiz aqui, deu tudo certinho. Só lembra"
de mudar o endereço aqui para colocar o
endereço do seu web hook no editor. Você
põe o endereço do editor. Qualquer
dúvida é só acessar aqui e comentar.
Você que é membro já tem acesso aqui e
"comenta. Você que não é aluno da Promov,"
é só se cadastrar de modo gratuito aqui
na comunidade e você vai poder vir aqui
e comentar também. O link tá aqui na
"descrição. Valeu, pessoal. M."