﻿Olá tudo bem seja bem-vindo Neste vídeo
você vai aprender como gerar relatórios
PDF de forma dinâmica diretamente no seu
workflow utilizando a incrível e
totalmente gratuita ferramenta goberg
vamos juntos
automatizar no site oficial você
encontra o link para o github onde a
ferramenta já acumula mais de 60 milhões
de
downloads veja aqui um exemplo de
relatório convertido para PDF e ao longo
do vídeo você aprenderá a fazer o mesmo
imagine todos os relatórios da sua
empresa que você poderá automatizar e
enviar de forma prática sem mais
delongas vamos direto ao ponto a
primeira coisa que precisamos fazer
conforme o site oficial é instalar o
goberg na nossa máquina para isso
verifique se o docker desktop já está
instalado pois ele é um pré-requisito no
site na São de instalação você
encontrará o comando para instalar o
goberg no docker Via linha de
comando com o comando copiado abre o
terminal preferencialmente como
administrador e insir o comando para
iniciar o download do goberg simples e
rápido enquanto Aguardamos a instalação
ser concluída quero adiantar que no
final do vídeo Vou compartilhar algumas
possibilidades incríveis que você pode
realizar de forma dinâmica quando
descobri a dimensão do que é possível
fazer com goberg dentro de um workflow
minha cabeça quase explodiu de tantas
ideias dá para criar inúmeras automações
legais que vão transformar sua
rotina quero aproveitar para agradecer o
seu apoio ao canal cada like comentário
inscrição no canal e participação no
nosso grupo do WhatsApp faz toda a
diferença se você ainda não é membro
considere se juntar a nós Esse
engajamento é fundamental para que nossa
comunidade cresça cada vez mais e
possamos continuar trazendo conteúdos
incríveis como este obrigado por fazer
parte disso a instalação leva alguns
instantes mas nada muito demorado assim
que concluída a Instância do goberg é
iniciada
automaticamente vamos conferir no docker
desktop se tudo está funcionando
corretamente imagem criada e container
em execução sucesso total a parte
burocrática já ficou para trás agora é
hora de colocar a mão na massa vamos
direto pras integrações e começar a
aproveitar o potencial do goberg a api
do goberg oferece diversos end points
para para conversão em PDF e um dos mais
utilizados é o de conversão de uil para
PDF vamos testar essa
funcionalidade primeiro Vamos abrir o
Postman para verificar se o endereço
está sendo acessado corretamente se tudo
funcionar como esperado replicarem esse
exemplo dentro da nossa automação vamos
lá para agilizar o processo já copiei o
código disponível no site oficial preste
atenção aos detalhes o método utilizado
é post o endereço do endpoint que está
sendo acionado e no corpo da requisição
são enviados via form data incluindo o
camp da url que será conver em PDF V
oendereço
padrão
eando proc
convers perfeito noo primeiro pd foi
gerado com sucesso estamos
definitivamente no caminho certo
agora que verificamos que o endp está
funcionando perfeitamente é hora de
replicar o processo dentro do nosso
fluxo de automação Vamos criar um novo
fluxo adicionar um gatilho manual e
incluir um nó de http request para
agilizar a configuração podemos importar
o código disponível no site oficial
diretamente no
nó o corpo da requisição não foi trazido
automaticamente Então vamos habilitar
essa opção e incluir o parâmetro URL via
form
data assim garantimos que o campo
necessário para a conversão seja enviado
corretamente com a configuração feita
basta clicar no botão test step aguardar
alguns segundos o tempo que o goberg
leva para converter o site em PDF e ver
o resultado perfeito pela primeira vez
dentro do nosso processo o arquivo foi
gerado Note que que a extensão é pon PDF
tudo certinho voltando ao site oficial
outro endp bastante utilizado e que
Vamos explorar bastante nas nossas
automações é o HTML para PDF com ele
Podemos trabalhar com o nosso no HTML
criar templates integrar dados de bancos
de dados e enriquecer nossos templates
gerando dinamicamente arquivos PDF
incríveis as possibilidades são imensas
e totalmente dinâmicas abrindo um
verdadeiro mar de
oportunidades para este exemplo o
Primeiro passo é adicionar um gatilho
manual em seguida inserimos o nó HTML
onde trabalharemos com o nosso template
de
PDF também precisaremos incluir um nó
set Pois é necessário converter o HTML
para binário o no set será crucial para
transformar o HTML em base 64 que é o
formato de entrada necessário para
conversão em
binário aqui é importante prestar
bastante
pois precisamos criar a variável do HTML
em base
64 para isso vamos usar uma expressão
que pegará nosso código HTML e o
converterá para o formato base
64 Não tem segredo mas Se surgir
qualquer dúvida pause o vídeo e Copie o
comando com atenção para garantir que
tudo seja feito
corretamente por fim com tudo
configurado Vamos conectar ao nó de http
request para realizar a conversão do o
arquivo HTML em formato binário para
PDF agora vamos preencher o campo de
entrada Bas 64 e adicionar duas
propriedades
essenciais o mime Type que é um padrão
utilizado para identificar o tipo de
conteúdo de um arquivo ou mensagem
garantindo que seja processado
corretamente por sistemas como
navegadores e a opção para definir o
nome do arquivo que neste caso será
index.htm para finalizar esse exemplo
vamos adicionar o último nó de http
request responsável pela conversão vamos
importar o código disponível no site
oficial da mesma forma que fizemos no
primeiro exemplo e preencher o corpo da
requisição com os dados necessários
nesta etapa é importante prestar muita
atenção assim como nas anteriores como
foi preciso transformar o arquivo HTML
em um arquivo binário é necessário
configurar o corpo da requisição do http
request com mais cautela Fique atento Ao
jeito que estou fazendo para garantir
que tudo saia perfeito confio em
você agora vamos fazer uma última
conferida em todos os itens e em seguida
é só dar o play para ver a mágica
acontecer desta vez estamos puxando o
código fonte da página gerado dentro do
nosso fluxo e não de uma página
externa agora vem a melhor parte como
prometi no início do vídeo Vou
compartilhar algumas dicas valiosas se
você conseguiu Acompanhar até aqui tá de
parabéns continue assim você está indo
muito
bem o ponto é que uma vez que você tem o
código fonte na sua mão agora as
possibilidades são infinitas para gerar
pdfs de forma dinâmica vou ilustrar isso
com três exemplos O primeiro é apenas
para mostrar o que é possível vou pedir
para o chat GPT montar um relatório em
HTML de faturamento algo muito
requisitado em várias empresas que traga
o número total de vendas por estado e
por vendedor incluindo um gráfico este
exemplo está sendo feito no chat GPT Mas
você poderia facilmente trazer dados
reais e atualizados de forma dinâmica
para gerar o pdf na hora nos vídeos
anteriores já temos alguns exemplos
práticos disso confira o vídeo 19 da
nossa
playlist uma vez criado vamos conferir o
resultado
Ah e só um adendo está cada vez mais
fácil criar soluções Agora graças ao uso
da Inteligência Artificial mesmo que
você não saiba programar conseguirá
montar soluções incríveis de forma
simples e prática
ficou excelente agora vou baixar o pdf e
abrir em tamanho maior para você ver
como ficou
incrível com apenas algumas informações
o chat GPT montou um template super
bacana e o melhor de tudo aqui você pode
usar e abusar da sua criatividade
combinando com os exemplos dos vídeos
anteriores para criar soluções
incríveis neste segundo exemplo Quero
mostrar uma forma dinâmica de trabalhar
pedi pro chat GPT criar um leite de
certificado para o curso do nosso
canal e agora vou demonstrar como
podemos criar algumas variáveis e trocar
as informações permitindo que você veja
como é simples personalizar o template
de acordo com o que
precisar basicamente basta incluir um nó
de Edit Field antes do template para
definir algumas variáveis e dentro do
template substituir as informações nos
pontos necessários dessa forma você
consegue personalizar facilmente o
conteúdo de forma
dinâmica
Ah se estiver gostando das dicas não se
esqueça de se inscrever no canal para
receber mais conteúdos como esse e
deixar aquele like isso ajuda a gente a
trazer cada vez mais novidades para
você uma vez criadas as variáveis É só
inserir nos pontos certos lembre-se de
que você pode trazer esses dados de
forma dinâmica e em grande volume
imagine criar um laço que gere os
certificados automaticamente para seus
alunos por exemplo o céu é o limite aqui
Use e abuse da sua criatividade
excelente ficou maravilhoso agora vamos
para Terceiro exemplo Vamos criar
aquelas placas de promoção de preço de
produto que incluem a descrição do
produto O Valor anterior e o novo valor
vai ser mais um exemplo prático de como
podemos usar a automação para soluções
personalizadas enquanto o vídeo vai
rolando ao fundo Quero fazer algumas
observações
o goberg é uma ferramenta extremamente
poderosa então não deixe de explorar
todos os Ends que Ele oferece além da
conversão para PDF ele também permite
tirar screenshots o que é super útil em
automações como as de WhatsApp quando
você deseja enviar relatórios em forma
de imagem para facilitar a visualização
e tem mais o goberg também permite Ler
arquivos do Office e conos em PD se você
temil por exemplo facilment
transformá-las em pdfs e exportá-los
para quem precisar de maneira muito mais
prática chegamos ao final do vídeo hoje
vimos Como usar o goberg para gerar pdfs
de forma dinâmica dentro da nossa
automação desde a criação de templates
até a personalização com variáveis
também exploramos como transformar HTML
em PDF como gerar relatórios
certificados e até placas de promoção de
preços o goberg é uma ferramenta
poderosa com uma Variedade de recursos
que podem facilitar muito seu trabalho
como conversão de arquivos do Office e
captura de
screenshots Se gostou do conteúdo não
Sea de se insc Noal para não per nenhuma
novidade deixar se like para nos ajudar
a crescer e compartilhar com mais
pessoas e entrar no noso grupo de
WhatsApp para trar ideias Eber mais
dicas
exclusivas obgado por assistir e até o
próximo vídeo onde Vamos explorar ainda
mais soluções criativas com Automação i
[Música]