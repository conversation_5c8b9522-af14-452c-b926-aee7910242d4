# Sistema de Extração TJSP - Vers<PERSON> Limpa

Sistema copiado em: 22/07/2025 19:22:45

## 🚀 Como usar:

1. **Instalar dependências:**
   ```bash
   pip install -r config/requirements_excel_manager.txt
   ```

2. **Colocar PDFs na pasta:**
   ```
   data/input/
   ```

3. **Executar sistema:**
   ```bash
   # Opção 1: Usar batch file
   EXECUTAR_CORRETAMENTE.bat
   
   # Opção 2: Executar diretamente
   python src/sistema_principal.py
   ```

4. **Verificar resultados:**
   ```
   data/output/TJSP_PRECATORIOS_EXTRAIDOS.xlsx
   ```

## 📊 Arquivos principais:
- `src/extrator_tjsp.py` - Sistema principal de extração
- `src/excel_file_manager.py` - Gerenciador enterprise para Excel
- `src/sistema_principal.py` - Script de execução

## 📁 Estrutura:
```
TJSP_Sistema_Limpo/
├── src/                    # Código-fonte
├── data/
│   ├── input/             # Colocar PDFs aqui
│   └── output/            # Resultados Excel
├── config/                # Configurações
├── logs/                  # Logs do sistema
└── docs/                  # Documentação
```

## ⚠️ Importante:
- Colocar os PDFs na pasta `data/input/`
- O sistema processa automaticamente todos os PDFs
- Resultados são salvos em `data/output/`
- Logs são salvos em `logs/`
