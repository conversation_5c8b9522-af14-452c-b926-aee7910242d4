#!/usr/bin/env python3
"""
MCP Daemon Centralizado - Augment Code Orchestrator V5.0
Servidor centralizado para gerenciar todos os servidores MCP
Elimina duplicação e processos órfãos
"""

import asyncio
import json
import logging
import subprocess
import signal
import sys
import time
import psutil
from pathlib import Path
from typing import Dict, List, Optional
import websockets
import threading
from dataclasses import dataclass
from datetime import datetime

@dataclass
class MCPServer:
    name: str
    command: str
    args: List[str]
    process: Optional[subprocess.Popen] = None
    port: Optional[int] = None
    status: str = "stopped"
    last_heartbeat: Optional[datetime] = None
    restart_count: int = 0

class MCPDaemon:
    def __init__(self, config_file: str = "mcp_daemon_config.json"):
        self.config_file = config_file
        self.servers: Dict[str, MCPServer] = {}
        self.clients: Dict[str, websockets.WebSocketServerProtocol] = {}
        self.running = False
        self.base_port = 8000
        self.daemon_port = 7999
        
        # Configurar logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('mcp_daemon.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def load_config(self):
        """Carrega configuração dos servidores MCP"""
        default_config = {
            "servers": {
                "memory": {
                    "command": "npx",
                    "args": ["-y", "@modelcontextprotocol/server-memory"]
                },
                "everything": {
                    "command": "npx",
                    "args": ["-y", "@modelcontextprotocol/server-everything"]
                },
                "github": {
                    "command": "npx",
                    "args": ["-y", "@modelcontextprotocol/server-github"]
                },
                "sequential-thinking": {
                    "command": "npx",
                    "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]
                },
                "context7": {
                    "command": "npx",
                    "args": ["-y", "@upstash/context7-mcp@latest"]
                },
                "magic": {
                    "command": "npx",
                    "args": ["-y", "@21st-dev/magic@latest"]
                },
                "supabase": {
                    "command": "npx",
                    "args": ["-y", "@supabase/mcp-server-supabase@latest"]
                },
                "playwright": {
                    "command": "npx",
                    "args": ["-y", "@playwright/mcp@latest"]
                },
                "windows-mcp": {
                    "command": "python",
                    "args": ["main.py"],
                    "cwd": "windows-mcp"
                }
            },
            "settings": {
                "auto_restart": True,
                "max_restarts": 3,
                "heartbeat_interval": 30,
                "cleanup_orphans": True
            }
        }
        
        config_path = Path(self.config_file)
        if not config_path.exists():
            with open(config_path, 'w') as f:
                json.dump(default_config, f, indent=2)
            self.logger.info(f"Configuração padrão criada: {self.config_file}")
        
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        # Criar objetos MCPServer
        for name, server_config in config["servers"].items():
            self.servers[name] = MCPServer(
                name=name,
                command=server_config["command"],
                args=server_config["args"]
            )
        
        return config["settings"]
    
    def cleanup_orphan_processes(self):
        """Remove processos MCP órfãos"""
        orphan_count = 0
        for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'ppid']):
            try:
                if proc.info['name'] in ['node.exe', 'python.exe']:
                    cmdline = ' '.join(proc.info['cmdline'] or [])
                    if 'mcp' in cmdline.lower():
                        # Verificar se processo pai existe
                        try:
                            parent = psutil.Process(proc.info['ppid'])
                        except psutil.NoSuchProcess:
                            # Processo órfão encontrado
                            self.logger.warning(f"Finalizando processo órfão: {proc.info['pid']} - {cmdline}")
                            proc.terminate()
                            orphan_count += 1
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        if orphan_count > 0:
            self.logger.info(f"Finalizados {orphan_count} processos órfãos")
    
    def start_server(self, server: MCPServer) -> bool:
        """Inicia um servidor MCP"""
        try:
            # Definir porta
            server.port = self.base_port + len([s for s in self.servers.values() if s.status == "running"])
            
            # Configurar ambiente
            env = {
                "PORT": str(server.port),
                "MCP_DAEMON_MODE": "true"
            }
            
            # Iniciar processo
            cwd = getattr(server, 'cwd', None)
            server.process = subprocess.Popen(
                [server.command] + server.args,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                env=env,
                cwd=cwd
            )
            
            server.status = "running"
            server.last_heartbeat = datetime.now()
            
            self.logger.info(f"Servidor {server.name} iniciado na porta {server.port} (PID: {server.process.pid})")
            return True
            
        except Exception as e:
            self.logger.error(f"Erro ao iniciar servidor {server.name}: {e}")
            server.status = "error"
            return False
    
    def stop_server(self, server: MCPServer) -> bool:
        """Para um servidor MCP"""
        try:
            if server.process and server.process.poll() is None:
                server.process.terminate()
                server.process.wait(timeout=5)
            
            server.status = "stopped"
            server.process = None
            server.port = None
            
            self.logger.info(f"Servidor {server.name} finalizado")
            return True
            
        except Exception as e:
            self.logger.error(f"Erro ao finalizar servidor {server.name}: {e}")
            return False
    
    def monitor_servers(self):
        """Monitora saúde dos servidores"""
        while self.running:
            for server in self.servers.values():
                if server.status == "running" and server.process:
                    # Verificar se processo ainda existe
                    if server.process.poll() is not None:
                        self.logger.warning(f"Servidor {server.name} parou inesperadamente")
                        server.status = "error"
                        
                        # Auto-restart se configurado
                        if server.restart_count < 3:
                            server.restart_count += 1
                            self.logger.info(f"Reiniciando servidor {server.name} (tentativa {server.restart_count})")
                            self.start_server(server)
            
            time.sleep(10)
    
    async def handle_client(self, websocket, path):
        """Gerencia conexões de clientes (VSCode, Claude, etc.)"""
        client_id = f"{websocket.remote_address[0]}:{websocket.remote_address[1]}"
        self.clients[client_id] = websocket
        self.logger.info(f"Cliente conectado: {client_id}")
        
        try:
            async for message in websocket:
                data = json.loads(message)
                response = await self.handle_request(data)
                await websocket.send(json.dumps(response))
        except websockets.exceptions.ConnectionClosed:
            pass
        finally:
            if client_id in self.clients:
                del self.clients[client_id]
            self.logger.info(f"Cliente desconectado: {client_id}")
    
    async def handle_request(self, data: dict) -> dict:
        """Processa requisições dos clientes"""
        command = data.get("command")
        
        if command == "list_servers":
            return {
                "status": "success",
                "servers": {
                    name: {
                        "status": server.status,
                        "port": server.port,
                        "restart_count": server.restart_count
                    }
                    for name, server in self.servers.items()
                }
            }
        
        elif command == "start_server":
            server_name = data.get("server")
            if server_name in self.servers:
                success = self.start_server(self.servers[server_name])
                return {"status": "success" if success else "error"}
        
        elif command == "stop_server":
            server_name = data.get("server")
            if server_name in self.servers:
                success = self.stop_server(self.servers[server_name])
                return {"status": "success" if success else "error"}
        
        elif command == "get_proxy_config":
            # Retorna configuração para clientes se conectarem aos servidores
            return {
                "status": "success",
                "proxy_config": {
                    name: f"ws://localhost:{server.port}"
                    for name, server in self.servers.items()
                    if server.status == "running"
                }
            }
        
        return {"status": "error", "message": "Comando não reconhecido"}
    
    async def start_daemon(self):
        """Inicia o daemon MCP"""
        self.logger.info("Iniciando MCP Daemon Centralizado...")
        
        # Limpar processos órfãos
        self.cleanup_orphan_processes()
        
        # Carregar configuração
        settings = self.load_config()
        
        # Iniciar servidores essenciais
        essential_servers = ["memory", "everything", "github", "sequential-thinking"]
        for server_name in essential_servers:
            if server_name in self.servers:
                self.start_server(self.servers[server_name])
        
        # Iniciar monitor em thread separada
        self.running = True
        monitor_thread = threading.Thread(target=self.monitor_servers, daemon=True)
        monitor_thread.start()
        
        # Iniciar servidor WebSocket
        self.logger.info(f"Daemon MCP rodando na porta {self.daemon_port}")
        await websockets.serve(self.handle_client, "localhost", self.daemon_port)
    
    def shutdown(self):
        """Finaliza o daemon e todos os servidores"""
        self.logger.info("Finalizando MCP Daemon...")
        self.running = False
        
        for server in self.servers.values():
            if server.status == "running":
                self.stop_server(server)
        
        self.logger.info("MCP Daemon finalizado")

def main():
    daemon = MCPDaemon()
    
    # Configurar handlers de sinal
    def signal_handler(signum, frame):
        daemon.shutdown()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Iniciar daemon
    try:
        asyncio.run(daemon.start_daemon())
    except KeyboardInterrupt:
        daemon.shutdown()

if __name__ == "__main__":
    main()
