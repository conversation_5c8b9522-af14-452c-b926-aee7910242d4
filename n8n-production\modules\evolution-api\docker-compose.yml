version: '3.8'

services:
  # === POSTGRESQL DEDICADO EVOLUTION ===
  postgres-evolution:
    image: postgres:15-alpine
    container_name: evolution-postgres
    environment:
      POSTGRES_DB: ${EVOLUTION_POSTGRES_DB:-evolution}
      POSTGRES_USER: ${EVOLUTION_POSTGRES_USER:-evolution_user}
      POSTGRES_PASSWORD: ${EVOLUTION_POSTGRES_PASSWORD:-evolution123}
    volumes:
      - postgres_evolution_data:/var/lib/postgresql/data
    ports:
      - "${EVOLUTION_POSTGRES_PORT:-5433}:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${EVOLUTION_POSTGRES_USER:-evolution_user} -d ${EVOLUTION_POSTGRES_DB:-evolution}"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - evolution-network

  # === REDIS DEDICADO EVOLUTION ===
  redis-evolution:
    image: redis:7-alpine
    container_name: evolution-redis
    command: redis-server --appendonly yes --maxmemory ${EVOLUTION_REDIS_MAXMEMORY:-512mb} --maxmemory-policy allkeys-lru
    volumes:
      - redis_evolution_data:/data
    ports:
      - "${EVOLUTION_REDIS_PORT:-6380}:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - evolution-network

  # === EVOLUTION API ===
  evolution-api:
    image: atendai/evolution-api:v2.2.0
    container_name: evolution-api
    ports:
      - "${EVOLUTION_PORT:-8001}:8080"
    environment:
      # Database Configuration
      DATABASE_PROVIDER: postgresql
      DATABASE_CONNECTION_URI: postgresql://${EVOLUTION_POSTGRES_USER:-evolution_user}:${EVOLUTION_POSTGRES_PASSWORD:-evolution123}@postgres-evolution:5432/${EVOLUTION_POSTGRES_DB:-evolution}?schema=public
      
      # Redis Configuration
      REDIS_URI: redis://redis-evolution:6379
      
      # API Configuration
      API_KEY: ${EVOLUTION_API_KEY:-evolution_api_key_123}
      JWT_SECRET: ${EVOLUTION_JWT_SECRET:-evolution_jwt_secret_456}
      
      # Server Configuration
      SERVER_TYPE: http
      SERVER_PORT: 8080
      SERVER_URL: ${EVOLUTION_SERVER_URL:-http://localhost:8001}
      
      # Logs Configuration
      LOG_LEVEL: ${LOG_LEVEL:-info}
      LOG_COLOR: true
      LOG_BAILEYS: error
      
      # Instance Configuration
      DEL_INSTANCE: false
      CLEAN_STORE_CLEANING_INTERVAL: 7200000
      CLEAN_STORE_MESSAGES: true
      CLEAN_STORE_MESSAGE_UP_TO: 3600
      CLEAN_STORE_CONTACTS: true
      CLEAN_STORE_CHATS: true
      
      # Webhook Configuration
      WEBHOOK_GLOBAL_URL: ${EVOLUTION_WEBHOOK_URL:-}
      WEBHOOK_GLOBAL_ENABLED: ${EVOLUTION_WEBHOOK_ENABLED:-false}
      WEBHOOK_GLOBAL_WEBHOOK_BY_EVENTS: false
      
      # QR Code Configuration
      QRCODE_LIMIT: 10
      QRCODE_COLOR: '#198754'
      
      # Authentication Configuration
      AUTHENTICATION_TYPE: apikey
      AUTHENTICATION_API_KEY: ${EVOLUTION_API_KEY:-evolution_api_key_123}
      AUTHENTICATION_EXPOSE_IN_FETCH_INSTANCES: true
      
      # Timezone
      TZ: ${TIMEZONE:-America/Sao_Paulo}
      
      # Language
      LANGUAGE: pt-BR
      
      # Storage
      STORE_MESSAGES: true
      STORE_MESSAGE_UP_TO: 3600
      STORE_CONTACTS: true
      STORE_CHATS: true
      
      # Typebot Integration (opcional)
      TYPEBOT_ENABLED: false
      
      # Chatwoot Integration (opcional)
      CHATWOOT_ENABLED: false
      
    volumes:
      - evolution_instances:/evolution/instances
      - evolution_store:/evolution/store
    depends_on:
      postgres-evolution:
        condition: service_healthy
      redis-evolution:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - evolution-network

volumes:
  postgres_evolution_data:
    driver: local
  redis_evolution_data:
    driver: local
  evolution_instances:
    driver: local
  evolution_store:
    driver: local

networks:
  evolution-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
