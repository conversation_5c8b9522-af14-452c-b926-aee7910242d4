# 📚 BASE DE CONHECIMENTO - BIBLIOTECAS DE EXTRAÇÃO PARA ANÁLISE VIRAL

**Data:** 2025-01-23  
**Versão:** v1.0 - Documentação Técnica Completa  
**Escopo:** Bibliotecas especializadas para extração de conteúdo viral  

---

## 🎯 OVERVIEW TÉCNICO

Esta documentação consolida a pesquisa especializada de bibliotecas para extração de conteúdo das principais plataformas sociais, com foco em análise de tendências virais e automação de coleta de dados.

### METODOLOGIA DE PESQUISA:
- **Context7 MCP** - Documentação oficial e snippets
- **GitHub Search** - Repositórios ativos e atualizados
- **Trust Score Analysis** - Avaliação de confiabilidade
- **Code Coverage** - Análise de funcionalidades

---

## 🔥 TWIKIT - ESPECIALISTA TWITTER/X

### INFORMAÇÕES TÉCNICAS:
- **Repositório:** https://github.com/d60/twikit
- **Library ID:** /d60/twikit
- **Trust Score:** 7.9/10 (Excelente)
- **Code Snippets:** 23 (Bem documentado)
- **Última Atualização:** 2025-07-06
- **Licença:** MIT
- **Python:** 3.7+

### FUNCIONALIDADES PRINCIPAIS:
```python
# Instalação
pip install twikit

# Funcionalidades avançadas
✓ Busca de tweets por palavra-chave
✓ Tweets de usuários específicos
✓ Trending topics em tempo real
✓ Envio de DMs
✓ Upload de mídia
✓ Streaming em tempo real
✓ Autenticação com cookies (sem API key!)
✓ Rate limiting inteligente
✓ Proxy support
```

### CÓDIGO COMPLETO DE IMPLEMENTAÇÃO:
```python
import asyncio
from twikit import Client
import pandas as pd
from datetime import datetime
import json

class TwitterViralAnalyzer:
    def __init__(self):
        self.client = Client('en-US')
        self.authenticated = False
    
    async def authenticate(self, username, email, password):
        """Autenticação sem API key"""
        try:
            await self.client.login(
                auth_info_1=username,
                auth_info_2=email,
                password=password,
                cookies_file='twitter_cookies.json'  # Salva cookies
            )
            self.authenticated = True
            print("✅ Autenticação realizada com sucesso")
        except Exception as e:
            print(f"❌ Erro na autenticação: {e}")
    
    async def get_trending_topics(self, location='trending'):
        """Buscar trending topics"""
        if not self.authenticated:
            raise Exception("Não autenticado")
        
        trends = await self.client.get_trends(location)
        return [trend.name for trend in trends]
    
    async def analyze_viral_tweets(self, keyword, max_results=100):
        """Analisar tweets virais por palavra-chave"""
        tweets_data = []
        
        # Buscar tweets mais recentes
        tweets = await self.client.search_tweet(keyword, 'Latest', count=max_results)
        
        for tweet in tweets:
            # Calcular métricas de viralidade
            engagement = tweet.favorite_count + tweet.retweet_count + tweet.reply_count
            
            tweet_data = {
                'id': tweet.id,
                'text': tweet.text,
                'author': tweet.user.screen_name,
                'created_at': tweet.created_at,
                'likes': tweet.favorite_count,
                'retweets': tweet.retweet_count,
                'replies': tweet.reply_count,
                'engagement_total': engagement,
                'engagement_rate': engagement / max(tweet.user.followers_count, 1),
                'hashtags': [tag.text for tag in tweet.hashtags] if tweet.hashtags else [],
                'mentions': [mention.screen_name for mention in tweet.mentions] if tweet.mentions else [],
                'media_urls': [media.media_url for media in tweet.media] if tweet.media else []
            }
            tweets_data.append(tweet_data)
        
        return tweets_data
    
    async def monitor_real_time(self, keywords, duration_minutes=60):
        """Monitoramento em tempo real"""
        start_time = datetime.now()
        collected_tweets = []
        
        while (datetime.now() - start_time).seconds < duration_minutes * 60:
            for keyword in keywords:
                try:
                    tweets = await self.client.search_tweet(keyword, 'Latest', count=10)
                    for tweet in tweets:
                        if tweet.id not in [t['id'] for t in collected_tweets]:
                            collected_tweets.append({
                                'keyword': keyword,
                                'tweet_id': tweet.id,
                                'text': tweet.text,
                                'engagement': tweet.favorite_count + tweet.retweet_count,
                                'timestamp': datetime.now()
                            })
                except Exception as e:
                    print(f"Erro ao buscar {keyword}: {e}")
                
                await asyncio.sleep(30)  # Rate limiting
        
        return collected_tweets

# Exemplo de uso
async def main():
    analyzer = TwitterViralAnalyzer()
    
    # Autenticar
    await analyzer.authenticate('username', 'email', 'password')
    
    # Buscar trending topics
    trends = await analyzer.get_trending_topics()
    print(f"Trending topics: {trends[:5]}")
    
    # Analisar tweets virais
    viral_tweets = await analyzer.analyze_viral_tweets('AI', max_results=50)
    
    # Converter para DataFrame
    df = pd.DataFrame(viral_tweets)
    print(f"Coletados {len(df)} tweets")
    print(f"Engagement médio: {df['engagement_total'].mean():.2f}")

# Executar
asyncio.run(main())
```

### PROJETOS COMPLEMENTARES:
1. **TWSCRAPE** (vladkens/twscrape) - 2025 atualizado
2. **TWITTER-SCRAPER-SELENIUM** (shaikhsajid1111)
3. **X-USER-ARCHIVE-SCRAPER** (Prathyush-KKK) - 2025
4. **TWITTERTRENDS_SCRAPER** (selenophile1805) - 2024

---

## 🎬 YOUTUBE TRANSCRIPT API - TRANSCRIÇÕES AVANÇADAS

### INFORMAÇÕES TÉCNICAS:
- **Repositório:** https://github.com/jdepoix/youtube-transcript-api
- **Library ID:** /jdepoix/youtube-transcript-api
- **Trust Score:** 8.9/10 (Excelente)
- **Code Snippets:** 24 (Muito bem documentado)
- **Python:** 3.6+
- **Licença:** MIT

### FUNCIONALIDADES AVANÇADAS:
```python
# Instalação
pip install youtube-transcript-api

# Capacidades
✓ Legendas automáticas e manuais
✓ Múltiplos idiomas (100+ idiomas)
✓ Tradução automática de legendas
✓ Formatos: JSON, SRT, WebVTT, Text
✓ Suporte a proxies
✓ Autenticação com cookies
✓ Batch processing
✓ Error handling robusto
```

### IMPLEMENTAÇÃO COMPLETA:
```python
from youtube_transcript_api import YouTubeTranscriptApi
from youtube_transcript_api.formatters import SRTFormatter, TextFormatter
import re
import pandas as pd

class YouTubeTranscriptAnalyzer:
    def __init__(self):
        self.api = YouTubeTranscriptApi()
        self.srt_formatter = SRTFormatter()
        self.text_formatter = TextFormatter()
    
    def extract_video_id(self, url):
        """Extrair video ID de URL do YouTube"""
        patterns = [
            r'(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)',
            r'youtube\.com\/watch\?.*v=([^&\n?#]+)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)
        return None
    
    def get_transcript(self, video_id, languages=['pt', 'en']):
        """Obter transcrição com fallback de idiomas"""
        try:
            # Tentar idiomas preferidos
            transcript = self.api.get_transcript(video_id, languages=languages)
            return transcript, languages[0]
        except:
            try:
                # Tentar qualquer idioma disponível
                transcript_list = self.api.list_transcripts(video_id)
                transcript = transcript_list.find_generated_transcript(['en']).fetch()
                return transcript, 'en'
            except:
                return None, None
    
    def get_translated_transcript(self, video_id, target_language='pt'):
        """Obter transcrição traduzida"""
        try:
            transcript_list = self.api.list_transcripts(video_id)
            
            # Tentar transcrição em inglês primeiro
            try:
                transcript = transcript_list.find_transcript(['en'])
            except:
                # Usar primeira disponível
                transcript = transcript_list.find_generated_transcript(['en'])
            
            # Traduzir para idioma alvo
            translated = transcript.translate(target_language).fetch()
            return translated, target_language
        except Exception as e:
            print(f"Erro na tradução: {e}")
            return None, None
    
    def analyze_content_sentiment(self, transcript):
        """Análise básica de sentimento do conteúdo"""
        text = ' '.join([entry['text'] for entry in transcript])
        
        # Palavras positivas e negativas (exemplo básico)
        positive_words = ['bom', 'ótimo', 'excelente', 'incrível', 'fantástico', 'love', 'great', 'amazing']
        negative_words = ['ruim', 'péssimo', 'terrível', 'horrível', 'bad', 'terrible', 'awful']
        
        text_lower = text.lower()
        positive_count = sum(1 for word in positive_words if word in text_lower)
        negative_count = sum(1 for word in negative_words if word in text_lower)
        
        if positive_count > negative_count:
            sentiment = 'positive'
        elif negative_count > positive_count:
            sentiment = 'negative'
        else:
            sentiment = 'neutral'
        
        return {
            'sentiment': sentiment,
            'positive_words': positive_count,
            'negative_words': negative_count,
            'total_words': len(text.split())
        }
    
    def extract_keywords(self, transcript, top_n=10):
        """Extrair palavras-chave mais frequentes"""
        text = ' '.join([entry['text'] for entry in transcript])
        
        # Limpeza básica
        words = re.findall(r'\b\w+\b', text.lower())
        
        # Remover stop words básicas
        stop_words = {'o', 'a', 'e', 'de', 'do', 'da', 'em', 'um', 'uma', 'para', 'com', 'não', 'que', 'se', 'é', 'foi', 'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}
        filtered_words = [word for word in words if word not in stop_words and len(word) > 3]
        
        # Contar frequência
        word_freq = {}
        for word in filtered_words:
            word_freq[word] = word_freq.get(word, 0) + 1
        
        # Retornar top palavras
        return sorted(word_freq.items(), key=lambda x: x[1], reverse=True)[:top_n]
    
    def process_video_batch(self, video_urls):
        """Processar múltiplos vídeos"""
        results = []
        
        for url in video_urls:
            video_id = self.extract_video_id(url)
            if not video_id:
                continue
            
            transcript, language = self.get_transcript(video_id)
            if not transcript:
                continue
            
            # Análises
            sentiment = self.analyze_content_sentiment(transcript)
            keywords = self.extract_keywords(transcript)
            
            # Estatísticas
            duration = transcript[-1]['start'] + transcript[-1]['duration'] if transcript else 0
            word_count = sum(len(entry['text'].split()) for entry in transcript)
            
            result = {
                'video_id': video_id,
                'url': url,
                'language': language,
                'duration_seconds': duration,
                'word_count': word_count,
                'sentiment': sentiment,
                'top_keywords': keywords,
                'transcript_length': len(transcript)
            }
            results.append(result)
        
        return results

# Exemplo de uso
analyzer = YouTubeTranscriptAnalyzer()

# URLs de exemplo
video_urls = [
    'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
    'https://youtu.be/dQw4w9WgXcQ'
]

# Processar vídeos
results = analyzer.process_video_batch(video_urls)

# Converter para DataFrame
df = pd.DataFrame(results)
print(df.head())
```

---

## 📊 YOUTUBE DATA API PYTHON CLIENT

### INFORMAÇÕES TÉCNICAS:
- **Repositório:** https://github.com/smappnyu/youtube-data-api
- **Code Snippets:** 181 (Muito completo)
- **Funcionalidade:** API oficial do YouTube
- **Requer:** Google Cloud API Key

### FUNCIONALIDADES COMPLETAS:
```python
# Instalação
pip install youtube-api

# Capacidades avançadas
✓ Metadados completos de vídeos
✓ Comentários e análise de sentimento
✓ Estatísticas de engajamento
✓ Canais e playlists
✓ Vídeos recomendados
✓ Trending videos
✓ Análise temporal
✓ Batch processing (até 50 vídeos)
✓ Rate limiting automático
```

### IMPLEMENTAÇÃO AVANÇADA:
```python
from youtube_api import YouTubeDataAPI
import pandas as pd
import time
from collections import Counter
from datetime import datetime, timedelta

class YouTubeViralAnalyzer:
    def __init__(self, api_key):
        self.yt = YouTubeDataAPI(api_key)
        self.video_categories = {
            '1': 'Film & Animation', '2': 'Autos & Vehicles', '10': 'Music',
            '15': 'Pets & Animals', '17': 'Sports', '19': 'Travel & Events',
            '20': 'Gaming', '22': 'People & Blogs', '23': 'Comedy',
            '24': 'Entertainment', '25': 'News & Politics', '26': 'Howto & Style',
            '27': 'Education', '28': 'Science & Technology'
        }
    
    def search_viral_content(self, query, max_results=50):
        """Buscar conteúdo viral por query"""
        try:
            videos = self.yt.search(q=query, max_results=max_results)
            return videos
        except Exception as e:
            print(f"Erro na busca: {e}")
            return []
    
    def get_detailed_video_data(self, video_ids):
        """Obter dados detalhados de vídeos"""
        if isinstance(video_ids, str):
            video_ids = [video_ids]
        
        # Processar em chunks de 50 (limite da API)
        all_metadata = []
        for i in range(0, len(video_ids), 50):
            chunk = video_ids[i:i+50]
            try:
                metadata = self.yt.get_video_metadata(chunk)
                if isinstance(metadata, list):
                    all_metadata.extend(metadata)
                else:
                    all_metadata.append(metadata)
                time.sleep(0.1)  # Rate limiting
            except Exception as e:
                print(f"Erro ao obter metadados: {e}")
        
        return all_metadata
    
    def analyze_comments_sentiment(self, video_id, max_comments=100):
        """Analisar sentimento dos comentários"""
        try:
            comments = self.yt.get_video_comments(video_id, max_results=max_comments)
            
            if not comments:
                return None
            
            # Análise básica de sentimento
            positive_words = ['love', 'great', 'amazing', 'awesome', 'fantastic', 'excellent']
            negative_words = ['hate', 'bad', 'terrible', 'awful', 'horrible', 'worst']
            
            sentiment_scores = []
            for comment in comments:
                text = comment.get('text', '').lower()
                positive_count = sum(1 for word in positive_words if word in text)
                negative_count = sum(1 for word in negative_words if word in text)
                
                if positive_count > negative_count:
                    sentiment_scores.append(1)  # Positive
                elif negative_count > positive_count:
                    sentiment_scores.append(-1)  # Negative
                else:
                    sentiment_scores.append(0)  # Neutral
            
            avg_sentiment = sum(sentiment_scores) / len(sentiment_scores) if sentiment_scores else 0
            
            return {
                'total_comments': len(comments),
                'avg_sentiment': avg_sentiment,
                'positive_ratio': sentiment_scores.count(1) / len(sentiment_scores),
                'negative_ratio': sentiment_scores.count(-1) / len(sentiment_scores),
                'neutral_ratio': sentiment_scores.count(0) / len(sentiment_scores)
            }
        except Exception as e:
            print(f"Erro na análise de comentários: {e}")
            return None
    
    def calculate_viral_metrics(self, video_data, comments_data=None):
        """Calcular métricas de viralidade"""
        try:
            views = int(video_data.get('video_view_count', 0))
            likes = int(video_data.get('video_like_count', 0))
            comments_count = int(video_data.get('video_comment_count', 0))
            
            # Métricas básicas
            engagement_rate = (likes + comments_count) / max(views, 1)
            like_ratio = likes / max(views, 1)
            comment_ratio = comments_count / max(views, 1)
            
            # Score viral composto
            viral_score = (
                engagement_rate * 1000 +  # Engajamento geral
                like_ratio * 500 +        # Taxa de likes
                comment_ratio * 300       # Taxa de comentários
            )
            
            # Adicionar sentimento se disponível
            if comments_data:
                sentiment_boost = comments_data['avg_sentiment'] * 100
                viral_score += sentiment_boost
            
            return {
                'views': views,
                'likes': likes,
                'comments': comments_count,
                'engagement_rate': engagement_rate,
                'like_ratio': like_ratio,
                'comment_ratio': comment_ratio,
                'viral_score': viral_score,
                'sentiment_data': comments_data
            }
        except Exception as e:
            print(f"Erro no cálculo de métricas: {e}")
            return None
    
    def analyze_trending_videos(self, region_code='US', category_id=None):
        """Analisar vídeos em trending"""
        try:
            # Nota: Esta funcionalidade pode precisar de implementação customizada
            # pois a biblioteca pode não ter método direto para trending
            
            # Alternativa: buscar por termos populares
            trending_terms = ['viral', 'trending', 'popular', 'breaking']
            all_videos = []
            
            for term in trending_terms:
                videos = self.search_viral_content(term, max_results=25)
                all_videos.extend(videos)
            
            # Remover duplicatas
            unique_videos = {v['video_id']: v for v in all_videos}.values()
            
            return list(unique_videos)
        except Exception as e:
            print(f"Erro ao analisar trending: {e}")
            return []

# Exemplo de uso completo
def main():
    # Inicializar analisador
    analyzer = YouTubeViralAnalyzer('YOUR_API_KEY')
    
    # Buscar vídeos virais
    viral_videos = analyzer.search_viral_content('AI technology', max_results=20)
    
    # Extrair IDs
    video_ids = [v['video_id'] for v in viral_videos]
    
    # Obter dados detalhados
    detailed_data = analyzer.get_detailed_video_data(video_ids)
    
    # Analisar cada vídeo
    results = []
    for video_data in detailed_data:
        video_id = video_data['video_id']
        
        # Analisar comentários
        comments_analysis = analyzer.analyze_comments_sentiment(video_id)
        
        # Calcular métricas virais
        viral_metrics = analyzer.calculate_viral_metrics(video_data, comments_analysis)
        
        # Combinar dados
        result = {
            **video_data,
            **viral_metrics
        }
        results.append(result)
    
    # Converter para DataFrame
    df = pd.DataFrame(results)
    
    # Ordenar por score viral
    df_sorted = df.sort_values('viral_score', ascending=False)
    
    print("Top 5 vídeos mais virais:")
    print(df_sorted[['video_title', 'viral_score', 'views', 'engagement_rate']].head())
    
    return df_sorted

# Executar análise
# results_df = main()
```

---

## 📱 INSTALOADER - ESPECIALISTA INSTAGRAM

### INFORMAÇÕES TÉCNICAS:
- **Repositório:** https://github.com/instaloader/instaloader
- **Trust Score:** 6.5/10 (Bom)
- **Code Snippets:** 52 (Bem documentado)
- **Python:** 3.6+
- **Licença:** MIT

### FUNCIONALIDADES COMPLETAS:
```python
# Instalação
pip install instaloader

# Capacidades avançadas
✓ Posts, stories, highlights
✓ Reels e IGTV
✓ Comentários e likes
✓ Metadados completos
✓ Hashtags e localizações
✓ Perfis privados (com login)
✓ Filtros avançados por data/engajamento
✓ Organização automática por data
✓ Atualizações incrementais
✓ Rate limiting inteligente
```

### IMPLEMENTAÇÃO PROFISSIONAL:
```python
import instaloader
import pandas as pd
from datetime import datetime, timedelta
from collections import Counter
import json
import time

class InstagramViralAnalyzer:
    def __init__(self, username=None, password=None):
        self.L = instaloader.Instaloader(
            download_videos=False,  # Não baixar vídeos por padrão
            download_video_thumbnails=False,
            download_geotags=False,
            download_comments=True,
            save_metadata=True,
            compress_json=False
        )
        
        if username and password:
            self.login(username, password)
    
    def login(self, username, password):
        """Login no Instagram"""
        try:
            self.L.login(username, password)
            print("✅ Login realizado com sucesso")
        except Exception as e:
            print(f"❌ Erro no login: {e}")
    
    def analyze_hashtag_viral_content(self, hashtag, max_posts=100, min_engagement_rate=0.03):
        """Analisar conteúdo viral por hashtag"""
        try:
            hashtag_obj = instaloader.Hashtag.from_name(self.L.context, hashtag)
            viral_posts = []
            
            count = 0
            for post in hashtag_obj.get_posts():
                if count >= max_posts:
                    break
                
                try:
                    # Calcular taxa de engajamento
                    engagement = post.likes + post.comments
                    followers = post.owner_profile.followers if hasattr(post, 'owner_profile') else 1
                    engagement_rate = engagement / max(followers, 1)
                    
                    # Filtrar por engajamento mínimo
                    if engagement_rate >= min_engagement_rate:
                        post_data = {
                            'shortcode': post.shortcode,
                            'url': f'https://instagram.com/p/{post.shortcode}/',
                            'likes': post.likes,
                            'comments': post.comments,
                            'engagement_total': engagement,
                            'engagement_rate': engagement_rate,
                            'date': post.date,
                            'caption': post.caption[:500] if post.caption else '',
                            'hashtags': post.caption_hashtags,
                            'mentions': post.caption_mentions,
                            'is_video': post.is_video,
                            'owner_username': post.owner_username,
                            'location': post.location.name if post.location else None
                        }
                        viral_posts.append(post_data)
                    
                    count += 1
                    time.sleep(1)  # Rate limiting
                    
                except Exception as e:
                    print(f"Erro ao processar post: {e}")
                    continue
            
            return viral_posts
            
        except Exception as e:
            print(f"Erro ao analisar hashtag {hashtag}: {e}")
            return []
    
    def analyze_profile_viral_posts(self, username, max_posts=50):
        """Analisar posts virais de um perfil"""
        try:
            profile = instaloader.Profile.from_username(self.L.context, username)
            viral_posts = []
            
            count = 0
            for post in profile.get_posts():
                if count >= max_posts:
                    break
                
                try:
                    # Métricas do post
                    engagement = post.likes + post.comments
                    engagement_rate = engagement / max(profile.followers, 1)
                    
                    post_data = {
                        'shortcode': post.shortcode,
                        'url': f'https://instagram.com/p/{post.shortcode}/',
                        'likes': post.likes,
                        'comments': post.comments,
                        'engagement_total': engagement,
                        'engagement_rate': engagement_rate,
                        'date': post.date,
                        'caption': post.caption[:500] if post.caption else '',
                        'hashtags': post.caption_hashtags,
                        'mentions': post.caption_mentions,
                        'is_video': post.is_video,
                        'owner_username': username,
                        'owner_followers': profile.followers,
                        'location': post.location.name if post.location else None
                    }
                    viral_posts.append(post_data)
                    count += 1
                    time.sleep(1)
                    
                except Exception as e:
                    print(f"Erro ao processar post: {e}")
                    continue
            
            return viral_posts
            
        except Exception as e:
            print(f"Erro ao analisar perfil {username}: {e}")
            return []
    
    def find_viral_hashtags(self, hashtags_list, sample_size=20):
        """Encontrar hashtags mais virais"""
        hashtag_metrics = {}
        
        for hashtag in hashtags_list:
            try:
                hashtag_obj = instaloader.Hashtag.from_name(self.L.context, hashtag)
                posts_sample = []
                
                count = 0
                total_engagement = 0
                
                for post in hashtag_obj.get_posts():
                    if count >= sample_size:
                        break
                    
                    engagement = post.likes + post.comments
                    total_engagement += engagement
                    count += 1
                    time.sleep(0.5)
                
                avg_engagement = total_engagement / max(count, 1)
                
                hashtag_metrics[hashtag] = {
                    'avg_engagement': avg_engagement,
                    'posts_analyzed': count,
                    'total_engagement': total_engagement
                }
                
                print(f"✅ Analisado #{hashtag}: {avg_engagement:.0f} engajamento médio")
                
            except Exception as e:
                print(f"❌ Erro ao analisar #{hashtag}: {e}")
                hashtag_metrics[hashtag] = {'avg_engagement': 0, 'posts_analyzed': 0}
        
        # Ordenar por engajamento
        sorted_hashtags = sorted(hashtag_metrics.items(), 
                               key=lambda x: x[1]['avg_engagement'], 
                               reverse=True)
        
        return sorted_hashtags
    
    def analyze_viral_patterns(self, posts_data):
        """Analisar padrões de viralidade"""
        if not posts_data:
            return {}
        
        df = pd.DataFrame(posts_data)
        
        # Análise temporal
        df['hour'] = df['date'].dt.hour
        df['day_of_week'] = df['date'].dt.day_name()
        
        best_hours = df.groupby('hour')['engagement_rate'].mean().sort_values(ascending=False)
        best_days = df.groupby('day_of_week')['engagement_rate'].mean().sort_values(ascending=False)
        
        # Análise de hashtags
        all_hashtags = []
        for hashtags in df['hashtags']:
            if hashtags:
                all_hashtags.extend(hashtags)
        
        top_hashtags = Counter(all_hashtags).most_common(10)
        
        # Análise de conteúdo
        video_vs_photo = df.groupby('is_video')['engagement_rate'].mean()
        
        # Estatísticas gerais
        stats = {
            'total_posts': len(df),
            'avg_engagement_rate': df['engagement_rate'].mean(),
            'median_engagement_rate': df['engagement_rate'].median(),
            'top_post_engagement': df['engagement_rate'].max(),
            'best_hours': best_hours.head(3).to_dict(),
            'best_days': best_days.head(3).to_dict(),
            'top_hashtags': top_hashtags,
            'video_vs_photo_engagement': video_vs_photo.to_dict(),
            'posts_with_location': df['location'].notna().sum(),
            'avg_hashtags_per_post': df['hashtags'].apply(lambda x: len(x) if x else 0).mean()
        }
        
        return stats

# Exemplo de uso completo
def main():
    # Inicializar analisador (com login opcional)
    analyzer = InstagramViralAnalyzer('username', 'password')
    
    # 1. Analisar hashtags virais
    viral_hashtags = ['viral', 'trending', 'fyp', 'explore', 'instagood']
    hashtag_ranking = analyzer.find_viral_hashtags(viral_hashtags)
    
    print("Ranking de hashtags por engajamento:")
    for hashtag, metrics in hashtag_ranking[:3]:
        print(f"#{hashtag}: {metrics['avg_engagement']:.0f} engajamento médio")
    
    # 2. Analisar conteúdo viral da hashtag mais performática
    top_hashtag = hashtag_ranking[0][0]
    viral_posts = analyzer.analyze_hashtag_viral_content(top_hashtag, max_posts=50)
    
    # 3. Analisar padrões
    patterns = analyzer.analyze_viral_patterns(viral_posts)
    
    print(f"\nPadrões de viralidade para #{top_hashtag}:")
    print(f"Posts analisados: {patterns['total_posts']}")
    print(f"Engajamento médio: {patterns['avg_engagement_rate']:.4f}")
    print(f"Melhores horários: {list(patterns['best_hours'].keys())[:3]}")
    print(f"Top hashtags: {[h[0] for h in patterns['top_hashtags'][:5]]}")
    
    # 4. Salvar resultados
    df_posts = pd.DataFrame(viral_posts)
    df_posts.to_csv(f'viral_posts_{top_hashtag}_{datetime.now().strftime("%Y%m%d")}.csv', index=False)
    
    with open(f'viral_patterns_{top_hashtag}.json', 'w') as f:
        json.dump(patterns, f, indent=2, default=str)
    
    return viral_posts, patterns

# Executar análise
# viral_posts, patterns = main()
```

---

## 🔗 YT-DLP - DOWNLOAD UNIVERSAL

### INFORMAÇÕES TÉCNICAS:
- **Repositório:** https://github.com/yt-dlp/yt-dlp
- **Funcionalidade:** Download de vídeos de 1000+ sites
- **Suporte:** YouTube, Instagram, TikTok, Twitter, etc.

### INTEGRAÇÃO COM OUTRAS BIBLIOTECAS:
```python
import yt_dlp
import json

class UniversalMediaDownloader:
    def __init__(self):
        self.ydl_opts = {
            'writeinfojson': True,
            'writesubtitles': True,
            'writeautomaticsub': True,
            'subtitleslangs': ['pt', 'en'],
            'format': 'best[height<=720]',
            'outtmpl': '%(uploader)s/%(title)s.%(ext)s'
        }
    
    def download_with_metadata(self, url):
        """Download com metadados completos"""
        with yt_dlp.YoutubeDL(self.ydl_opts) as ydl:
            try:
                info = ydl.extract_info(url, download=True)
                return info
            except Exception as e:
                print(f"Erro no download: {e}")
                return None
    
    def extract_metadata_only(self, url):
        """Extrair apenas metadados sem download"""
        opts = {**self.ydl_opts, 'skip_download': True}
        
        with yt_dlp.YoutubeDL(opts) as ydl:
            try:
                info = ydl.extract_info(url, download=False)
                return info
            except Exception as e:
                print(f"Erro na extração: {e}")
                return None

# Uso integrado
downloader = UniversalMediaDownloader()

# URLs de diferentes plataformas
urls = [
    'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
    'https://www.instagram.com/p/ABC123/',
    'https://twitter.com/user/status/123456789'
]

for url in urls:
    metadata = downloader.extract_metadata_only(url)
    if metadata:
        print(f"Título: {metadata.get('title')}")
        print(f"Views: {metadata.get('view_count')}")
        print(f"Duração: {metadata.get('duration')} segundos")
```

---

## ✅ RESUMO EXECUTIVO

### BIBLIOTECAS VALIDADAS:
1. **Twikit** - Twitter/X sem API key (Score: 7.9/10)
2. **YouTube Transcript API** - Transcrições avançadas (Score: 8.9/10)
3. **YouTube Data API** - API oficial completa (181 snippets)
4. **Instaloader** - Instagram especializado (Score: 6.5/10)
5. **YT-DLP** - Download universal (1000+ sites)

### CAPACIDADES COMBINADAS:
- ✅ **Extração completa** de todas as principais plataformas
- ✅ **Análise de sentimento** e engajamento
- ✅ **Métricas de viralidade** personalizadas
- ✅ **Processamento em lote** otimizado
- ✅ **Rate limiting** inteligente
- ✅ **Exportação estruturada** (CSV, JSON, DataFrame)

### PRÓXIMOS PASSOS:
1. **Integração** com Web-Agent para automação
2. **Sistema de cache** para performance
3. **Dashboard** de visualização
4. **API unificada** para todas as plataformas
5. **Machine Learning** para predição viral

**Esta base de conhecimento serve como referência técnica completa para implementação de sistemas avançados de análise de conteúdo viral.** 🎯
