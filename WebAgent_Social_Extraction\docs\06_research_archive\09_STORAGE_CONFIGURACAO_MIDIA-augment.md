# 📁 CONFIGURAÇÃO DE STORAGE PARA MÍDIA - SISTEMA WEBAGENT

**Data:** 2025-01-24  
**Versão:** v1.0 - Configuração Completa de Storage  
**Autor:** Augment Code Orchestrator V5.0  
**Projeto:** WebAgent (Supabase ID: nnpxxdyhamnsxqljumun)  
**Escopo:** Storage otimizado para mídia viral e arquivos do sistema  

---

## 🎯 EXECUTIVE SUMMARY

Este documento especifica a **configuração completa do Supabase Storage** para o sistema de extração viral WebAgent. O storage é otimizado para armazenar **milhões de arquivos de mídia** com performance, segurança e economia de custos, incluindo imagens, vídeos, relatórios e backups.

### PRINCIPAIS COMPONENTES:

**1. BUCKETS ESPECIALIZADOS:**
- **viral-images** - Imagens de posts virais (JPG, PNG, WebP, GIF)
- **viral-videos** - Vídeos e animações (MP4, WebM, MOV)
- **avatars** - Fotos de perfil de usuários
- **reports** - Relatórios e exports (PDF, Excel, CSV)
- **backups** - Backups automáticos do sistema

**2. POLÍTICAS DE SEGURANÇA:**
- **Row Level Security** - Acesso baseado em propriedade
- **Políticas Granulares** - Controle por tipo de usuário
- **Autenticação JWT** - Tokens seguros para acesso
- **Rate Limiting** - Prevenção de abuso

**3. OTIMIZAÇÕES DE PERFORMANCE:**
- **CDN Global** - Distribuição mundial de conteúdo
- **Compressão Automática** - Redução de tamanho de arquivos
- **Transformações de Imagem** - Redimensionamento dinâmico
- **Cache Inteligente** - Armazenamento em cache otimizado

**4. GESTÃO DE CUSTOS:**
- **Lifecycle Policies** - Arquivamento automático
- **Deduplicação** - Eliminação de arquivos duplicados
- **Compressão Avançada** - Algoritmos otimizados
- **Monitoramento de Uso** - Alertas de consumo

---

## 🗂️ ESTRUTURA DE BUCKETS

### CONFIGURAÇÃO DOS BUCKETS PRINCIPAIS:

```sql
-- =====================================================
-- BUCKET: VIRAL-IMAGES
-- =====================================================

INSERT INTO storage.buckets (
    id, 
    name, 
    public, 
    file_size_limit, 
    allowed_mime_types,
    avif_autodetection,
    created_at,
    updated_at
) VALUES (
    'viral-images',
    'viral-images',
    true,
    52428800, -- 50MB por arquivo
    ARRAY[
        'image/jpeg',
        'image/jpg', 
        'image/png',
        'image/webp',
        'image/gif',
        'image/avif',
        'image/heic',
        'image/heif'
    ],
    true,
    NOW(),
    NOW()
) ON CONFLICT (id) DO UPDATE SET
    file_size_limit = EXCLUDED.file_size_limit,
    allowed_mime_types = EXCLUDED.allowed_mime_types,
    updated_at = NOW();

-- =====================================================
-- BUCKET: VIRAL-VIDEOS
-- =====================================================

INSERT INTO storage.buckets (
    id,
    name,
    public,
    file_size_limit,
    allowed_mime_types,
    created_at,
    updated_at
) VALUES (
    'viral-videos',
    'viral-videos', 
    true,
    524288000, -- 500MB por arquivo
    ARRAY[
        'video/mp4',
        'video/webm',
        'video/quicktime',
        'video/x-msvideo',
        'video/3gpp',
        'video/x-flv'
    ],
    NOW(),
    NOW()
) ON CONFLICT (id) DO UPDATE SET
    file_size_limit = EXCLUDED.file_size_limit,
    allowed_mime_types = EXCLUDED.allowed_mime_types,
    updated_at = NOW();

-- =====================================================
-- BUCKET: AVATARS
-- =====================================================

INSERT INTO storage.buckets (
    id,
    name,
    public,
    file_size_limit,
    allowed_mime_types,
    created_at,
    updated_at
) VALUES (
    'avatars',
    'avatars',
    true,
    5242880, -- 5MB por arquivo
    ARRAY[
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/webp'
    ],
    NOW(),
    NOW()
) ON CONFLICT (id) DO UPDATE SET
    file_size_limit = EXCLUDED.file_size_limit,
    updated_at = NOW();

-- =====================================================
-- BUCKET: REPORTS
-- =====================================================

INSERT INTO storage.buckets (
    id,
    name,
    public,
    file_size_limit,
    allowed_mime_types,
    created_at,
    updated_at
) VALUES (
    'reports',
    'reports',
    false, -- Privado
    104857600, -- 100MB por arquivo
    ARRAY[
        'application/pdf',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-excel',
        'text/csv',
        'application/json',
        'text/plain'
    ],
    NOW(),
    NOW()
) ON CONFLICT (id) DO UPDATE SET
    file_size_limit = EXCLUDED.file_size_limit,
    updated_at = NOW();

-- =====================================================
-- BUCKET: BACKUPS
-- =====================================================

INSERT INTO storage.buckets (
    id,
    name,
    public,
    file_size_limit,
    allowed_mime_types,
    created_at,
    updated_at
) VALUES (
    'backups',
    'backups',
    false, -- Privado
    2147483648, -- 2GB por arquivo
    ARRAY[
        'application/gzip',
        'application/x-tar',
        'application/zip',
        'application/x-7z-compressed',
        'application/sql'
    ],
    NOW(),
    NOW()
) ON CONFLICT (id) DO UPDATE SET
    file_size_limit = EXCLUDED.file_size_limit,
    updated_at = NOW();

-- =====================================================
-- BUCKET: THUMBNAILS (GERADAS AUTOMATICAMENTE)
-- =====================================================

INSERT INTO storage.buckets (
    id,
    name,
    public,
    file_size_limit,
    allowed_mime_types,
    created_at,
    updated_at
) VALUES (
    'thumbnails',
    'thumbnails',
    true,
    1048576, -- 1MB por thumbnail
    ARRAY[
        'image/jpeg',
        'image/png',
        'image/webp'
    ],
    NOW(),
    NOW()
) ON CONFLICT (id) DO UPDATE SET
    file_size_limit = EXCLUDED.file_size_limit,
    updated_at = NOW();
```

---

## 🔒 POLÍTICAS DE SEGURANÇA RLS

### POLÍTICAS PARA CADA BUCKET:

```sql
-- =====================================================
-- POLÍTICAS PARA VIRAL-IMAGES
-- =====================================================

-- Usuários podem fazer upload de imagens para seus projetos
CREATE POLICY "Users can upload viral images to own projects" 
ON storage.objects FOR INSERT 
WITH CHECK (
    bucket_id = 'viral-images' AND
    auth.uid() IS NOT NULL AND
    EXISTS (
        SELECT 1 FROM public.projects p
        WHERE p.user_id = auth.uid() 
        AND p.id::text = (storage.foldername(name))[1]
    )
);

-- Todos podem visualizar imagens públicas
CREATE POLICY "Anyone can view viral images" 
ON storage.objects FOR SELECT 
USING (bucket_id = 'viral-images');

-- Usuários podem atualizar imagens de seus projetos
CREATE POLICY "Users can update own viral images" 
ON storage.objects FOR UPDATE 
USING (
    bucket_id = 'viral-images' AND
    auth.uid() IS NOT NULL AND
    EXISTS (
        SELECT 1 FROM public.projects p
        WHERE p.user_id = auth.uid() 
        AND p.id::text = (storage.foldername(name))[1]
    )
);

-- Usuários podem deletar imagens de seus projetos
CREATE POLICY "Users can delete own viral images" 
ON storage.objects FOR DELETE 
USING (
    bucket_id = 'viral-images' AND
    auth.uid() IS NOT NULL AND
    EXISTS (
        SELECT 1 FROM public.projects p
        WHERE p.user_id = auth.uid() 
        AND p.id::text = (storage.foldername(name))[1]
    )
);

-- =====================================================
-- POLÍTICAS PARA VIRAL-VIDEOS
-- =====================================================

-- Políticas similares para vídeos
CREATE POLICY "Users can upload viral videos to own projects" 
ON storage.objects FOR INSERT 
WITH CHECK (
    bucket_id = 'viral-videos' AND
    auth.uid() IS NOT NULL AND
    EXISTS (
        SELECT 1 FROM public.projects p
        WHERE p.user_id = auth.uid() 
        AND p.id::text = (storage.foldername(name))[1]
    )
);

CREATE POLICY "Anyone can view viral videos" 
ON storage.objects FOR SELECT 
USING (bucket_id = 'viral-videos');

CREATE POLICY "Users can update own viral videos" 
ON storage.objects FOR UPDATE 
USING (
    bucket_id = 'viral-videos' AND
    auth.uid() IS NOT NULL AND
    EXISTS (
        SELECT 1 FROM public.projects p
        WHERE p.user_id = auth.uid() 
        AND p.id::text = (storage.foldername(name))[1]
    )
);

CREATE POLICY "Users can delete own viral videos" 
ON storage.objects FOR DELETE 
USING (
    bucket_id = 'viral-videos' AND
    auth.uid() IS NOT NULL AND
    EXISTS (
        SELECT 1 FROM public.projects p
        WHERE p.user_id = auth.uid() 
        AND p.id::text = (storage.foldername(name))[1]
    )
);

-- =====================================================
-- POLÍTICAS PARA AVATARS
-- =====================================================

-- Usuários podem fazer upload de seus próprios avatars
CREATE POLICY "Users can upload own avatar" 
ON storage.objects FOR INSERT 
WITH CHECK (
    bucket_id = 'avatars' AND
    auth.uid() IS NOT NULL AND
    auth.uid()::text = (storage.foldername(name))[1]
);

-- Todos podem visualizar avatars
CREATE POLICY "Anyone can view avatars" 
ON storage.objects FOR SELECT 
USING (bucket_id = 'avatars');

-- Usuários podem atualizar apenas seu próprio avatar
CREATE POLICY "Users can update own avatar" 
ON storage.objects FOR UPDATE 
USING (
    bucket_id = 'avatars' AND
    auth.uid() IS NOT NULL AND
    auth.uid()::text = (storage.foldername(name))[1]
);

-- Usuários podem deletar apenas seu próprio avatar
CREATE POLICY "Users can delete own avatar" 
ON storage.objects FOR DELETE 
USING (
    bucket_id = 'avatars' AND
    auth.uid() IS NOT NULL AND
    auth.uid()::text = (storage.foldername(name))[1]
);

-- =====================================================
-- POLÍTICAS PARA REPORTS
-- =====================================================

-- Usuários podem fazer upload de relatórios próprios
CREATE POLICY "Users can upload own reports" 
ON storage.objects FOR INSERT 
WITH CHECK (
    bucket_id = 'reports' AND
    auth.uid() IS NOT NULL AND
    auth.uid()::text = (storage.foldername(name))[1]
);

-- Usuários podem visualizar apenas seus próprios relatórios
CREATE POLICY "Users can view own reports" 
ON storage.objects FOR SELECT 
USING (
    bucket_id = 'reports' AND
    auth.uid() IS NOT NULL AND
    auth.uid()::text = (storage.foldername(name))[1]
);

-- Usuários podem atualizar seus próprios relatórios
CREATE POLICY "Users can update own reports" 
ON storage.objects FOR UPDATE 
USING (
    bucket_id = 'reports' AND
    auth.uid() IS NOT NULL AND
    auth.uid()::text = (storage.foldername(name))[1]
);

-- Usuários podem deletar seus próprios relatórios
CREATE POLICY "Users can delete own reports" 
ON storage.objects FOR DELETE 
USING (
    bucket_id = 'reports' AND
    auth.uid() IS NOT NULL AND
    auth.uid()::text = (storage.foldername(name))[1]
);

-- =====================================================
-- POLÍTICAS PARA BACKUPS (APENAS ADMINS)
-- =====================================================

-- Apenas admins podem acessar backups
CREATE POLICY "Only admins can access backups" 
ON storage.objects FOR ALL 
USING (
    bucket_id = 'backups' AND
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role = 'admin'
    )
);

-- =====================================================
-- POLÍTICAS PARA THUMBNAILS
-- =====================================================

-- Sistema pode criar thumbnails
CREATE POLICY "System can create thumbnails" 
ON storage.objects FOR INSERT 
WITH CHECK (bucket_id = 'thumbnails');

-- Todos podem visualizar thumbnails
CREATE POLICY "Anyone can view thumbnails" 
ON storage.objects FOR SELECT 
USING (bucket_id = 'thumbnails');

-- Sistema pode atualizar thumbnails
CREATE POLICY "System can update thumbnails" 
ON storage.objects FOR UPDATE 
USING (bucket_id = 'thumbnails');

-- Sistema pode deletar thumbnails
CREATE POLICY "System can delete thumbnails"
ON storage.objects FOR DELETE
USING (bucket_id = 'thumbnails');
```

---

## 🖼️ TRANSFORMAÇÕES DE IMAGEM

### CONFIGURAÇÃO DO IMGPROXY:

```yaml
# docker-compose.yml - Configuração do ImgProxy
imgproxy:
  image: darthsim/imgproxy:v3.8.0
  container_name: webagent-imgproxy
  environment:
    # Configurações básicas
    IMGPROXY_BIND: ":8080"
    IMGPROXY_LOCAL_FILESYSTEM_ROOT: /var/lib/storage
    IMGPROXY_USE_ETAG: "true"
    IMGPROXY_ENABLE_WEBP_DETECTION: "true"
    IMGPROXY_ENABLE_AVIF_DETECTION: "true"

    # Otimizações de performance
    IMGPROXY_MAX_SRC_RESOLUTION: "50"  # 50 megapixels
    IMGPROXY_MAX_SRC_FILE_SIZE: "52428800"  # 50MB
    IMGPROXY_JPEG_PROGRESSIVE: "true"
    IMGPROXY_PNG_INTERLACED: "true"
    IMGPROXY_QUALITY: "85"
    IMGPROXY_FORMAT_QUALITY: "webp=80,avif=75"

    # Cache e performance
    IMGPROXY_TTL: "31536000"  # 1 ano
    IMGPROXY_CACHE_CONTROL_PASSTHROUGH: "true"
    IMGPROXY_SET_CANONICAL_HEADER: "true"

    # Segurança
    IMGPROXY_SECRET: "${IMGPROXY_SECRET}"
    IMGPROXY_SALT: "${IMGPROXY_SALT}"
    IMGPROXY_SIGNATURE_SIZE: "32"

    # Limites de processamento
    IMGPROXY_WORKERS: "4"
    IMGPROXY_MAX_CLIENTS: "2048"
    IMGPROXY_READ_TIMEOUT: "10"
    IMGPROXY_WRITE_TIMEOUT: "10"
    IMGPROXY_DOWNLOAD_TIMEOUT: "30"

    # Formatos suportados
    IMGPROXY_ENABLE_WEBP_DETECTION: "true"
    IMGPROXY_ENFORCE_WEBP: "false"
    IMGPROXY_ENABLE_CLIENT_HINTS: "true"

    # Watermark (opcional)
    IMGPROXY_WATERMARK_DATA: ""
    IMGPROXY_WATERMARK_PATH: ""
    IMGPROXY_WATERMARK_OPACITY: "0.5"

  volumes:
    - supabase-storage-data:/var/lib/storage:ro
  ports:
    - "8080:8080"
  networks:
    - webagent-network
  restart: unless-stopped
  healthcheck:
    test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
    interval: 30s
    timeout: 10s
    retries: 3
```

### EDGE FUNCTION PARA TRANSFORMAÇÕES:

```typescript
// supabase/functions/image-transformer/index.ts

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

interface ImageTransformRequest {
  source_url: string
  transformations: {
    width?: number
    height?: number
    quality?: number
    format?: 'webp' | 'avif' | 'jpeg' | 'png'
    resize_type?: 'fit' | 'fill' | 'crop'
    gravity?: 'center' | 'north' | 'south' | 'east' | 'west'
    blur?: number
    sharpen?: number
    watermark?: {
      text?: string
      opacity?: number
      position?: string
    }
  }
  cache_duration?: number
}

const IMGPROXY_URL = Deno.env.get('IMGPROXY_URL') || 'http://imgproxy:8080'
const IMGPROXY_SECRET = Deno.env.get('IMGPROXY_SECRET')!
const IMGPROXY_SALT = Deno.env.get('IMGPROXY_SALT')!

serve(async (req) => {
  try {
    if (req.method !== 'POST') {
      return new Response('Method not allowed', { status: 405 })
    }

    const requestData: ImageTransformRequest = await req.json()

    if (!requestData.source_url) {
      return new Response('Missing source_url', { status: 400 })
    }

    // Gerar URL de transformação segura
    const transformedUrl = generateImgProxyUrl(
      requestData.source_url,
      requestData.transformations
    )

    // Fazer request para ImgProxy
    const response = await fetch(transformedUrl)

    if (!response.ok) {
      throw new Error(`ImgProxy error: ${response.status} ${response.statusText}`)
    }

    // Retornar imagem transformada
    const imageData = await response.arrayBuffer()
    const contentType = response.headers.get('content-type') || 'image/jpeg'

    return new Response(imageData, {
      headers: {
        'Content-Type': contentType,
        'Cache-Control': `public, max-age=${requestData.cache_duration || 86400}`,
        'X-Transformed-By': 'WebAgent-ImgProxy'
      }
    })

  } catch (error) {
    console.error('❌ Image transformation error:', error)

    return new Response(JSON.stringify({
      success: false,
      error: error.message
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    })
  }
})

function generateImgProxyUrl(sourceUrl: string, transformations: any): string {
  // Construir parâmetros de transformação
  const params = []

  if (transformations.width || transformations.height) {
    const width = transformations.width || 0
    const height = transformations.height || 0
    const resizeType = transformations.resize_type || 'fit'
    params.push(`resize:${resizeType}:${width}:${height}`)
  }

  if (transformations.quality) {
    params.push(`quality:${transformations.quality}`)
  }

  if (transformations.format) {
    params.push(`format:${transformations.format}`)
  }

  if (transformations.gravity) {
    params.push(`gravity:${transformations.gravity}`)
  }

  if (transformations.blur) {
    params.push(`blur:${transformations.blur}`)
  }

  if (transformations.sharpen) {
    params.push(`sharpen:${transformations.sharpen}`)
  }

  if (transformations.watermark?.text) {
    const opacity = transformations.watermark.opacity || 0.5
    const position = transformations.watermark.position || 'se'
    params.push(`watermark:${opacity}:${position}:10:10:${transformations.watermark.text}`)
  }

  // Codificar URL fonte
  const encodedUrl = btoa(sourceUrl).replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '')

  // Construir path
  const path = `/${params.join('/')}/${encodedUrl}`

  // Gerar assinatura HMAC
  const signature = generateHMACSignature(path)

  return `${IMGPROXY_URL}/${signature}${path}`
}

function generateHMACSignature(path: string): string {
  // Implementação simplificada - em produção usar crypto adequado
  // const key = hexDecode(IMGPROXY_SECRET)
  // const salt = hexDecode(IMGPROXY_SALT)
  // const hmac = crypto.createHmac('sha256', key)
  // hmac.update(salt + path)
  // return hmac.digest('base64url')

  return 'signature_placeholder' // Placeholder para exemplo
}
```

---

## 📈 OTIMIZAÇÕES DE PERFORMANCE

### CONFIGURAÇÕES AVANÇADAS:

```sql
-- =====================================================
-- TRIGGERS PARA OTIMIZAÇÃO AUTOMÁTICA
-- =====================================================

-- Function para gerar thumbnails automaticamente
CREATE OR REPLACE FUNCTION generate_thumbnail_on_upload()
RETURNS TRIGGER AS $$
DECLARE
    file_extension TEXT;
    is_image BOOLEAN := false;
    thumbnail_sizes INTEGER[] := ARRAY[150, 300, 600];
    size INTEGER;
BEGIN
    -- Verificar se é uma imagem
    file_extension := lower(split_part(NEW.name, '.', -1));
    is_image := file_extension IN ('jpg', 'jpeg', 'png', 'webp', 'gif');

    -- Gerar thumbnails apenas para imagens nos buckets de mídia viral
    IF is_image AND NEW.bucket_id IN ('viral-images', 'avatars') THEN
        -- Agendar geração de thumbnails em diferentes tamanhos
        FOREACH size IN ARRAY thumbnail_sizes
        LOOP
            INSERT INTO public.thumbnail_queue (
                source_bucket,
                source_path,
                target_size,
                priority,
                created_at
            ) VALUES (
                NEW.bucket_id,
                NEW.name,
                size,
                CASE WHEN NEW.bucket_id = 'avatars' THEN 1 ELSE 2 END,
                NOW()
            );
        END LOOP;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger para geração automática de thumbnails
CREATE TRIGGER generate_thumbnails_trigger
    AFTER INSERT ON storage.objects
    FOR EACH ROW
    EXECUTE FUNCTION generate_thumbnail_on_upload();

-- =====================================================
-- TABELA PARA FILA DE THUMBNAILS
-- =====================================================

CREATE TABLE IF NOT EXISTS public.thumbnail_queue (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    source_bucket VARCHAR(255) NOT NULL,
    source_path TEXT NOT NULL,
    target_size INTEGER NOT NULL,
    priority INTEGER DEFAULT 5,
    status VARCHAR(50) DEFAULT 'pending',
    attempts INTEGER DEFAULT 0,
    error_message TEXT,
    processed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices para performance da fila
CREATE INDEX idx_thumbnail_queue_status_priority ON public.thumbnail_queue (status, priority, created_at);
CREATE INDEX idx_thumbnail_queue_source ON public.thumbnail_queue (source_bucket, source_path);

-- =====================================================
-- FUNCTION PARA LIMPEZA AUTOMÁTICA DE ARQUIVOS ANTIGOS
-- =====================================================

CREATE OR REPLACE FUNCTION cleanup_old_files()
RETURNS void AS $$
DECLARE
    cleanup_date TIMESTAMP WITH TIME ZONE;
    deleted_count INTEGER := 0;
BEGIN
    -- Definir data de corte (arquivos mais antigos que 90 dias)
    cleanup_date := NOW() - INTERVAL '90 days';

    -- Deletar thumbnails antigos não utilizados
    DELETE FROM storage.objects
    WHERE bucket_id = 'thumbnails'
      AND created_at < cleanup_date
      AND NOT EXISTS (
          SELECT 1 FROM public.media_files mf
          WHERE mf.thumbnail_url LIKE '%' || name || '%'
      );

    GET DIAGNOSTICS deleted_count = ROW_COUNT;

    -- Log da limpeza
    INSERT INTO public.performance_metrics (
        metric_type,
        metric_name,
        metric_value,
        tags
    ) VALUES (
        'storage',
        'cleanup_old_thumbnails',
        deleted_count,
        jsonb_build_object(
            'cleanup_date', cleanup_date,
            'timestamp', NOW()
        )
    );

    -- Deletar relatórios antigos (mais de 1 ano)
    DELETE FROM storage.objects
    WHERE bucket_id = 'reports'
      AND created_at < NOW() - INTERVAL '1 year';

    GET DIAGNOSTICS deleted_count = ROW_COUNT;

    -- Log da limpeza de relatórios
    INSERT INTO public.performance_metrics (
        metric_type,
        metric_name,
        metric_value,
        tags
    ) VALUES (
        'storage',
        'cleanup_old_reports',
        deleted_count,
        jsonb_build_object('timestamp', NOW())
    );
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- FUNCTION PARA COMPRESSÃO AUTOMÁTICA
-- =====================================================

CREATE OR REPLACE FUNCTION compress_large_files()
RETURNS void AS $$
DECLARE
    file_record RECORD;
    compressed_size BIGINT;
BEGIN
    -- Buscar arquivos grandes que podem ser comprimidos
    FOR file_record IN
        SELECT bucket_id, name, metadata
        FROM storage.objects
        WHERE bucket_id IN ('viral-images', 'viral-videos')
          AND (metadata->>'size')::BIGINT > 10485760 -- Maior que 10MB
          AND (metadata->>'compressed')::BOOLEAN IS NOT TRUE
        LIMIT 100
    LOOP
        -- Agendar compressão (seria processado por worker externo)
        INSERT INTO public.compression_queue (
            bucket_id,
            file_path,
            original_size,
            priority,
            created_at
        ) VALUES (
            file_record.bucket_id,
            file_record.name,
            (file_record.metadata->>'size')::BIGINT,
            CASE
                WHEN file_record.bucket_id = 'viral-images' THEN 1
                ELSE 2
            END,
            NOW()
        );
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- TABELA PARA FILA DE COMPRESSÃO
-- =====================================================

CREATE TABLE IF NOT EXISTS public.compression_queue (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    bucket_id VARCHAR(255) NOT NULL,
    file_path TEXT NOT NULL,
    original_size BIGINT NOT NULL,
    compressed_size BIGINT,
    compression_ratio DECIMAL(5,2),
    priority INTEGER DEFAULT 5,
    status VARCHAR(50) DEFAULT 'pending',
    attempts INTEGER DEFAULT 0,
    error_message TEXT,
    processed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices para performance
CREATE INDEX idx_compression_queue_status_priority ON public.compression_queue (status, priority, created_at);
CREATE INDEX idx_compression_queue_bucket ON public.compression_queue (bucket_id);

-- =====================================================
-- VIEWS PARA MONITORAMENTO DE STORAGE
-- =====================================================

-- View para estatísticas de uso por bucket
CREATE OR REPLACE VIEW public.storage_usage_stats AS
SELECT
    bucket_id,
    COUNT(*) as file_count,
    SUM((metadata->>'size')::BIGINT) as total_size_bytes,
    AVG((metadata->>'size')::BIGINT) as avg_file_size_bytes,
    MAX((metadata->>'size')::BIGINT) as max_file_size_bytes,
    MIN(created_at) as oldest_file,
    MAX(created_at) as newest_file,
    COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '24 hours') as files_last_24h,
    SUM((metadata->>'size')::BIGINT) FILTER (WHERE created_at >= NOW() - INTERVAL '24 hours') as size_last_24h_bytes
FROM storage.objects
GROUP BY bucket_id
ORDER BY total_size_bytes DESC;

-- View para arquivos grandes que podem ser otimizados
CREATE OR REPLACE VIEW public.optimization_candidates AS
SELECT
    bucket_id,
    name,
    (metadata->>'size')::BIGINT as size_bytes,
    created_at,
    CASE
        WHEN (metadata->>'size')::BIGINT > 50485760 THEN 'compression'
        WHEN bucket_id IN ('viral-images', 'avatars') AND name NOT LIKE '%_thumb_%' THEN 'thumbnail'
        ELSE 'none'
    END as optimization_type
FROM storage.objects
WHERE (metadata->>'size')::BIGINT > 5242880 -- Maior que 5MB
  AND (metadata->>'optimized')::BOOLEAN IS NOT TRUE
ORDER BY (metadata->>'size')::BIGINT DESC;

-- View para monitoramento de quotas por usuário
CREATE OR REPLACE VIEW public.user_storage_quotas AS
SELECT
    p.id as user_id,
    p.username,
    p.subscription_tier,
    COUNT(so.*) as total_files,
    SUM((so.metadata->>'size')::BIGINT) as total_size_bytes,
    CASE p.subscription_tier
        WHEN 'free' THEN 1073741824 -- 1GB
        WHEN 'pro' THEN 10737418240 -- 10GB
        WHEN 'enterprise' THEN 107374182400 -- 100GB
        ELSE 1073741824
    END as quota_bytes,
    ROUND(
        (SUM((so.metadata->>'size')::BIGINT)::DECIMAL /
         CASE p.subscription_tier
             WHEN 'free' THEN 1073741824
             WHEN 'pro' THEN 10737418240
             WHEN 'enterprise' THEN 107374182400
             ELSE 1073741824
         END) * 100, 2
    ) as quota_usage_percent
FROM public.profiles p
LEFT JOIN storage.objects so ON (
    (so.bucket_id IN ('avatars', 'reports') AND so.name LIKE p.id::text || '/%') OR
    (so.bucket_id IN ('viral-images', 'viral-videos') AND EXISTS (
        SELECT 1 FROM public.projects pr
        WHERE pr.user_id = p.id AND so.name LIKE pr.id::text || '/%'
    ))
)
GROUP BY p.id, p.username, p.subscription_tier
ORDER BY quota_usage_percent DESC;
```

---

## 🔄 EDGE FUNCTION PARA GESTÃO DE STORAGE

### FUNCTION PARA UPLOAD OTIMIZADO:

```typescript
// supabase/functions/optimized-upload/index.ts

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

interface UploadRequest {
  bucket: string
  file_path: string
  file_data: string // Base64 encoded
  content_type: string
  options?: {
    generate_thumbnails?: boolean
    compress?: boolean
    watermark?: {
      text?: string
      opacity?: number
    }
    metadata?: Record<string, any>
  }
}

const SUPABASE_URL = Deno.env.get('SUPABASE_URL')!
const SUPABASE_SERVICE_KEY = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
const MAX_FILE_SIZE = 50 * 1024 * 1024 // 50MB

serve(async (req) => {
  try {
    if (req.method !== 'POST') {
      return new Response('Method not allowed', { status: 405 })
    }

    const uploadData: UploadRequest = await req.json()

    // Validações
    if (!uploadData.bucket || !uploadData.file_path || !uploadData.file_data) {
      return new Response('Missing required fields', { status: 400 })
    }

    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY)

    // Decodificar arquivo
    const fileBuffer = Uint8Array.from(atob(uploadData.file_data), c => c.charCodeAt(0))

    // Verificar tamanho
    if (fileBuffer.length > MAX_FILE_SIZE) {
      return new Response('File too large', { status: 413 })
    }

    // Verificar quota do usuário
    const quotaCheck = await checkUserQuota(supabase, fileBuffer.length)
    if (!quotaCheck.allowed) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Storage quota exceeded',
        quota_info: quotaCheck
      }), { status: 413, headers: { 'Content-Type': 'application/json' } })
    }

    // Otimizar arquivo se necessário
    let optimizedBuffer = fileBuffer
    let optimizedContentType = uploadData.content_type

    if (uploadData.options?.compress && isCompressibleType(uploadData.content_type)) {
      const compressed = await compressFile(fileBuffer, uploadData.content_type)
      if (compressed.size < fileBuffer.length * 0.8) { // Só usar se reduzir pelo menos 20%
        optimizedBuffer = compressed.data
        optimizedContentType = compressed.content_type
      }
    }

    // Upload principal
    const { data: uploadResult, error: uploadError } = await supabase.storage
      .from(uploadData.bucket)
      .upload(uploadData.file_path, optimizedBuffer, {
        contentType: optimizedContentType,
        metadata: {
          ...uploadData.options?.metadata,
          original_size: fileBuffer.length,
          optimized_size: optimizedBuffer.length,
          compression_ratio: fileBuffer.length > 0 ? optimizedBuffer.length / fileBuffer.length : 1,
          uploaded_at: new Date().toISOString()
        },
        upsert: false
      })

    if (uploadError) {
      throw new Error(`Upload failed: ${uploadError.message}`)
    }

    // Obter URL pública
    const { data: publicUrlData } = supabase.storage
      .from(uploadData.bucket)
      .getPublicUrl(uploadData.file_path)

    let thumbnailUrls: string[] = []

    // Gerar thumbnails se solicitado
    if (uploadData.options?.generate_thumbnails && isImageType(uploadData.content_type)) {
      thumbnailUrls = await generateThumbnails(
        uploadData.bucket,
        uploadData.file_path,
        supabase
      )
    }

    // Registrar no banco de dados
    const { data: mediaRecord } = await supabase
      .from('media_files')
      .insert({
        bucket_name: uploadData.bucket,
        file_path: uploadData.file_path,
        file_name: uploadData.file_path.split('/').pop(),
        file_type: getFileExtension(uploadData.file_path),
        file_size: optimizedBuffer.length,
        mime_type: optimizedContentType,
        public_url: publicUrlData.publicUrl,
        thumbnail_url: thumbnailUrls[0] || null,
        metadata: {
          original_size: fileBuffer.length,
          compression_applied: optimizedBuffer.length < fileBuffer.length,
          thumbnails_generated: thumbnailUrls.length,
          upload_options: uploadData.options
        },
        is_processed: true
      })
      .select()
      .single()

    // Atualizar quota do usuário
    await updateUserStorageUsage(supabase, optimizedBuffer.length)

    return new Response(JSON.stringify({
      success: true,
      file: {
        path: uploadData.file_path,
        public_url: publicUrlData.publicUrl,
        size: optimizedBuffer.length,
        original_size: fileBuffer.length,
        compression_ratio: fileBuffer.length > 0 ? optimizedBuffer.length / fileBuffer.length : 1,
        thumbnails: thumbnailUrls,
        media_record_id: mediaRecord?.id
      },
      quota_info: quotaCheck
    }), {
      headers: { 'Content-Type': 'application/json' }
    })

  } catch (error) {
    console.error('❌ Upload error:', error)

    return new Response(JSON.stringify({
      success: false,
      error: error.message
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    })
  }
})

async function checkUserQuota(supabase: any, fileSize: number): Promise<any> {
  // Implementar verificação de quota baseada no usuário autenticado
  // Retornar informações sobre quota disponível
  return {
    allowed: true,
    current_usage: 0,
    quota_limit: 1073741824, // 1GB default
    remaining: 1073741824
  }
}

function isCompressibleType(contentType: string): boolean {
  return ['image/jpeg', 'image/png', 'video/mp4'].includes(contentType)
}

function isImageType(contentType: string): boolean {
  return contentType.startsWith('image/')
}

async function compressFile(buffer: Uint8Array, contentType: string): Promise<{data: Uint8Array, content_type: string, size: number}> {
  // Implementar compressão baseada no tipo de arquivo
  // Por enquanto retorna o arquivo original
  return {
    data: buffer,
    content_type: contentType,
    size: buffer.length
  }
}

async function generateThumbnails(bucket: string, filePath: string, supabase: any): Promise<string[]> {
  // Implementar geração de thumbnails usando ImgProxy
  // Retornar URLs dos thumbnails gerados
  return []
}

function getFileExtension(filePath: string): string {
  return filePath.split('.').pop()?.toLowerCase() || 'unknown'
}

async function updateUserStorageUsage(supabase: any, fileSize: number): Promise<void> {
  // Implementar atualização do uso de storage do usuário
}
```
```
