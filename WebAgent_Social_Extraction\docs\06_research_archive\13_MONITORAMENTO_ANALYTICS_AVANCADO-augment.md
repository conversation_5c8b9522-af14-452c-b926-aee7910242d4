# 📊 MONITORAMENTO E ANALYTICS AVANÇADO - SISTEMA WEBAGENT

**Data:** 2025-01-24  
**Versão:** v1.0 - Sistema Completo de Monitoramento e Analytics  
**Autor:** Augment Code Orchestrator V5.0  
**Projeto:** WebAgent (Supabase ID: nnpxxdyhamnsxqljumun)  
**Escopo:** Monitoramento completo, alertas e analytics em tempo real  

---

## 🎯 EXECUTIVE SUMMARY

Este documento especifica o **sistema completo de monitoramento e analytics** para o WebAgent, incluindo dashboards em tempo real, alertas automáticos, métricas de performance e observabilidade completa da infraestrutura e aplicação.

### PRINCIPAIS COMPONENTES:

**1. DASHBOARDS EM TEMPO REAL:**
- **Executive Dashboard** - Métricas executivas e KPIs
- **Operational Dashboard** - Monitoramento operacional
- **Analytics Dashboard** - <PERSON><PERSON><PERSON><PERSON> de conteúdo viral
- **Performance Dashboard** - Métricas de sistema

**2. SISTEMA DE ALERTAS:**
- **Alertas de Performance** - CPU, memória, latência
- **Alertas de Negócio** - Conteúdo viral, quotas
- **Alertas de Segurança** - Tentativas de acesso, anomalias
- **Alertas de Sistema** - Falhas, indisponibilidade

**3. MÉTRICAS E KPIs:**
- **Business Metrics** - Viral score, engagement, trends
- **Technical Metrics** - Response time, error rate, throughput
- **User Metrics** - Active users, API usage, quotas
- **Infrastructure Metrics** - Database, storage, functions

**4. OBSERVABILIDADE:**
- **Distributed Tracing** - Rastreamento de requests
- **Structured Logging** - Logs estruturados e pesquisáveis
- **Error Tracking** - Monitoramento de erros
- **Health Checks** - Verificação de saúde dos serviços

---

## 📈 DASHBOARDS E VISUALIZAÇÕES

### EXECUTIVE DASHBOARD:

```sql
-- =====================================================
-- VIEW: EXECUTIVE KPIs
-- =====================================================

CREATE MATERIALIZED VIEW public.executive_kpis AS
WITH base_metrics AS (
    SELECT 
        COUNT(DISTINCT p.id) as total_projects,
        COUNT(DISTINCT p.user_id) as active_users,
        COUNT(DISTINCT e.id) as total_extractions,
        COUNT(DISTINCT vc.id) as total_content,
        AVG(vc.viral_score) as avg_viral_score,
        COUNT(DISTINCT vc.id) FILTER (WHERE vc.viral_score > 50) as viral_content_count,
        SUM((vc.engagement_metrics->>'likes_count')::BIGINT) as total_likes,
        SUM((vc.engagement_metrics->>'shares_count')::BIGINT) as total_shares,
        SUM((vc.engagement_metrics->>'comments_count')::BIGINT) as total_comments,
        COUNT(DISTINCT vc.platform) as platforms_active
    FROM public.projects p
    LEFT JOIN public.extractions e ON p.id = e.project_id
    LEFT JOIN public.viral_content vc ON e.id = vc.extraction_id
    WHERE p.is_active = true
      AND vc.published_at >= NOW() - INTERVAL '30 days'
),
growth_metrics AS (
    SELECT 
        COUNT(DISTINCT vc.id) as content_last_7d,
        COUNT(DISTINCT e.id) as extractions_last_7d,
        AVG(vc.viral_score) as avg_score_last_7d
    FROM public.extractions e
    LEFT JOIN public.viral_content vc ON e.id = vc.extraction_id
    WHERE e.created_at >= NOW() - INTERVAL '7 days'
),
prev_growth_metrics AS (
    SELECT 
        COUNT(DISTINCT vc.id) as content_prev_7d,
        COUNT(DISTINCT e.id) as extractions_prev_7d
    FROM public.extractions e
    LEFT JOIN public.viral_content vc ON e.id = vc.extraction_id
    WHERE e.created_at >= NOW() - INTERVAL '14 days'
      AND e.created_at < NOW() - INTERVAL '7 days'
)
SELECT 
    -- Métricas principais
    bm.total_projects,
    bm.active_users,
    bm.total_extractions,
    bm.total_content,
    ROUND(bm.avg_viral_score::NUMERIC, 2) as avg_viral_score,
    bm.viral_content_count,
    bm.total_likes,
    bm.total_shares,
    bm.total_comments,
    bm.platforms_active,
    
    -- Métricas de crescimento
    gm.content_last_7d,
    gm.extractions_last_7d,
    ROUND(gm.avg_score_last_7d::NUMERIC, 2) as avg_score_last_7d,
    
    -- Taxas de crescimento
    CASE 
        WHEN pgm.content_prev_7d > 0 THEN 
            ROUND(((gm.content_last_7d - pgm.content_prev_7d)::DECIMAL / pgm.content_prev_7d * 100), 1)
        ELSE NULL 
    END as content_growth_rate,
    
    CASE 
        WHEN pgm.extractions_prev_7d > 0 THEN 
            ROUND(((gm.extractions_last_7d - pgm.extractions_prev_7d)::DECIMAL / pgm.extractions_prev_7d * 100), 1)
        ELSE NULL 
    END as extraction_growth_rate,
    
    -- Métricas de qualidade
    CASE 
        WHEN bm.total_content > 0 THEN 
            ROUND((bm.viral_content_count::DECIMAL / bm.total_content * 100), 1)
        ELSE 0 
    END as viral_content_percentage,
    
    -- Timestamp da atualização
    NOW() as updated_at
    
FROM base_metrics bm
CROSS JOIN growth_metrics gm
CROSS JOIN prev_growth_metrics pgm;

-- Índice único para refresh concorrente
CREATE UNIQUE INDEX idx_executive_kpis_updated ON public.executive_kpis (updated_at);

-- =====================================================
-- VIEW: REAL-TIME ACTIVITY FEED
-- =====================================================

CREATE OR REPLACE VIEW public.realtime_activity AS
SELECT 
    'extraction' as activity_type,
    e.id as activity_id,
    e.user_id,
    p.name as project_name,
    e.platform,
    e.status,
    e.total_processed as metric_value,
    e.created_at as activity_time,
    jsonb_build_object(
        'platform', e.platform,
        'status', e.status,
        'processed', e.total_processed,
        'project', p.name
    ) as activity_data
FROM public.extractions e
JOIN public.projects p ON e.project_id = p.id
WHERE e.created_at >= NOW() - INTERVAL '24 hours'

UNION ALL

SELECT 
    'viral_content' as activity_type,
    vc.id as activity_id,
    p.user_id,
    p.name as project_name,
    vc.platform::text,
    'detected' as status,
    vc.viral_score as metric_value,
    vc.extracted_at as activity_time,
    jsonb_build_object(
        'platform', vc.platform,
        'viral_score', vc.viral_score,
        'title', vc.title,
        'author', vc.author_username
    ) as activity_data
FROM public.viral_content vc
JOIN public.extractions e ON vc.extraction_id = e.id
JOIN public.projects p ON e.project_id = p.id
WHERE vc.viral_score > 25
  AND vc.extracted_at >= NOW() - INTERVAL '24 hours'

UNION ALL

SELECT 
    'user_activity' as activity_type,
    al.id as activity_id,
    al.user_id,
    'System' as project_name,
    al.action as platform,
    'completed' as status,
    1 as metric_value,
    al.created_at as activity_time,
    al.details as activity_data
FROM public.activity_logs al
WHERE al.created_at >= NOW() - INTERVAL '24 hours'
  AND al.action IN ('project_created', 'extraction_started', 'viral_alert')

ORDER BY activity_time DESC
LIMIT 100;

-- =====================================================
-- FUNCTION: PERFORMANCE METRICS AGGREGATION
-- =====================================================

CREATE OR REPLACE FUNCTION public.get_performance_metrics(
    time_window INTERVAL DEFAULT '1 hour'
)
RETURNS TABLE(
    metric_category TEXT,
    metric_name TEXT,
    current_value DECIMAL,
    avg_value DECIMAL,
    max_value DECIMAL,
    min_value DECIMAL,
    trend_direction TEXT,
    alert_level TEXT
) AS $$
BEGIN
    RETURN QUERY
    WITH metric_stats AS (
        SELECT 
            pm.metric_type as category,
            pm.metric_name as name,
            pm.metric_value as current_val,
            AVG(pm.metric_value) OVER (PARTITION BY pm.metric_name) as avg_val,
            MAX(pm.metric_value) OVER (PARTITION BY pm.metric_name) as max_val,
            MIN(pm.metric_value) OVER (PARTITION BY pm.metric_name) as min_val,
            LAG(pm.metric_value) OVER (PARTITION BY pm.metric_name ORDER BY pm.measured_at) as prev_val,
            ROW_NUMBER() OVER (PARTITION BY pm.metric_name ORDER BY pm.measured_at DESC) as rn
        FROM public.performance_metrics pm
        WHERE pm.measured_at >= NOW() - time_window
    )
    SELECT 
        ms.category,
        ms.name,
        ms.current_val,
        ROUND(ms.avg_val::NUMERIC, 2),
        ms.max_val,
        ms.min_val,
        CASE 
            WHEN ms.prev_val IS NULL THEN 'stable'
            WHEN ms.current_val > ms.prev_val * 1.1 THEN 'up'
            WHEN ms.current_val < ms.prev_val * 0.9 THEN 'down'
            ELSE 'stable'
        END as trend_direction,
        CASE 
            WHEN ms.name LIKE '%error%' AND ms.current_val > 5 THEN 'critical'
            WHEN ms.name LIKE '%response_time%' AND ms.current_val > 1000 THEN 'warning'
            WHEN ms.name LIKE '%cpu%' AND ms.current_val > 80 THEN 'warning'
            WHEN ms.name LIKE '%memory%' AND ms.current_val > 85 THEN 'critical'
            ELSE 'normal'
        END as alert_level
    FROM metric_stats ms
    WHERE ms.rn = 1
    ORDER BY ms.category, ms.name;
END;
$$ LANGUAGE plpgsql;
```

---

## 🚨 SISTEMA DE ALERTAS

### CONFIGURAÇÃO DE ALERTAS:

```sql
-- =====================================================
-- TABELA: ALERT RULES
-- =====================================================

CREATE TABLE IF NOT EXISTS public.alert_rules (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    rule_name VARCHAR(255) NOT NULL,
    rule_type VARCHAR(50) NOT NULL, -- performance, business, security, system
    metric_name VARCHAR(255) NOT NULL,
    condition_operator VARCHAR(10) NOT NULL, -- >, <, >=, <=, =, !=
    threshold_value DECIMAL NOT NULL,
    severity VARCHAR(20) NOT NULL, -- critical, warning, info
    is_active BOOLEAN DEFAULT true,
    notification_channels TEXT[] DEFAULT '{}', -- email, slack, webhook
    cooldown_minutes INTEGER DEFAULT 15,
    description TEXT,
    created_by UUID REFERENCES public.profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- TABELA: ALERT INSTANCES
-- =====================================================

CREATE TABLE IF NOT EXISTS public.alert_instances (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    rule_id UUID REFERENCES public.alert_rules(id) NOT NULL,
    metric_value DECIMAL NOT NULL,
    threshold_value DECIMAL NOT NULL,
    severity VARCHAR(20) NOT NULL,
    status VARCHAR(20) DEFAULT 'active', -- active, resolved, acknowledged
    message TEXT NOT NULL,
    context_data JSONB DEFAULT '{}',
    triggered_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    resolved_at TIMESTAMP WITH TIME ZONE,
    acknowledged_at TIMESTAMP WITH TIME ZONE,
    acknowledged_by UUID REFERENCES public.profiles(id),
    notification_sent BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- FUNCTION: ALERT EVALUATION
-- =====================================================

CREATE OR REPLACE FUNCTION public.evaluate_alerts()
RETURNS void AS $$
DECLARE
    rule_record RECORD;
    current_metric_value DECIMAL;
    alert_triggered BOOLEAN;
    last_alert TIMESTAMP WITH TIME ZONE;
BEGIN
    -- Iterar sobre todas as regras ativas
    FOR rule_record IN 
        SELECT * FROM public.alert_rules WHERE is_active = true
    LOOP
        -- Obter valor atual da métrica
        SELECT metric_value INTO current_metric_value
        FROM public.performance_metrics
        WHERE metric_name = rule_record.metric_name
        ORDER BY measured_at DESC
        LIMIT 1;
        
        -- Verificar se a condição é atendida
        alert_triggered := CASE rule_record.condition_operator
            WHEN '>' THEN current_metric_value > rule_record.threshold_value
            WHEN '<' THEN current_metric_value < rule_record.threshold_value
            WHEN '>=' THEN current_metric_value >= rule_record.threshold_value
            WHEN '<=' THEN current_metric_value <= rule_record.threshold_value
            WHEN '=' THEN current_metric_value = rule_record.threshold_value
            WHEN '!=' THEN current_metric_value != rule_record.threshold_value
            ELSE false
        END;
        
        -- Verificar cooldown
        SELECT MAX(triggered_at) INTO last_alert
        FROM public.alert_instances
        WHERE rule_id = rule_record.id
          AND status = 'active';
        
        -- Disparar alerta se necessário
        IF alert_triggered AND (
            last_alert IS NULL OR 
            last_alert < NOW() - (rule_record.cooldown_minutes || ' minutes')::INTERVAL
        ) THEN
            INSERT INTO public.alert_instances (
                rule_id,
                metric_value,
                threshold_value,
                severity,
                message,
                context_data
            ) VALUES (
                rule_record.id,
                current_metric_value,
                rule_record.threshold_value,
                rule_record.severity,
                format('Alert: %s - %s %s %s (current: %s)',
                    rule_record.rule_name,
                    rule_record.metric_name,
                    rule_record.condition_operator,
                    rule_record.threshold_value,
                    current_metric_value
                ),
                jsonb_build_object(
                    'rule_name', rule_record.rule_name,
                    'metric_name', rule_record.metric_name,
                    'current_value', current_metric_value,
                    'threshold', rule_record.threshold_value,
                    'timestamp', NOW()
                )
            );
        END IF;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- REGRAS DE ALERTA PADRÃO
-- =====================================================

INSERT INTO public.alert_rules (rule_name, rule_type, metric_name, condition_operator, threshold_value, severity, description) VALUES
('High Error Rate', 'performance', 'error_rate_percent', '>', 5, 'critical', 'Error rate above 5%'),
('Slow Response Time', 'performance', 'api_response_time_ms', '>', 1000, 'warning', 'API response time above 1 second'),
('High CPU Usage', 'system', 'cpu_usage_percent', '>', 80, 'warning', 'CPU usage above 80%'),
('High Memory Usage', 'system', 'memory_usage_percent', '>', 85, 'critical', 'Memory usage above 85%'),
('Viral Content Detected', 'business', 'viral_score', '>', 50, 'info', 'High viral score content detected'),
('API Quota Exceeded', 'business', 'api_quota_usage_percent', '>', 90, 'warning', 'API quota usage above 90%'),
('Database Connection Issues', 'system', 'db_connection_errors', '>', 0, 'critical', 'Database connection errors detected'),
('Storage Usage High', 'system', 'storage_usage_percent', '>', 80, 'warning', 'Storage usage above 80%');

-- =====================================================
-- TRIGGER: AUTOMATIC ALERT EVALUATION
-- =====================================================

CREATE OR REPLACE FUNCTION trigger_alert_evaluation()
RETURNS TRIGGER AS $$
BEGIN
    -- Avaliar alertas quando nova métrica é inserida
    PERFORM public.evaluate_alerts();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER performance_metrics_alert_trigger
    AFTER INSERT ON public.performance_metrics
    FOR EACH ROW
    EXECUTE FUNCTION trigger_alert_evaluation();
```

---

## 📊 EDGE FUNCTION: METRICS COLLECTOR

### COLETOR DE MÉTRICAS EM TEMPO REAL:

```typescript
// supabase/functions/metrics-collector/index.ts

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

interface MetricData {
  metric_type: string
  metric_name: string
  metric_value: number
  tags?: Record<string, any>
  timestamp?: string
}

interface SystemMetrics {
  cpu_usage_percent: number
  memory_usage_percent: number
  disk_usage_percent: number
  active_connections: number
  response_time_ms: number
  error_rate_percent: number
  throughput_rps: number
}

const SUPABASE_URL = Deno.env.get('SUPABASE_URL')!
const SUPABASE_SERVICE_KEY = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!

serve(async (req) => {
  try {
    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY)

    if (req.method === 'POST') {
      // Receber métricas externas
      const metrics: MetricData[] = await req.json()
      
      const { error } = await supabase
        .from('performance_metrics')
        .insert(metrics.map(metric => ({
          metric_type: metric.metric_type,
          metric_name: metric.metric_name,
          metric_value: metric.metric_value,
          tags: metric.tags || {},
          measured_at: metric.timestamp || new Date().toISOString()
        })))

      if (error) {
        throw new Error(`Failed to insert metrics: ${error.message}`)
      }

      return new Response(JSON.stringify({ success: true, inserted: metrics.length }), {
        headers: { 'Content-Type': 'application/json' }
      })
    }

    if (req.method === 'GET') {
      // Coletar métricas do sistema
      const systemMetrics = await collectSystemMetrics(supabase)
      const businessMetrics = await collectBusinessMetrics(supabase)
      const applicationMetrics = await collectApplicationMetrics()

      // Inserir métricas coletadas
      const allMetrics = [
        ...systemMetrics,
        ...businessMetrics,
        ...applicationMetrics
      ]

      const { error } = await supabase
        .from('performance_metrics')
        .insert(allMetrics)

      if (error) {
        console.error('❌ Failed to insert collected metrics:', error)
      }

      // Avaliar alertas
      await supabase.rpc('evaluate_alerts')

      return new Response(JSON.stringify({
        success: true,
        metrics_collected: allMetrics.length,
        timestamp: new Date().toISOString()
      }), {
        headers: { 'Content-Type': 'application/json' }
      })
    }

    return new Response('Method not allowed', { status: 405 })

  } catch (error) {
    console.error('❌ Metrics collector error:', error)
    
    return new Response(JSON.stringify({
      success: false,
      error: error.message
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    })
  }
})

async function collectSystemMetrics(supabase: any): Promise<MetricData[]> {
  const metrics: MetricData[] = []

  try {
    // Métricas do banco de dados
    const { data: dbStats } = await supabase.rpc('get_database_stats')
    
    if (dbStats) {
      metrics.push(
        {
          metric_type: 'database',
          metric_name: 'active_connections',
          metric_value: dbStats.active_connections || 0
        },
        {
          metric_type: 'database',
          metric_name: 'database_size_mb',
          metric_value: dbStats.database_size_mb || 0
        },
        {
          metric_type: 'database',
          metric_name: 'query_avg_time_ms',
          metric_value: dbStats.avg_query_time_ms || 0
        }
      )
    }

    // Métricas de storage
    const { data: storageStats } = await supabase.rpc('get_storage_stats')
    
    if (storageStats) {
      metrics.push(
        {
          metric_type: 'storage',
          metric_name: 'total_files',
          metric_value: storageStats.total_files || 0
        },
        {
          metric_type: 'storage',
          metric_name: 'total_size_gb',
          metric_value: storageStats.total_size_gb || 0
        },
        {
          metric_type: 'storage',
          metric_name: 'storage_usage_percent',
          metric_value: storageStats.usage_percent || 0
        }
      )
    }

  } catch (error) {
    console.error('❌ Error collecting system metrics:', error)
  }

  return metrics
}

async function collectBusinessMetrics(supabase: any): Promise<MetricData[]> {
  const metrics: MetricData[] = []

  try {
    // Métricas de conteúdo viral
    const { data: viralStats } = await supabase
      .from('viral_content')
      .select('viral_score, published_at')
      .gte('published_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString())

    if (viralStats && viralStats.length > 0) {
      const avgViralScore = viralStats.reduce((sum, item) => sum + (item.viral_score || 0), 0) / viralStats.length
      const viralContentCount = viralStats.filter(item => item.viral_score > 50).length

      metrics.push(
        {
          metric_type: 'business',
          metric_name: 'content_count_24h',
          metric_value: viralStats.length
        },
        {
          metric_type: 'business',
          metric_name: 'avg_viral_score_24h',
          metric_value: avgViralScore
        },
        {
          metric_type: 'business',
          metric_name: 'viral_content_count_24h',
          metric_value: viralContentCount
        }
      )
    }

    // Métricas de usuários ativos
    const { data: userStats } = await supabase
      .from('activity_logs')
      .select('user_id')
      .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString())

    if (userStats) {
      const uniqueUsers = new Set(userStats.map(log => log.user_id)).size

      metrics.push({
        metric_type: 'business',
        metric_name: 'active_users_24h',
        metric_value: uniqueUsers
      })
    }

  } catch (error) {
    console.error('❌ Error collecting business metrics:', error)
  }

  return metrics
}

async function collectApplicationMetrics(): Promise<MetricData[]> {
  const metrics: MetricData[] = []

  try {
    // Simular métricas de aplicação (em produção, viria de APM)
    const memoryUsage = (Deno.memoryUsage().rss / 1024 / 1024) // MB
    
    metrics.push(
      {
        metric_type: 'application',
        metric_name: 'memory_usage_mb',
        metric_value: memoryUsage
      },
      {
        metric_type: 'application',
        metric_name: 'uptime_seconds',
        metric_value: performance.now() / 1000
      }
    )

  } catch (error) {
    console.error('❌ Error collecting application metrics:', error)
  }

  return metrics
}
```

---

## 🔍 HEALTH CHECKS E OBSERVABILIDADE

### SISTEMA DE HEALTH CHECKS:

```sql
-- =====================================================
-- FUNCTION: COMPREHENSIVE HEALTH CHECK
-- =====================================================

CREATE OR REPLACE FUNCTION public.system_health_check()
RETURNS TABLE(
    component TEXT,
    status TEXT,
    response_time_ms INTEGER,
    details JSONB,
    last_check TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    WITH health_checks AS (
        -- Database health
        SELECT 
            'database' as component,
            CASE 
                WHEN COUNT(*) > 0 THEN 'healthy'
                ELSE 'unhealthy'
            END as status,
            EXTRACT(MILLISECONDS FROM NOW() - NOW())::INTEGER as response_time_ms,
            jsonb_build_object(
                'connection_count', (SELECT COUNT(*) FROM pg_stat_activity),
                'database_size', pg_size_pretty(pg_database_size(current_database())),
                'version', version()
            ) as details,
            NOW() as last_check
        FROM pg_stat_activity
        WHERE state = 'active'
        
        UNION ALL
        
        -- Storage health
        SELECT 
            'storage' as component,
            CASE 
                WHEN COUNT(*) >= 0 THEN 'healthy'
                ELSE 'unhealthy'
            END as status,
            50 as response_time_ms,
            jsonb_build_object(
                'total_objects', COUNT(*),
                'total_buckets', COUNT(DISTINCT bucket_id)
            ) as details,
            NOW() as last_check
        FROM storage.objects
        
        UNION ALL
        
        -- Application health
        SELECT 
            'application' as component,
            CASE 
                WHEN COUNT(*) > 0 THEN 'healthy'
                ELSE 'degraded'
            END as status,
            25 as response_time_ms,
            jsonb_build_object(
                'active_projects', COUNT(DISTINCT p.id),
                'recent_extractions', COUNT(DISTINCT e.id),
                'recent_content', COUNT(DISTINCT vc.id)
            ) as details,
            NOW() as last_check
        FROM public.projects p
        LEFT JOIN public.extractions e ON p.id = e.project_id AND e.created_at >= NOW() - INTERVAL '1 hour'
        LEFT JOIN public.viral_content vc ON e.id = vc.extraction_id AND vc.extracted_at >= NOW() - INTERVAL '1 hour'
        WHERE p.is_active = true
    )
    SELECT * FROM health_checks;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- FUNCTION: PERFORMANCE SUMMARY
-- =====================================================

CREATE OR REPLACE FUNCTION public.get_performance_summary()
RETURNS JSONB AS $$
DECLARE
    result JSONB;
BEGIN
    SELECT jsonb_build_object(
        'system_health', (
            SELECT jsonb_agg(
                jsonb_build_object(
                    'component', component,
                    'status', status,
                    'response_time_ms', response_time_ms
                )
            )
            FROM public.system_health_check()
        ),
        'key_metrics', (
            SELECT jsonb_build_object(
                'total_projects', total_projects,
                'active_users', active_users,
                'viral_content_count', viral_content_count,
                'avg_viral_score', avg_viral_score,
                'content_growth_rate', content_growth_rate
            )
            FROM public.executive_kpis
            ORDER BY updated_at DESC
            LIMIT 1
        ),
        'recent_alerts', (
            SELECT jsonb_agg(
                jsonb_build_object(
                    'severity', severity,
                    'message', message,
                    'triggered_at', triggered_at
                )
            )
            FROM public.alert_instances
            WHERE status = 'active'
              AND triggered_at >= NOW() - INTERVAL '24 hours'
            ORDER BY triggered_at DESC
            LIMIT 10
        ),
        'performance_trends', (
            SELECT jsonb_agg(
                jsonb_build_object(
                    'metric_name', metric_name,
                    'current_value', current_value,
                    'trend_direction', trend_direction,
                    'alert_level', alert_level
                )
            )
            FROM public.get_performance_metrics('1 hour'::INTERVAL)
            WHERE alert_level != 'normal'
        ),
        'generated_at', NOW()
    ) INTO result;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;
```
