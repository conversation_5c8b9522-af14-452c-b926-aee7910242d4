﻿Olá pessoal tudo joia aqui o luí nesse
vídeo eu vou falar com vocês sobre o
termios que é um gerenciador de
terminais SSH que vai ajudar muito você
a gerenciar os seus servidores Ele é
simples ele é fácil de utilizar ele tem
uma versão gratuita que vai ajudar muito
você mas se você quiser ir além e se
realmente você trabalha aí
profissionalmente gerenciando servidores
pra sua empresa pra sua equipe pros seus
clientes vale muito a pena dar uma
olhadinha também aqui no plano pago do
términos tá pessoal então eu vou passar
com você vou fazer um overview aqui vou
dar um exemplo de como que vocês podem
gerenciar um SSH através dele eu tenho
certeza pessoal que independente do
provedor que vocês usam esse cara aqui
vai ajudar bastante você tá então deixa
um like no vídeo pessoal comenta o que
que você achou aqui do termios
compartilha esse vídeo nas comunidades
nos grupos que você participa que eu
tenho certeza que vai ajudar bastante
você pessoal Começando aqui pelo
términos então começando do começo tá E
ele é um um terminal ele é um cliente S
comum tá porém ele também faz o papel de
um gerenciador Esse é o maior
diferencial dele então você pode
utilizar caso você esteja no Mac você
pode utilizar o terminal da Apple que é
maravilhoso no Linux também tem um
terminal nativo no Windows você pode
instalar o aplicativo Terminal da
Microsoft E aí você vai ter um terminal
o problema pessoal é que gerenciar tudo
é muito complicado pensa no meu dia a
dia aqui como consultor pessoal eu tenho
aqui o términos eu uso ele há bastante
tempo
e e o mais legal dele mais legal mesmo
assim do termos é que eu tenho todos os
os servidores de todos os meus clientes
conectados ali os meus da partic os meus
da promov web os meus dos meus clientes
eu tenho tudo no terminos né então fica
muito fácil Porque como vocês podem ver
na versão free dele você vai poder
utilizar ele no seu o desktop Então você
vai ter ele numa máquina só você vai
configurar todos os seus terminais ali
todos os seus SSH ali e fica numa
máquina só na versão Pro eu uso a versão
paga dele deixa eu passar aqui pelo
preço para vocês verem eu uso aqui a
versão Pro custa e 10 por mês Se você
assinar pela loja de aplicativos fica
mais em conta tá então o que eu me
lembre pessoal eu pago r$ 2 por mês nele
lá na loja do Google Play tá então no
caso que eu uso Android então fica muito
mais em conta assinar pela loja de
aplicativo do que assinar aqui pelo site
tá que ele vai ficar muito mais caro tá
bom E aqui pessoal eu posso ter vários
dispositivos Esse é o grande atrativo
dele já falei para vocês né que eu tenho
aqui o computador que eu uso o Mac que
eu uso aqui atrás eu também tenho um
notebook que eu também uso aqui em casa
mas aqui em casa outras pessoas também
usam e eu tenho um tablet que é o que eu
o que eu mais uso ao longo do dia eu uso
muito muito mais o meu tablet do que o
meu celular mas eu tenho términos no meu
notebook eu tenho términos no Mac que eu
tenho tmos no Android do celular e no
Android do tablet aonde eu tiver pessoal
eu posso acompanhar um servidor eu posso
fazer algum tipo de análise algum olhar
algum log aonde eu tiver eu posso
reiniciar um servidor eu posso atualizar
um serviço atualizar um nhn olhar um log
do nhn conectar no terminal de algum
contêiner então isso dá uma
flexibilidade muito grande para mim
diversas vezes eh esse cara aqui já
salvou a minha pele na gestão aí não só
das minhas máquinas mas das máquinas dos
meus clientes tá então vale muito a pena
pessoal Ass pelo site é mais caro tá
assina pela loja de aplicativo que vocês
vão ter aí muitos recursos tá gente na
versão free que eu acho que já é mais do
que o suficiente pra maioria das pessoas
Lembrando que você não vai ter a opção
aí de utilizar vários dispositivos tá
então você vai utilizar ela num lugar só
mas você vai poder gerenciar o SSH sftp
telnet tá então já é legal telnet a
gente não usa tanto assim mas SSH e sftp
é bem legal para você poder copiar
arquivo no servidor por exemplo tá no
caso aqui da versão e free né você vai
ter o local só né então você vai poder
armazenar os seus SSH somente no seu
computador mesmo ele não vai fazer a
sincronização para você poder acessar em
outros aplicativos na versão Pro já vai
ter Tá bom você vai poder vai
sincronizar tá também você pode integrar
com a WS digital H vanta para poder
buscar informações através de api Então
você Pode listar já no caso aqui eu uso
a CS mesmo Por mais que ten integração
ali eu acabo não usando tá então assim
pessoal é muito muito muito muito legal
na versão Pro também pessoal tem aqui e
também tem na versão free os snips isso
aqui é muito bom você sempre roda
algumas rotinas eu tenho aqui se você é
aluna da promov web Você já fez nosso
curs de docker lá no curs de docker tem
Unos scripts para você provisionar o
servidor e tanto no Manager quanto do
worker né então eu tenho aqui no meu no
meu pessoal aqui ó no meu eh términos
pessoal eu tenho ele prontinho já salvo
como snip só aperta o botão el colle já
executa aqueles scripts para mim no
servidor tá E também tem o histórico do
Shell que é muito bom né apertar o
famoso setinha para cima ali no terminal
mas já fica também o histórico na versão
Pro você vai ter autocompletar você pode
criar variável de ambiente especificar
variável de ambiente no servidor tem uma
série de de itens interessantíssimos
aqui que vocês podem utilizar né até
mesmo pessoal o famoso port forward tá
disponível também na free então você
pode rodar um servidor web por exemplo
no no no sistema e mapear essa porta pra
sua máquina local aqui então pessoal tem
muito recurso tá em breve eu vou gravar
um curso para vocês de tmos hoje mais só
fazer uma visão geral mesmo aí e
apresentar para vocês essa ferramenta
fantástica então aqui pessoal eu tenho
ele aqui tá eu tô com o términos
instalado aqui eh como eu não estou
usando a versão Pro tá eu deslogue da
minha conta principal não posso mostrar
minha conta principal para vocês aqui
mas eu vou criar uma conta zerada para
vocês verem como é que funciona então se
você só instalar se você vem aqui em só
instala ele tá você vai vir aqui vai
instalar ele pelo no no Windows no Mac
no Android no iPhone tá você vai cair
aqui nessa telinha Tá bom então aqui
você vai poder
e cadastrar os seus sistemas Lembrando
que se você assinar a conta pro Eu
recomendo muito pessoal que vocês usem
lembrando sempre tá assina pela loja de
aplicativos você vai poder sincronizar
nos seus dispositivos então é como se
fosse um backup também que você tem eu
tenho aqui pessoal uma máquina na H tá o
meu o meu nhn aqui é o vou adicionar
essa máquina aqui no meu términos tá mas
antes de adicionar aqui eu vou ativar o
trio deles para vocês verem Tá bom então
por exemplo você pode vir aqui ó vou
simular aqui um setup integration aqui
qualquer coisinha que você for fazer
aqui e que só tem opção na na versão
paga ele vai aparecer para você poder
vir aqui ó e testar por por 14 dias
então vou vir aqui né eu vou marcar a
opção aqui ó entrar com Google eu vou
logar aqui então você vai logar com o
Google Recomendo muito muito que vocês
usem o login do Google acho que é mais
prático né e assim que você criar sua
conta no termos ele vai entrar aqui né
vou fazer uma eu vou escolher aqui a
minha conta que eu vou logar Vou
permitir aqui lembrando que o Google
mudou né a tela de login dele era uma
telinha feinha que ficava no meio Agora
ficou mais bonitinho aqui ele dá uma
mensagem tá ó que o Chrome quer abrir o
términos Então vou eu vou clicar aqui
para ele poder abrir ó e aí pessoal
assim que você criar sua conta Lembrando
que eu vou usar um Trial aqui da conta
pro se você não quiser pagar você não
precisa fazer essa tapa aqui tá bom eu
vou vir aqui ó eu vou configurar uma
palavra-chave para criptografar os meus
dados então ele como ele vai ser
sincronizado com os servidores do terms
eu vou criar uma criptografia minha tá
então eles prometem né Que eles só vão
descriptografar nos seus clientes nos
seus aplicativos tá essa informação não
é salva lá tá bom então vou criar aqui
uma palavra-chave simples
tá el não acho que faltou aqui
ã Deixa eu pôr no comecinho aqui um
caracter
especial Pronto agora
passou então ele deixou configurar aqui
o meu a minha encryption kit sempre que
você for cadastrar um dispositivo novo
para você poder utilizar você vai ter
que logar na sua conta do Google no caso
aqui e também vai ter que informar essa
encrypt kin senão ele não vai carregar
os seus os seus dados Tá bom então agora
pessoal eu tô aqui numa conta que tá no
trio tá usa o try para ver o que vocês
acham conecta no seus no seu celular nos
seus outros dispositivos para você ver
se vale a pena pagar tá bom Luiz testei
aqui mas eu eu só uso o computador mesmo
então eu acho que não precisa pagar tá
gente pode usar ele no Free mesmo normal
tá é que eu eu acabo fica numa parte do
dia fora de casa eu eu gosto de ter essa
facilidade de poder num computador que
eu que eu quiser ou então no meu próprio
tablet no próprio celular poder acessar
aqui os meus servidores Mas pode ser que
não seja essa a sua realidade tá Então
pessoal ó super simples criou a conta
ali num passo a passo bem simples mesmo
tá E aí você vai vir aqui ó e vai
cadastrar os seus servidores Então você
vai pegar o IP lembrando pessoal
qualquer servidor VPS tá independente do
provedor que você usa tá então vou vir
aqui ó ele pede o IP ou hostname então
eu vou criar aqui com o IP tá ó aqui o
primeiro campo lá em cima ele pede o IP
né o endereço então pode ser o IP ou
pode ser o fqdn se você fez o curso de
docker você poderia colocar por exemplo
aqui o CN lá do seu do seu Manager tá aí
ele pede um Label então aqui eu vou
poder colocar um nome que seja legível
como a minha máquina chama nhn eu vou
chamar ele de meu nhn também meu
nhn eu posso criar grupos né eu posso
agrupar as minhas máquinas eu posso
colocar tags então eu posso criar aqui
uma tag chamada rner então eu quero
agrupar todos os meus minhas as minhas
VPS que estão na hestner por exemplo tá
então assim fica muito muito mais fácil
de você poder gerenciar isso daí aqui
embaixo ele pergunta né a porta a porta
vai ser 22 mesmo né Não sei que você
altere isso aí mas vai ser 22 aqui ele
pede o
username e aqui ele vai pedir a senha né
ó Então vou vir aqui ó vou digitar minha
senha então digitei aqui a minha senha
Luiz eu uso Amazon ou então eu uso algum
servidor aqui que eu tenho que informar
uma chave você pode vir aqui especificar
a chave você pode especificar Um
certificado você vai ter que ter esse
arquivo disponível tá ele vai
sincronizar também isso aí mas você vai
ter que informar aqui o arquivo da sua
chave ou o seu certificado aqui você vai
ter outras opções não cabe nesse vídeo
explorar essas outras opções aqui tá no
curso eu vou abordar mais isso assim que
a gente for gravar esse curso aqui eu
vou abordar para você e você pode até
escolher que modelo que você quer aqui
de terminal tá esse terminos Dark aqui
já é muito legal aí pessoal você vai
aparecer aqui embaixo o botão vou até
sair da tela PR vocês verem aparece um
botãozinho aqui ó Connect tá então é só
clicar nesse botãozinho Connect ele vai
fazer a conexão Ele pergunta né se eu
quero fazer o Fingerprint da máquina eu
posso só continuar então ele não faz o
Fingerprint dessa máquina com o meu
Servidor ou eu posso adicionar essa esse
Fingerprint aqui no meu KN hosts e ele
conecta pessoal tá na sua máquina
adiciona tá numa máquina que não é sua
não precisa adicionar mais mais ou menos
a regra é essa tá bom ele vai fazer aqui
vai fazer as verificações dele ó e
conectou tô lá na minha maquinazinha da
rner Prontinho e conectado luí quero
quero desconectar é só vir na barrinha
lateral aqui ó e fechar ó Então você vai
poder vir aqui dar dois cliques nele ele
faz a conexão de novo para você como eu
adicionei né a o Fingerprint então ele
não perguntou mais para mim mas se eu
não tivesse adicionado o Fingerprint
toda vez que eu fosse conectar ele ia
perguntar tá gente super simples pessoal
por aqui ó na versão free mesmo Insta na
sua máquina vai ser o melhor terminal
que você vai utilizar é melhor do que o
terminal web do digital otion é melhor
do que o terminal web da H é melhor do
que o terminar o Web do do linode de
qualquer outro provedor que você queira
utilizar tá gente então muito muito
legal como coloquei aqui um Label nele
né Você pode vir aqui e filtrar ó Então
eu só quero mostrar aqui as máquinas da
hetzner ou você pode pôr um Label com o
nome do cliente do seu cliente ou você
pode vir aqui e criar um grupo e agrupar
suas vpss aqui por cliente então por
exemplo eu vou criar um grupo aqui ó
chamado cliente A tá bom né Ele pergunta
se eu quero sincronizar né como eu tô
como eu tô utilizando aqui a versão Pro
Eu vou ter esse recurso né dele poder
automaticamente listar para mim as vpss
aqui tá então não é o meu caso né mas eu
criei aqui o grupo ó cliente a é só eu
arrastar essa VPS aqui para lá ó lá ele
como eu ele ele sincroniza a informação
ele vai sincronizar então sincronizou se
eu clicar aqui agora no cliente Ah eu
vou ver todas as VPS desse cliente aqui
isso aqui pessoal maravilhoso tá você
pode agrupar aí como você quiser por
provedor por cliente você pode aplicar
Label muito legal mesmo tá gente muito é
um recurso Nossa isso aqui é fantástico
Imagina eu tenho eu gerencio hoje em
torno de 80 servidores então eu tenho
todos esses servidores aí que eu que eu
gerencio hoje e eu tenho todos eles
cadastrados no términos né então fica
muito mais prático para mim fora Aqueles
temporários né que a gente cria durante
lançamento su arme permite que você use
máquinas temporárias né então também
coloca aqui tudo então pessoal fica
muito muito muito legal tá aqui em snips
você pode vir aqui e criar os seus
snippets inclusive ele tem agora aqui
uma uma I para você vir aqui né E poder
criar ó o exemplo que legal ó encontre
arquivos maiores do que 100 m Então vou
colocar aqui ó find
files
larger 100
MP então o ai vai poder criar para você
aqui ó ele vai rodar o ele vai criar o
comandinho que você roda no terminal
para poder rodar isso daí tá então você
pode vir aqui e salvar esse seu snip vai
ficar salvo aqui ó então aqui de novo
você vai poder colocar ali os seus
docker files você pode colocar ali para
ficar mais prático os cominhos de join
de de worker Às vezes você faz vários né
então a já pode deixar pronto ali para
um servidor específico seu os os scripts
da promov para você poder provisionar o
servidor então muito muito muito prático
tá gente eu eu conheço gente que deixa
até aqui já os stacks salvos aqui também
né não vai rodar pelo terminal mas pelo
menos tá aqui salvo e sincronizado na
mão tá gente então usem usem olha
realmente se você puder utilizar a
versão paga melhor ainda a sincronização
ela é fantástica mas lembrando pessoal
na versão free Você já consegue fazer
muito bom uso dele e gerenciar muito bem
os seus servidores tá pessoal então acho
que é um é um movimento importante
Instala aí tá com dúvida entra no
discord Pergunta lá a sua dúvida no
discord tá se você é aluno Você tem o
Canal VIP tem um fórum tá onde eu tenho
mais tempo para poder responder ali se
você não é aluno também acessa o nosso
discord porque tem o bate-papo lá pra
gente poder conversar ali e mostrar e
você vai poder perguntar também alguma
dúvida aí em relação ao terminos pessoal
gostou do terminos deixa o like pessoal
aqui no vídeo se inscreve no canal
conheça promov web eu vou deixar na
descrição aqui para você ver o nosso
site para você poder virar membro da
promov web Olha o preço tá muito bom
para você poder ter uma anuidade aqui
você vai poder acessar todos os nossos
cursos todos os nossos cursos e todos
que já existem e todos que vão existir
ainda ao longo desse ano enquanto valer
a sua assinatura você vai acessar todo e
qualquer conteúdo a promov web o nosso a
nossa comunidade Vip as nossas lives os
nosso as gravações de aula as aulas ao
vivo tudo isso você vai poder acessar
ali né pessoal e crescer
profissionalmente tá isso aqui pessoal
daqui a pouco daqui a um tempo a gente
vai gravar um curso dele tá Tô
terminando aqui a pauta dele para gravar
esse curso e é um recurso Fantástico tá
gente Luiz compensa investir nele
compensa pessoal se você quiser investir
nele Lembrando que pela loja de
aplicativo ele é mais barato mas mesmo
na versão free compensa você instalar
agora na sua máquina aí tá e você fazer
muito bom uso do thms tá pessoal então
eu vou deixar o link para vocês aqui do
thms eu vou deixar o link da promovo
weeb eu vou deixar o link do nosso grupo
do WhatsApp e no nosso discord para você
poder entrar e participar e interagir
ali com a promov web um grande abraço
pessoal e faça muito bom uso aí desse
gerenciador de servidores que é
fantástico até mais