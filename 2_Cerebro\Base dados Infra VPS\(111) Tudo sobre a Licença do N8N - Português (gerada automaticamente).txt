﻿Olá pessoal tudo joia aqui o Luís nesse
vídeo eu quero bater um papo com vocês
sobre a licença do NN Eu Tenho recebido
aqui pessoal muitos comentários nos
vídeos inclusive recentemente eu
publiquei um vídeo falando sobre os
novos recursos gratuitos né da versão
enterprise que estão sendo
disponibilizados aqui para usuários self
host do neen e surgiam alguns
comentários sobre licença recebi
mensagem nos grupos né nos nossos grupos
do WhatsApp da comunidade de alunos eu
falei vou gravar um vídeo então
explicando a licença do nen porque a
licença dele é simplesinho pessoal é um
vídeo rápido mas eu acho que é muito bom
que vocês entendam para que isso não
Gere nenhum tipo de dúvida para vocês tá
então dá o like no vídeo pessoal comenta
se você não entender alguma coisa
pergunta aqui que eu faço questão de
poder responder para você tá e deixa o
like no vídeo e se inscreve aqui no
canal pessoal antes de começar aqui eu
queria passar primeiro com vocês sobre o
que que é que que é um software Quais
são os pontos envolvidos num software
pessoal de um lado eu vou ter aqui o
código fonte né então o código fonte é o
primeiro item polêmico em relação a isso
existem ferramentas que possuem o código
fonte fechado tá por exemplo mql mong DB
né são são sistemas que eles são
gratuitos porém não são opce no termo de
código fonte aberta ter o código fonte
aberto não significa que ele é open sce
Tá bom mas eu posso ter ou não código
fonte aberto tá pessoal Qual que é a
vantagem do NN dele deixar o código
fonte aberto isso daqui é um ponto
importante a gente conversar a primeira
vantagem pessoal é que eu posso ir lá e
auditar o sistema vocês vão vão se
deparar ao longo da carreira de vocês
com infinidade de clientes que eles eles
fintech por exemplo com fintec Como que
o cara sabe que eu estou trabalhando com
os dados dele e não estou desviando uma
informação para algum outro lugar
através de uma auditoria e o código
fonte tem que est aberto para eu poder
fazer essa essa auditoria simples assim
pessoal simples assim então o maior
benefício do código fonte aberto é que
você pode auditar o sistema o segundo
benefício do código fonte aberto é que
você pode testar ele você pode encontrar
bugs e no caso do n Chan a licença
também permite que você envie para eles
algum tipo de correção então você pode
colaborar pra estabilidade do sistema
isso também pessoal ó importantíssimo tá
terceiro ponto pessoal que é importante
em relação ao código aberto é você ter
uma noção eh para quem quer aprender às
vezes pessoal você quer montar algo
parecido ou você está começando na área
de programação né Então nada melhor do
que você olhar um sistema como é que ele
foi construído como é que eles o estilo
de codificação a maneira como eles
modular o sistema Quais são os pacotes
que eles utilizam quais são o que que
eles utilizaram de biblioteca o que que
eles próprios desenvolveram como que
eles montaram a interface tudo isso
pessoal é muito muito útil é muito
importante que vocês façam isso então
pessoal o en liberou o acesso ao código
fonte para que isso possa ser feito Tá
bom então primeiro ponto esclarecido
algumas ferramentas como mais k mongo DB
não tenho acesso ao código fonte delas
eu não sei internamente como é que é
feito lá dentro então eh todas são
gratuitas mas nem todos me permitem
fazer esse tipo de verificação tá
segundo ponto aqui pessoal distribuição
né então eu vou ter o código e eu vou
ter a distribuição o NN pessoal ele é
uma ferramenta que ele é distribuído no
github então você pode ir no github
baixar o nhn e também pessoal eles
oferece para você já pronto um docker
file já pronto uma imagem do docker e
também já oferece compadinho o sistema
lá no npmjs para você poder baixar ele
então a distribuição dele é muito
transparente algumas ferramentas que
oferecem o código aberto elas nem
oferecem o sistema pronto para uso você
tem que ir lá e vamos dizer assim
compilar ela do zero tá então o n não
ele te dá pronto em relação à
distribuição também pessoal é bom
entender que você é livre para poder
utilizar a ferramenta ela é distribuída
gratuitamente Tá bom então eu tenho
acesso ao código fonte eu posso ver eu
tenho acesso ao sistema ao executável
vamos dizer assim do sistema então eu
posso utilizá-lo tá e o terceiro ponto
pessoal que o que gera muita polêmica é
a licença né é a licença que vai dizer
para mim pessoal o que que eu posso
fazer com o software e o que que eu
posso fazer com a distribuição dele e a
licença pessoal ela também gera uma
infinidade de dúvida na galera tá essa
questão de licença Então vamos lá
pessoal vamos vamos pensar o seguinte eu
tenho código fonte eu tenho código fonte
para que eu possa estudar eu também
tenho o NN Prontinho compilado rodando
Eu só preciso parametrizar lá o meu
docker e fazer ele funcionar o que que
vai ser o o cara que vai controlar isso
daí é a licença então a licença ela me
diz o que que eu posso fazer com o
código fonte o que que é permitido não é
permitido e a licença me diz o que que
eu posso fazer com a ferramenta em fins
comerciais EMS de distribuição de venda
então não tem a ver pessoal com código
fonte tá aberto ou não não tem a ver com
a distribuição se é aberta ou não tem a
ver com o que que é a licença a licença
que é a sacola a licença que é esse
sistema que vai me dizer o que que eu
posso fazer com a ferramenta e vamos dar
uma olhadinha pessoal na licença do Enen
Então olha só eu tenho aqui pessoal a
licença do Enen que é o seguinte lá no
site do Enen tem essa página é traduzir
para português fica mais fácil pra gente
poder passar aqui no vídeo tá então Olha
só pessoal o que que acontece aqui com a
licença do NN eles TM o modelo chamado
licença de Uso Sustentável tá Guarda
esse termo aí tá bom e o que que é
pessoal essa licença sustentável vamos
vamos fazer uma uma brincadeirinha aqui
pessoal que a gente faz a gente faz
muito né fala assim pessoal eu preciso
colocar comida na mes de casa né então o
nosso trabalho o nosso esforço ele de
alguma maneira deve ser recompensado e o
en pessoal ele tem uma abordagem e olha
só a abordagem do pessoal é fazer com
que eu e você possamos utilizar a
ferramenta pro nossos fins próprios eu
posso usar o nen para promov para
poder resolver aqui as minhas tretas
então eu tenho aqui no meu dia a dia
sincronizar L do hotm ar sincronizar CRM
com MK enviar
e-mail aluno completou o curso tem que
registrar que ele completou o curso eu
tenho problemas no dia para resolver o
nhn fala assim ó luí você pode usar cara
tranquilo não não não vou te cobrar nada
tá Então pessoal é uma questão assim ele
permite que eu faça uso sem me cobrar o
que que ele não permite pessoal e vocês
vão ver aqui que tá muito claro que eu
falo assim beleza NN você criou a caneta
Eu Vou Pegar a caneta e eu vou criar um
concorrente para você agora isso não
pode tá isso que a licença dele exclui
basicamente pessoal Resumindo totalmente
a licença do é isso se você faz algo
para você ok se você faz algo que
concorre com eles não por qu pessoal
porque no fim das contas quem tem que
pagar deve é o NN quem tem que fazer
todo o restante gerenciamento o tempo
imagina o tempo que não tem isso daí a
gestão que não tem isso daí envolvimento
envolvendo pessoas pessoas que t a hora
caríssima programador pessoal não é uma
hora barata né Então nada mais justo e
por isso que ele chama né de licença
sustentável que quem for ganhar dinheiro
com hospedagem seja eles e todo mundo
que ele permite que use a ferramenta não
volte contra eles e Gere algum tipo de
concorrência com eles tá pessoal
basicamente é isso essa Licença aqui
então vamos dar uma olhadinha nela com
mais calma para vocês verem Então
pessoal eles usam
uma licença chamada sustainable use
licence né E essa licença pessoal foi
criada pelo próprio neen tá a licença do
NN eles próprios criaram baseado no
modelo Fir code tá bom essa licença
pessoal ela ela ela é bem simples de
entender tá é é bem simples de entender
então olha só o que que eles escreveram
aqui pessoal no próprio site a licença
de Uso Sustentável é uma licença de
software de código justa criado pelo NN
em 202
Você pode ler mais sobre motivo no link
que tem ali a licença permite a você o
direito livre de usar modic e criar
trabalhos derivados e rbu com três
somente três limitações quais são essas
três limitações vocêde usar ou modificar
o software somente para fins comerciais
internos ou para uso não comercial Ou
pessoal
pessoal o que que é fim comercial
interno é o meu uso que eu faço aqui na
promov web é o uso que você faz aí na
sua empresa é também o uso que você faz
pros seus clientes tá então simples
assim tá lembrando pessoal que eu não
posso e aqui vai ter também falando isso
daí eu não posso concorrer diretamente
com nhn então é sempre importante vocês
terem isso aí em mente tá você pode
distribuir o software ou fornecê-lo a
terceiros se o fizer gratuitamente e
para fins não comerciais tá Então
pessoal eu não posso montar uma
hospedagem de NN eu não posso oferecer o
NN vamos usar o termo que a galera usa
aí nos modelo SAS tá bom não posso tá
então eu posso usar para mim eu posso
instalar para você o NN no seu servidor
e vender isso para você perfeitamente tá
antigamente pessoal nem isso podia tá
nem isso podia agora agora pode você
pode você não pode alterar remover
ocultar qualquer licença direitos
autorais ou outros avisos de
licenciamento de software tá pessoal
qualquer uso de marcas registradas do
licenciamento está sujeito a lei
aplicável e os caras vem atrás mesmo tá
gente fica de olho nisso daí então
pessoal aqui inclusive Eles deixam o
e-mail que é o licencia @ nn. caso você
tenha alguma dúvida em relação à licença
aqui da ferramenta tá pessoal e Olha só
pessoal o que é permitido e o que que
não é permitido de uma maneira super
super super simples tá gente nossa
licença regist restringe o uso fins
comerciais eh internos né Então você só
pode fazer esse uso de fins comerciais
internos então eu tenho aqui a promov
web eu tenho aqui a Power tick os dois
usam NN eu trabalho PR os dois são fins
comerciais internos tá gente na prática
Isso significa que todo uso é permitido
a menos que você esteja vendendo um
produto serviço ou módulo em que o valor
deriva inteiramente ou substancialmente
do nhn aqui estão alguns exemplos que
não seriam permitidos pessoal fazer um
White Label então não façam White Label
do NN tá e oferecer isso em troca de
dinheiro fazer uma hospedagem montar um
SAS tá bom e hospedar o NN cobrar as
pessoas para acessá-lo Tá então não pode
tá gente não pode todos os exemplos a
seguir são permitido sobre a licença do
NN você sincronizar leads internamente
permitido você criar um node permitido
fornecer serviço de consultoria
eh permitido manutenção tá ó lá oferece
suporte por exemplo configurando e
mantendo em um servidor interno da
empresa esse último pessoal é importante
então ao mesmo tempo que você não pode
criar um SAS de nhn você pode criar um
serviço que vai falar assim ó ô José eu
vou instalar para você o NN no seu
digital otion na sua conta da rner E aí
você cobra para poder fazer a instalação
e a manutenção disso Sem problema nenhum
o que não pode é eu pagar o nhn eu pagar
a rner O cliente me paga para ter acesso
do nhn isso não pode tá agora se ele
paga o servidor então o servidor é dele
é para fim comerciais internos dele
então tá perfeitamente dentro da regra
aí tá pessoal super simples de entender
Len tá gente não vamos não não não é
complicado aqui tá aqui tem um ponto
pessoal que é importante que eu acho que
vai muito pra galera do nocode tá E
também para quem trabalha no modelo de
agência que é é important que você se
adqu tá gente Posso usar o nhn para
atuar como um backend no meu aplicativo
Normalmente sim Normalmente sim desde
que o processo de backend não US as
credenciais dos próprios usuários para
acessar os dados pessoal isso que é
importante pessoal super simples de
entender eu tenho aqui o meu NN Ok e o
meu e o meu nhn vai receber um web Hook
e acessar o meu hubspot é a credencial
tem que ser a minha do dono do NN então
a instalação é minha a credencial do
hubspot é minha o que não pode acontecer
pessoal é o é eu é eu na minha
instalação acessar informações con
credenciais dos usuários
Então imagina que eu montei pessoal aqui
na promov web um NN e eu vou fornecer
para vocês acesso ao mtic eu falo PR
vocês assim ó pessoal você pode vir aqui
ó e a sua credencial do M meu en vai
acessar lá isso não pode eu poderia
oferecer para você o n com acess ao MK
do meu MK Não do seu MK Tá gente então é
importante também isso daqui é uma uma
bobeirinha Mas é bom você ficar atento
daí tá E aqui
pessoal caso você queira fazer o uso do
nhn embedado ou você queira até mesmo
fazer algum tipo de brand nele você pode
você tem aqui a versão enterprise você
pode assinar uma versão enterprise que
te dá alguns benefícios até mesmo a
versão embed que você pode embedar o NN
legalmente dentro da sua ferramenta tá E
aqui pessoal tem um ponto importante que
eu acho que vale também a pena a gente
falar por que que o não usa um código
aberto de uma vez por que que ele fala
assim ah tô aí cara faz aí né a missão
do Nate pessoal é dar a todos que usam
um computador super poderes técnicos
pessoal isso aqui é importante porque
para quem é programador entende que quer
dizer isso daqui eu cansei de falar para
vocês no curso pessoal eu faço o curso
Eu ofereço o curso de neen desde 2020
então ten 4ro anos indo pro 5to ano aí
pessoas não técnicas pessoas que talvez
nunca tiveram qualquer tipo de acesso
qualquer tipo de de trabalho com uma API
com qualquer tipo de sistema assim
consegue operar perfeito amente uma API
enviar uma mensagem pela Evolution
sincronizar um lead pro mtic então ele
realmente pessoal ele dá uma essa esse
super poder o que que ele quer pessoal
ele quer que você possa fazer o seu
trabalho com nhn eles vão fazer o
trabalho deles de oferecer um SAS
oferecer
consultoria montar ali um grupo de
experts então NN tem hoje resellers né
Eu sou resellers tem experts e tem
embaixadores né também sou Embaixador
então assim você tem esse grupo de
pessoas que o próprio NN Te indica Para
quê Para que ele também tenha algum tipo
de autoridade ali tá então eu posso
utilizar a ferramenta eu posso montar PR
os meus clientes eu posso instalar para
os meus clientes eu posso fazer curso eu
posso fazer consultoria Só não posso
pessoal matar a galinha por causa do ovo
nesse nessa nossa história pessoal o n
Chen ele que é o nosso provedor aqui eu
não vou nunca em direção a ele eu eu não
vou nunca contra ele não vou vou nunca
tirar algo dele por quê Porque é ele que
me provê isso ele me me provê a
ferramenta eu vou olhar o que ele faz eu
vou ler a licença eu vou ver o que que
eu posso fazer com a licença o que que
eu não posso fazer com a licença e
dentro do que eu posso fazer eu vou
atuar Pessoal vocês aproximam da
ferramenta E vocês viram os melhores
amigos então acho que é importante
pessoal gravei esse vídeo aqui só para
poder explicar para vocês mesmo isso o
ENEN pessoal ele não pode ser
considerado um software open sece
inclusive até tem aqui pessoal no
próprio na própria documentação aqui
eles falam isso né que o en ele não pode
ser considerado como um Open porque ele
não tá de acordo com a osi quer ver
então algum momento que eles falam sobre
isso daí que é o que pessoal ó lá então
existe a osi né Open sce initiative né
que ela que dá uma série de regras para
que você possa considerar um software
como Open sce então só ter o código
disponibil não caracteriza ele como Open
tá a licença do software é o que vai
caracterizar isso e a licença do n ela é
restrita todos os direitos são deles tá
então o inclusive ele criou esse termo
código justo aqui né que é uma forma de
descrever o nosso modelo de
licenciamento tá outra pessoal vamos
combinar que olha a gente tá aqui ó hoje
é dia
11/11 pessoal o Type bot tá usando essa
mesma regra o F usa essa mesma regra o
chatot usa essa mesma regra o nhn também
já tá usando essa mesma regra e eu vou
dizer que tudo indica que o WordPress
também vá para essa linha aí tá de você
poder ter ferramentas que tem o código
fonte disponibilizado você pode criar
componentes para ele você pode sugerir
correções para ele você pode compreender
melhor como ele funciona pessoal pegando
o meu caso aqui o nosso curso do DN
pessoal ele parte ali do uso geral da
ferramenta e também aí no uso específico
de devops para quem quer escalar
ferramenta instalar a ferramenta tudo
isso é possível eu observando o código
fonte não é só ler a documentação a
documentação é muito rica mas olhando o
código fonte como programador eu consigo
entender melhor compreender melhor o que
que eles fizeram ali e o porqu que tem
aquelas configurações nas variáveis
então isso pessoal e são inúmeras
vantagens que a gente tem tá de utilizar
ferramenta dessa graças ao código fonte
dele aberto a maneira de distribuição
dele e a licença controlando o que que
pode ser feito o que que não pode ser
feito com a ferramenta tá gente então
acho que é bom deixar claro deixar os
pingos vi você espero ter respondido
para vocês aí essa dúvida tá dá um like
no vídeo se ficou alguma dúvida pessoal
comenta aqui tá luí eu vou comentar pode
comentar então fazendo resumão pessoal
não é não é open source na maneiras na
maneira correta da palavra Open sce ele
é código aberto Ele só não é código
livre não é acesso livre tá ele tem uma
licença que te dá direito a muita coisa
mas te restringe No que diz respeito à
competitividade com eles mesmo tá
pessoal você pode criar seus componentes
você pode estudar a ferramenta você pode
sugerir correções para ele sem problema
nenhum você pode instalar em todos os
seus clientes os seus clientes vão fazer
uso irrestrito da ferramenta tá gente
você não pode criar um SAS não pode
fazer um White Label tá E caso você faça
alguma modificação na ferramenta você
tem que distribuir ela do mesmo modo que
você recebeu ela isso aí é um um
componente básico do Pens luí eu vim
aqui luí eu mudei a interface Eu pus uma
grade ali no no canva né para deixar
mais bonitinho eu troquei ali alguns
logos de ferramenta lá
dentro se você fizer isso pessoal Você
pode você deve distribuir no github essa
versão sua tá então é bom ficar claro se
tiver dúvida pessoal tá aqui tá comenta
no vídeo aqui na descrição do vídeo
pessoal tem o link da promovo para você
poder entrar na promov web e conhecer
mais nosso curso Ali você vai aprender a
instalar e configurar o nhn configurar
um servidor corretamente aprender docker
swarm aprender a configurar mtic Type
bot chatot Defy tá o nhn no modo fila
explicando todas as variáveis para vocês
detalhe por detalhe Como que você escala
a ferramenta tá fora uma série de outros
outras configurações que vocês vão
encontrar ali tá curso de de Evolution
curso de de malk tá então aqui na
descrição tem um link para vocês poder
conhecer aí um pouquinho mais sobre
promov web e também tem um link da nossa
comunidade acesso a nossa comunidade
pessoal o cadastro lá é gratuito lá tem
conteúdo legal para vocês também e
também os nossos grupos do WhatsApp tá
Um abraço pessoal até a próxima