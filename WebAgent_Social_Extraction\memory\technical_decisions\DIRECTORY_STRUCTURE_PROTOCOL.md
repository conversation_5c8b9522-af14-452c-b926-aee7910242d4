# 🚨 PROTOCOLO DE ESTRUTURA DE DIRETÓRIOS - WEBAGENT

**Versão:** 1.0  
**Data:** 2025-01-24  
**Objetivo:** Evitar criação de arquivos fora do diretório correto  
**Status:** ✅ **CRÍTICO - SEMPRE SEGUIR**  

---

## 🎯 PROBLEMA IDENTIFICADO

### 🚨 **ERRO RECORRENTE**
- **Problema:** Arquivos sendo criados em `C:\Users\<USER>\Documents\Augment\` em vez de `C:\Users\<USER>\Documents\Augment\WebAgent_Social_Extraction\`
- **Causa:** Tool `save-file` não respeita working directory do terminal
- **Impacto:** Estrutura duplicada e arquivos fora do projeto
- **Frequência:** Recorrente em múltiplas sessões

### 📍 **DIRETÓRIOS ENVOLVIDOS**
```
❌ INCORRETO: C:\Users\<USER>\Documents\Augment\
├── src/                    # CRIADO INCORRETAMENTE
├── tests/                  # CRIADO INCORRETAMENTE  
├── config/                 # CRIADO INCORRETAMENTE
├── poc/                    # CRIADO INCORRETAMENTE
├── docs/                   # CRIADO INCORRETAMENTE
├── memory/                 # CRIADO INCORRETAMENTE
├── requirements.txt        # CRIADO INCORRETAMENTE
└── .env.example           # CRIADO INCORRETAMENTE

✅ CORRETO: C:\Users\<USER>\Documents\Augment\WebAgent_Social_Extraction\
├── src/                    # LOCALIZAÇÃO CORRETA
├── tests/                  # LOCALIZAÇÃO CORRETA
├── config/                 # LOCALIZAÇÃO CORRETA
├── poc/                    # LOCALIZAÇÃO CORRETA
├── docs/                   # LOCALIZAÇÃO CORRETA
├── memory/                 # LOCALIZAÇÃO CORRETA
├── requirements.txt        # LOCALIZAÇÃO CORRETA
└── .env.example           # LOCALIZAÇÃO CORRETA
```

---

## 🔧 PROTOCOLO OBRIGATÓRIO

### 📋 **REGRAS PARA CRIAÇÃO DE ARQUIVOS**

#### **1. SEMPRE USAR PATH COMPLETO**
```
❌ INCORRETO:
save-file path="src/api/main.py"

✅ CORRETO:
save-file path="WebAgent_Social_Extraction/src/api/main.py"
```

#### **2. VERIFICAR WORKING DIRECTORY**
```bash
# Sempre verificar antes de criar arquivos
view path="." type="directory"

# Se não estiver em WebAgent_Social_Extraction, navegar
launch-process command="cd WebAgent_Social_Extraction"
```

#### **3. USAR LAUNCH-PROCESS PARA OPERAÇÕES**
```bash
# Para operações de arquivo, usar launch-process
launch-process command="mkdir src\api\routes" cwd="C:\Users\<USER>\Documents\Augment\WebAgent_Social_Extraction"
```

### 🎯 **ESTRUTURA CORRETA REGISTRADA**
```
WebAgent_Social_Extraction/                 # PROJETO RAIZ
├── 📁 src/                                 # CÓDIGO FONTE
│   ├── api/                                # FastAPI Gateway
│   │   ├── __init__.py
│   │   ├── main.py
│   │   └── routes/
│   ├── workers/                            # Celery Workers
│   │   ├── __init__.py
│   │   ├── main.py
│   │   ├── extractors/
│   │   ├── analyzers/
│   │   ├── processors/
│   │   └── tasks/
│   ├── core/                               # Business Logic
│   ├── integrations/                       # APIs Externas
│   └── shared/                             # Utilitários
├── 📁 tests/                               # TESTES
│   ├── unit/
│   ├── integration/
│   ├── e2e/
│   ├── performance/
│   └── fixtures/
├── 📁 docs/                                # DOCUMENTAÇÃO
│   ├── 01_project_management/
│   ├── 02_technical_specs/
│   ├── 03_api_documentation/
│   ├── 04_deployment_guides/
│   ├── 05_knowledge_base/
│   └── 06_research_archive/
├── 📁 config/                              # CONFIGURAÇÕES
│   ├── environments/
│   ├── database/
│   ├── logging/
│   └── redis/
├── 📁 scripts/                             # AUTOMAÇÃO
├── 📁 infra/                               # INFRAESTRUTURA
├── 📁 poc/                                 # PROOF OF CONCEPT
│   ├── youtube_extraction/
│   ├── gemini_analysis/
│   ├── performance_tests/
│   ├── cost_analysis/
│   └── poc_results/
├── 📁 frontend/                            # REACT FRONTEND
├── 📁 data/                                # DADOS
├── 📁 .devops/                             # CI/CD
├── 📁 memory/                              # CONTEXTO
│   ├── project_context/
│   ├── technical_decisions/
│   ├── lessons_learned/
│   └── knowledge_graph/
├── 📄 README.md                            # DOCUMENTAÇÃO PRINCIPAL
├── 📄 requirements.txt                     # DEPENDÊNCIAS
├── 📄 .env.example                         # TEMPLATE ENV
├── 📄 pyproject.toml                       # CONFIGURAÇÃO PROJETO
├── 📄 Makefile                             # AUTOMAÇÃO
└── 📄 docker-compose.yml                   # CONTAINERS
```

---

## ✅ CHECKLIST DE VERIFICAÇÃO

### 🔍 **ANTES DE CRIAR ARQUIVOS**
- [ ] Verificar working directory atual
- [ ] Confirmar que está em WebAgent_Social_Extraction/
- [ ] Usar path completo no save-file
- [ ] Verificar se arquivo foi criado no local correto

### 🛠️ **COMANDOS DE VERIFICAÇÃO**
```bash
# 1. Verificar localização atual
view path="." type="directory"

# 2. Navegar para projeto se necessário
launch-process command="cd WebAgent_Social_Extraction"

# 3. Verificar estrutura do projeto
view path="WebAgent_Social_Extraction" type="directory"

# 4. Criar arquivo com path correto
save-file path="WebAgent_Social_Extraction/[caminho]/[arquivo]"
```

### 🚨 **SINAIS DE ERRO**
- Arquivos aparecendo em `C:\Users\<USER>\Documents\Augment\` (raiz)
- Estrutura duplicada fora do projeto
- Working directory incorreto
- Paths relativos sem prefixo do projeto

---

## 🔄 PROCEDIMENTO DE CORREÇÃO

### 📋 **SE ARQUIVOS FORAM CRIADOS INCORRETAMENTE**
1. **Identificar arquivos incorretos:**
   ```bash
   view path="." type="directory"
   ```

2. **Remover arquivos duplicados:**
   ```bash
   Remove-Item -Recurse -Force [diretório_incorreto]
   ```

3. **Recriar no local correto:**
   ```bash
   save-file path="WebAgent_Social_Extraction/[caminho_correto]"
   ```

4. **Verificar estrutura final:**
   ```bash
   view path="WebAgent_Social_Extraction" type="directory"
   ```

---

## 📊 HISTÓRICO DE CORREÇÕES

### 🗓️ **2025-01-24**
- **Problema:** Arquivos criados em diretório raiz Augment
- **Arquivos Afetados:** src/, tests/, config/, poc/, docs/, memory/, requirements.txt, .env.example
- **Ação:** Removidos arquivos duplicados do diretório raiz
- **Status:** ✅ Corrigido
- **Prevenção:** Protocolo documentado

---

## 🎯 RESPONSABILIDADES

### 👥 **EQUIPE IA**
- **Augment Agent:** Sempre verificar working directory antes de criar arquivos
- **Claude Code:** Usar paths completos para arquivos em src/
- **Gemini CLI:** Usar paths completos para arquivos em workers/

### 📋 **COMANDOS SEGUROS**
```bash
# Template seguro para criação de arquivos
save-file path="WebAgent_Social_Extraction/[módulo]/[arquivo]" content="[conteúdo]"

# Template seguro para operações
launch-process command="[comando]" cwd="C:\Users\<USER>\Documents\Augment\WebAgent_Social_Extraction"
```

---

**🚨 Status:** ✅ **PROTOCOLO ATIVO - SEMPRE SEGUIR**  
**📅 Criado:** 2025-01-24  
**🔄 Revisão:** A cada criação de arquivo  
**👥 Responsável:** Toda equipe IA  
**📍 Localização:** `WebAgent_Social_Extraction/memory/technical_decisions/DIRECTORY_STRUCTURE_PROTOCOL.md`
