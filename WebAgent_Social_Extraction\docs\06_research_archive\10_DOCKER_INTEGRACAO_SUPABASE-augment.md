# 🐳 INTEGRAÇÃO DOCKER + SUPABASE - SISTEMA WEBAGENT

**Data:** 2025-01-24  
**Versão:** v1.0 - Integração Completa Docker e Supabase  
**Autor:** Augment Code Orchestrator V5.0  
**Projeto:** WebAgent (Supabase ID: nnpxxdyhamnsxqljumun)  
**Escopo:** Containerização completa para desenvolvimento e produção  

---

## 🎯 EXECUTIVE SUMMARY

Este documento especifica a **integração completa entre Docker e Supabase** para o sistema de extração viral WebAgent. A solução oferece ambiente de desenvolvimento local completo, deploy automatizado e configuração de produção escalável com **Supabase local** e **cloud**.

### PRINCIPAIS COMPONENTES:

**1. DESENVOLVIMENTO LOCAL:**
- **Supabase Local Stack** - DB, Auth, Storage, Functions, Studio
- **WebAgent Application** - Frontend e backend containerizados
- **Serviços Auxiliares** - Redis, Nginx, monitoring
- **Hot Reload** - Desenvolvimento com recarga automática

**2. PRODUÇÃO CLOUD:**
- **Supabase Cloud** - Infraestrutura gerenciada
- **Container Registry** - Imagens otimizadas
- **CI/CD Pipeline** - Deploy automatizado
- **Monitoring Stack** - Observabilidade completa

**3. CONFIGURAÇÕES AVANÇADAS:**
- **Multi-stage Builds** - Otimização de imagens
- **Health Checks** - Monitoramento de saúde
- **Secrets Management** - Gestão segura de credenciais
- **Network Isolation** - Segurança de rede

**4. FERRAMENTAS DE DESENVOLVIMENTO:**
- **Database Migrations** - Versionamento de schema
- **Seed Data** - Dados de teste automatizados
- **Backup/Restore** - Gestão de dados
- **Performance Monitoring** - Métricas em tempo real

---

## 🏗️ DOCKER COMPOSE COMPLETO

### CONFIGURAÇÃO PARA DESENVOLVIMENTO LOCAL:

```yaml
# docker-compose.yml
version: '3.8'

services:
  # =====================================================
  # SUPABASE LOCAL STACK
  # =====================================================
  
  # PostgreSQL Database
  supabase-db:
    image: supabase/postgres:**********
    container_name: webagent-supabase-db
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_PORT: 5432
      PGUSER: postgres
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "${POSTGRES_PORT:-54322}:5432"
    volumes:
      - supabase-db-data:/var/lib/postgresql/data
      - ./supabase/migrations:/docker-entrypoint-initdb.d/migrations:ro
      - ./supabase/seed:/docker-entrypoint-initdb.d/seed:ro
      - ./supabase/config/postgresql.conf:/etc/postgresql/postgresql.conf:ro
    networks:
      - webagent-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    command: >
      postgres
      -c config_file=/etc/postgresql/postgresql.conf
      -c log_statement=all
      -c log_destination=stderr
      -c logging_collector=on
      -c log_directory=/var/log/postgresql
      -c log_filename=postgresql-%Y-%m-%d_%H%M%S.log
      -c log_rotation_age=1d
      -c log_rotation_size=100MB

  # Supabase Studio (Dashboard)
  supabase-studio:
    image: supabase/studio:20240101-ce42139
    container_name: webagent-supabase-studio
    environment:
      SUPABASE_URL: http://supabase-kong:8000
      SUPABASE_REST_URL: http://localhost:${KONG_HTTP_PORT:-54320}/rest/v1/
      SUPABASE_ANON_KEY: ${SUPABASE_ANON_KEY}
      SUPABASE_SERVICE_KEY: ${SUPABASE_SERVICE_ROLE_KEY}
      STUDIO_PG_META_URL: http://supabase-meta:8080
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    ports:
      - "${STUDIO_PORT:-54323}:3000"
    networks:
      - webagent-network
    restart: unless-stopped
    depends_on:
      supabase-db:
        condition: service_healthy
      supabase-meta:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgREST API
  supabase-rest:
    image: postgrest/postgrest:v12.0.1
    container_name: webagent-supabase-rest
    environment:
      PGRST_DB_URI: postgres://authenticator:${POSTGRES_PASSWORD}@supabase-db:5432/postgres
      PGRST_DB_SCHEMAS: public,storage,graphql_public
      PGRST_DB_ANON_ROLE: anon
      PGRST_JWT_SECRET: ${JWT_SECRET}
      PGRST_DB_USE_LEGACY_GUCS: "false"
      PGRST_APP_SETTINGS_JWT_SECRET: ${JWT_SECRET}
      PGRST_APP_SETTINGS_JWT_EXP: 3600
      PGRST_DB_CONFIG: true
      PGRST_DB_PRE_CONFIG: true
      PGRST_DB_MAX_ROWS: 10000
      PGRST_DB_POOL: 20
      PGRST_DB_POOL_TIMEOUT: 10
      PGRST_SERVER_CORS_ALLOWED_ORIGINS: "*"
    ports:
      - "${REST_PORT:-54321}:3000"
    networks:
      - webagent-network
    restart: unless-stopped
    depends_on:
      supabase-db:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Supabase Auth (GoTrue)
  supabase-auth:
    image: supabase/gotrue:v2.132.3
    container_name: webagent-supabase-auth
    environment:
      GOTRUE_API_HOST: 0.0.0.0
      GOTRUE_API_PORT: 9999
      API_EXTERNAL_URL: ${API_EXTERNAL_URL:-http://localhost:54321}
      GOTRUE_DB_DRIVER: postgres
      GOTRUE_DB_DATABASE_URL: postgres://supabase_auth_admin:${POSTGRES_PASSWORD}@supabase-db:5432/postgres
      GOTRUE_SITE_URL: ${SITE_URL:-http://localhost:3000}
      GOTRUE_URI_ALLOW_LIST: ${ADDITIONAL_REDIRECT_URLS:-}
      GOTRUE_DISABLE_SIGNUP: ${DISABLE_SIGNUP:-false}
      GOTRUE_JWT_ADMIN_ROLES: service_role
      GOTRUE_JWT_AUD: authenticated
      GOTRUE_JWT_DEFAULT_GROUP_NAME: authenticated
      GOTRUE_JWT_EXP: ${JWT_EXPIRY:-3600}
      GOTRUE_JWT_SECRET: ${JWT_SECRET}
      GOTRUE_EXTERNAL_EMAIL_ENABLED: ${ENABLE_EMAIL_SIGNUP:-true}
      GOTRUE_EXTERNAL_ANONYMOUS_USERS_ENABLED: ${ENABLE_ANONYMOUS_USERS:-false}
      GOTRUE_MAILER_AUTOCONFIRM: ${ENABLE_EMAIL_AUTOCONFIRM:-false}
      GOTRUE_SMTP_ADMIN_EMAIL: ${SMTP_ADMIN_EMAIL:-<EMAIL>}
      GOTRUE_SMTP_HOST: ${SMTP_HOST:-supabase-inbucket}
      GOTRUE_SMTP_PORT: ${SMTP_PORT:-2500}
      GOTRUE_SMTP_USER: ${SMTP_USER:-fake_mail_user}
      GOTRUE_SMTP_PASS: ${SMTP_PASS:-fake_mail_password}
      GOTRUE_SMTP_SENDER_NAME: ${SMTP_SENDER_NAME:-WebAgent}
      GOTRUE_RATE_LIMIT_HEADER: X-RateLimit-Remaining
      GOTRUE_RATE_LIMIT_EMAIL_SENT: 30
      GOTRUE_RATE_LIMIT_SMS_SENT: 30
    ports:
      - "${AUTH_PORT:-54324}:9999"
    networks:
      - webagent-network
    restart: unless-stopped
    depends_on:
      supabase-db:
        condition: service_healthy
      supabase-mail:
        condition: service_started
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9999/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Supabase Storage
  supabase-storage:
    image: supabase/storage-api:v0.46.4
    container_name: webagent-supabase-storage
    environment:
      ANON_KEY: ${SUPABASE_ANON_KEY}
      SERVICE_KEY: ${SUPABASE_SERVICE_ROLE_KEY}
      POSTGREST_URL: http://supabase-rest:3000
      PGRST_JWT_SECRET: ${JWT_SECRET}
      DATABASE_URL: postgres://supabase_storage_admin:${POSTGRES_PASSWORD}@supabase-db:5432/postgres
      FILE_SIZE_LIMIT: 52428800
      STORAGE_BACKEND: file
      FILE_STORAGE_BACKEND_PATH: /var/lib/storage
      TENANT_ID: stub
      REGION: ${REGION:-us-east-1}
      GLOBAL_S3_BUCKET: stub
      ENABLE_IMAGE_TRANSFORMATION: "true"
      IMGPROXY_URL: http://supabase-imgproxy:5001
      IMGPROXY_REQUEST_TIMEOUT: 15
      STORAGE_S3_FORCE_PATH_STYLE: "true"
      STORAGE_S3_ENDPOINT: ${S3_ENDPOINT:-}
      AWS_ACCESS_KEY_ID: ${S3_ACCESS_KEY_ID:-}
      AWS_SECRET_ACCESS_KEY: ${S3_SECRET_ACCESS_KEY:-}
      AWS_DEFAULT_REGION: ${AWS_DEFAULT_REGION:-us-east-1}
    ports:
      - "${STORAGE_PORT:-54325}:5000"
    volumes:
      - supabase-storage-data:/var/lib/storage
    networks:
      - webagent-network
    restart: unless-stopped
    depends_on:
      supabase-db:
        condition: service_healthy
      supabase-rest:
        condition: service_healthy
      supabase-imgproxy:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/status"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Image Proxy para transformações
  supabase-imgproxy:
    image: darthsim/imgproxy:v3.8.0
    container_name: webagent-supabase-imgproxy
    environment:
      IMGPROXY_BIND: ":5001"
      IMGPROXY_LOCAL_FILESYSTEM_ROOT: /
      IMGPROXY_USE_ETAG: "true"
      IMGPROXY_ENABLE_WEBP_DETECTION: "true"
      IMGPROXY_ENABLE_AVIF_DETECTION: "true"
      IMGPROXY_ENFORCE_WEBP: "false"
      IMGPROXY_ENFORCE_AVIF: "false"
      IMGPROXY_ENABLE_CLIENT_HINTS: "true"
      IMGPROXY_QUALITY: 85
      IMGPROXY_FORMAT_QUALITY: "webp=80,avif=75"
      IMGPROXY_JPEG_PROGRESSIVE: "true"
      IMGPROXY_PNG_INTERLACED: "true"
      IMGPROXY_MAX_SRC_RESOLUTION: 50
      IMGPROXY_MAX_SRC_FILE_SIZE: 52428800
      IMGPROXY_TTL: 31536000
      IMGPROXY_CACHE_CONTROL_PASSTHROUGH: "true"
      IMGPROXY_SET_CANONICAL_HEADER: "true"
      IMGPROXY_SECRET: ${IMGPROXY_SECRET}
      IMGPROXY_SALT: ${IMGPROXY_SALT}
      IMGPROXY_SIGNATURE_SIZE: 32
      IMGPROXY_WORKERS: 4
      IMGPROXY_MAX_CLIENTS: 2048
      IMGPROXY_READ_TIMEOUT: 10
      IMGPROXY_WRITE_TIMEOUT: 10
      IMGPROXY_DOWNLOAD_TIMEOUT: 30
    volumes:
      - supabase-storage-data:/var/lib/storage:ro
    networks:
      - webagent-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Meta API para Studio
  supabase-meta:
    image: supabase/postgres-meta:v0.68.0
    container_name: webagent-supabase-meta
    environment:
      PG_META_PORT: 8080
      PG_META_DB_HOST: supabase-db
      PG_META_DB_PORT: 5432
      PG_META_DB_NAME: postgres
      PG_META_DB_USER: postgres
      PG_META_DB_PASSWORD: ${POSTGRES_PASSWORD}
    ports:
      - "${META_PORT:-54327}:8080"
    networks:
      - webagent-network
    restart: unless-stopped
    depends_on:
      supabase-db:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Kong API Gateway
  supabase-kong:
    image: kong:2.8.1
    container_name: webagent-supabase-kong
    environment:
      KONG_DATABASE: "off"
      KONG_DECLARATIVE_CONFIG: /var/lib/kong/kong.yml
      KONG_DNS_ORDER: LAST,A,CNAME
      KONG_PLUGINS: request-transformer,cors,key-auth,acl,basic-auth,rate-limiting
      KONG_NGINX_PROXY_PROXY_BUFFER_SIZE: 160k
      KONG_NGINX_PROXY_PROXY_BUFFERS: 64 160k
      KONG_LOG_LEVEL: info
      KONG_ACCESS_LOG: /dev/stdout
      KONG_ERROR_LOG: /dev/stderr
    ports:
      - "${KONG_HTTP_PORT:-54320}:8000/tcp"
      - "${KONG_HTTPS_PORT:-54326}:8443/tcp"
    volumes:
      - ./supabase/config/kong.yml:/var/lib/kong/kong.yml:ro
    networks:
      - webagent-network
    restart: unless-stopped
    depends_on:
      - supabase-auth
      - supabase-rest
      - supabase-storage
      - supabase-meta
    healthcheck:
      test: ["CMD", "kong", "health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Inbucket (Email Testing)
  supabase-mail:
    image: inbucket/inbucket:3.0.3
    container_name: webagent-supabase-mail
    environment:
      INBUCKET_WEB_ADDR: 0.0.0.0:9000
      INBUCKET_POP3_ADDR: 0.0.0.0:1100
      INBUCKET_SMTP_ADDR: 0.0.0.0:2500
    ports:
      - "${INBUCKET_PORT:-54328}:9000"
    networks:
      - webagent-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # =====================================================
  # WEBAGENT APPLICATION
  # =====================================================

  # WebAgent Main Application
  webagent-app:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
      args:
        NODE_ENV: development
        BUILD_DATE: ${BUILD_DATE:-}
        GIT_COMMIT: ${GIT_COMMIT:-}
        VERSION: ${VERSION:-latest}
    container_name: webagent-app
    environment:
      NODE_ENV: development
      PORT: 3000
      
      # Supabase Configuration
      SUPABASE_URL: http://supabase-kong:8000
      SUPABASE_ANON_KEY: ${SUPABASE_ANON_KEY}
      SUPABASE_SERVICE_ROLE_KEY: ${SUPABASE_SERVICE_ROLE_KEY}
      
      # Database
      DATABASE_URL: postgres://postgres:${POSTGRES_PASSWORD}@supabase-db:5432/postgres
      
      # Redis
      REDIS_URL: redis://redis:6379
      
      # External APIs
      TWITTER_BEARER_TOKEN: ${TWITTER_BEARER_TOKEN:-}
      YOUTUBE_API_KEY: ${YOUTUBE_API_KEY:-}
      INSTAGRAM_SESSION_ID: ${INSTAGRAM_SESSION_ID:-}
      OPENAI_API_KEY: ${OPENAI_API_KEY:-}
      HUGGINGFACE_ACCESS_TOKEN: ${HUGGINGFACE_ACCESS_TOKEN:-}
      
      # Application Settings
      JWT_SECRET: ${JWT_SECRET}
      ENCRYPTION_KEY: ${ENCRYPTION_KEY:-}
      SESSION_SECRET: ${SESSION_SECRET:-}
      
      # Monitoring
      SENTRY_DSN: ${SENTRY_DSN:-}
      LOG_LEVEL: ${LOG_LEVEL:-info}
      
      # Development
      CHOKIDAR_USEPOLLING: "true"
      WATCHPACK_POLLING: "true"
      
    ports:
      - "${APP_PORT:-3000}:3000"
      - "${DEBUG_PORT:-9229}:9229" # Debug port
    volumes:
      - ./src:/app/src:cached
      - ./public:/app/public:cached
      - ./package.json:/app/package.json:ro
      - ./package-lock.json:/app/package-lock.json:ro
      - ./tsconfig.json:/app/tsconfig.json:ro
      - ./next.config.js:/app/next.config.js:ro
      - ./tailwind.config.js:/app/tailwind.config.js:ro
      - node_modules:/app/node_modules
      - ./logs:/app/logs
    networks:
      - webagent-network
    restart: unless-stopped
    depends_on:
      supabase-kong:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    develop:
      watch:
        - action: sync
          path: ./src
          target: /app/src
          ignore:
            - node_modules/
        - action: rebuild
          path: package.json
        - action: rebuild
          path: package-lock.json

  # =====================================================
  # SERVIÇOS AUXILIARES
  # =====================================================

  # Redis para cache e sessões
  redis:
    image: redis:7-alpine
    container_name: webagent-redis
    environment:
      REDIS_PASSWORD: ${REDIS_PASSWORD:-}
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis-data:/data
      - ./redis/redis.conf:/etc/redis/redis.conf:ro
    networks:
      - webagent-network
    restart: unless-stopped
    command: >
      redis-server /etc/redis/redis.conf
      --requirepass ${REDIS_PASSWORD:-}
      --appendonly yes
      --appendfsync everysec
      --maxmemory 256mb
      --maxmemory-policy allkeys-lru
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx para proxy reverso e load balancing
  nginx:
    image: nginx:alpine
    container_name: webagent-nginx
    ports:
      - "${HTTP_PORT:-80}:80"
      - "${HTTPS_PORT:-443}:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./nginx/logs:/var/log/nginx
      - nginx-cache:/var/cache/nginx
    networks:
      - webagent-network
    restart: unless-stopped
    depends_on:
      - webagent-app
      - supabase-kong
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus para métricas
  prometheus:
    image: prom/prometheus:latest
    container_name: webagent-prometheus
    ports:
      - "${PROMETHEUS_PORT:-9090}:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - ./monitoring/rules:/etc/prometheus/rules:ro
      - prometheus-data:/prometheus
    networks:
      - webagent-network
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:9090/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Grafana para dashboards
  grafana:
    image: grafana/grafana:latest
    container_name: webagent-grafana
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD:-admin}
      GF_USERS_ALLOW_SIGN_UP: "false"
      GF_INSTALL_PLUGINS: grafana-piechart-panel,grafana-worldmap-panel
    ports:
      - "${GRAFANA_PORT:-3001}:3000"
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - webagent-network
    restart: unless-stopped
    depends_on:
      - prometheus
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

# =====================================================
# VOLUMES
# =====================================================

volumes:
  supabase-db-data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/data/postgres
  supabase-storage-data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/data/storage
  redis-data:
    driver: local
  node_modules:
    driver: local
  nginx-cache:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local

# =====================================================
# NETWORKS
# =====================================================

networks:
  webagent-network:
    driver: bridge
    ipam:
      driver: default
      config:
        - subnet: **********/16
          gateway: **********
    driver_opts:
      com.docker.network.bridge.name: webagent-br0
      com.docker.network.driver.mtu: 1500
```
