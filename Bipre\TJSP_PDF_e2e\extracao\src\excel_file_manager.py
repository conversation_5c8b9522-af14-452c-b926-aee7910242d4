#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EXCEL FILE MANAGER ENTERPRISE
Gerenciador avançado para operações seguras em arquivos Excel
com detecção e resolução automática de file locking.

Baseado em pesquisa profunda de soluções enterprise para file locking.
"""

import psutil
import gc
import time
import tempfile
import shutil
import os
import sys
import atexit
import contextlib
from contextlib import contextmanager
import uuid
import traceback
from pathlib import Path
from datetime import datetime
import logging

# Configurar logging específico para ExcelFileManager
excel_logger = logging.getLogger('ExcelFileManager')
excel_logger.setLevel(logging.INFO)

# Importações condicionais para Windows COM
try:
    if sys.platform.startswith('win'):
        import pythoncom
        COM_AVAILABLE = True
    else:
        COM_AVAILABLE = False
except ImportError:
    COM_AVAILABLE = False

class ExcelFileManager:
    """
    Gerenciador enterprise para operações seguras em Excel.
    
    Funcionalidades:
    - Detecção automática de arquivos Excel abertos
    - Fechamento automático de processos Excel
    - Operações atômicas com temporary files
    - Resource tracking e cleanup automático
    - Retry logic com exponential backoff
    - Error handling robusto
    """
    
    def __init__(self, enable_auto_cleanup=True):
        self.temp_files = []
        self.open_handles = {}
        self.enable_auto_cleanup = enable_auto_cleanup
        
        if self.enable_auto_cleanup:
            self.setup_cleanup()
        
        excel_logger.info("ExcelFileManager inicializado")
    
    def setup_cleanup(self):
        """Registra cleanup automático na saída da aplicação."""
        atexit.register(self.cleanup_all)
    
    def track_resource(self, resource_type: str, resource_obj, resource_path: str = None) -> str:
        """Track um recurso para cleanup posterior."""
        handle_id = str(uuid.uuid4())
        self.open_handles[handle_id] = {
            'type': resource_type,
            'object': resource_obj,
            'path': resource_path,
            'created': time.time()
        }
        excel_logger.debug(f"Recurso tracked: {handle_id} ({resource_type})")
        return handle_id
    
    def close_resource(self, handle_id: str) -> bool:
        """Close e cleanup de recurso tracked."""
        if handle_id not in self.open_handles:
            return False
            
        handle_info = self.open_handles.pop(handle_id)
        resource_type = handle_info['type']
        resource_obj = handle_info['object']
        
        try:
            if resource_type == 'excel':
                resource_obj.Quit()
            elif resource_type == 'workbook':
                resource_obj.Close(SaveChanges=False)
            elif resource_type == 'openpyxl_workbook':
                resource_obj.close()
            elif resource_type == 'file_handle':
                resource_obj.close()
                
            gc.collect()
            excel_logger.debug(f"Recurso fechado: {handle_id}")
            return True
        except Exception as e:
            excel_logger.warning(f"Erro fechando recurso {handle_id}: {e}")
            return False
    
    def is_file_locked(self, file_path: str) -> bool:
        """
        Verifica se arquivo está locked/aberto.
        
        Args:
            file_path: Caminho para o arquivo
            
        Returns:
            bool: True se arquivo está locked, False caso contrário
        """
        try:
            # Tentar abrir arquivo para escrita
            with open(file_path, 'r+b'):
                return False
        except (IOError, OSError, PermissionError):
            return True
    
    def close_excel_processes(self) -> list:
        """
        Fecha todos processos Excel running para prevenir file locking.
        
        Returns:
            list: Lista de PIDs dos processos terminados
        """
        terminated_pids = []
        
        excel_logger.info("🔄 Detectando e fechando processos Excel...")
        
        # Encontrar todos processos Excel
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                if proc.info['name'] and 'excel' in proc.info['name'].lower():
                    pid = proc.info['pid']
                    excel_logger.info(f"📋 Terminando processo Excel (PID: {pid})")
                    
                    try:
                        process = psutil.Process(pid)
                        process.terminate()
                        try:
                            process.wait(timeout=5)
                            terminated_pids.append(pid)
                            excel_logger.info(f"✅ Excel terminado: PID {pid}")
                        except psutil.TimeoutExpired:
                            process.kill()
                            terminated_pids.append(pid)
                            excel_logger.warning(f"⚠️ Excel forçado a fechar: PID {pid}")
                    except (psutil.NoSuchProcess, psutil.AccessDenied, Exception) as e:
                        excel_logger.warning(f"❌ Falha terminando Excel (PID {pid}): {e}")
                        
            except Exception:
                pass
        
        # Force COM cleanup se disponível
        if COM_AVAILABLE:
            try:
                pythoncom.CoFreeUnusedLibraries()
                excel_logger.debug("COM libraries liberadas")
            except:
                pass
        
        gc.collect()
        time.sleep(1)
        
        if terminated_pids:
            excel_logger.info(f"🎉 {len(terminated_pids)} processos Excel fechados")
        else:
            excel_logger.info("ℹ️ Nenhum processo Excel encontrado")
        
        return terminated_pids
    
    def wait_for_file_release(self, file_path: str, timeout: int = 60) -> bool:
        """
        Aguarda arquivo ser liberado com timeout.
        
        Args:
            file_path: Caminho para o arquivo
            timeout: Timeout em segundos
            
        Returns:
            bool: True se arquivo foi liberado, False se timeout
        """
        start = time.time()
        while time.time() - start < timeout:
            if not self.is_file_locked(file_path):
                excel_logger.info(f"✅ Arquivo liberado: {Path(file_path).name}")
                return True
            
            elapsed = int(time.time() - start)
            excel_logger.info(f"⏳ Aguardando liberação... {elapsed}s/{timeout}s")
            time.sleep(2)
        
        excel_logger.error(f"❌ Timeout aguardando liberação: {Path(file_path).name}")
        return False
    
    def unlock_file(self, file_path: str) -> bool:
        """
        Detecta e resolve file locking usando psutil (Windows específico).
        
        Args:
            file_path: Caminho para o arquivo
            
        Returns:
            bool: True se arquivo foi desbloqueado com sucesso
        """
        try:
            # Fechar Excel instances primeiro
            self.close_excel_processes()
            
            # Windows: usar psutil para detectar processos com arquivo aberto
            if sys.platform.startswith('win'):
                file_path = os.path.abspath(file_path)
                
                # Cleanup COM connections se disponível
                if COM_AVAILABLE:
                    pythoncom.CoFreeUnusedLibraries()
                gc.collect()
                
                # Aguardar handles liberarem
                time.sleep(0.5)
                
                # Usar psutil para verificar processos com arquivo aberto
                target_path = os.path.normcase(os.path.normpath(file_path))
                for proc in psutil.process_iter(['pid', 'name', 'open_files']):
                    try:
                        open_files = proc.info.get('open_files', [])
                        if open_files:
                            for file_info in open_files:
                                if os.path.normcase(os.path.normpath(file_info.path)) == target_path:
                                    excel_logger.info(f"🔍 Processo {proc.info['name']} (PID {proc.info['pid']}) com handle aberto")
                                    proc.kill()
                                    excel_logger.info(f"✅ Processo terminado: {proc.info['pid']}")
                                    return True
                    except (psutil.AccessDenied, psutil.NoSuchProcess, Exception):
                        continue
                        
            return True
        except Exception as e:
            excel_logger.warning(f"⚠️ Erro unlocking arquivo {file_path}: {e}")
            return False
    
    def create_backup_file(self, file_path: str) -> str:
        """
        Cria backup do arquivo antes de operações.
        
        Args:
            file_path: Caminho do arquivo original
            
        Returns:
            str: Caminho do arquivo de backup
        """
        if not os.path.exists(file_path):
            return None
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = f"{file_path}.backup_{timestamp}"
        
        try:
            shutil.copy2(file_path, backup_path)
            excel_logger.info(f"💾 Backup criado: {Path(backup_path).name}")
            return backup_path
        except Exception as e:
            excel_logger.warning(f"⚠️ Erro criando backup: {e}")
            return None
    
    def get_temp_file_path(self, prefix="excel_temp", suffix=".xlsx") -> str:
        """
        Gera caminho para arquivo temporário.
        
        Args:
            prefix: Prefixo do arquivo
            suffix: Extensão do arquivo
            
        Returns:
            str: Caminho do arquivo temporário
        """
        temp_file = tempfile.mktemp(prefix=prefix, suffix=suffix)
        self.temp_files.append(temp_file)
        return temp_file
    
    def cleanup_temp_files(self):
        """Limpa arquivos temporários."""
        for temp_file in self.temp_files[:]:
            try:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
                    excel_logger.debug(f"Temp file removido: {Path(temp_file).name}")
                self.temp_files.remove(temp_file)
            except Exception as e:
                excel_logger.warning(f"Erro removendo temp file {temp_file}: {e}")
    
    def cleanup_all(self):
        """Cleanup completo de todos os recursos."""
        excel_logger.info("🧹 Executando cleanup completo...")
        
        # Fechar recursos tracked
        for handle_id in list(self.open_handles.keys()):
            self.close_resource(handle_id)
        
        # Limpar temp files
        self.cleanup_temp_files()
        
        # COM cleanup se disponível
        if COM_AVAILABLE:
            try:
                pythoncom.CoFreeUnusedLibraries()
            except:
                pass
        
        gc.collect()
        excel_logger.info("✅ Cleanup completo finalizado")

    @contextmanager
    def safe_excel_operation(self, file_path: str, create_backup: bool = True):
        """
        Context manager para operações seguras em Excel.

        Args:
            file_path: Caminho do arquivo Excel
            create_backup: Se deve criar backup antes da operação

        Yields:
            str: Caminho do arquivo temporário para operações
        """
        excel_logger.info(f"🔒 Iniciando operação segura: {Path(file_path).name}")

        # Verificar se arquivo está aberto
        if os.path.exists(file_path) and self.is_file_locked(file_path):
            excel_logger.warning("⚠️ Arquivo Excel detectado como aberto!")
            excel_logger.info("💡 Fechando processos Excel automaticamente...")

            self.close_excel_processes()

            if not self.wait_for_file_release(file_path, timeout=30):
                raise Exception(f"Arquivo ainda está locked após cleanup: {file_path}")

        # Criar backup se solicitado
        backup_path = None
        if create_backup and os.path.exists(file_path):
            backup_path = self.create_backup_file(file_path)

        # Criar temp file para operação segura
        temp_file = self.get_temp_file_path(
            prefix=f"safe_{Path(file_path).stem}_",
            suffix=Path(file_path).suffix
        )

        try:
            excel_logger.info(f"📝 Usando arquivo temporário: {Path(temp_file).name}")
            yield temp_file

            # Mover temp file para destino final atomicamente
            if os.path.exists(temp_file):
                excel_logger.info("🔄 Movendo arquivo temporário para destino final...")

                # Verificar integridade do temp file
                if os.path.getsize(temp_file) == 0:
                    raise ValueError("Arquivo temporário está vazio!")

                # Remover arquivo original se existir
                if os.path.exists(file_path):
                    self.unlock_file(file_path)
                    try:
                        os.remove(file_path)
                    except PermissionError:
                        excel_logger.warning("⚠️ Permission error, tentando novamente...")
                        time.sleep(0.5)
                        os.remove(file_path)

                # Mover temp file para destino
                shutil.move(temp_file, file_path)
                excel_logger.info(f"✅ Arquivo salvo com segurança: {Path(file_path).name}")
            else:
                raise FileNotFoundError("Arquivo temporário não foi criado!")

        except Exception as e:
            excel_logger.error(f"❌ Erro durante operação segura: {e}")
            excel_logger.error(f"Traceback: {traceback.format_exc()}")

            # Tentar restaurar backup se disponível
            if backup_path and os.path.exists(backup_path):
                try:
                    if os.path.exists(file_path):
                        os.remove(file_path)
                    shutil.copy2(backup_path, file_path)
                    excel_logger.info(f"🔄 Backup restaurado: {Path(file_path).name}")
                except Exception as restore_error:
                    excel_logger.error(f"❌ Erro restaurando backup: {restore_error}")

            raise
        finally:
            # Cleanup temp files
            self.cleanup_temp_files()

            # Cleanup COM se disponível
            if COM_AVAILABLE:
                try:
                    pythoncom.CoFreeUnusedLibraries()
                except:
                    pass

            gc.collect()

    def safe_save_excel_with_pandas(self, dataframes_dict: dict, file_path: str,
                                   engine: str = 'openpyxl', retries: int = 3) -> bool:
        """
        Salva múltiplos DataFrames em Excel de forma segura.

        Args:
            dataframes_dict: Dict com {sheet_name: dataframe}
            file_path: Caminho do arquivo Excel
            engine: Engine do pandas (openpyxl, xlsxwriter)
            retries: Número de tentativas

        Returns:
            bool: True se salvou com sucesso
        """
        import pandas as pd

        for attempt in range(retries):
            try:
                excel_logger.info(f"💾 Tentativa {attempt + 1}/{retries} - Salvando Excel...")

                with self.safe_excel_operation(file_path) as temp_file:
                    with pd.ExcelWriter(temp_file, engine=engine) as writer:
                        for sheet_name, df in dataframes_dict.items():
                            df.to_excel(writer, sheet_name=sheet_name, index=False)
                            excel_logger.debug(f"📋 Aba criada: {sheet_name} ({len(df)} registros)")

                excel_logger.info(f"🎉 Excel salvo com sucesso: {Path(file_path).name}")
                return True

            except Exception as e:
                excel_logger.warning(f"⚠️ Tentativa {attempt + 1} falhou: {e}")
                if attempt < retries - 1:
                    delay = (attempt + 1) * 2  # Exponential backoff
                    excel_logger.info(f"⏳ Aguardando {delay}s antes da próxima tentativa...")
                    time.sleep(delay)

                    # Force cleanup entre tentativas
                    self.close_excel_processes()
                    if COM_AVAILABLE:
                        pythoncom.CoFreeUnusedLibraries()
                    gc.collect()
                else:
                    excel_logger.error(f"❌ Falha após {retries} tentativas: {traceback.format_exc()}")
                    return False

        return False
