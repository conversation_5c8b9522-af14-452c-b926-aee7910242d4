﻿bom vamos então à aula prática aqui com
exemplos né de como você manipular dados
pelo node code usando a linguagem Python
tá então eu vou começar
aqui node code
tá E aí eu já altero para essa opção
aqui né para executar para cada item né
e já deixo definido aqui Python Beleza
então ele já vai me dar uma estrutura
padrão aqui
né já vem já por default Ok e agora a
gente vai começar a fazer a customização
aqui tá
eh então para ficar uma coisa mais
profissional né Vamos renomeando aqui os
nodes eu vou colocar aqui start né
Eh aí aqui eu posso colocar formata
dados
formata
dados e eu vou colocar aqui um node
setet simulando né o input ali eh dos
dados do Lead né vamos dizer que são
Dados que veio de alguma aplicação
externa sua né um CRM uma ferramenta de
meio Mar enfim tá E aí vou começar a
colocar os campos aqui tá então o
primeiro vai ser um nome tá por exemplo
posso colocar aqui o meu
nome segundo pode ser e-mail
tá ou seja os dados que geralmente a
gente costuma eh captar né do lit às
vezes de um formulário e a gente vai
formatar eles tá isso se o seu
formulário por exemplo não já fazer a
formatação ali eh de forma Nativa
E aí aqui eu vou colocar um e-mail
qualquer botar Hugo
@
auto.com.br
tá e o outro eu vou colocar o
WhatsApp
e e aqui eu posso colocar um número
fictício né botar
aqui mais
55
21
eh botar um número aqui
qualquer
9 Beleza
então posso ativar essa opção aqui só
para ele retornar para mim aqui ó dados
do
Lead então se eu executa aqui ó ele
retorna né
os dados do Lead aqui tá que eu defini
aqui de forma fixa
beleza e agora vamos utilizar né a
linguagem Python aqui no node code para
começar a formatar esses dados né para
definir ali um padrão de formatação para
poder talvez
eh inserir em uma outra aplicação né É
Para Isso que serve o ntn tá para você
ter aí uma padronização de formato de
dados que às vezes você pode ter várias
fontes de dados e cada aplicação externa
ali tem uma forma diferente né de usar
um formato diferente de dados então o NN
vai padronizar isso por meio do node
code tá porém a gente vai fazer isso com
a linguagem Python Então vamos começar
primeiramente eh aqui pelo nome né Por
exemplo
eh eu vou colocar primeiro aqui um
comentário né Eh Lembrando que o
comentário no Python você você coloca
aqui eh sustenido né o jogo jogo da
velha e coloca aqui uma descrição por
exemplo vou botar aqui ó
eh Separa ou então posso botar assim
extrai nome e
sobrenome Vou apagar aqui só vou deixar
esse return aqui item porque é o que
ele vai retornar né depois as
informações então primeira coisa aqui né
Vamos declarar uma variável eh para o
nome tá eu botei aqui nome mesmo beleza
e aí vai receber o quê o input nome
então vou botar aqui underline input né
Lembrando que no Python eh ao invers de
ser dólar né como é utilizado no no
JavaScript a gente utiliza L tá para
poder referenciar os nossos inputs né
que são os dados que vem eh dos nodes
anteriores e aí Vou colocar aqui item né
porque isso aqui é um item tá ponto
Jon por quê Porque os dados estão na
estrutura Jon e agora vou botar o nome
do campo tá ó se você for reparar no
Python ele não tem o aut complete né
diferente do JavaScript JavaScript né
antes de você você informar o nome aqui
você deu ponto ele já te dá a sugestão
ali de todos os campos né aqui do input
tá o Python ainda não tem por quê porque
ele ainda tá na versão Beta tá então
embora o ntn ele já tenha essa opção de
você usar Python na nativamente aqui no
code
eh é uma funcionalidade que ainda não tá
100% tá na fase ainda de teste algumas
coisas ainda para ser melhorados tá
então é só você colocar aqui manualmente
Então vou botar aqui nome tá
E aí eu vou começar agora a criar os
campos do meu output que é aqui desse
lado aqui tá então eu vou começar
criando aqui
eh um
campo chamado eh
fname seria first Name né ou seja o
primeiro
nome e aí eu simplesmente pego a minha
variável aqui
tá nome Ok E aí eu posso usar uma função
Nativa aqui do pyon sem precisar usar
biblioteca tá
eh que é o split que também é muito
utilizado em JavaScript e serve pra
gente poder eh extrair índices né Eh de
uma string com base em um delimitador
então se eu dou um ponto split
tá aliás perdão tem colocar aqui
item Agora sim Jon Ok não na verdade
perdão tá certo mesmo eu tô colocando o
nome é porque tá confundindo aqui né eu
tô colocando nome por quê Porque nome já
é a variável que já está recebendo o meu
input nome tá então perdão a gente pode
ir direto aqui vou dar nome ponto split
tá e de fato é uma função
eh da do dos dados do do tipo string Ok
E aí eu posso colocar aqui um
delimitador Tá qual seria esse
delimitador o espaço
tá então eu dou um backspace aqui para
ele dar um espaço e aí ele vai pegar
essa minha string aqui e vai separar em
dois índices índice zero e índice um né
Por quê Porque ele tá usando o espaço
como delimitador então o o que é do
espaço pra esquerda vai ser o índice
Zero O que é o do espaço para a direita
vai ser o índice um tá então se eu chego
aqui e coloco o índice zero tá olha o
que que vai acontecer
ó ele vai retornar apenas o primeiro
nome
tá E aí por exemplo se eu coloco
aqui um outro campo né Por exemplo vou
criar um campo aqui no meu output
chamado LN né de Lash name seria
sobrenome
E aí aqui eu já colocar
um e
executar aí olha que legal ó ele já vai
retornar o sobrenome Ok por quê Porque
esse split pegou a string e dividiu em
dois índices ali com base no delimitador
tá é claro que se aqui fosse Hugo
Santiago Perez né Eh eu teria três índic
o zero que seria Hugo o um que seria
Santiago e o dois que seria PES tá ou
seja usando o delimitador espaço né para
poder separar esses índices Beleza então
primeiro passo aqui concluído né
conseguimos eh extrair nome e sobrenome
e atribuir eles a Campos tá e o próximo
passo agora seria a gente
eh formatar WhatsApp tá E também validar
e-mail ok que às vezes o usuário ali né
quando vai informar um WhatsApp informa
WhatsApp
eh com caracteres Inválidos ou então
quando vai informar um e-mail informa um
e-mail inválido tá com a sintaxe
inválido e aqui no node code você vai
poder também tratar isso usando a
linguagem Python tá E aí que que a gente
vai fazer para ficar uma coisa mais
organizada eu vou usar esse espaço aqui
de cima deixa eu ver aqui não a gente
pode ir por por etapa não tem problema
não tá eu vou colocar aqui um outro
deixa eu ver se dá para dar um espaço
Vou colocar aqui um outro comentário Tá
eu vou botar aqui
e
formata bem que eu não sei se ele
funciona assim deixa eu
ver é na verdade ele não tá nem
aceitando você colocar comentários
né durante o script parece que ele só
deixa você colocar na primeira
linha deixa eu ver
aqui é tá vendo ó el dá até
erro porque como eu falei né como ele tá
na versão Beta Então ainda não tá 100%
mas você pode usar simplesmente a
primeira linha aqui para descrever tudo
que o script faz tá então vou botar aqui
ó eh extrai
nome nome sobre nome tá
eh
formata
WhatsApp e verifica se o
e-mail é válido beleza
Então vamos lá para ficar uma coisa mais
organizada é Logo Aqui em cima eu vou
deixar
separado para as variáveis
Tá eu vou criar uma vari aqui
chamado
WhatsApp e a outra e mail
tá aí do
WhatsApp eu vou pegar o input aqui ó o
WhatsApp
tá Inclusive eu posso até botar aqui ó
wpp né para ficar mais fácil
aqui e aqui no outro eu posso pegar um
input e meil
tá beleza e aqui embaixo
eh vai ser o tratamento né das
informações então o primeiro aqui ele
extraiu o nome sobrenome
tá o segundo a gente vai eh utilizar a
expressão regular tá pra gente poder
formatar o número de WhatsApp da mesma
forma que a gente também vai usar a
expressão regular para verificar o
e-mail tá e o Python ele não tem uma
função Nativa eh para poder trabalhar
com a expressão regular você precisa
importar uma bibl que é a biblioteca R
né re tá onde você vai poder e usar
algumas funções para poder usar o
conceito aí de expressão irregular tá E
então aqui ó na nossa lista de
bibliotecas nativas aliás padrão né do
do Python você tem essa biblioteca aqui
ó que é o r tá
re referente a operações com expressões
regulares
tá
e ao vir aqui mais para baixo você vai
se
deparar com uma função chamado não me
engano acho queer
subir de eu ver
aqui é isso mesmo ó tá vendo ó é a
função sub
tá
e o que que faz essa função sub tá essa
daqui
ó ela simplesmente eh vai analisar uma
string Ok e vai ver se naquela string
existe um padrão que você espera e o que
que você pretende substituir por esse
padrão por exemplo se você coloca lá um
padrão
eh que você
quer que detecte por exemplo caracteres
especiais e letras ali no no número de
WhatsApp você pode determinar que ao
detectar esses tipos de caracteres
substitua pelo vazio tá então por
exemplo voltando aqui no nosso NM né E
logo aqui em cima tá declaração de
biblioteca sempre aqui no início tá você
vai colocar aqui um Import re
tá pode dar um espaço né para não ficar
muito embolado
eh e aí o que que você pode fazer aqui
eh na própria declaração né do aqui da
variável por exemplo Ó eu peguei aqui o
WhatsApp e armazenei aqui na na variável
WhatsApp
tá eu posso criar aqui um um novo Campo
né de output
aqui aí eu posso dar o nome de
fpp que seria WhatsApp formatado tá E aí
eu coloco Igual E aí eu chamaria a
função né e e primeiro e virar a
biblioteca então eu posso posso colocar
aqui ó re né que seria a biblioteca de
expressão regular ponto e aí colocaria
sub tá
E aí essa função ela espera
e três parâmetros
tá que seria o primeiro a sua expressão
regular ou seja o padrão que você quer
que seja detectado naquela string o
valor que você quer substituir
para esses caracteres que atenderam esse
padrão
tá e em seguida o string que você quer
que seja feito essa análise de de padrão
tá então o primeiro eu vou colocar o
seguinte eu vou colocar aqui eh
contrabarra né D que significa o
seguinte ó tudo que for diferente de
número eu quero que substitua por E aí
se você for reparar aqui eu tô deixando
vazio tá vendo ó é como fosse uma string
vazia eu não tô informando nenhum valor
Se eu por exemplo se eu coloco
significa que onde
tiv o padrão aqui identificado ele ia
substituir por mas se eu deixo assim
vazio significa que ele vai deixar vazio
se ele vai
remover aqueles caracteres que atenderam
o padrão tá e no terceiro eu passaria a
string que eu quero que seja feita essa
análise qual seria essa string o meu
número de
WhatsApp número de WhatsApp então eu vou
chegar aqui agora vou colocar a variável
tá E aí se eu
executar Olha que legal
eh a variável WhatsApp né retornou aqui
um valor eh cheio de caracteres
especiais né ó também com espaços tá
vendo ó e uma vez aplicando essa função
sub né do da biblioteca re ele aplicou
essa formato ação então ele só retornou
número para mim tá que de fato é o valor
que uma ap de WhatsApp espera né Ainda
mais se você precisa dessa informação
para poder fazer um disparo de WhatsApp
você tem que saber manipular isso tá
porque como eu falei às vezes são várias
aplicações externas cada um com a sua
forma de de retornar dados né Às vezes o
número vai vai vir asse formatado mas
tem aplicações que vai vir dessa forma
tá então você precisa
saber como manipular isso aqui no ntn no
node code tá seja usando JavaScript ou
usando Python como é o caso aqui da aula
Beleza então segundo procedimento
concluído aqui com sucesso também
formatamos aqui o WhatsApp tá eh e agora
por último a gente vai fazer uma
verificação do e-mail né para saber se
aquele e-mail é válido ou não tá mas
quando eu falo válido não é se o e-mail
e tem caixa de entrar
se
ele não tá em Blacklist Não é nada disso
tá essa verificação é mais no sentido de
sintaxe para ver se a sintaxe daquele
eil está correta tá E aí como é que vai
funcionar
aqui eu vou colocar
aqui vou criar um outro Campo aqui
tá na verdade antes dear
campo eu vou ter que fazer primeiro uma
verificação tá então eu vou utilizar
aqui o if onde eu vou usar uma outra
função da biblioteca re que é o match tá
que é a função que ela vai retornar um
valor boleano ou seja vai retornar True
or
false se a condição ali for atendida Ou
seja eu vou informar um padrão que eu
espero encontrar na na minha string E se
ele encontrar ele vai retornar true se
ele não encontrar ele vai retornar false
é basicamente isso
tá então eu como eu já tenho aqui né a a
variável que que eu vou
fazer eu vou colocar o if aqui né E aí
vou abrir e fechar parêntese e aqui eu
vou colocar a condição ó é a biblioteca
re
ponto
se me engano acho que é assim match né
E E aí quais são os parâmetros que é
exigido aqui
tá apenas o padrão que seria a expressão
regular tá
e e a a string onde eu quero fazer a
verificação dessa dessa desse padrão
para ver se se esse padrão está presente
nessa
diferente aqui do sub né o sub o sub né
ele quer verificar string para ver se
tem um padrão mas achando o padrão ele
vai substituir por uma string ou uma
substring que você vai declarar tá já no
Mat no Mat aqui ele só vai ver se aquele
padrão está presente ou não na string e
retornar true ou false beleza
e E aí qual seria a expressão que nós
vamos utilizar aqui tá e eu deixei até
aqui anotado porque é até difícil
decorar de cabeça né essa expressão como
você pode ver aqui ó eh eu tenho essa
expressão
aqui essa expressão regular aqui tá ó
vou copiar ela
toda onde ela verifica se aquela string
está num formato de meio tá inclusive
depois você pode pesquisar na internet
para saber qual é o padrão de cada tipo
de informação que você quer tem padrão
de meil tem padrão para saber se uma url
é válida tem padrão para saber se um CPF
está num formato válido se o e-mail está
no formato vai então hoje existe
expressões regulares para tudo tá então
você vai colocar a sua expressão regular
né de verificação de e-mail aqui ó
e no primeiro parâmetro tá E aqui no
segundo você vai colocar a sua string
que no caso aqui é e-mail certo a minha
variável e-mail
tá
eh deixa eu até verificar aqui
tá na verdade eu acredito que o if ele
nem precisaria desse parêntese aqui
tá de fato Você só coloca if E aí você
já coloca aqui a função né que vai
retornar true ou false beleza e aí no
Python né em vez de você colocar then né
ou seja if then ou seja then Então tá o
Então é dois pontos é assim ó Ok E aí
ele já vai eh identar aqui né a próxima
linha para você dizer o que que vai ser
feito se essa condição aqui for atendida
tá porque isso aqui é se der true Beleza
se der true verdadeiro que que eu quero
que faça eu quero que crie um campo tá
do do no meu output
eh Pode ser
aqui chamado e-mail
válido tá meio
válido E aí eu vou colocar
eh o valor sim tá para dizer que é
válido ou seja atendeu essa condição
aqui então o meio é válido tá mas se não
atender eu coloco aqui um ELS tá é E aí
também coloca aqui dois
pontos e aí aqui embaixo eu já aproveito
essa mesma estrutura porém mudando para
o valor não tá então se essa condição
aqui fori atendida é porque o e-mail é
válido mas se não atender a condição é
porque o e-mail não é válido Então esse
campo e-mail válido vai retornar a
resposta se sim ou se não tá
bom E aí se eu executar agora
Olha que legal
eh repare que agora ele retornou aqui o
campo né e-mail válido dizendo que sim
por quê Porque aparentemente aqui o meu
e-mail está num formato válido né ele
tem aqui o username @ e o domínio Tá mas
por exemplo se eu chego aqui e altero
por exemplo
ã eu tiro aqui o username por exemp só
deixa o arroba
tá ó e
executo ele já vai retornar aqui o valor
não que não é válido por quê Porque eu
tirei o username do e-mail tá da mesma
forma que se eu por exemplo deixasse
aqui eh o
username e tirasse por exemplo a
extensão né a extensão pcom.br
ó e
executar ele também vai retornar como
e-mail não válido
tá então é basicamente isso concluímos
aqui então a o terceiro procedimento que
é poder verificar e-mail tá então é
assim que você manipula os dados né você
formata os dados
eh no ntn usando a linguagem Python aqui
no node code Tá bom então espero que
você tenha gostado um forte abraço e
valeu