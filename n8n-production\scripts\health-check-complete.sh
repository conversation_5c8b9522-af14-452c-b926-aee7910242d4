#!/bin/bash

# ===================================================================
# HEALTH CHECK COMPLETO - FRAMEWORK N8N + EVOLUTION API
# ===================================================================

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Contadores
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

# Funções de log
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[✅ PASS]${NC} $1"
    ((PASSED_CHECKS++))
}

log_fail() {
    echo -e "${RED}[❌ FAIL]${NC} $1"
    ((FAILED_CHECKS++))
}

log_warning() {
    echo -e "${YELLOW}[⚠️  WARN]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}[HEADER]${NC} $1"
}

# Incrementar contador total
check_total() {
    ((TOTAL_CHECKS++))
}

# Banner
show_banner() {
    clear
    echo -e "${CYAN}"
    echo "====================================================================="
    echo "    🔍 HEALTH CHECK COMPLETO - FRAMEWORK N8N + EVOLUTION API"
    echo "====================================================================="
    echo -e "${NC}"
}

# Verificar Docker
check_docker() {
    log_header "1. VERIFICANDO DOCKER"
    
    check_total
    if command -v docker &> /dev/null; then
        log_success "Docker está instalado"
    else
        log_fail "Docker não está instalado"
        return 1
    fi
    
    check_total
    if docker info &> /dev/null; then
        log_success "Docker está rodando"
    else
        log_fail "Docker não está rodando"
        return 1
    fi
    
    check_total
    if command -v docker-compose &> /dev/null; then
        log_success "Docker Compose está instalado"
    else
        log_fail "Docker Compose não está instalado"
        return 1
    fi
    
    # Versões
    DOCKER_VERSION=$(docker --version | cut -d' ' -f3 | cut -d',' -f1)
    COMPOSE_VERSION=$(docker-compose --version | cut -d' ' -f3 | cut -d',' -f1)
    log_info "Docker: $DOCKER_VERSION | Compose: $COMPOSE_VERSION"
}

# Verificar recursos do sistema
check_system_resources() {
    log_header "2. VERIFICANDO RECURSOS DO SISTEMA"
    
    # RAM
    check_total
    TOTAL_RAM=$(free -m | awk 'NR==2{printf "%.0f", $2/1024}')
    AVAILABLE_RAM=$(free -m | awk 'NR==2{printf "%.0f", $7/1024}')
    
    if [ "$TOTAL_RAM" -ge 4 ]; then
        log_success "RAM Total: ${TOTAL_RAM}GB (Suficiente)"
    else
        log_warning "RAM Total: ${TOTAL_RAM}GB (Recomendado: 4GB+)"
    fi
    
    # Disco
    check_total
    AVAILABLE_DISK=$(df -BG . | awk 'NR==2{print $4}' | sed 's/G//')
    if [ "$AVAILABLE_DISK" -ge 20 ]; then
        log_success "Disco Disponível: ${AVAILABLE_DISK}GB (Suficiente)"
    else
        log_warning "Disco Disponível: ${AVAILABLE_DISK}GB (Recomendado: 20GB+)"
    fi
    
    # CPU
    check_total
    CPU_CORES=$(nproc)
    if [ "$CPU_CORES" -ge 2 ]; then
        log_success "CPU Cores: $CPU_CORES (Suficiente)"
    else
        log_warning "CPU Cores: $CPU_CORES (Recomendado: 2+)"
    fi
}

# Verificar containers
check_containers() {
    log_header "3. VERIFICANDO CONTAINERS"
    
    # Verificar se há containers rodando
    RUNNING_CONTAINERS=$(docker ps --format "table {{.Names}}\t{{.Status}}" | grep -v "NAMES" | wc -l)
    log_info "Containers rodando: $RUNNING_CONTAINERS"
    
    # Verificar containers específicos
    CONTAINERS=("n8n-postgres-basic" "n8n-redis-basic" "n8n-app-basic" "evolution-postgres" "evolution-redis" "evolution-api")
    
    for container in "${CONTAINERS[@]}"; do
        check_total
        if docker ps --format "{{.Names}}" | grep -q "^${container}$"; then
            STATUS=$(docker ps --format "{{.Names}}\t{{.Status}}" | grep "^${container}" | cut -f2)
            if [[ $STATUS == *"Up"* ]]; then
                log_success "Container $container está rodando"
            else
                log_fail "Container $container não está saudável: $STATUS"
            fi
        else
            log_warning "Container $container não encontrado (pode não estar instalado)"
        fi
    done
}

# Verificar conectividade de rede
check_network_connectivity() {
    log_header "4. VERIFICANDO CONECTIVIDADE DE REDE"
    
    # N8N Network
    check_total
    if docker network ls | grep -q "n8n-network\|n8n-production_n8n-network"; then
        log_success "Rede N8N existe"
    else
        log_warning "Rede N8N não encontrada"
    fi
    
    # Evolution Network
    check_total
    if docker network ls | grep -q "evolution-network"; then
        log_success "Rede Evolution existe"
    else
        log_warning "Rede Evolution não encontrada"
    fi
}

# Verificar bancos de dados
check_databases() {
    log_header "5. VERIFICANDO BANCOS DE DADOS"
    
    # PostgreSQL N8N
    check_total
    if docker ps --format "{{.Names}}" | grep -q "postgres"; then
        if docker-compose exec -T postgres pg_isready -U n8n_user -d n8n &> /dev/null; then
            log_success "PostgreSQL N8N está respondendo"
        else
            log_fail "PostgreSQL N8N não está respondendo"
        fi
    else
        log_warning "PostgreSQL N8N não encontrado"
    fi
    
    # Redis N8N
    check_total
    if docker ps --format "{{.Names}}" | grep -q "redis"; then
        if docker-compose exec -T redis redis-cli ping &> /dev/null; then
            log_success "Redis N8N está respondendo"
        else
            log_fail "Redis N8N não está respondendo"
        fi
    else
        log_warning "Redis N8N não encontrado"
    fi
    
    # PostgreSQL Evolution
    check_total
    if docker ps --format "{{.Names}}" | grep -q "evolution-postgres"; then
        if docker-compose exec -T postgres-evolution pg_isready -U evolution_user -d evolution &> /dev/null; then
            log_success "PostgreSQL Evolution está respondendo"
        else
            log_fail "PostgreSQL Evolution não está respondendo"
        fi
    else
        log_warning "PostgreSQL Evolution não encontrado"
    fi
    
    # Redis Evolution
    check_total
    if docker ps --format "{{.Names}}" | grep -q "evolution-redis"; then
        if docker-compose exec -T redis-evolution redis-cli ping &> /dev/null; then
            log_success "Redis Evolution está respondendo"
        else
            log_fail "Redis Evolution não está respondendo"
        fi
    else
        log_warning "Redis Evolution não encontrado"
    fi
}

# Verificar aplicações web
check_web_applications() {
    log_header "6. VERIFICANDO APLICAÇÕES WEB"
    
    # N8N Web Interface
    check_total
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:5678 | grep -q "200\|401"; then
        log_success "N8N Web Interface está respondendo"
    else
        log_fail "N8N Web Interface não está respondendo"
    fi
    
    # Evolution API
    check_total
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:8001 | grep -q "200\|401"; then
        log_success "Evolution API está respondendo"
    else
        log_warning "Evolution API não está respondendo (pode não estar instalada)"
    fi
}

# Verificar logs por erros
check_logs_for_errors() {
    log_header "7. VERIFICANDO LOGS POR ERROS"
    
    # N8N Logs
    check_total
    if docker ps --format "{{.Names}}" | grep -q "n8n"; then
        ERROR_COUNT=$(docker-compose logs n8n 2>/dev/null | grep -i "error\|fail\|exception" | wc -l)
        if [ "$ERROR_COUNT" -eq 0 ]; then
            log_success "N8N: Nenhum erro encontrado nos logs"
        else
            log_warning "N8N: $ERROR_COUNT erros encontrados nos logs"
        fi
    else
        log_warning "N8N não encontrado para verificação de logs"
    fi
    
    # Evolution Logs
    check_total
    if docker ps --format "{{.Names}}" | grep -q "evolution-api"; then
        ERROR_COUNT=$(docker-compose logs evolution-api 2>/dev/null | grep -i "error\|fail\|exception" | wc -l)
        if [ "$ERROR_COUNT" -eq 0 ]; then
            log_success "Evolution API: Nenhum erro encontrado nos logs"
        else
            log_warning "Evolution API: $ERROR_COUNT erros encontrados nos logs"
        fi
    else
        log_warning "Evolution API não encontrada para verificação de logs"
    fi
}

# Verificar performance
check_performance() {
    log_header "8. VERIFICANDO PERFORMANCE"
    
    # CPU Usage
    check_total
    CPU_USAGE=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)
    if (( $(echo "$CPU_USAGE < 80" | bc -l) )); then
        log_success "CPU Usage: ${CPU_USAGE}% (Normal)"
    else
        log_warning "CPU Usage: ${CPU_USAGE}% (Alto)"
    fi
    
    # Memory Usage
    check_total
    MEMORY_USAGE=$(free | grep Mem | awk '{printf("%.1f"), $3/$2 * 100.0}')
    if (( $(echo "$MEMORY_USAGE < 80" | bc -l) )); then
        log_success "Memory Usage: ${MEMORY_USAGE}% (Normal)"
    else
        log_warning "Memory Usage: ${MEMORY_USAGE}% (Alto)"
    fi
}

# Relatório final
show_final_report() {
    echo ""
    echo -e "${CYAN}"
    echo "====================================================================="
    echo "                        RELATÓRIO FINAL"
    echo "====================================================================="
    echo -e "${NC}"
    
    echo -e "${BLUE}Total de Verificações:${NC} $TOTAL_CHECKS"
    echo -e "${GREEN}Verificações Passaram:${NC} $PASSED_CHECKS"
    echo -e "${RED}Verificações Falharam:${NC} $FAILED_CHECKS"
    
    PASS_PERCENTAGE=$((PASSED_CHECKS * 100 / TOTAL_CHECKS))
    
    echo ""
    if [ "$PASS_PERCENTAGE" -ge 90 ]; then
        echo -e "${GREEN}🎉 SISTEMA SAUDÁVEL ($PASS_PERCENTAGE% das verificações passaram)${NC}"
    elif [ "$PASS_PERCENTAGE" -ge 70 ]; then
        echo -e "${YELLOW}⚠️  SISTEMA COM AVISOS ($PASS_PERCENTAGE% das verificações passaram)${NC}"
    else
        echo -e "${RED}🚨 SISTEMA COM PROBLEMAS ($PASS_PERCENTAGE% das verificações passaram)${NC}"
    fi
    
    echo ""
    echo -e "${BLUE}Recomendações:${NC}"
    if [ "$FAILED_CHECKS" -gt 0 ]; then
        echo "• Verifique os itens que falharam acima"
        echo "• Execute: docker-compose logs para mais detalhes"
        echo "• Considere reiniciar os serviços: docker-compose restart"
    fi
    
    if [ "$PASS_PERCENTAGE" -ge 90 ]; then
        echo "• Sistema funcionando bem!"
        echo "• Continue monitorando regularmente"
    fi
    
    echo ""
    echo -e "${BLUE}Comandos úteis:${NC}"
    echo "• Status: docker-compose ps"
    echo "• Logs: docker-compose logs -f"
    echo "• Restart: docker-compose restart"
    echo "• Health Check: ./scripts/health-check-complete.sh"
}

# Função principal
main() {
    show_banner
    
    check_docker
    check_system_resources
    check_containers
    check_network_connectivity
    check_databases
    check_web_applications
    check_logs_for_errors
    check_performance
    
    show_final_report
}

# Executar se for script principal
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
