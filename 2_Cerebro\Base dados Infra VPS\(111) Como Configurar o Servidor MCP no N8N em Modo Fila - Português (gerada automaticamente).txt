﻿"Pessoal, olha só, você vai assistir uma"
aula do nosso curso NHN Experts Devops.
Eu vou configurar com vocês corretamente
o servidor MCP rodando no NHN em modo
"fila. Essa formação aqui, pessoal, tem"
"115 aulas, né? E eu ensino do começo ao"
fim a você instalar o NHN mais básico
até você incrementando ele com
"escalabilidade, observabilidade, você"
poder montar relatórios em cima do seu n
"de erro, configurar o centre e receber"
informações em tempo real sobre os erros
"do CNN. Backup, restore e migração,"
roteamento avançado com o
Congri URLs mais limpas pros seus
serviços. Como vendeu o NHN com
contratos ali para que você possa
utilizar como modelo para você começar a
fazer dinheiro com a ferramenta? Como
que você pode configurar o túnel do
Cloud Flir para oferecer o seu NHN mais
escalável? Também o Nate de al<PERSON>
"disponibilidade, configurar ali vários"
"managers para que caso um caia, o outro"
assuma e você vai ter ali o tão sonhado
NH NHA. Também tem uma aula falando
"sobre versionamento de workflows, que é"
"muito importante para vocês, salvando lá"
"no GitHub com commit, bonitinho. E"
"também pessoal, gestão de projetos"
tendente com Clickap. O último workflow
dessa aula vai analisar o seu log da Nen
em tempo real ou de tempos em tempos
conforme você configurar e criar uma
tarefa no clickup caso apareça ali algum
"erro. pessoal, ó, conteúdo 10 para"
"vocês. Então, essa aula que você vai"
"assistir agora, eu gravei hoje com a"
galera explicando ali como fazer o
servidor MCP e vai ficar disponível para
vocês aqui nesse curso. Você que não é
"aluno da Promov, aqui na descrição tem o"
link para você assinar a Promov e ter
acesso ao nosso curso completo de Docker
"Swarm e também a nossa formação Martec,"
tá? Esse conteúdo aqui tá disponível em
qualquer assinatura da Promov. Se você
"gostou, dá um like no vídeo, se inscreve"
"no canal e, ó, assiste com atenção,"
"porque é um aulão aqui, um masterclass"
para você de como configurar o seu NN
"para rodar com o MCP. Valeu, pessoal."
"Pessoal, nessa parte aqui vou falar com"
"vocês sobre o MCP. Então, o Enin lançou"
"agora o suporte total ao MCP, né? E por"
"total, entenda como total mesmo, né?"
Então você pode ter não só um cliente
"MCP, mas também um servidor MCP. E de lá"
para cá apareceu algumas situações e
agora chegamos num consenso. Então vou
gravar aqui esse vídeo e o próximo vídeo
ensinando vocês a configurar de maneira
correta o MCP. Eu não gosto muito de
"utilizar e alguns vão estranhar, né? Eu"
"falar que é correto, que nesse caso,"
"pessoal, essa é a única maneira mesmo"
que tem de funcionar. Então é bom também
"que a gente já põe ali os pingos nos,"
"tá? para poder entender, pessoal, o MCP"
ele vai utilizar um protocolo chamado
"SSE, que vai manter aqui, ó, ele vai"
manter uma conexão ativa com o seu
"servidor. Isso aí é um desafio pro NHN,"
porque ele não foi projetado para
trabalhar assim. A ideia do NHN é
"receber um web hook, morreu a conexão"
com o web hook e ele já joga pro worker.
O worker vai lá e termina o trabalho pra
"gente. Se você usa uma API, você recebe"
"no seu web hook, processa com worker,"
devolve pro web hook e ele responde.
Então sempre foi assim. Essa é a
arquitetura do NateN. Quando quando
"chegou essa questão do do MCP, que é"
"muito recente, quando eles trouxeram o"
"Node pra ferramenta, apareceu uma"
"questão aí, apareceu um efeito"
colateral. Inclusive na própria
"documentação da NTN, se você vir aqui"
"embaixo, eles acabaram de atualizar, né?"
Então eu tô gravando esse curso aqui
depois que eles atualizaram a a
"documentação, ele vem aqui e fala: ""Ó,"
"configurar, tem uma limitação que é"
configurar o MCP trigger com réplicas de
"web hook."" Então, até ontem tinha uma"
mensagem aqui que ele não era não era
"compatível com fila, né? Então é uma"
questão que tava disponibilizado. Por
"isso que é importante vocês virem aqui,"
"eu não sou dono da razão, tá? Tá,"
"pessoal, mas aqui eu tenho acesso a"
informações ali que vão ajudar vocês.
"Então eu falei, gente, olha, vamos"
esperar eles corrigirem. Então aqui
agora na documentação eles estão
informando que você pode sim rodar o MCP
"em fila, desde que você não use réplicas"
"no seu web hook, que é o que eu ensino"
"vocês fazerem no curso, né? Então, no"
"curso a gente não fala, vamos usar"
réplica em tudo? Vamos usar réplica em
"tudo, menos nos endereços do MCP."
Então aqui na própria documentação para
poder eliminar qualquer tipo de de
problema e nos últimos dias a gente a
"gente vem testando bastante isso,"
"diversos cenários, né? Chegou então esse"
"consenso aqui, essa maneira correta de"
vocês poderem utilizar o MCP com NHN. Ó
"lá. Então quer dizer, pessoal, que eu"
"consigo sim utilizar o NHN com fila, eu"
só não vou poder rodar réplicas de web
"hook pro MCP. Luiz, mas aí quebra minhas"
"pernas, cara, porque o meu o meu web"
hook tem na cinco instância e eu preciso
que tenha essas cinco instâncias para
"poder escalar o meu sistema. Perfeito,"
pessoal. Vou mostrar aqui para vocês um
pequeno truque de como que vocês podem
fazer uma adaptação no seu web hook para
quando vier uma requisição e
"barracp, ele já encaminha para um"
serviço específico do seu NN só para MCP
com uma réplica e qualquer outro web
hook chegar para você vai chegar lá nas
suas réplicas para poder você ter a sua
"escalabilidade. Então, é importante,"
"pessoal, eu comentar com vocês o"
seguinte. Uma parte do problema quem vai
ter que resolver é o software. Por isso
que eu falo para vocês que vocês têm que
"estudar a infra, porque a infra anda"
"junto. Nesse caso aqui, ó, é uma é"
dividido uma parte do problema a infra
"resolve, outra parte o software resolve."
Então é interessante porque essas duas
"coisas, infra e software, vão andar"
junta no opence. Se eu utilizasse algum
"outro serviço SAS, eu não ia ter que me"
preocupar com isso. Eles iam fazer lá
essa parte da infra e para mim seria
transparente. Mas como eu estou usando o
"opence, eu tenho que conhecer um pouco"
"da infra, minimamente conhecer um pouco"
da infra para poder resolver até esses
problemas de avanço de tecnologia.
"Pessoal, o opence cresce muito rápido,"
esse movimento de a cresce muito rápido
"e vocês, como gestores de infra,"
"gestores de agente, gestores de"
"automação, vocês, desculpa falar, são"
"obrigados a acompanhar isso daqui,"
"porque você vai querer usar o MCP, o seu"
"MCP dá erro, aí a infra consegue"
"resolver o problema para você, mas por"
"você pular o aprendizado da infra, você"
continua lá com o seu erro. Não tem como
"separar, pessoal. Infelizmente não tem"
como separar o opence da infra. Então eu
"vou ter que aprender o NHN, a Evolution,"
"o chatut, tudo. Vou ter que aprender"
"tudo, mas também vou ter que aprender"
infra. Você precisa ser um certificado
"em Docker? Não, pessoal, não precisa."
"Luía, eu preciso ser o melhor do mundo."
"Não, não precisa. Mas o minimamente a"
gente tem que entender por quê? Porque
"quando aparece uma questão dessa daqui,"
vocês vão ver na internet a galera
inventando os maiores
absurdos. Por não ler
"documentação, por não por utilizar um"
chá de EPT. Eu não sou contra o chá de
"EPT, mas esse aqui é o específico do"
específico do específico. O chá de EPT
não vai saber que especificamente no
NITN tem uma condição tal que vai ser
assim. Talvez pessoal não chegou nele
"esse conhecimento ainda, né? né? Então,"
"em algum momento, com certeza ele vai"
"saber. E e outra, vamos lembrar sempre,"
"pessoal, IA, ela precisa ser treinada."
"Para ela trabalhar no seu específico,"
ela precisa saber o que que é específico
para você. Ela precisa ter o seu
"treinamento, o seu conhecimento. Então,"
muito cuidado quando vocês vão no GPT e
pede para ele criar uma regrinha pro
"trafic, porque essa regrinha pro trafic"
não tá errada. acontece que ela no seu
"universo NHN, dentro das customizações e"
"especificidades que ele precisa, ela"
pode quebrar o seu NHN. Então eu acho
"que é importante, por isso que eu"
"esperei sair essa documentação aqui,"
porque até ontem essa página aqui tinha
"um quadradinho azul informando o NHN, o"
servidor MCP não é compatível com o modo
em fila. A gente já sabia que ele era
"compatível com o modo em fila, só"
precisou mesmo realizar ali os últimos
"testes. A comunidade testou,"
"embaixadores testaram, a galera do dev"
"testou, teve um bate-papo legal ali,"
"chegou à conclusão que sim, eu fui um"
"que defendi, falei: ""Cara, funciona"
"assim, ó, for assim, assim, assim,"
"funciona."" Uma galera tava comigo nessa"
daí também que descobriu até antes de
"mim que funcionava. né? Eu, como tive o"
"evento do Sup, eu fiquei dois dias"
offline ali. Esses dois dias muita coisa
"rolou, muito teste rolou até chegar hoje"
que chegamos então nesse consenso aqui.
"Então, quando você ouvir o o MCP não tá"
"pronto a produção, o MCP tá bugado, ah,"
"o setup, pessoal, geralmente é o cara"
que pulou a etapa da infra. E eu tenho
"objetivo, eu tenho uma missão de vida,"
que é fazer vocês não serem esses caras
"aí, que vocês pensem, pô, não é possível"
que o Nate Chin vai lançar um negócio
bugado. Não é possível que o N vai
lançar um negócio sabendo que o modo
fila é o modo padrão dele e o negócio
"não funciona. Então pessoal, vamos ter"
"um pouco de cuidado, tá? Tá assim, nessa"
"aula aqui, a ideia é mostrar para vocês,"
e esse aqui acho que vai ser o melhor
"exemplo que eu vou utilizar, que o NITN,"
"o chatwut, a Evolution, o que você"
utilizar anda junto com a infra o tempo
"inteiro, não tem como desconectar um do"
"outro, né? Uma um recurso para usar, eu"
tenho que fazer a mudança aqui. Sempre
"vai ser assim, não só escalabilidade,"
mas também
"liberação de de réplicas, né, de de"
"recursos. Olha só, pessoal, tá claro"
"como sol aqui, ó. Se você usa o modo"
"fila com uma única réplica de web hook,"
então o MCP Server vai funcionar como
esperado. Se você usa múltiplas
"réplicas, que é o que eu ensino no curso"
"para vocês, e é o que vocês realmente"
"têm que fazer para rodar o NHN, você"
precisa rotear todas as
requisições/MCP para uma um para uma
réplica dedicada para poder receber esse
cara. Então eu não posso ter duas
"réplicas pro MCP, mas eu posso ter"
quantas réplicas eu quiser pro restante
"do do webhook. E aqui ele fala, né, para"
"você pegar o seu ingresso, que no nosso"
"caso é o é o trafic, e configurar no"
"traffic, falar traffic, seguinte, cara,"
"quando chegar barra mcp aqui, você"
"encaminha para esse serviço aqui, tá,"
"pessoal? É isso. É, é só isso que a"
gente vai ter que fazer
"aqui. Aqui ele fala, né, ó, para vocês"
poderem então eh rotear nessa réplica. O
que eu quero explicar aqui para vocês é
"o porquê disso, porque eu acho que é"
"importante também, né? Eu eu tenho o meu"
objetivo de vida não é formar usuários
"de NITN, eu quero formar o gestor de N."
"E o gestor, o consultor, ele é um cara"
"que ele sabe das coisas. Ele não, ele"
"não pode responder, que não pode, né,"
"gente, mas não convém responder um não"
"sei, tá? Não sei, mas vou pesquisar no"
"mínimo isso, né? Então, olha só,"
"pessoal, eu vou ter aqui, então, o meu o"
meu
"MCP, né? Esse aqui vai ser o nosso"
servidor. Então eu vou ter uma única
"réplica dele. O seu cliente MCP, que"
"pode ser o cursor, pode ser um outro"
"outro workflow, pode ser algum outro"
"sistema, o cloud lá que vocês usam, seja"
"lá o que for, ele vai abrir uma conexão"
com esse cara aqui. E essa conexão que é
"do tipo SSE, ela é uma conexão de"
"longing, que ele chama, né? Ela é uma"
ela é uma conexão que ela vai ter uma
vida longa.
"O que foge do padrão do NITN, o padrão"
do NITN são conexões normais de de web
"hook, de olha, mandei para lá, recebi,"
fechei a conexão. O padrão da web é
"esse. A gente chama de stateless, né?"
"Ele chama para cá, recebeu, morreu. O"
"MCP não, a conexão fica aberta. Então,"
"quando você começar a rodar um workflow,"
ele já vai abrir uma conexão lá. Ele só
vai fechar essa conexão quando acabar o
"seu workflow, mesmo mesmo que ele não"
chame o
MCP. Então ele não precisa chegar a
chamar o MCP para abrir a conexão. Então
começou o primeiríssimo no seu começou a
"rodar, tem um um cliente MCP ali, ele já"
abriu a conexão com
"ele, vai acontecer o seu o seu workflow"
"e vai terminar. A questão é, o MCP ele"
tem um conceito de ponteiros. Então aqui
dentro o que que acontece? Quando eu
"chamar a primeira vez, o primeiro node"
"que chamar o MCP, vai ter uma URL só"
dele. E aí ele vai vir aqui e vai rodar
os nodes lá do seu lá da sua atu seu
servidor. Na próxima e a conexão tá
aberta. Na próxima vez que tiver que
"chamar o MCP, ele vai gerar uma outra"
URL. para esse cara que vai chamar
"outras tools. Então, uma mesma conexão"
vai gerar internamente várias URLs para
poder fazer as
requisições. Por que que eu não posso
rodar isso aqui em fila? Eu acredito que
"por enquanto, eu acho que o NTN vai"
desenvolver alguma coisa na arquitetura
dele para dar um suporte mais escalável
"para isso, mas mesmo se não der, tá OK,"
tá? O que que acontece? Por que eu não
"posso ter fila? Se eu criar aqui, ó, eu"
tenho o meu MCP aqui e eu tenho o MCP
"aqui, o A e o"
B. Já que toda vez que ele vai chamar
uma uma fazer uma chamada para um ele
cria uma nova URL e essa URL fica numa
"variável. Na programação, pessoal, a"
"variável ela fica na memória da máquina,"
"né? Então, se eu tiver duas réplicas, eu"
tenho dois contêiners.
"A hora que eu chamar a primeira vez, ele"
criou na memória desse contêiner aqui.
"Se eu chamar a segunda vez, ele vai"
rodar aqui. Por quê? Porque o o load
balancer ele é um round robing. Cada
requisição ele encaminha para um
"serviço. Já falamos sobre isso no curso,"
né? Para poder fazer o balanceamento de
carga. Então é certeza de que cada
requisição vai vir para um servidor
"diferente. Então, como ele criou esse"
"ponteiro aqui, ele criou esse ponteiro"
"aqui nessa máquina, na hora que ele"
"rodar aqui, ele vai querer ler essa"
"variável, vai falar: ""Ué, não tem essa"
"variável aqui? Não tem mesmo, porque ele"
ele criou na máquina anterior. E se eu
"tivesse uma máquina C aqui, na hora que"
"ele fosse rodar a requisição aqui, ele"
"ia falar assim: ""Opa, não tem"". Por quê?"
porque ele criou na B e assim vai. Esse
"que é o bug do MCP. Não é um bug, tá"
gente? É um comportamento que não pode
"ser chamado de erro, porque ele não é um"
erro. É que quando eu escalo ele cria na
variável e não tem como uma variável tá
presente em várias máquinas. Eu consigo
armazenar em várias máquinas algum tipo
"de cash. Enfim, tem coisa que consigo,"
mas variável ela é definida localmente
no computador. A variável que tá aqui no
meu Mac não é a que tá no seu aí. O meu
"tem o meu, o seu tem o seu. Então esse é"
o problema. É por isso que eu tenho que
rodar o servidor MCP com uma única
"réplica, porque toda vez que eu fizer"
uma requisição vai vir pro mesmo
servidor. Então a hora que ele for ler
"qual que é a variável, ele vai achar,"
"vai falar: ""Opa, tem a variável aqui""."
Por quê? que eu sempre tô indo pro mesmo
servidor. Então aquela máquina sempre
vai ter essa variável definida da
"memória. Só por isso, pessoal, é uma"
"questão de arquitetura do NITN, né? Ele"
"é desacoplado, então ele é feito para"
"funcionar assim. Porém, todavia,"
"contudo, todavia, esse sistema aqui do"
"SSE, do MCP, não é compatível com isso."
"Simples, depois de muito teste, a galera"
"chegou nessa questão, falou: ""Olha,"
"seguinte, se eu rodar então várias"
"réplicas, não dá bom, então eu vou rodar"
"uma única réplica."" E tecnicamente é"
muito fácil fazer isso. Olha como é que
é interessante você aprender a infra. A
infra resolve muitos problemas. Então eu
"tô aqui, ó, tá bem explicado para vocês"
o porquê das coisas. Eu não posso rodar
"o NHN MCP Server em fila, porque cada"
hora ele vai criar uma numa ele vai
"rodar numa máquina, porque é padrão do"
"Docker fazer isso, é padrão do Swarm"
fazer o Hobbing. E ele vai fazer o uso
uma variável local e não tem como
replicar essa variável local em todas as
"minhas máquinas. Uma variável local, ela"
"é local. Só por isso, pessoal, não é"
"bug, não é porque tá pronto em produção,"
"é a maneira como funciona, tá? Então é"
bom de colocar esse pingo no i porque eu
"vi muita especulação, vi muita"
"gambiarra. Eu não, pessoal, eu não sou"
contra fazer gambiarra. O problema é que
"eu que sei fazer eu fazer uma gambiarra,"
uma coisa. O problema é o cara que às
vezes não sabe fazer porque ele não fez
o curso e ele vai lá e faz gambiarra
ainda. Então tá mais a gambiarra tá mais
errada ainda. Esse é o problema. E
"outra, aquela velha história, arruma"
coisa e estraga outra. Eu vim aqui na
"comunidade, eu gravei um vídeo,"
"expliquei, gente, copia esse esse trecho"
"pro editor, esse trecho pro web Hook."
"Teve gente que inverteu, teve gente que"
"só copiou um, teve gente que modificou"
coisas que não era para modificar. É
"assim que funciona, pessoal. Teve um que"
"passou no GPT, o GPT falou outra coisa"
para ele. Ao invés dele colocar o que eu
"fiz, testei e instruir, ele colocou do"
"GPT e caiu tudo na dele, que é o caso do"
"GZIP, que a gente vai ver daqui a pouco"
sobre sobre isso daí. Então é
"importante, pessoal, que vocês tenham um"
"pouco mais de paciência e que,"
"principalmente, ó, estuda a infra. A"
infra vai resolver muitos problemas.
"Vocês são gestores, vocês não são"
usuários. É importante ter esse papo
"aqui, pessoal. Por que que eu não posso"
ter uma arvada do GESIP aqui? O GESIP o
que que ele faz? O
"servidor, vou ter uma conexão com o"
servidor. O servidor vai
"responder, certo? Ele vai responder. O"
"GZIP vai fazer o nome dele, né? vai"
comprimir essa resposta e vai responder
de volta pro cliente. O problema é que
"assim na web em geral tem que ser assim,"
o seu site WordPress ele é obrigado a
fazer isso daqui. Acho que a grande
maioria dos sistemas hoje trabalham com
esse tipo de de qualquer tipo de
"compressão, seja o GESIP, seja qualquer"
outro tipo que vocês venham utilizar aí
"o Cloud Flare tem a dele, o Google tem a"
"dele, o Chrome tem a dele. Então tem"
vários tipos de compressões aí. No caso
"do SSE, por ele ser uma conexão de vida"
"longa, ou seja, ele vai ficar sempre"
"conectado, não tem como eu comprimir"
ele. A hora que ele chamar a primeira
"vez e vier a resposta, a resposta tem"
que vir em texto plano. Ela não pode vir
compactada. Então é por isso que eu
tenho que deixar toda a minha aplicação
com o
"GESIP, com exceção das rotas. do MCP."
Então qualquer URL vai que tenha barra
MCP vai acontecer isso. Então a nossa
"arquitetura vai ficar o seguinte, eu vou"
ter aqui o
"trafic, você que usa o Cloud Flare"
"Tunel, vou mostrar também aqui para"
vocês como é que vocês vão fazer isso no
"Cloud Flare Tunel, tá? Então eu vou ter"
"aqui, ó, o"
"trafic, vai vir para mim uma ORL, então"
vai vir lá eh tal tal tal barra web
"hooks, não vem? Vocês já repararam que"
todos
os os seus
"endereços de web hook, eles eles têm um"
barra webhooks e também vai vir o tal
tal tal tal tal barra mcp.
Então eu vou ter dois tipos possíveis de
"URL que chegam no web hook, o barra"
webooks e o barrac.
Quando chegar um barra web hooks ou
"qualquer outra URL, o traffic vai falar"
"assim: ""Opa, existe uma divisão"
"aqui."" Essa divisão diz o seguinte: se"
"for barra webhooks, vai vir para esses"
vários serviços
aqui. Sei lá quantas réplicas que você
"tenha, tenha muitas, pessoal. Se for"
"barra MCP, vai vir só para essa única"
"réplica aqui. O mesmo NN, o mesmo banco"
"de dados, o mesmo RS, 100% igual. Só vai"
mudar uma regrinha no
"trafic. E esse cara aqui,"
"especificamente, ele não pode ter o"
GZIP. Ele não pode ter o GZIP. Esse
daqui tem que ter por obrigação. Tudo
hoje tem que ter o GZIP. Então esse tem
"que ter, esse não pode ter. Essa é a"
principal diferença. Então se você fizer
"uma regrinha errada, o seu NN para de"
"funcionar, o seu web hook para de"
"receber tudo, o seu editor nem abre no"
navegador. Então é só fazer com calma e
seguir o passo a passo que eu vou
mostrar para vocês. Então a nossa
arquitetura vai ficar assim. Se for
"barra webhooks, vem para cá normal, é o"
"que vocês fazem hoje. Se for barra mcp,"
vem para cá. E aí eu tenho o melhor dos
dois mundos. Eu tenho uma configuração
perfeita paraa web hook normal e uma
configuração perfeita para quando vier
"só pro MCP. Simples assim. Então,"
"recapitulando, pessoal, posso ter o MCP"
"em fila, só não posso rodar ele em"
réplica. Eu preciso de réplica para
"poder escalar o meu NHN. Então, eu vou"
continuar replicando as URLs que não são
"do MCP. Então, você vai ter um serviço"
web hook normal e um serviço web hook só
"pro MCP. Vai mudar isso daí, pessoal."
"Então, qualquer coisa que vocês verem"
"fora disso, não vou dizer para vocês"
fugirem que vocês tenha a liberdade de
fazer aí as modificações na nas
"estâncias de vocês, mas esse essa é a"
"regra, esse é o conceito, inclusive é o"
"que tá documentado lá no seu NHN, lá no"
site do NHN. Vocês viram lá eles falando
"sobre isso, né? Então fica atento,"
"pessoal, em relação a essas a essas"
"esses detalhes, né? Porque aqui eles"
"falam, né? Ó, se você rodar um web hook"
"com múltiplas réplicas, é só você rotear"
tudo que for MCP para uma uma um web
"hook de uma réplica dedicada, rodando"
"uma réplica só, né? Ó, no modo single,"
"né? Eu acho ruim single ou réplica,"
"porque se é réplica para ter mais de um,"
né? Mas é assim que o nome funciona
"mesmo, né? Ó, então ele fala: ""Cria uma"
réplica
"separada, rodando só uma instância, só"
"para receber as requisições MCP""."
"Simplão, pessoal. Vou mostrar para vocês"
aqui no próximo vídeo como é que
funciona e aí a gente põe um ponto final
"nisso. Daí, pessoal, na prática é bem"
simples fazer essa modificação aí no seu
NN para que você consiga ter um serviço
à parte do MCP. Então aqui no stack da
"aula, tá? Para download no stack agora"
do nosso stack do do NHN também esse
"quarto stack, esse quarto elemento aqui"
"que é o do MCP. Então, olha só, vou"
pegar aqui do web Hooks para vocês
"verem, tá? Vou começar explicando por"
"ele. Olha que legal, eu vou ter o meu"
"stack normal do web Hook, como vocês já"
viram ao longo dos últimos 4 anos aqui
"no promovo web. Eu vou ter aqui, ó, esse"
"roast web hook rule aqui, né, ó, onde eu"
vou pegar qualquer requisição que vier
"pra web hook, eu vou jogar aqui nesse"
nesse serviço que é o meu NHN Web Hook.
Isso aqui não vai mudar. Não vou mexer
uma linha aqui no meu stack do web hook.
"Que que eu vou fazer, pessoal? Eu vou"
"abrir para vocês aqui, então, o do"
"MCP, o stack do MCP, tá? Ó, ele é um"
"stack de web hook também. Então, deixa"
"eu arrumar aqui para vocês verem aqui,"
ó. Eu vou dar para vocês o steack
"arrumadinho, tá? Ele é um stack de web"
"hook. Coloquei aqui uma observação, tá?"
que eh o MCP tá disponível a partir da
versão
1.88. Você no futuro aí que tiver
"assistindo essa aula, então com certeza"
você já vai est com a a o MCP disponível
"para vocês, tá? Mas a partir da versão"
"1.88. Beleza? Lembrar, né, que eu vou"
"ter que manter a mesma versão no editor,"
"no web hook e também aqui no MCP, tá"
bom? Tem que manter a mesma versão em
"todos eles. O que muda aqui, pessoal? O"
principal ponto é aqui. Até pus o
"comentário, né? Ó, vamos ter apenas uma"
"instância do MCP. Então, vai chegar uma"
"requisição, o trafic vai falar assim:"
"""Opa, é um web hook normal, encaminha"
"para esse monte aqui, faz load balancer,"
"aquela magia toda que vocês gostam. Ah,"
"é um barra mcp, então encaminha para"
esse cara sozinho aqui. Eu só posso ter
"um, pelo menos por enquanto, né, gente?"
"Então, ó, o principal é isso daqui, né?"
É claro que vocês vão colocar as
"variáveis de ambiente de vocês aqui, né?"
Eu vou colar daqui a pouco para vocês
"verem. Mas, ó, uma réplica, o esse stack"
"do MCP, uma"
"réplica. Outra coisa que eu aconselho,"
dá um pouquinho mais de memória para
"ele. No nosso caso aqui, ó, eu dei uma"
CPU e 2 GB. Não existe o número mágico
aqui. Você vai ter que testar aí para
poder ver de acordo com o seu servidor
"MCP, quais são os workflows que você"
"montou, quantos quantas tools vocês vão"
colocar
por ali
"por workflow, né, por servidor que você"
criar. Eu aconselho e eu vi uma média
"dessa daí, pessoal, uma média, tá? 40"
"meg por tool, que é mais ou menos em"
média o que o que ele vai consumir.
"Vamos lembrar, e é bom lembrar que ele"
"não vai rodar todas as tools, é só uma."
"Então, cada vez que chamar um um MCP,"
ele vai usar 40 MB de memória em média.
"Então, não é muito, então dá para"
"escalar bastante. Eu pus 2 GB aqui, ó."
"Então, dá dá para chamar muito"
"concorrente aqui que ele vai aguentar,"
tá bom? Outro ponto que é importante
dizer é que eu fiz algumas modificações
"aqui no trafic, tá bom? Então, presta"
"atenção aqui, pessoal, porque não para"
que vocês entendam o que vocês estão
"fazendo. Primeira modificação, eu"
adicionei aqui um middleware. Que que é
o middleware? Ele acontece antes da
requisição chegar no destino dele.
"Então, chegou no trafic, o trafic vai"
rodar esse middleware e daí vai
"encaminhar pro NHN. Ou o contrário,"
quando sai do
"NHN, passa pelo trafic, o trafic roda"
esse middleware e aí sim ele envia pro
"navegador, que é esse nosso caso aqui,"
"né? Então, na hora que o NTN responder e"
"na hora que chegar a requisição nele, eu"
vou remover ao GESIP. Eu não vou ter
"nada aqui no meu content encoding, eu"
vou limpar esse campo. Então vai chegar
a informação bruta ali. Tem que ser
"assim especificamente pro MCP, porque"
"ele é um SSE, ele é um end point de"
"longa duração, uma conexão que vai ficar"
"um bom tempo aberta, então não pode ter"
nenhum tipo de compactação ali. Então é
"bom ficar claro também para vocês isso,"
"tá? O restante do seu NHN, ou seja, o"
"seu editor e seu web hook normal, não"
pode ter isso daqui. Se ele tiver isso
"aqui, ele para de funcionar porque ele é"
"um HTTP, ele não é um SSE. Vocês"
"entendem, pessoal? Então, o que é SSE?"
Eu vou tirar o GZIP. O que é o web hook
"normal, o que é o editor normal? O que é"
"um HTP normal, eu devo manter o GZIP, tá"
"bom? Então, olha só."
"Então, nesse serviço, eu vou ter essa"
"regrinha aqui, tá? Ó,"
especificamente nesse serviço aqui.
"Então, eu criei aqui, ó, HT trafic, HTP,"
Middlewares no
"GESIP. Esse serviço no GESIP, eu vou"
"aqui embaixo, na última linha, ó,"
adicionar o middleware dele ness nesse
"meu serviço, ó. Então eu vou adicionar,"
"vou falar, ó, é para colocar esse"
"middleware aqui no GZIP, nesse serviço"
"do Nat, não estou colocando no meu"
abrook normal e também não estou
"colocando no meu editor, tá gente? Vocês"
"só vão ter que mexer aqui o seguinte, o"
"domínio do seu web hook, então o mesmo"
endereço que você colocou no seu stack
"web hooks, o mesmo endereço você vai"
colocar aqui no seu stack do MCP. com a
diferença que eu pus uma regrinha a mais
"aqui, né? Eu vim aqui, coloquei mais uma"
pequena regrinha. Vou até pôr um
"parênteses a mais aqui, ó, para poder"
bater certinho a regra. Que que eu
"coloquei aqui, ó? Tem que ser o domínio"
web hooks e tem que começar com barra
"mcp. Olha só, pessoal, qual que é a"
regra do web hook? Qualquer coisa que
"tenha o domínio web Hooks, qualquer"
coisa que tenha domínio web Hooks vai
bater ali. Qual que é a regra do MCP?
Qualquer coisa do domínio Web Hooks mais
o barra MCP. E aqui vem uma informação
importante para vocês do nosso querido
"Prox Reverso, que é o trafic. O trafic"
tem um tem uma questão de
prioridade. Então a prioridade dele se
dá pelo tamanho da regra. Então essa
regra
aqui ela ela é ela tem uma prioridade
menor do que essa regra aqui. E o que
quer dizer isso aí? Quer dizer que a
requisição do MCP não vai chegar aqui
"porque ele vai rodar essa primeiro,"
"porque essa regra é maior. Então, chegou"
"uma requisição do MCP, ele roda primeiro"
essa regra. Essa requisição é de web
"hooks e tem barra mcp. Sim, já fica por"
ali mesmo. Chegou a requisição. Essa
"requisição é web hooks e é barra mcp,"
não é um webhook normal. Próxima regra.
"Próxima regra, qual que é? Vai ser a"
"menor, que é essa regra aqui, ó."
Então é literalmente pelo tamanho da
"regra, a prioridade dele. Então não vai"
"acontecer porque esse cara aqui,"
"pessoal, em teoria ele engloba tudo."
"Qualquer requisição para web hook,"
"pessoal, qualquer requisição para web"
hook. Concorda que o barra mcp também se
"encaixaria, né? Mas não é esse caso, tá?"
"Eu, como criei uma nova regra parecida,"
"como ela é maior, ele roda primeiro"
"essas regras maiores. Bateu, já fica por"
"ali, não bateu, aí ele desce paraa"
"próxima, tá gente? Então isso aqui o"
traffic fez de maneira maravilhosa para
evitar esse esse conflito de de
"requisições, tá"
"gente? De resto é normal. Eu criei aqui,"
"ó, esse nosso serviço vai chamar NHN"
MCPI.
"Então, esse mesmo nome do serviço tá"
"aqui embaixo, ó. Eu usei esse mesmo nome"
"nas regras, né, nos routers aqui do"
trafic e também especifiquei aqui o
"serviço, ó. Então, isso aqui isola"
totalmente esse serviço lá do nosso
serviço de web hook. Não existe nenhuma
"ligação entre os dois. Então, você vai"
"ter um contêiner de web hook, um ou mais"
contêiner de web hook e vai ter mais um
contêiner de MCP.
Então eles eles correspondem ao mesmo
"domínio, porém tudo que for barra MCP,"
assim como descreveu a documentação do
"NHN, eu vou encaminhar especificamente"
para esse cara aqui. Eu vou remover o
GZIP dele e vou rodar ele numa única
"réplica, pessoal. Literalmente só isso"
que eu faço aqui nesse esquema. Então o
que que eu vou fazer? Eu vou pegar o meu
"que tá em produção aqui, pessoal, que eu"
gravei a aula. Então vocês vão pegar as
"variáveis de ambiente, né, que vocês têm"
"rodando hoje. Aí eu vou copiar daqui, tá"
gente? Porque eu esse aqui tá em
"produção, que é o que eu gravei a aula."
"Aquele do stack ali, ele é só um"
"template, tá bom? Então eu vou copiar"
essas variáveis de ambiente aqui todas.
Vocês vão copiar todas as variáve de
ambiente do seu editor. Já ensinei
"vocês, né, gente? Sempre, sempre copiem"
as variáveis de ambiente de um único
"serviço, né? Então, esse aqui é do"
"mesmo, ó. Vim aqui, copiei as variáveis."
"Agora eu posso vir aqui, ó, e eu vou"
colar ela no meu
"environment. Não bonito, né?"
"Environment. Fiz isso. Pronto, já posso"
pegar esse cara aqui e rodar ele lá no
"meu no meu servidor. No caso, pessoal,"
"da aula aqui, eu uso a imagem Next."
Vocês sabem que a imagem Next é a versão
"beta. Então, como é um servidor aqui de"
"aula, beleza, eu vou dar a versão beta."
"Mas você aí, eu recomendo que vocês não"
"usem nem a latest e nem a Next, que"
vocês usem uma versão específica que
vocês vão homologar e vão garantir que
essa versão roda bem os seus workflows.
"Fez isso, pessoal, eu copio esse meu"
stack e eu vou lançar esse stack a mais.
"Pode ver, ó, que eu já tenho o editor,"
"eu já tenho o web hook, eu já tenho o"
"worker. Então, eu vou colocar esse"
"quarto stack, que vai ser uma versão do"
"web hook, correto? Só que adaptado pro"
"MCP, né? Então, o que que eu adaptei? Eu"
rodei numa única réplica e eu removi o
"GZIP. Fez isso, pessoal, quando você"
"apertar o deploy aqui, ó, ele"
simplesmente vai entrar e vai funcionar.
"Então, a partir de agora, toda a"
requisição que acontecer pro seu NHN e
"que vier pro MCP, vai vir para esse"
"único contêiner aqui. O seu web hook,"
"caso você tenha 10, 20, 100 web hooks,"
"continua normal, tá? Então, só para"
mostrar para vocês que é um ajuste
"simples, para quem gosta de"
"documentação, para quem gosta de"
"pesquisar, fuçar, realmente, pessoal, é"
um é um leve ajuste fazer isso daqui.
"Para você que tá começando, pode parecer"
"muito complicado, mas eu recomendo,"
"pessoal, que vocês estudem isso daqui,"
que vocês pesquisem mais sobre isso no
"Google, no chat EPT, na própria"
"documentação dos sistemas de novo, tá?"
"Tá, pessoal? o NHN, o Chatwood,"
"Evolution, qualquer sistema que vocês"
"forem utilizar, eles estão intimamente"
ligados à infra. Não tem como eu
"descartar, eu terceirizar. falei: ""Não,"
"cara, não vou ligar muito para infra,"
não. Vou vir aqui e vou rodar um
"comando."" Não é assim, pessoal, não é"
assim que funciona. Vão acontecer essas
situações e diversas outras que
"aconteceram nos últimos 3 anos, que uma"
boa infra ajuda você a ter sucesso na
"sua operação, não ter dor de cabeça com"
os clientes e conseguir fazer
"efetivamente as coisas funcionarem, tá"
"gente? Então é só isso, ó. É só lançar"
um serviço a mais. Vocês vão colocar
aqui a versão do NHN que vocês estão
rodando. Vocês vão colar aqui as
"variáveis de ambiente do seu editor, né?"
"E vocês vão colocar aqui embaixo, ó,"
qual que é o domínio do seu web hook. Só
isso. Isso aí vai lançar um novo
"serviço. Se você vem no portainer, você"
vai ver que se você vai ter agora quatro
"serviços do NITN: Netn Editor, Web Hook,"
"o Worker e MCP. E é isso, pessoal. tá"
ali prontinho ali para que vocês
possam eh poder usufruir corretamente do
MCP. E aí você vai ter não vai ter mais
aquela mensagem de que o servidor MCP
"não está disponível, você não está"
utilizando os seus abhooks comum que
você pode escalar perfeitamente e também
tá disponibilizando um recurso
"específico ali pro MCP. Só vai a dica,"
como ele não é um sistema escalável na
"horizontal, ele é na vertical. Então eu"
vou ter que vir aqui e para esse único
serviço que eu vou ter do MCP dar
memória e dar CPU para ele conforme
necessário. Vamos partir de uma CPU e 2
"GB. Luiz, tem uma CPU e 2 GB. Então um"
bom número para você partir a partir
"disso, tá gente? Agora, eh, com o tempo,"
"se necessário, dá um pouquinho mais de"
"CPU, dá um pouquinho mais de memória, se"
você vê que precisa. Lembrando que vai
ter tudo centralizado num único
"contêiner. Questões aqui, pessoal. Luiz,"
posso criar uma máquina específica pro
"MCP, colocar no meu Swarm e vir aqui no"
placements e jogar lá para essa máquina?
"Pode, você decide. Não tá errado fazer"
"isso daí. Tá bom, Luís, eu posso criar"
um servidor só de MCP? Eu acho que esse
essa seria a melhor
"opção, tá? Eu acho. Então eu não não é"
"errado, tá, gente? Não é errado, mas eu"
acho que pra sua estratégia fica melhor.
MCP.probwobweb.com. Ali eu vou ter um
NHN que só tem um workflow que é
servidor de MCP. Então para mim fica
mais fácil até para poder gerenciar os
"meus clientes. Todos os meus clientes,"
eu vou jogar ali para aquele cara ali.
Eu tenho um lugar só para poder
"versionar, gerenciar, fazer backup. Eu"
"acho que com o tempo, conforme vocês"
"forem utilizando mais o MCP, seria"
"interessante que vocês pensassem nisso,"
né? Ao invés de vocês colocarem no En
Chen do cliente o servidor MCP para
"poder usar ali, vocês trazerem para um"
"seu maquinazinha melhor, uma"
maquinazinha que vocês fazem um backup
"legal delas para falar: ""Pô, Luiz, ó,"
"centralizei aqui, cara, ficou"
"maravilhoso, tá bem prático mesmo aqui"
de utilizar tudo numa máquina só. São
"opções, pessoal. Você vai escolher o que"
for melhor pro seu negócio. Eu só quero
"que vocês perguntem. Perguntem no fórum,"
perguntem no grupo. Toda terça e quinta
temos o suporte ali ao vivo para que
"vocês possam entrar e perguntar também,"
porque assim a gente ajuda vocês a
"modelarem melhor a empresa de vocês,"
"modelarem melhor a infra de vocês, tá? E"
"fica seu critério, o servidor MCP é seu"
e os seus clientes acessam ou o servidor
"MCP é do cliente menos, fica lá na"
instalação dele e roda um contzinho a
"mais lá e tá tudo certo, pessoal. Não"
tem nada de errado aí. Só quero que
vocês pensem um pouquinho mais na hora
"de configurar isso daqui, porque é um"
"novo recurso, é novidade, tudo aqui é"
"muito novo, ainda não tem ninguém"
"especialista disso, não tem ninguém que"
"seja fera disso daqui, tá todo mundo no"
começo. Vocês viram na aula anterior que
a própria galera da NateN bateu um pouco
de cabeça ali em relação a essas
"questões do MCP. Então vamos com calma,"
"vamos utilizar em produção. Sim, tá"
"pronto pra produção o MCP, mas vai com"
"calma. Tudo na vida é assim, devagar e"
"sem pessoal, ninguém tá com pressa aqui,"
tá? Então acho que é legal
"você testa esse workflow, pega esse esse"
"stack, roda na sua infra, faz os testes."
"Lembrar, tá pessoal, acabou de virar"
"leite isso daqui, então é a"
primeiríssima versão que tem isso daqui.
Então é normal ter algum erro. normal
"ter algum bugzinho, mas mais mas usa, tá"
gente? Começa a utilizar para ficar mais
"prático para vocês, tá bom?"