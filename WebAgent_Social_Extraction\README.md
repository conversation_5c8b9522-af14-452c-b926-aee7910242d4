# 🚀 WebAgent Social Extraction Platform

**Enterprise-Grade AI Solution for Social Media Analytics & Viral Prediction**

[![Status](https://img.shields.io/badge/Status-PoC%20Phase-yellow)](https://github.com/webagent/social-extraction)
[![Version](https://img.shields.io/badge/Version-2.0-blue)](./docs/01_project_management/PRD_WEBAGENT_FINAL_V2_REFINED.md)
[![License](https://img.shields.io/badge/License-MIT-green)](./LICENSE)
[![Python](https://img.shields.io/badge/Python-3.11+-blue)](https://python.org)

---

## 📋 Overview

WebAgent Social Extraction Platform automatiza a extração, análise e predição viral de conteúdo de redes sociais através de uma **arquitetura simplificada e escalável** baseada em IA.

### 🎯 **Core Features**
- **🔍 Extração Automatizada:** YouTube, Instagram, Twitter/X
- **🤖 Análise Viral com IA:** Gemini-powered multimodal analysis
- **📊 Predição de Tendências:** ML-based viral score prediction
- **🛡️ Compliance by Design:** LGPD + platform ToS automation
- **⚡ Performance Enterprise:** <5s latency, 200 posts/min throughput

### 📊 **Project Status**
- **Current Phase:** PoC (Proof of Concept) - 2 weeks
- **Team Score:** 7.8/10 technical viability
- **Architecture:** 2-tier simplified (FastAPI + Celery)
- **Next Milestone:** MVP Alpha (12 weeks)

---

## 🏗️ Project Structure

```
WebAgent_Social_Extraction/
├── 📁 src/                          # Source code
│   ├── api/                         # FastAPI Gateway (Layer 1)
│   ├── workers/                     # Celery Workers (Layer 2)
│   ├── core/                        # Business logic
│   ├── integrations/                # External integrations
│   └── shared/                      # Shared utilities
├── 📁 tests/                        # Automated testing
├── 📁 docs/                         # Documentation
├── 📁 config/                       # Configuration files
├── 📁 scripts/                      # Automation scripts
├── 📁 infra/                        # Infrastructure as Code
├── 📁 poc/                          # Proof of Concept
├── 📁 frontend/                     # React frontend
├── 📁 data/                         # Data & schemas
├── 📁 .devops/                      # CI/CD & DevOps
└── 📁 memory/                       # Context & memory
```

---

## 🚀 Quick Start

### 📋 **Prerequisites**
- Python 3.11+
- Docker & Docker Compose
- Redis
- Supabase account
- Gemini API key

### ⚡ **Development Setup**
```bash
# Clone repository
git clone <repository-url>
cd WebAgent_Social_Extraction

# Setup virtual environment
python -m venv venv
source venv/bin/activate  # Linux/Mac
# or
venv\Scripts\activate     # Windows

# Install dependencies
pip install -r requirements.txt

# Setup environment variables
cp .env.example .env
# Edit .env with your API keys

# Start development environment
docker-compose up -d

# Run PoC
cd poc/youtube_extraction
python youtube_poc.py
```

---

## 📚 Documentation

### 📖 **Core Documentation**
- **[📋 PRD V2.0 Refined](./docs/01_project_management/PRD_WEBAGENT_FINAL_V2_REFINED.md)** - Product Requirements
- **[🔬 Technical Analysis](./docs/01_project_management/)** - Team technical reviews
- **[🏗️ Architecture Guide](./docs/02_technical_specs/)** - System architecture
- **[🚀 Deployment Guide](./docs/04_deployment_guides/)** - Infrastructure setup

### 🎯 **Quick Links**
- **[PoC Results](./poc/poc_results/)** - Proof of concept findings
- **[API Documentation](./docs/03_api_documentation/)** - REST API specs
- **[Knowledge Base](./docs/05_knowledge_base/)** - Technical knowledge
- **[Research Archive](./docs/06_research_archive/)** - Research & analysis

---

## 🔧 Technology Stack

### 🏗️ **Core Architecture (2-Tier)**
- **Layer 1:** FastAPI (API Gateway)
- **Layer 2:** Celery Workers (Processing)

### 🛠️ **Technologies**
- **Backend:** Python, FastAPI, Celery
- **Database:** Supabase (PostgreSQL)
- **Cache:** Redis
- **AI/ML:** Gemini 2.5 Pro, OpenCV
- **Frontend:** React, TypeScript
- **Infrastructure:** Docker, Kubernetes
- **Monitoring:** Prometheus, Grafana

---

## 📊 Performance Targets

### 🎯 **PoC Targets (2 weeks)**
- ✅ YouTube extraction functional
- ✅ Gemini analysis basic
- ✅ Latency <10s
- ✅ Accuracy >80%

### 🚀 **MVP Targets (12 weeks)**
- ✅ Accuracy >85%
- ✅ Latency <8s
- ✅ Uptime >99%
- ✅ 25 active users

---

## 👥 Team & Contributions

### 🔧 **Core Team**
- **Tech Lead/Architect** (1)
- **Senior Developers** (2)
- **AI/ML Engineer** (1)
- **DevOps Engineer** (1)
- **QA Engineer** (1)

### 🤝 **Contributing**
See [CONTRIBUTING.md](./CONTRIBUTING.md) for development guidelines.

---

## 📈 Roadmap

### 🎯 **Phase 0: PoC** (2 weeks) - **CURRENT**
- YouTube extraction + Gemini analysis
- Performance benchmarks
- Cost estimates

### 🚀 **Phase 1: MVP Alpha** (12 weeks)
- YouTube-only platform
- Basic viral analysis
- Simple dashboard

### 📈 **Phase 2: Beta Private** (16 weeks)
- Add Instagram support
- Multimodal analysis
- API access

### 🌟 **Phase 3: Beta Public** (20 weeks)
- Full platform (YouTube + Instagram + Twitter)
- Complete API
- Advanced analytics

---

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](./LICENSE) file for details.

---

## 📞 Support

- **Documentation:** [./docs/](./docs/)
- **Issues:** [GitHub Issues](https://github.com/webagent/social-extraction/issues)
- **Discussions:** [GitHub Discussions](https://github.com/webagent/social-extraction/discussions)

---

**🎯 Status:** ✅ **Ready for PoC Development**  
**📅 Last Updated:** 2025-01-24  
**🔄 Next Review:** Weekly team sync
