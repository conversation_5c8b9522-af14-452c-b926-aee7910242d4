# ⚡ EDGE FUNCTIONS ESPECIALIZADAS - SISTEMA DE EXTRAÇÃO VIRAL

**Data:** 2025-01-24  
**Versão:** v1.0 - Especificação Completa de Edge Functions  
**Autor:** Augment Code Orchestrator V5.0  
**Projeto:** WebAgent (Supabase ID: nnpxxdyhamnsxqljumun)  
**Escopo:** Edge Functions para processamento, análise e integração  

---

## 🎯 EXECUTIVE SUMMARY

Este documento especifica **Edge Functions especializadas** para o sistema de extração viral WebAgent. As functions são implementadas em **TypeScript** com **Deno runtime**, oferecendo processamento serverless de alta performance para análise de conteúdo viral, geração de relatórios e integração com APIs externas.

### PRINCIPAIS EDGE FUNCTIONS:

**1. VIRAL-CONTENT-PROCESSOR:**
- **Processamento Inteligente** - Análise de sentimento e scoring viral
- **Download de Mídia** - Armazenamento automático no Supabase Storage
- **Detecção de Duplicatas** - Prevenção de conteúdo repetido
- **Enriquecimento de Dados** - Metadados e geolocalização

**2. TREND-ANALYZER:**
- **Análise de Tendências** - Hashtags, keywords e padrões emergentes
- **Machine Learning** - Predições baseadas em dados históricos
- **Análise de Sentimento** - Distribuição emocional do conteúdo
- **Relatórios Automáticos** - Insights acionáveis em tempo real

**3. REPORT-GENERATOR:**
- **Relatórios Executivos** - PDFs e Excel com métricas avançadas
- **Dashboards Dinâmicos** - Visualizações interativas
- **Recomendações IA** - Sugestões baseadas em performance
- **Agendamento Automático** - Relatórios periódicos

**4. SOCIAL-MEDIA-CONNECTOR:**
- **APIs Unificadas** - Integração com múltiplas plataformas
- **Rate Limiting** - Gestão inteligente de quotas
- **Webhook Handler** - Processamento de eventos em tempo real
- **Autenticação Segura** - OAuth e tokens gerenciados

---

## 🔧 VIRAL-CONTENT-PROCESSOR

### ESPECIFICAÇÃO TÉCNICA:

```typescript
// supabase/functions/viral-content-processor/index.ts

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { HfInference } from 'https://esm.sh/@huggingface/inference@2.3.2'

// =====================================================
// INTERFACES E TIPOS
// =====================================================

interface ContentProcessingRequest {
  extraction_id: string
  content_batch: ViralContentItem[]
  processing_options: ProcessingOptions
  user_id: string
  project_id: string
}

interface ViralContentItem {
  external_id: string
  platform: 'twitter' | 'youtube' | 'instagram' | 'tiktok' | 'linkedin'
  content_type: 'tweet' | 'video' | 'post' | 'story' | 'reel' | 'short'
  url: string
  title?: string
  description?: string
  content_text?: string
  author_username: string
  author_display_name?: string
  author_followers_count?: number
  author_verified?: boolean
  hashtags: string[]
  mentions: string[]
  media_urls: string[]
  media_types: string[]
  engagement_metrics: EngagementMetrics
  published_at: string
  location_data?: LocationData
}

interface EngagementMetrics {
  likes_count: number
  shares_count: number
  comments_count: number
  views_count?: number
  saves_count?: number
  engagement_rate?: number
}

interface ProcessingOptions {
  sentiment_analysis: boolean
  viral_score_calculation: boolean
  media_download: boolean
  duplicate_detection: boolean
  content_enrichment: boolean
  language_detection: boolean
  spam_filtering: boolean
}

interface LocationData {
  country?: string
  region?: string
  city?: string
  coordinates?: {
    lat: number
    lng: number
  }
}

// =====================================================
// CONFIGURAÇÕES E CONSTANTES
// =====================================================

const SUPABASE_URL = Deno.env.get('SUPABASE_URL')!
const SUPABASE_SERVICE_KEY = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
const HUGGINGFACE_TOKEN = Deno.env.get('HUGGINGFACE_ACCESS_TOKEN')!
const OPENAI_API_KEY = Deno.env.get('OPENAI_API_KEY')!

const MAX_BATCH_SIZE = 100
const MAX_PROCESSING_TIME = 300000 // 5 minutos
const VIRAL_SCORE_THRESHOLD = 5.0
const DUPLICATE_SIMILARITY_THRESHOLD = 0.85

// =====================================================
// FUNÇÃO PRINCIPAL
// =====================================================

serve(async (req) => {
  const startTime = Date.now()
  
  try {
    // Validar método HTTP
    if (req.method !== 'POST') {
      return new Response('Method not allowed', { status: 405 })
    }

    // Parse do request
    const requestData: ContentProcessingRequest = await req.json()
    
    // Validações básicas
    if (!requestData.extraction_id || !requestData.content_batch) {
      return new Response('Missing required fields', { status: 400 })
    }

    if (requestData.content_batch.length > MAX_BATCH_SIZE) {
      return new Response(`Batch size exceeds limit of ${MAX_BATCH_SIZE}`, { status: 400 })
    }

    // Inicializar clientes
    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY)
    const hf = new HfInference(HUGGINGFACE_TOKEN)

    // Log início do processamento
    console.log(`🚀 Processing batch of ${requestData.content_batch.length} items for extraction ${requestData.extraction_id}`)

    // Atualizar status da extração
    await supabase
      .from('extractions')
      .update({ 
        status: 'processing',
        total_found: requestData.content_batch.length
      })
      .eq('id', requestData.extraction_id)

    // Processar conteúdo em paralelo
    const processedContent = await processContentBatch(
      requestData.content_batch,
      requestData.processing_options,
      supabase,
      hf
    )

    // Filtrar conteúdo válido
    const validContent = processedContent.filter(item => item !== null)

    // Inserir no banco de dados
    const { data: insertedContent, error: insertError } = await supabase
      .from('viral_content')
      .insert(validContent)
      .select()

    if (insertError) {
      console.error('❌ Database insertion error:', insertError)
      throw new Error(`Database insertion failed: ${insertError.message}`)
    }

    // Processar métricas de engajamento
    if (insertedContent && insertedContent.length > 0) {
      await processEngagementMetrics(insertedContent, supabase)
    }

    // Atualizar status final da extração
    const processingTime = Date.now() - startTime
    await supabase
      .from('extractions')
      .update({ 
        status: 'completed',
        total_processed: validContent.length,
        execution_time_ms: processingTime,
        completed_at: new Date().toISOString()
      })
      .eq('id', requestData.extraction_id)

    // Log de métricas de performance
    await logPerformanceMetrics(processingTime, validContent.length, supabase)

    // Resposta de sucesso
    return new Response(JSON.stringify({
      success: true,
      extraction_id: requestData.extraction_id,
      processed_count: validContent.length,
      skipped_count: requestData.content_batch.length - validContent.length,
      processing_time_ms: processingTime,
      viral_content_count: validContent.filter(item => item.viral_score > VIRAL_SCORE_THRESHOLD).length,
      timestamp: new Date().toISOString()
    }), {
      headers: { 'Content-Type': 'application/json' },
      status: 200
    })

  } catch (error) {
    console.error('❌ Processing error:', error)
    
    // Atualizar status de erro na extração
    if (requestData?.extraction_id) {
      const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY)
      await supabase
        .from('extractions')
        .update({ 
          status: 'failed',
          error_message: error.message,
          completed_at: new Date().toISOString()
        })
        .eq('id', requestData.extraction_id)
    }

    return new Response(JSON.stringify({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    })
  }
})

// =====================================================
// FUNÇÕES DE PROCESSAMENTO
// =====================================================

async function processContentBatch(
  contentBatch: ViralContentItem[],
  options: ProcessingOptions,
  supabase: any,
  hf: any
): Promise<any[]> {
  const processedItems = []

  for (const item of contentBatch) {
    try {
      let processedItem = { ...item }

      // 1. Detecção de duplicatas
      if (options.duplicate_detection) {
        const isDuplicate = await checkForDuplicates(item, supabase)
        if (isDuplicate) {
          console.log(`⚠️ Skipping duplicate content: ${item.external_id}`)
          continue
        }
      }

      // 2. Filtro de spam
      if (options.spam_filtering) {
        const isSpam = await detectSpam(item)
        if (isSpam) {
          console.log(`🚫 Skipping spam content: ${item.external_id}`)
          continue
        }
      }

      // 3. Detecção de idioma
      if (options.language_detection) {
        processedItem.language_code = await detectLanguage(item.content_text || item.title || '', hf)
      }

      // 4. Análise de sentimento
      if (options.sentiment_analysis && processedItem.content_text) {
        const sentiment = await analyzeSentiment(processedItem.content_text, hf)
        processedItem.sentiment_score = sentiment.score
        processedItem.sentiment_label = sentiment.label
      }

      // 5. Enriquecimento de conteúdo
      if (options.content_enrichment) {
        processedItem = await enrichContent(processedItem, hf)
      }

      // 6. Download de mídia
      if (options.media_download && item.media_urls.length > 0) {
        const mediaFiles = await downloadAndStoreMedia(item.media_urls, item.external_id, supabase)
        processedItem.local_media_files = mediaFiles
      }

      // 7. Cálculo do viral score (sempre executado)
      processedItem.viral_score = calculateViralScore(processedItem)

      // 8. Preparar para inserção no banco
      const dbRecord = prepareForDatabase(processedItem)
      processedItems.push(dbRecord)

    } catch (error) {
      console.error(`❌ Error processing item ${item.external_id}:`, error)
      // Continuar processamento dos outros itens
    }
  }

  return processedItems
}

// =====================================================
// ANÁLISE DE SENTIMENTO
// =====================================================

async function analyzeSentiment(text: string, hf: any): Promise<{score: number, label: string}> {
  try {
    // Limitar tamanho do texto para análise
    const truncatedText = text.substring(0, 512)
    
    const result = await hf.textClassification({
      model: 'cardiffnlp/twitter-roberta-base-sentiment-latest',
      inputs: truncatedText
    })

    if (result && result.length > 0) {
      const topResult = result[0]
      
      // Mapear labels para scores numéricos
      let score = 0
      switch (topResult.label.toLowerCase()) {
        case 'positive':
        case 'pos':
          score = topResult.score
          break
        case 'negative':
        case 'neg':
          score = -topResult.score
          break
        case 'neutral':
        default:
          score = 0
          break
      }

      return {
        score: Math.round(score * 100) / 100, // Arredondar para 2 casas decimais
        label: topResult.label.toLowerCase()
      }
    }

    return { score: 0, label: 'neutral' }

  } catch (error) {
    console.error('❌ Sentiment analysis error:', error)
    return { score: 0, label: 'neutral' }
  }
}

// =====================================================
// DETECÇÃO DE IDIOMA
// =====================================================

async function detectLanguage(text: string, hf: any): Promise<string> {
  try {
    if (!text || text.length < 10) return 'unknown'

    const result = await hf.textClassification({
      model: 'facebook/fasttext-language-identification',
      inputs: text.substring(0, 200)
    })

    if (result && result.length > 0) {
      return result[0].label.substring(0, 2) // Retornar código ISO de 2 letras
    }

    return 'unknown'

  } catch (error) {
    console.error('❌ Language detection error:', error)
    return 'unknown'
  }
}

// =====================================================
// CÁLCULO DE VIRAL SCORE
// =====================================================

function calculateViralScore(item: any): number {
  try {
    const metrics = item.engagement_metrics
    
    // Fatores base de engajamento
    const likes = metrics.likes_count || 0
    const shares = metrics.shares_count || 0
    const comments = metrics.comments_count || 0
    const views = metrics.views_count || 0
    const saves = metrics.saves_count || 0

    // Pesos por tipo de engajamento
    const engagementScore = (
      likes * 1.0 +
      shares * 3.0 +  // Shares têm peso maior
      comments * 2.0 +
      views * 0.1 +
      saves * 1.5
    )

    // Fator de seguidores (normalizado)
    const followerFactor = item.author_followers_count > 0 
      ? Math.min(Math.log10(item.author_followers_count + 1) / 2, 3.0)
      : 1.0

    // Fator temporal (conteúdo mais recente tem peso maior)
    const publishedAt = new Date(item.published_at)
    const hoursOld = (Date.now() - publishedAt.getTime()) / (1000 * 60 * 60)
    const timeFactor = Math.max(1 - (hoursOld / (24 * 7)), 0.1) // Decai ao longo de 1 semana

    // Fator de verificação
    const verificationFactor = item.author_verified ? 1.2 : 1.0

    // Fator de sentimento (conteúdo positivo tem leve boost)
    const sentimentFactor = item.sentiment_score > 0.5 ? 1.1 : 
                           item.sentiment_score < -0.5 ? 0.9 : 1.0

    // Cálculo final do viral score
    const viralScore = (engagementScore * followerFactor * timeFactor * verificationFactor * sentimentFactor) / 1000

    return Math.min(Math.round(viralScore * 100) / 100, 99.99) // Máximo 99.99

  } catch (error) {
    console.error('❌ Viral score calculation error:', error)
    return 0
  }
}

// =====================================================
// DETECÇÃO DE DUPLICATAS
// =====================================================

async function checkForDuplicates(item: ViralContentItem, supabase: any): Promise<boolean> {
  try {
    // Verificar por external_id e platform (duplicata exata)
    const { data: exactDuplicate } = await supabase
      .from('viral_content')
      .select('id')
      .eq('external_id', item.external_id)
      .eq('platform', item.platform)
      .limit(1)

    if (exactDuplicate && exactDuplicate.length > 0) {
      return true
    }

    // Verificar similaridade de conteúdo (duplicata semântica)
    if (item.content_text && item.content_text.length > 50) {
      const { data: similarContent } = await supabase
        .from('viral_content')
        .select('id, content_text')
        .eq('platform', item.platform)
        .gte('published_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()) // Últimas 24h
        .limit(100)

      if (similarContent && similarContent.length > 0) {
        for (const existing of similarContent) {
          if (existing.content_text) {
            const similarity = calculateTextSimilarity(item.content_text, existing.content_text)
            if (similarity > DUPLICATE_SIMILARITY_THRESHOLD) {
              return true
            }
          }
        }
      }
    }

    return false

  } catch (error) {
    console.error('❌ Duplicate detection error:', error)
    return false // Em caso de erro, não bloquear o conteúdo
  }
}

// =====================================================
// CÁLCULO DE SIMILARIDADE DE TEXTO
// =====================================================

function calculateTextSimilarity(text1: string, text2: string): number {
  try {
    // Normalizar textos
    const normalize = (text: string) => text.toLowerCase().replace(/[^\w\s]/g, '').trim()
    const norm1 = normalize(text1)
    const norm2 = normalize(text2)

    // Verificar se são idênticos
    if (norm1 === norm2) return 1.0

    // Calcular similaridade usando Jaccard
    const words1 = new Set(norm1.split(/\s+/))
    const words2 = new Set(norm2.split(/\s+/))
    
    const intersection = new Set([...words1].filter(x => words2.has(x)))
    const union = new Set([...words1, ...words2])
    
    return intersection.size / union.size

  } catch (error) {
    console.error('❌ Text similarity calculation error:', error)
    return 0
  }
}

// =====================================================
// DETECÇÃO DE SPAM
// =====================================================

async function detectSpam(item: ViralContentItem): Promise<boolean> {
  try {
    const text = (item.content_text || '') + ' ' + (item.title || '')
    
    // Regras básicas de spam
    const spamIndicators = [
      /(.)\1{4,}/g, // Caracteres repetidos (aaaaa)
      /[🎁🎉💰💸🔥⚡]{3,}/g, // Muitos emojis promocionais
      /(buy now|click here|limited time|act fast)/gi, // Frases promocionais
      /(.{1,10})\1{3,}/g, // Padrões repetitivos
      /[A-Z]{10,}/g, // Muito texto em maiúscula
    ]

    let spamScore = 0
    
    for (const pattern of spamIndicators) {
      if (pattern.test(text)) {
        spamScore += 1
      }
    }

    // Verificar ratio de hashtags
    const hashtagRatio = item.hashtags.length / Math.max(text.length / 100, 1)
    if (hashtagRatio > 5) spamScore += 1

    // Verificar URLs suspeitas
    const urlPattern = /https?:\/\/[^\s]+/g
    const urls = text.match(urlPattern) || []
    if (urls.length > 3) spamScore += 1

    return spamScore >= 3

  } catch (error) {
    console.error('❌ Spam detection error:', error)
    return false
  }
}

// =====================================================
// ENRIQUECIMENTO DE CONTEÚDO
// =====================================================

async function enrichContent(item: any, hf: any): Promise<any> {
  try {
    const enrichedItem = { ...item }

    // Extrair entidades nomeadas
    if (item.content_text) {
      const entities = await extractNamedEntities(item.content_text, hf)
      enrichedItem.named_entities = entities
    }

    // Classificar categoria do conteúdo
    const category = await classifyContentCategory(item, hf)
    enrichedItem.content_category = category

    // Extrair tópicos principais
    const topics = await extractTopics(item.content_text || item.title || '', hf)
    enrichedItem.main_topics = topics

    return enrichedItem

  } catch (error) {
    console.error('❌ Content enrichment error:', error)
    return item
  }
}

async function extractNamedEntities(text: string, hf: any): Promise<string[]> {
  try {
    const result = await hf.tokenClassification({
      model: 'dbmdz/bert-large-cased-finetuned-conll03-english',
      inputs: text.substring(0, 512)
    })

    const entities = result
      .filter((entity: any) => entity.score > 0.8)
      .map((entity: any) => entity.word)
      .filter((word: string) => word.length > 2)

    return [...new Set(entities)] // Remover duplicatas

  } catch (error) {
    console.error('❌ Named entity extraction error:', error)
    return []
  }
}

async function classifyContentCategory(item: any, hf: any): Promise<string> {
  try {
    const text = (item.content_text || '') + ' ' + (item.title || '')

    if (!text.trim()) return 'unknown'

    const result = await hf.textClassification({
      model: 'facebook/bart-large-mnli',
      inputs: text.substring(0, 512),
      parameters: {
        candidate_labels: [
          'entertainment', 'news', 'sports', 'technology', 'business',
          'politics', 'lifestyle', 'education', 'health', 'travel',
          'food', 'fashion', 'music', 'gaming', 'science'
        ]
      }
    })

    if (result && result.length > 0) {
      return result[0].label
    }

    return 'unknown'

  } catch (error) {
    console.error('❌ Content classification error:', error)
    return 'unknown'
  }
}

async function extractTopics(text: string, hf: any): Promise<string[]> {
  try {
    if (!text || text.length < 20) return []

    // Usar modelo de sumarização para extrair tópicos principais
    const result = await hf.summarization({
      model: 'facebook/bart-large-cnn',
      inputs: text.substring(0, 1024),
      parameters: {
        max_length: 50,
        min_length: 10
      }
    })

    if (result && result.summary_text) {
      // Extrair palavras-chave do resumo
      const keywords = result.summary_text
        .toLowerCase()
        .split(/\W+/)
        .filter((word: string) => word.length > 3)
        .slice(0, 5)

      return keywords
    }

    return []

  } catch (error) {
    console.error('❌ Topic extraction error:', error)
    return []
  }
}

// =====================================================
// DOWNLOAD E ARMAZENAMENTO DE MÍDIA
// =====================================================

async function downloadAndStoreMedia(
  mediaUrls: string[],
  contentId: string,
  supabase: any
): Promise<any[]> {
  const storedFiles = []

  for (let i = 0; i < Math.min(mediaUrls.length, 5); i++) { // Máximo 5 arquivos por conteúdo
    try {
      const url = mediaUrls[i]
      const fileExtension = getFileExtension(url)
      const fileName = `${contentId}_${i}.${fileExtension}`
      const bucketName = getBucketForFileType(fileExtension)

      // Download do arquivo
      const response = await fetch(url)
      if (!response.ok) continue

      const fileData = await response.arrayBuffer()
      const fileSize = fileData.byteLength

      // Verificar tamanho máximo (50MB)
      if (fileSize > 50 * 1024 * 1024) {
        console.log(`⚠️ File too large: ${fileName} (${fileSize} bytes)`)
        continue
      }

      // Upload para Supabase Storage
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from(bucketName)
        .upload(`viral-content/${fileName}`, fileData, {
          contentType: getMimeType(fileExtension),
          upsert: false
        })

      if (uploadError) {
        console.error(`❌ Upload error for ${fileName}:`, uploadError)
        continue
      }

      // Obter URL pública
      const { data: publicUrlData } = supabase.storage
        .from(bucketName)
        .getPublicUrl(`viral-content/${fileName}`)

      const storedFile = {
        bucket_name: bucketName,
        file_path: `viral-content/${fileName}`,
        file_name: fileName,
        file_type: fileExtension,
        file_size: fileSize,
        mime_type: getMimeType(fileExtension),
        public_url: publicUrlData.publicUrl,
        original_url: url
      }

      storedFiles.push(storedFile)

    } catch (error) {
      console.error(`❌ Media download error for ${mediaUrls[i]}:`, error)
    }
  }

  return storedFiles
}

function getFileExtension(url: string): string {
  try {
    const pathname = new URL(url).pathname
    const extension = pathname.split('.').pop()?.toLowerCase()
    return extension || 'unknown'
  } catch {
    return 'unknown'
  }
}

function getBucketForFileType(extension: string): string {
  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp']
  const videoExtensions = ['mp4', 'webm', 'mov', 'avi']

  if (imageExtensions.includes(extension)) return 'viral-images'
  if (videoExtensions.includes(extension)) return 'viral-videos'
  return 'viral-images' // Default
}

function getMimeType(extension: string): string {
  const mimeTypes: { [key: string]: string } = {
    'jpg': 'image/jpeg',
    'jpeg': 'image/jpeg',
    'png': 'image/png',
    'gif': 'image/gif',
    'webp': 'image/webp',
    'mp4': 'video/mp4',
    'webm': 'video/webm',
    'mov': 'video/quicktime'
  }

  return mimeTypes[extension] || 'application/octet-stream'
}

// =====================================================
// PROCESSAMENTO DE MÉTRICAS DE ENGAJAMENTO
// =====================================================

async function processEngagementMetrics(insertedContent: any[], supabase: any): Promise<void> {
  try {
    const engagementRecords = insertedContent.map(content => ({
      content_id: content.id,
      likes_count: content.engagement_metrics?.likes_count || 0,
      shares_count: content.engagement_metrics?.shares_count || 0,
      comments_count: content.engagement_metrics?.comments_count || 0,
      views_count: content.engagement_metrics?.views_count || 0,
      saves_count: content.engagement_metrics?.saves_count || 0,
      engagement_rate: calculateEngagementRate(content.engagement_metrics, content.author_followers_count),
      viral_velocity: calculateViralVelocity(content.engagement_metrics, content.published_at),
      reach_estimate: estimateReach(content.engagement_metrics, content.author_followers_count),
      impressions_estimate: estimateImpressions(content.engagement_metrics, content.author_followers_count)
    }))

    const { error } = await supabase
      .from('engagement_metrics')
      .insert(engagementRecords)

    if (error) {
      console.error('❌ Engagement metrics insertion error:', error)
    }

  } catch (error) {
    console.error('❌ Engagement metrics processing error:', error)
  }
}

function calculateEngagementRate(metrics: any, followerCount: number): number {
  if (!metrics || !followerCount || followerCount === 0) return 0

  const totalEngagement = (metrics.likes_count || 0) +
                         (metrics.shares_count || 0) +
                         (metrics.comments_count || 0)

  return Math.round((totalEngagement / followerCount) * 10000) / 100 // Percentual com 2 casas decimais
}

function calculateViralVelocity(metrics: any, publishedAt: string): number {
  if (!metrics || !publishedAt) return 0

  const hoursOld = (Date.now() - new Date(publishedAt).getTime()) / (1000 * 60 * 60)
  if (hoursOld === 0) return 0

  const totalEngagement = (metrics.likes_count || 0) +
                         (metrics.shares_count || 0) +
                         (metrics.comments_count || 0)

  return Math.round((totalEngagement / hoursOld) * 100) / 100
}

function estimateReach(metrics: any, followerCount: number): number {
  if (!metrics || !followerCount) return 0

  // Estimativa baseada em algoritmos de redes sociais
  const baseReach = followerCount * 0.1 // 10% dos seguidores veem organicamente
  const viralMultiplier = Math.min((metrics.shares_count || 0) * 5, followerCount * 2)

  return Math.round(baseReach + viralMultiplier)
}

function estimateImpressions(metrics: any, followerCount: number): number {
  if (!metrics || !followerCount) return 0

  // Impressões são tipicamente 2-3x o reach
  const reach = estimateReach(metrics, followerCount)
  const impressionMultiplier = 2.5 + (Math.random() * 0.5) // 2.5-3x

  return Math.round(reach * impressionMultiplier)
}

// =====================================================
// PREPARAÇÃO PARA BANCO DE DADOS
// =====================================================

function prepareForDatabase(item: any): any {
  return {
    extraction_id: item.extraction_id,
    platform: item.platform,
    content_type: item.content_type,
    external_id: item.external_id,
    url: item.url,
    title: item.title?.substring(0, 500), // Limitar tamanho
    description: item.description?.substring(0, 2000),
    author_username: item.author_username,
    author_display_name: item.author_display_name,
    author_followers_count: item.author_followers_count,
    author_verified: item.author_verified || false,
    content_text: item.content_text?.substring(0, 5000),
    hashtags: item.hashtags || [],
    mentions: item.mentions || [],
    media_urls: item.media_urls || [],
    media_types: item.media_types || [],
    engagement_metrics: item.engagement_metrics || {},
    viral_score: item.viral_score || 0,
    sentiment_score: item.sentiment_score,
    sentiment_label: item.sentiment_label,
    language_code: item.language_code,
    content_category: item.content_category,
    main_topics: item.main_topics || [],
    named_entities: item.named_entities || [],
    location_data: item.location_data || null,
    published_at: item.published_at,
    extracted_at: new Date().toISOString()
  }
}

// =====================================================
// LOGGING DE PERFORMANCE
// =====================================================

async function logPerformanceMetrics(
  processingTime: number,
  processedCount: number,
  supabase: any
): Promise<void> {
  try {
    const metrics = [
      {
        metric_type: 'processing',
        metric_name: 'viral_content_processing_time_ms',
        metric_value: processingTime,
        tags: { function: 'viral-content-processor', count: processedCount }
      },
      {
        metric_type: 'processing',
        metric_name: 'viral_content_processed_count',
        metric_value: processedCount,
        tags: { function: 'viral-content-processor', time_ms: processingTime }
      },
      {
        metric_type: 'processing',
        metric_name: 'viral_content_processing_rate',
        metric_value: processedCount / (processingTime / 1000), // items per second
        tags: { function: 'viral-content-processor' }
      }
    ]

    await supabase
      .from('performance_metrics')
      .insert(metrics)

  } catch (error) {
    console.error('❌ Performance logging error:', error)
  }
}
```

---

## 📊 TREND-ANALYZER FUNCTION

### ANÁLISE AVANÇADA DE TENDÊNCIAS:

```typescript
// supabase/functions/trend-analyzer/index.ts

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

// =====================================================
// INTERFACES PARA ANÁLISE DE TENDÊNCIAS
// =====================================================

interface TrendAnalysisRequest {
  project_id: string
  analysis_type: 'hashtags' | 'keywords' | 'sentiment' | 'engagement' | 'comprehensive'
  time_period: '1h' | '6h' | '24h' | '7d' | '30d'
  platforms: string[]
  filters?: {
    min_viral_score?: number
    languages?: string[]
    content_categories?: string[]
    author_verified_only?: boolean
  }
  options?: {
    include_predictions?: boolean
    generate_insights?: boolean
    compare_with_previous?: boolean
    export_format?: 'json' | 'csv'
  }
}

interface TrendAnalysisResult {
  analysis_id: string
  project_id: string
  analysis_type: string
  time_period: string
  platforms_analyzed: string[]
  total_content_analyzed: number
  trending_topics: any
  sentiment_distribution: any
  engagement_patterns: any
  viral_predictions: any
  insights: any
  confidence_score: number
  generated_at: string
}

// =====================================================
// CONFIGURAÇÕES
// =====================================================

const SUPABASE_URL = Deno.env.get('SUPABASE_URL')!
const SUPABASE_SERVICE_KEY = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
const MIN_CONTENT_FOR_ANALYSIS = 10
const MAX_ANALYSIS_TIME = 180000 // 3 minutos

// =====================================================
// FUNÇÃO PRINCIPAL
// =====================================================

serve(async (req) => {
  const startTime = Date.now()

  try {
    if (req.method !== 'POST') {
      return new Response('Method not allowed', { status: 405 })
    }

    const requestData: TrendAnalysisRequest = await req.json()

    // Validações
    if (!requestData.project_id || !requestData.analysis_type) {
      return new Response('Missing required fields', { status: 400 })
    }

    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY)

    console.log(`🔍 Starting ${requestData.analysis_type} analysis for project ${requestData.project_id}`)

    // Calcular período de análise
    const { startDate, endDate } = calculateAnalysisPeriod(requestData.time_period)

    // Buscar dados para análise
    const contentData = await fetchContentForAnalysis(
      requestData.project_id,
      startDate,
      endDate,
      requestData.platforms,
      requestData.filters,
      supabase
    )

    if (contentData.length < MIN_CONTENT_FOR_ANALYSIS) {
      return new Response(JSON.stringify({
        success: false,
        error: `Insufficient data for analysis. Found ${contentData.length} items, minimum required: ${MIN_CONTENT_FOR_ANALYSIS}`
      }), { status: 400, headers: { 'Content-Type': 'application/json' } })
    }

    // Executar análise baseada no tipo
    let analysisResult: any = {}

    switch (requestData.analysis_type) {
      case 'hashtags':
        analysisResult = await analyzeHashtagTrends(contentData, requestData.options)
        break
      case 'keywords':
        analysisResult = await analyzeKeywordTrends(contentData, requestData.options)
        break
      case 'sentiment':
        analysisResult = await analyzeSentimentTrends(contentData, requestData.options)
        break
      case 'engagement':
        analysisResult = await analyzeEngagementTrends(contentData, requestData.options)
        break
      case 'comprehensive':
        analysisResult = await performComprehensiveAnalysis(contentData, requestData.options)
        break
      default:
        throw new Error(`Unknown analysis type: ${requestData.analysis_type}`)
    }

    // Gerar predições se solicitado
    if (requestData.options?.include_predictions) {
      analysisResult.viral_predictions = await generateViralPredictions(contentData, analysisResult)
    }

    // Gerar insights se solicitado
    if (requestData.options?.generate_insights) {
      analysisResult.insights = await generateActionableInsights(analysisResult, contentData)
    }

    // Comparar com período anterior se solicitado
    if (requestData.options?.compare_with_previous) {
      const comparison = await compareWithPreviousPeriod(
        requestData,
        analysisResult,
        supabase
      )
      analysisResult.comparison = comparison
    }

    // Calcular score de confiança
    const confidenceScore = calculateConfidenceScore(contentData.length, analysisResult)

    // Preparar resultado final
    const finalResult: TrendAnalysisResult = {
      analysis_id: crypto.randomUUID(),
      project_id: requestData.project_id,
      analysis_type: requestData.analysis_type,
      time_period: requestData.time_period,
      platforms_analyzed: requestData.platforms,
      total_content_analyzed: contentData.length,
      trending_topics: analysisResult.trending_topics || {},
      sentiment_distribution: analysisResult.sentiment_distribution || {},
      engagement_patterns: analysisResult.engagement_patterns || {},
      viral_predictions: analysisResult.viral_predictions || {},
      insights: analysisResult.insights || {},
      confidence_score: confidenceScore,
      generated_at: new Date().toISOString()
    }

    // Salvar análise no banco
    const { data: savedAnalysis, error: saveError } = await supabase
      .from('trend_analysis')
      .insert({
        project_id: requestData.project_id,
        analysis_type: requestData.analysis_type,
        time_period: requestData.time_period,
        platforms: requestData.platforms,
        total_content_analyzed: contentData.length,
        trending_topics: finalResult.trending_topics,
        sentiment_distribution: finalResult.sentiment_distribution,
        engagement_patterns: finalResult.engagement_patterns,
        viral_predictions: finalResult.viral_predictions,
        insights: finalResult.insights,
        confidence_score: confidenceScore
      })
      .select()
      .single()

    if (saveError) {
      console.error('❌ Error saving analysis:', saveError)
    }

    // Log de performance
    const processingTime = Date.now() - startTime
    await logAnalysisMetrics(requestData.analysis_type, processingTime, contentData.length, supabase)

    return new Response(JSON.stringify({
      success: true,
      analysis: finalResult,
      processing_time_ms: processingTime,
      saved_to_database: !saveError
    }), {
      headers: { 'Content-Type': 'application/json' }
    })

  } catch (error) {
    console.error('❌ Trend analysis error:', error)

    return new Response(JSON.stringify({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    })
  }
})

// =====================================================
// FUNÇÕES DE ANÁLISE DE TENDÊNCIAS
// =====================================================

function calculateAnalysisPeriod(timePeriod: string): { startDate: Date, endDate: Date } {
  const endDate = new Date()
  const startDate = new Date()

  switch (timePeriod) {
    case '1h':
      startDate.setHours(endDate.getHours() - 1)
      break
    case '6h':
      startDate.setHours(endDate.getHours() - 6)
      break
    case '24h':
      startDate.setDate(endDate.getDate() - 1)
      break
    case '7d':
      startDate.setDate(endDate.getDate() - 7)
      break
    case '30d':
      startDate.setDate(endDate.getDate() - 30)
      break
    default:
      startDate.setDate(endDate.getDate() - 1) // Default 24h
  }

  return { startDate, endDate }
}

async function fetchContentForAnalysis(
  projectId: string,
  startDate: Date,
  endDate: Date,
  platforms: string[],
  filters: any,
  supabase: any
): Promise<any[]> {
  let query = supabase
    .from('viral_content')
    .select(`
      *,
      engagement_metrics(*),
      extractions!inner(project_id)
    `)
    .eq('extractions.project_id', projectId)
    .gte('published_at', startDate.toISOString())
    .lte('published_at', endDate.toISOString())

  // Aplicar filtros de plataforma
  if (platforms && platforms.length > 0) {
    query = query.in('platform', platforms)
  }

  // Aplicar filtros adicionais
  if (filters) {
    if (filters.min_viral_score) {
      query = query.gte('viral_score', filters.min_viral_score)
    }
    if (filters.languages && filters.languages.length > 0) {
      query = query.in('language_code', filters.languages)
    }
    if (filters.content_categories && filters.content_categories.length > 0) {
      query = query.in('content_category', filters.content_categories)
    }
    if (filters.author_verified_only) {
      query = query.eq('author_verified', true)
    }
  }

  const { data, error } = await query.limit(10000) // Máximo 10k registros

  if (error) {
    throw new Error(`Database query failed: ${error.message}`)
  }

  return data || []
}

async function analyzeHashtagTrends(contentData: any[], options: any): Promise<any> {
  const hashtagStats = new Map()
  const hashtagEngagement = new Map()
  const hashtagSentiment = new Map()
  const hashtagPlatforms = new Map()
  const hashtagTimeline = new Map()

  // Processar cada item de conteúdo
  contentData.forEach(item => {
    const publishDate = new Date(item.published_at).toISOString().split('T')[0]

    item.hashtags?.forEach((hashtag: string) => {
      const tag = hashtag.toLowerCase()

      // Contadores básicos
      if (!hashtagStats.has(tag)) {
        hashtagStats.set(tag, {
          count: 0,
          unique_authors: new Set(),
          first_seen: item.published_at,
          last_seen: item.published_at,
          viral_scores: []
        })
      }

      const stats = hashtagStats.get(tag)
      stats.count++
      stats.unique_authors.add(item.author_username)
      stats.viral_scores.push(item.viral_score || 0)

      if (new Date(item.published_at) < new Date(stats.first_seen)) {
        stats.first_seen = item.published_at
      }
      if (new Date(item.published_at) > new Date(stats.last_seen)) {
        stats.last_seen = item.published_at
      }

      // Engajamento por hashtag
      const engagement = item.engagement_metrics?.[0]
      if (engagement) {
        if (!hashtagEngagement.has(tag)) {
          hashtagEngagement.set(tag, {
            total_likes: 0,
            total_shares: 0,
            total_comments: 0,
            total_views: 0
          })
        }

        const engStats = hashtagEngagement.get(tag)
        engStats.total_likes += engagement.likes_count || 0
        engStats.total_shares += engagement.shares_count || 0
        engStats.total_comments += engagement.comments_count || 0
        engStats.total_views += engagement.views_count || 0
      }

      // Sentimento por hashtag
      if (item.sentiment_score !== null && item.sentiment_score !== undefined) {
        if (!hashtagSentiment.has(tag)) {
          hashtagSentiment.set(tag, {
            sentiment_scores: [],
            positive_count: 0,
            negative_count: 0,
            neutral_count: 0
          })
        }

        const sentStats = hashtagSentiment.get(tag)
        sentStats.sentiment_scores.push(item.sentiment_score)

        if (item.sentiment_score > 0.1) sentStats.positive_count++
        else if (item.sentiment_score < -0.1) sentStats.negative_count++
        else sentStats.neutral_count++
      }

      // Plataformas por hashtag
      if (!hashtagPlatforms.has(tag)) {
        hashtagPlatforms.set(tag, new Set())
      }
      hashtagPlatforms.get(tag).add(item.platform)

      // Timeline por hashtag
      if (!hashtagTimeline.has(tag)) {
        hashtagTimeline.set(tag, new Map())
      }
      const timeline = hashtagTimeline.get(tag)
      timeline.set(publishDate, (timeline.get(publishDate) || 0) + 1)
    })
  })

  // Processar resultados finais
  const trendingHashtags = Array.from(hashtagStats.entries())
    .map(([hashtag, stats]) => {
      const engagement = hashtagEngagement.get(hashtag) || {}
      const sentiment = hashtagSentiment.get(hashtag) || {}
      const platforms = Array.from(hashtagPlatforms.get(hashtag) || [])
      const timeline = Array.from(hashtagTimeline.get(hashtag) || [])
        .map(([date, count]) => ({ date, count }))
        .sort((a, b) => a.date.localeCompare(b.date))

      return {
        hashtag,
        usage_count: stats.count,
        unique_authors: stats.unique_authors.size,
        avg_viral_score: stats.viral_scores.reduce((a, b) => a + b, 0) / stats.viral_scores.length,
        max_viral_score: Math.max(...stats.viral_scores),
        total_engagement: (engagement.total_likes || 0) +
                         (engagement.total_shares || 0) +
                         (engagement.total_comments || 0),
        avg_engagement_per_post: stats.count > 0 ?
          ((engagement.total_likes || 0) + (engagement.total_shares || 0) + (engagement.total_comments || 0)) / stats.count : 0,
        sentiment_distribution: {
          positive: sentiment.positive_count || 0,
          negative: sentiment.negative_count || 0,
          neutral: sentiment.neutral_count || 0,
          avg_sentiment: sentiment.sentiment_scores?.length > 0 ?
            sentiment.sentiment_scores.reduce((a, b) => a + b, 0) / sentiment.sentiment_scores.length : 0
        },
        platforms,
        timeline,
        growth_velocity: calculateHashtagGrowthVelocity(timeline),
        trend_momentum: calculateTrendMomentum(stats.viral_scores, timeline),
        first_seen: stats.first_seen,
        last_seen: stats.last_seen,
        days_active: Math.ceil((new Date(stats.last_seen).getTime() - new Date(stats.first_seen).getTime()) / (1000 * 60 * 60 * 24)) + 1
      }
    })
    .sort((a, b) => b.usage_count - a.usage_count)
    .slice(0, 50) // Top 50 hashtags

  return {
    trending_topics: {
      hashtags: trendingHashtags,
      total_unique_hashtags: hashtagStats.size,
      most_viral_hashtag: trendingHashtags[0]?.hashtag,
      fastest_growing: trendingHashtags.sort((a, b) => b.growth_velocity - a.growth_velocity)[0]?.hashtag,
      highest_engagement: trendingHashtags.sort((a, b) => b.avg_engagement_per_post - a.avg_engagement_per_post)[0]?.hashtag
    }
  }
}

function calculateHashtagGrowthVelocity(timeline: any[]): number {
  if (timeline.length < 2) return 0

  const recent = timeline.slice(-3) // Últimos 3 pontos
  const earlier = timeline.slice(0, 3) // Primeiros 3 pontos

  const recentAvg = recent.reduce((sum, point) => sum + point.count, 0) / recent.length
  const earlierAvg = earlier.reduce((sum, point) => sum + point.count, 0) / earlier.length

  return earlierAvg > 0 ? (recentAvg - earlierAvg) / earlierAvg : 0
}

function calculateTrendMomentum(viralScores: number[], timeline: any[]): number {
  if (viralScores.length === 0) return 0

  const avgViralScore = viralScores.reduce((a, b) => a + b, 0) / viralScores.length
  const usageFrequency = timeline.length > 0 ?
    timeline.reduce((sum, point) => sum + point.count, 0) / timeline.length : 0

  return avgViralScore * Math.log(usageFrequency + 1)
}

async function analyzeKeywordTrends(contentData: any[], options: any): Promise<any> {
  const keywordStats = new Map()
  const stopWords = new Set([
    'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
    'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did',
    'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those'
  ])

  // Extrair palavras-chave do conteúdo
  contentData.forEach(item => {
    const text = ((item.content_text || '') + ' ' + (item.title || '') + ' ' + (item.description || ''))
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 3 && !stopWords.has(word))

    const wordFreq = new Map()
    text.forEach(word => {
      wordFreq.set(word, (wordFreq.get(word) || 0) + 1)
    })

    // Processar palavras-chave significativas (aparecem mais de 1 vez no texto)
    Array.from(wordFreq.entries())
      .filter(([word, freq]) => freq > 1)
      .forEach(([word, freq]) => {
        if (!keywordStats.has(word)) {
          keywordStats.set(word, {
            total_mentions: 0,
            content_count: 0,
            viral_scores: [],
            platforms: new Set(),
            sentiment_scores: [],
            engagement_total: 0
          })
        }

        const stats = keywordStats.get(word)
        stats.total_mentions += freq
        stats.content_count++
        stats.viral_scores.push(item.viral_score || 0)
        stats.platforms.add(item.platform)

        if (item.sentiment_score !== null && item.sentiment_score !== undefined) {
          stats.sentiment_scores.push(item.sentiment_score)
        }

        const engagement = item.engagement_metrics?.[0]
        if (engagement) {
          stats.engagement_total += (engagement.likes_count || 0) +
                                   (engagement.shares_count || 0) +
                                   (engagement.comments_count || 0)
        }
      })
  })

  // Processar resultados
  const trendingKeywords = Array.from(keywordStats.entries())
    .filter(([word, stats]) => stats.content_count >= 5) // Mínimo 5 menções
    .map(([keyword, stats]) => ({
      keyword,
      total_mentions: stats.total_mentions,
      content_count: stats.content_count,
      avg_mentions_per_content: stats.total_mentions / stats.content_count,
      avg_viral_score: stats.viral_scores.reduce((a, b) => a + b, 0) / stats.viral_scores.length,
      max_viral_score: Math.max(...stats.viral_scores),
      platforms: Array.from(stats.platforms),
      avg_sentiment: stats.sentiment_scores.length > 0 ?
        stats.sentiment_scores.reduce((a, b) => a + b, 0) / stats.sentiment_scores.length : 0,
      total_engagement: stats.engagement_total,
      avg_engagement_per_content: stats.engagement_total / stats.content_count,
      relevance_score: calculateKeywordRelevance(stats)
    }))
    .sort((a, b) => b.relevance_score - a.relevance_score)
    .slice(0, 30) // Top 30 keywords

  return {
    trending_topics: {
      keywords: trendingKeywords,
      total_unique_keywords: keywordStats.size,
      most_relevant: trendingKeywords[0]?.keyword,
      highest_viral_impact: trendingKeywords.sort((a, b) => b.avg_viral_score - a.avg_viral_score)[0]?.keyword
    }
  }
}

function calculateKeywordRelevance(stats: any): number {
  const mentionWeight = Math.log(stats.total_mentions + 1)
  const viralWeight = stats.viral_scores.reduce((a, b) => a + b, 0) / stats.viral_scores.length
  const platformDiversity = stats.platforms.size
  const engagementWeight = Math.log(stats.engagement_total + 1)

  return mentionWeight * viralWeight * platformDiversity * engagementWeight / 1000
}

async function analyzeSentimentTrends(contentData: any[], options: any): Promise<any> {
  const sentimentByPlatform = new Map()
  const sentimentByTime = new Map()
  const sentimentByHashtag = new Map()
  const overallSentiment = {
    positive: 0,
    negative: 0,
    neutral: 0,
    scores: []
  }

  contentData.forEach(item => {
    if (item.sentiment_score === null || item.sentiment_score === undefined) return

    const score = item.sentiment_score
    const label = item.sentiment_label || 'neutral'
    const platform = item.platform
    const date = new Date(item.published_at).toISOString().split('T')[0]

    // Sentimento geral
    overallSentiment.scores.push(score)
    if (score > 0.1) overallSentiment.positive++
    else if (score < -0.1) overallSentiment.negative++
    else overallSentiment.neutral++

    // Sentimento por plataforma
    if (!sentimentByPlatform.has(platform)) {
      sentimentByPlatform.set(platform, {
        positive: 0, negative: 0, neutral: 0, scores: []
      })
    }
    const platformSentiment = sentimentByPlatform.get(platform)
    platformSentiment.scores.push(score)
    if (score > 0.1) platformSentiment.positive++
    else if (score < -0.1) platformSentiment.negative++
    else platformSentiment.neutral++

    // Sentimento por tempo
    if (!sentimentByTime.has(date)) {
      sentimentByTime.set(date, {
        positive: 0, negative: 0, neutral: 0, scores: []
      })
    }
    const timeSentiment = sentimentByTime.get(date)
    timeSentiment.scores.push(score)
    if (score > 0.1) timeSentiment.positive++
    else if (score < -0.1) timeSentiment.negative++
    else timeSentiment.neutral++

    // Sentimento por hashtag
    item.hashtags?.forEach((hashtag: string) => {
      const tag = hashtag.toLowerCase()
      if (!sentimentByHashtag.has(tag)) {
        sentimentByHashtag.set(tag, {
          positive: 0, negative: 0, neutral: 0, scores: []
        })
      }
      const hashtagSentiment = sentimentByHashtag.get(tag)
      hashtagSentiment.scores.push(score)
      if (score > 0.1) hashtagSentiment.positive++
      else if (score < -0.1) hashtagSentiment.negative++
      else hashtagSentiment.neutral++
    })
  })

  // Processar resultados
  const platformSentimentResults = Array.from(sentimentByPlatform.entries())
    .map(([platform, sentiment]) => ({
      platform,
      positive_percentage: (sentiment.positive / (sentiment.positive + sentiment.negative + sentiment.neutral)) * 100,
      negative_percentage: (sentiment.negative / (sentiment.positive + sentiment.negative + sentiment.neutral)) * 100,
      neutral_percentage: (sentiment.neutral / (sentiment.positive + sentiment.negative + sentiment.neutral)) * 100,
      avg_sentiment_score: sentiment.scores.reduce((a, b) => a + b, 0) / sentiment.scores.length,
      total_content: sentiment.scores.length
    }))

  const timelineSentiment = Array.from(sentimentByTime.entries())
    .map(([date, sentiment]) => ({
      date,
      positive_count: sentiment.positive,
      negative_count: sentiment.negative,
      neutral_count: sentiment.neutral,
      avg_sentiment_score: sentiment.scores.reduce((a, b) => a + b, 0) / sentiment.scores.length,
      total_content: sentiment.scores.length
    }))
    .sort((a, b) => a.date.localeCompare(b.date))

  const hashtagSentimentResults = Array.from(sentimentByHashtag.entries())
    .filter(([hashtag, sentiment]) => sentiment.scores.length >= 5)
    .map(([hashtag, sentiment]) => ({
      hashtag,
      avg_sentiment_score: sentiment.scores.reduce((a, b) => a + b, 0) / sentiment.scores.length,
      positive_percentage: (sentiment.positive / sentiment.scores.length) * 100,
      negative_percentage: (sentiment.negative / sentiment.scores.length) * 100,
      neutral_percentage: (sentiment.neutral / sentiment.scores.length) * 100,
      total_mentions: sentiment.scores.length,
      sentiment_volatility: calculateSentimentVolatility(sentiment.scores)
    }))
    .sort((a, b) => Math.abs(b.avg_sentiment_score) - Math.abs(a.avg_sentiment_score))
    .slice(0, 20)

  return {
    sentiment_distribution: {
      overall: {
        positive_percentage: (overallSentiment.positive / overallSentiment.scores.length) * 100,
        negative_percentage: (overallSentiment.negative / overallSentiment.scores.length) * 100,
        neutral_percentage: (overallSentiment.neutral / overallSentiment.scores.length) * 100,
        avg_sentiment_score: overallSentiment.scores.reduce((a, b) => a + b, 0) / overallSentiment.scores.length,
        sentiment_volatility: calculateSentimentVolatility(overallSentiment.scores)
      },
      by_platform: platformSentimentResults,
      timeline: timelineSentiment,
      by_hashtag: hashtagSentimentResults
    }
  }
}

function calculateSentimentVolatility(scores: number[]): number {
  if (scores.length < 2) return 0

  const mean = scores.reduce((a, b) => a + b, 0) / scores.length
  const variance = scores.reduce((sum, score) => sum + Math.pow(score - mean, 2), 0) / scores.length

  return Math.sqrt(variance)
}
```
```
```
