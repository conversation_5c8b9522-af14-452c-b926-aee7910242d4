#!/bin/bash

# ===================================================================
# SCRIPT DE INSTALAÇÃO N8N BÁSICO
# ===================================================================

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Funções de log
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Banner
echo -e "${BLUE}"
echo "====================================================================="
echo "           INSTALAÇÃO N8N BÁSICO - FRAMEWORK MODULAR"
echo "====================================================================="
echo -e "${NC}"

# Verificar pré-requisitos
log_info "Verificando pré-requisitos..."

# Verificar Docker
if ! command -v docker &> /dev/null; then
    log_error "Docker não está instalado!"
    log_info "Instale o Docker: https://docs.docker.com/get-docker/"
    exit 1
fi

# Verificar Docker Compose
if ! command -v docker-compose &> /dev/null; then
    log_error "Docker Compose não está instalado!"
    log_info "Instale o Docker Compose: https://docs.docker.com/compose/install/"
    exit 1
fi

# Verificar se Docker está rodando
if ! docker info &> /dev/null; then
    log_error "Docker não está rodando!"
    log_info "Inicie o Docker e tente novamente."
    exit 1
fi

log_success "Pré-requisitos verificados!"

# Criar diretório de trabalho
WORK_DIR="n8n-basic"
log_info "Criando diretório de trabalho: $WORK_DIR"

if [ -d "$WORK_DIR" ]; then
    log_warning "Diretório $WORK_DIR já existe!"
    read -p "Deseja continuar? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "Instalação cancelada."
        exit 0
    fi
else
    mkdir -p "$WORK_DIR"
fi

cd "$WORK_DIR"

# Baixar arquivos de configuração
log_info "Baixando arquivos de configuração..."

# Criar docker-compose.yml
cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  postgres:
    image: postgres:16-alpine
    container_name: n8n-postgres-basic
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-n8n}
      POSTGRES_USER: ${POSTGRES_USER:-n8n_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-n8n_password_123}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-n8n_user} -d ${POSTGRES_DB:-n8n}"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - n8n-network

  redis:
    image: redis:7-alpine
    container_name: n8n-redis-basic
    command: redis-server --appendonly yes --maxmemory ${REDIS_MAXMEMORY:-256mb}
    volumes:
      - redis_data:/data
    ports:
      - "${REDIS_PORT:-6379}:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - n8n-network

  n8n:
    image: docker.n8n.io/n8nio/n8n:latest
    container_name: n8n-app-basic
    ports:
      - "${N8N_PORT:-5678}:5678"
    environment:
      DB_TYPE: postgresdb
      DB_POSTGRESDB_HOST: postgres
      DB_POSTGRESDB_PORT: 5432
      DB_POSTGRESDB_DATABASE: ${POSTGRES_DB:-n8n}
      DB_POSTGRESDB_USER: ${POSTGRES_USER:-n8n_user}
      DB_POSTGRESDB_PASSWORD: ${POSTGRES_PASSWORD:-n8n_password_123}
      QUEUE_BULL_REDIS_HOST: redis
      QUEUE_BULL_REDIS_PORT: 6379
      N8N_BASIC_AUTH_ACTIVE: ${N8N_BASIC_AUTH_ACTIVE:-true}
      N8N_BASIC_AUTH_USER: ${N8N_BASIC_AUTH_USER:-admin}
      N8N_BASIC_AUTH_PASSWORD: ${N8N_BASIC_AUTH_PASSWORD:-admin123}
      WEBHOOK_URL: ${WEBHOOK_URL:-http://localhost:5678}
      GENERIC_TIMEZONE: ${TIMEZONE:-America/Sao_Paulo}
    volumes:
      - n8n_data:/home/<USER>/.n8n
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - n8n-network

volumes:
  postgres_data:
  redis_data:
  n8n_data:

networks:
  n8n-network:
    driver: bridge
EOF

# Criar arquivo .env
cat > .env << 'EOF'
# N8N BÁSICO - CONFIGURAÇÕES
POSTGRES_DB=n8n
POSTGRES_USER=n8n_user
POSTGRES_PASSWORD=n8n_password_123
POSTGRES_PORT=5432

REDIS_PORT=6379
REDIS_MAXMEMORY=256mb

N8N_PORT=5678
N8N_BASIC_AUTH_ACTIVE=true
N8N_BASIC_AUTH_USER=admin
N8N_BASIC_AUTH_PASSWORD=admin123

WEBHOOK_URL=http://localhost:5678
TIMEZONE=America/Sao_Paulo
EOF

log_success "Arquivos de configuração criados!"

# Iniciar serviços
log_info "Iniciando serviços..."
docker-compose up -d

# Aguardar inicialização
log_info "Aguardando inicialização dos serviços..."
sleep 30

# Verificar status
log_info "Verificando status dos serviços..."
docker-compose ps

# Verificar saúde dos serviços
log_info "Verificando saúde dos serviços..."
if docker-compose exec -T postgres pg_isready -U n8n_user -d n8n > /dev/null 2>&1; then
    log_success "PostgreSQL está funcionando!"
else
    log_error "PostgreSQL não está respondendo!"
fi

if docker-compose exec -T redis redis-cli ping > /dev/null 2>&1; then
    log_success "Redis está funcionando!"
else
    log_error "Redis não está respondendo!"
fi

# Verificar N8N
log_info "Verificando N8N..."
sleep 10
if curl -s http://localhost:5678 > /dev/null; then
    log_success "N8N está funcionando!"
else
    log_warning "N8N pode ainda estar inicializando..."
fi

# Informações finais
echo -e "${GREEN}"
echo "====================================================================="
echo "                    INSTALAÇÃO CONCLUÍDA!"
echo "====================================================================="
echo -e "${NC}"

log_info "Acesso ao N8N:"
echo "  URL: http://localhost:5678"
echo "  Usuário: admin"
echo "  Senha: admin123"
echo ""

log_info "Comandos úteis:"
echo "  Ver logs: docker-compose logs -f"
echo "  Parar: docker-compose down"
echo "  Reiniciar: docker-compose restart"
echo "  Status: docker-compose ps"
echo ""

log_warning "IMPORTANTE: Altere as senhas padrão em produção!"

log_success "N8N Básico instalado com sucesso!"
