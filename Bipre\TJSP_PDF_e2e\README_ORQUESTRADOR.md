# 🚀 TJSP Orquestrador End-to-End v1.0

Sistema que conecta automaticamente os processos de **download** e **extração** do TJSP, eliminando a necessidade de execução manual separada.

## 📋 Funcionalidades

### ✅ **Processo Completo Automatizado**
- Download automático de ofícios do TJSP (ESAJ)
- Sincronização automática de arquivos
- Extração automática de informações dos PDFs
- Relatório consolidado final

### 🔧 **Funcionalidades Avançadas**
- **Sistema de Checkpoint**: Retomada automática em caso de interrupção
- **Logging Consolidado**: Rastreamento completo de todas as operações
- **Interface Intuitiva**: Menu interativo para diferentes opções
- **Execução Flexível**: Execute apenas download, extração ou processo completo

## 🏗️ Estrutura do Sistema

```
TJSP_PDF_e2e/
├── orquestrador_tjsp_e2e.py      # Script principal do orquestrador
├── EXECUTAR_ORQUESTRADOR.bat     # Script de execução rápida
├── tjsp/                         # Sistema de download
│   ├── ProcessadorTJSPUnificado_final.py
│   ├── downloads_completos/      # PDFs baixados
│   └── ...
├── extracao/                     # Sistema de extração
│   ├── src/sistema_principal.py
│   ├── data/input/              # PDFs para extração
│   ├── data/output/             # Resultados extraídos
│   └── ...
└── logs_orquestrador/           # Logs do orquestrador
```

## 🚀 Como Usar

### **Opção 1: Execução Rápida**
```bash
# Windows
EXECUTAR_ORQUESTRADOR.bat

# Linux/Mac
python orquestrador_tjsp_e2e.py
```

### **Opção 2: Execução Manual**
```bash
cd Bipre/TJSP_PDF_e2e
python orquestrador_tjsp_e2e.py
```

## 📊 Menu Principal

```
🚀 TJSP ORQUESTRADOR END-TO-END v1.0
============================================================
1. 📥 Executar apenas Download (TJSP)
2. 📄 Executar apenas Extração
3. 🔄 Executar processo completo (Download + Extração)
4. 🔄 Sincronizar arquivos (Downloads → Extração)
5. 📊 Gerar relatório consolidado
6. 📋 Ver status atual
7. 🚪 Sair
```

## 🔄 Fluxo de Operação

### **Processo Completo (Opção 3)**
1. **Download**: Acessa ESAJ e baixa ofícios → `tjsp/downloads_completos/`
2. **Sincronização**: Copia PDFs → `extracao/data/input/`
3. **Extração**: Processa PDFs → `extracao/data/output/`
4. **Relatório**: Gera relatório consolidado

### **Execução Individual**
- **Opção 1**: Apenas download (para casos específicos)
- **Opção 2**: Apenas extração (quando já tem PDFs)
- **Opção 4**: Apenas sincronização (copiar arquivos)

## 📁 Arquivos Gerados

### **Logs**
- `logs_orquestrador/orquestrador_YYYYMMDD_HHMMSS.log`
- Rastreamento completo de todas as operações

### **Checkpoint**
- `checkpoint_orquestrador.json`
- Estado atual do processamento para retomada

### **Relatórios**
- `logs_orquestrador/relatorio_consolidado_YYYYMMDD_HHMMSS.json`
- Resumo completo da execução

## ⚙️ Configurações

### **Pré-requisitos**
- Python 3.8+
- Dependências dos sistemas TJSP e Extração instaladas
- Estrutura de diretórios correta

### **Diretórios Importantes**
- **Downloads**: `tjsp/downloads_completos/`
- **Input Extração**: `extracao/data/input/`
- **Output Extração**: `extracao/data/output/`
- **Logs**: `logs_orquestrador/`

## 🔧 Solução de Problemas

### **Erro: "Sistema não encontrado"**
- Verifique se está no diretório correto
- Confirme que os diretórios `tjsp/` e `extracao/` existem

### **Erro: "Python não encontrado"**
- Instale Python 3.8+ 
- Adicione Python ao PATH do sistema

### **Processo interrompido**
- Use a opção "6. Ver status atual" para verificar progresso
- Execute novamente - o checkpoint retomará automaticamente

## 📊 Monitoramento

### **Status em Tempo Real**
- Use opção "6. Ver status atual" para acompanhar
- Logs detalhados em `logs_orquestrador/`

### **Métricas Disponíveis**
- Total de PDFs baixados
- Arquivos sincronizados
- Resultados extraídos
- Tempo de processamento

## 🎯 Vantagens

### **Antes (Sistemas Separados)**
❌ Execução manual de cada sistema  
❌ Cópia manual de arquivos  
❌ Controle separado de progresso  
❌ Logs dispersos  

### **Agora (Orquestrador)**
✅ Execução automática end-to-end  
✅ Sincronização automática  
✅ Checkpoint unificado  
✅ Logging consolidado  
✅ Interface intuitiva  

---

**Desenvolvido por**: Sistema Augment  
**Data**: 2025-01-28  
**Versão**: 1.0
