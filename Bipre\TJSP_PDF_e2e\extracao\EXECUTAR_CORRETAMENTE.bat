@echo off
echo ========================================
echo   SISTEMA TJSP - EXECUCAO CORRETA
echo ========================================
echo.

REM Navegar para o diretorio correto
cd /d "%~dp0"

echo 📁 Diretorio atual: %CD%
echo 📊 Verificando PDFs disponiveis...

REM Contar PDFs
for /f %%i in ('dir "data\input\*.pdf" /b 2^>nul ^| find /c /v ""') do set PDF_COUNT=%%i

echo 📋 PDFs encontrados: %PDF_COUNT%
echo.

if %PDF_COUNT%==0 (
    echo ❌ ERRO: Nenhum PDF encontrado em data\input\
    echo 💡 Coloque os PDFs na pasta data\input\ antes de executar
    pause
    exit /b 1
)

echo 🚀 Iniciando processamento...
echo.

REM Executar o sistema principal
python src\sistema_principal.py

echo.
echo ✅ Processamento concluido!
echo 📊 Verifique os resultados em data\output\
echo.
pause
