#!/bin/bash

# ===================================================================
# FRAMEWORK COMPLETO N8N + EVOLUTION API - INSTALADOR INTERATIVO
# ===================================================================

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Funções de log
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}[HEADER]${NC} $1"
}

# Banner principal
show_banner() {
    clear
    echo -e "${CYAN}"
    echo "====================================================================="
    echo "    🚀 FRAMEWORK COMPLETO N8N + EVOLUTION API - INSTALADOR"
    echo "====================================================================="
    echo "                    Versão 1.0 - Framework Modular"
    echo "====================================================================="
    echo -e "${NC}"
}

# Menu principal
show_menu() {
    echo -e "${BLUE}"
    echo "Escolha o tipo de instalação:"
    echo -e "${NC}"
    echo "1) 🔧 N8N Básico (Mínimo - Desenvolvimento)"
    echo "2) 🏢 N8N Completo (Produção + Monitoramento)"
    echo "3) 📱 Evolution API (WhatsApp - Standalone)"
    echo "4) 🌟 Framework Integrado (Solução Completa)"
    echo "5) ℹ️  Informações sobre os módulos"
    echo "6) 🚪 Sair"
    echo ""
}

# Informações sobre módulos
show_module_info() {
    clear
    echo -e "${CYAN}"
    echo "====================================================================="
    echo "                    INFORMAÇÕES DOS MÓDULOS"
    echo "====================================================================="
    echo -e "${NC}"
    
    echo -e "${YELLOW}1. N8N BÁSICO:${NC}"
    echo "   • Componentes: N8N + PostgreSQL + Redis"
    echo "   • Recursos: 2GB RAM, 10GB Disk"
    echo "   • Uso: Desenvolvimento, testes, projetos pequenos"
    echo "   • Portas: 5678 (N8N), 5432 (PostgreSQL), 6379 (Redis)"
    echo ""
    
    echo -e "${YELLOW}2. N8N COMPLETO:${NC}"
    echo "   • Componentes: N8N + Workers + PostgreSQL + Redis + Monitoramento"
    echo "   • Recursos: 4GB RAM, 20GB Disk"
    echo "   • Uso: Produção, alta disponibilidade"
    echo "   • Extras: Grafana, Prometheus, PgAdmin, Redis Commander"
    echo ""
    
    echo -e "${YELLOW}3. EVOLUTION API:${NC}"
    echo "   • Componentes: Evolution API + PostgreSQL + Redis (dedicados)"
    echo "   • Recursos: 2GB RAM, 10GB Disk"
    echo "   • Uso: Integração WhatsApp independente"
    echo "   • Portas: 8001 (Evolution), 5433 (PostgreSQL), 6380 (Redis)"
    echo ""
    
    echo -e "${YELLOW}4. FRAMEWORK INTEGRADO:${NC}"
    echo "   • Componentes: Todos os módulos + Proxy + SSL"
    echo "   • Recursos: 8GB RAM, 50GB Disk"
    echo "   • Uso: Solução empresarial completa"
    echo "   • Extras: Nginx, SSL, Backup automatizado"
    echo ""
    
    read -p "Pressione Enter para voltar ao menu..."
}

# Verificar pré-requisitos
check_prerequisites() {
    log_info "Verificando pré-requisitos..."
    
    # Verificar Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker não está instalado!"
        log_info "Instale o Docker: https://docs.docker.com/get-docker/"
        exit 1
    fi
    
    # Verificar Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose não está instalado!"
        log_info "Instale o Docker Compose: https://docs.docker.com/compose/install/"
        exit 1
    fi
    
    # Verificar se Docker está rodando
    if ! docker info &> /dev/null; then
        log_error "Docker não está rodando!"
        log_info "Inicie o Docker e tente novamente."
        exit 1
    fi
    
    # Verificar recursos do sistema
    TOTAL_RAM=$(free -m | awk 'NR==2{printf "%.0f", $2/1024}')
    AVAILABLE_DISK=$(df -BG . | awk 'NR==2{print $4}' | sed 's/G//')
    
    log_info "Recursos do sistema:"
    echo "  RAM Total: ${TOTAL_RAM}GB"
    echo "  Disco Disponível: ${AVAILABLE_DISK}GB"
    
    if [ "$TOTAL_RAM" -lt 4 ]; then
        log_warning "RAM baixa detectada. Recomendado: 4GB+ para instalação completa"
    fi
    
    if [ "$AVAILABLE_DISK" -lt 20 ]; then
        log_warning "Espaço em disco baixo. Recomendado: 20GB+ para instalação completa"
    fi
    
    log_success "Pré-requisitos verificados!"
}

# Instalar N8N Básico
install_n8n_basic() {
    log_header "Instalando N8N Básico..."
    
    if [ -f "scripts/install-n8n-basic.sh" ]; then
        chmod +x scripts/install-n8n-basic.sh
        ./scripts/install-n8n-basic.sh
    else
        log_error "Script de instalação N8N Básico não encontrado!"
        return 1
    fi
}

# Instalar Evolution API
install_evolution_api() {
    log_header "Instalando Evolution API..."
    
    if [ -f "scripts/install-evolution-api.sh" ]; then
        chmod +x scripts/install-evolution-api.sh
        ./scripts/install-evolution-api.sh
    else
        log_error "Script de instalação Evolution API não encontrado!"
        return 1
    fi
}

# Instalar N8N Completo
install_n8n_complete() {
    log_header "Instalando N8N Completo..."
    log_warning "Esta funcionalidade será implementada na próxima versão."
    log_info "Por enquanto, use a instalação atual do n8n-production/"
}

# Instalar Framework Integrado
install_integrated() {
    log_header "Instalando Framework Integrado..."
    log_warning "Esta funcionalidade será implementada na próxima versão."
    log_info "Por enquanto, instale os módulos separadamente."
}

# Função principal
main() {
    show_banner
    check_prerequisites
    
    while true; do
        echo ""
        show_menu
        read -p "Digite sua opção (1-6): " choice
        
        case $choice in
            1)
                install_n8n_basic
                read -p "Pressione Enter para continuar..."
                ;;
            2)
                install_n8n_complete
                read -p "Pressione Enter para continuar..."
                ;;
            3)
                install_evolution_api
                read -p "Pressione Enter para continuar..."
                ;;
            4)
                install_integrated
                read -p "Pressione Enter para continuar..."
                ;;
            5)
                show_module_info
                ;;
            6)
                log_info "Saindo do instalador..."
                exit 0
                ;;
            *)
                log_error "Opção inválida! Digite um número de 1 a 6."
                ;;
        esac
        
        show_banner
    done
}

# Verificar se está sendo executado como script principal
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
