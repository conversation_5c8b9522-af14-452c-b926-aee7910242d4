#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Módulo de Sincronização de Arquivos TJSP
Sistema avançado para sincronização entre diretórios de download e extração

Funcionalidades:
- Sincronização inteligente com detecção de duplicatas
- Verificação de integridade de arquivos
- Backup automático
- Relatórios detalhados de sincronização
- Limpeza automática de arquivos antigos

Autor: Sistema Augment
Data: 2025-01-28
"""

import os
import shutil
import hashlib
import logging
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional

class SincronizadorArquivos:
    """Classe para sincronização avançada de arquivos PDF"""
    
    def __init__(self, origem: Path, destino: Path, backup_dir: Optional[Path] = None):
        self.origem = Path(origem)
        self.destino = Path(destino)
        self.backup_dir = Path(backup_dir) if backup_dir else None
        self.logger = logging.getLogger(__name__)
        
        # Criar diretórios se não existirem
        self.destino.mkdir(parents=True, exist_ok=True)
        if self.backup_dir:
            self.backup_dir.mkdir(parents=True, exist_ok=True)
            
    def calcular_hash_arquivo(self, arquivo: Path) -> str:
        """Calcula hash MD5 de um arquivo para verificação de integridade"""
        hash_md5 = hashlib.md5()
        try:
            with open(arquivo, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            self.logger.error(f"Erro ao calcular hash de {arquivo}: {e}")
            return ""
            
    def verificar_integridade(self, arquivo_origem: Path, arquivo_destino: Path) -> bool:
        """Verifica se dois arquivos são idênticos comparando hashes"""
        if not arquivo_destino.exists():
            return False
            
        hash_origem = self.calcular_hash_arquivo(arquivo_origem)
        hash_destino = self.calcular_hash_arquivo(arquivo_destino)
        
        return hash_origem == hash_destino and hash_origem != ""
        
    def listar_arquivos_pdf(self, diretorio: Path) -> List[Path]:
        """Lista todos os arquivos PDF em um diretório"""
        if not diretorio.exists():
            return []
        return list(diretorio.glob("*.pdf"))
        
    def criar_backup(self, arquivo: Path) -> bool:
        """Cria backup de um arquivo antes da sincronização"""
        if not self.backup_dir:
            return True
            
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            nome_backup = f"{arquivo.stem}_{timestamp}{arquivo.suffix}"
            caminho_backup = self.backup_dir / nome_backup
            
            shutil.copy2(arquivo, caminho_backup)
            self.logger.debug(f"Backup criado: {caminho_backup}")
            return True
            
        except Exception as e:
            self.logger.error(f"Erro ao criar backup de {arquivo}: {e}")
            return False
            
    def sincronizar_arquivo(self, arquivo_origem: Path, forcar_sobrescrita: bool = False) -> Dict:
        """Sincroniza um arquivo específico"""
        resultado = {
            "arquivo": arquivo_origem.name,
            "status": "erro",
            "acao": "nenhuma",
            "tamanho": 0,
            "hash_origem": "",
            "hash_destino": "",
            "erro": ""
        }
        
        try:
            arquivo_destino = self.destino / arquivo_origem.name
            resultado["tamanho"] = arquivo_origem.stat().st_size
            resultado["hash_origem"] = self.calcular_hash_arquivo(arquivo_origem)
            
            # Verificar se arquivo já existe no destino
            if arquivo_destino.exists():
                resultado["hash_destino"] = self.calcular_hash_arquivo(arquivo_destino)
                
                # Se arquivos são idênticos, não fazer nada
                if self.verificar_integridade(arquivo_origem, arquivo_destino):
                    resultado["status"] = "sucesso"
                    resultado["acao"] = "ja_sincronizado"
                    return resultado
                    
                # Se forçar sobrescrita ou arquivos diferentes
                if forcar_sobrescrita or resultado["hash_origem"] != resultado["hash_destino"]:
                    # Criar backup do arquivo existente
                    if not self.criar_backup(arquivo_destino):
                        resultado["erro"] = "Falha ao criar backup"
                        return resultado
                        
                    resultado["acao"] = "sobrescrito"
                else:
                    resultado["status"] = "sucesso"
                    resultado["acao"] = "ignorado_duplicata"
                    return resultado
            else:
                resultado["acao"] = "copiado"
                
            # Copiar arquivo
            shutil.copy2(arquivo_origem, arquivo_destino)
            
            # Verificar integridade após cópia
            if self.verificar_integridade(arquivo_origem, arquivo_destino):
                resultado["status"] = "sucesso"
                resultado["hash_destino"] = resultado["hash_origem"]
            else:
                resultado["status"] = "erro"
                resultado["erro"] = "Falha na verificação de integridade"
                
        except Exception as e:
            resultado["erro"] = str(e)
            self.logger.error(f"Erro ao sincronizar {arquivo_origem}: {e}")
            
        return resultado
        
    def sincronizar_todos(self, forcar_sobrescrita: bool = False) -> Dict:
        """Sincroniza todos os arquivos PDF do diretório origem"""
        self.logger.info(f"Iniciando sincronização: {self.origem} → {self.destino}")
        
        inicio = datetime.now()
        arquivos_origem = self.listar_arquivos_pdf(self.origem)
        
        resultados = {
            "inicio": inicio.isoformat(),
            "origem": str(self.origem),
            "destino": str(self.destino),
            "total_arquivos": len(arquivos_origem),
            "sucessos": 0,
            "erros": 0,
            "ja_sincronizados": 0,
            "copiados": 0,
            "sobrescritos": 0,
            "ignorados": 0,
            "tamanho_total": 0,
            "detalhes": [],
            "tempo_execucao": 0
        }
        
        if not arquivos_origem:
            self.logger.warning(f"Nenhum arquivo PDF encontrado em {self.origem}")
            resultados["fim"] = datetime.now().isoformat()
            return resultados
            
        for arquivo in arquivos_origem:
            resultado = self.sincronizar_arquivo(arquivo, forcar_sobrescrita)
            resultados["detalhes"].append(resultado)
            resultados["tamanho_total"] += resultado["tamanho"]
            
            if resultado["status"] == "sucesso":
                resultados["sucessos"] += 1
                
                if resultado["acao"] == "ja_sincronizado":
                    resultados["ja_sincronizados"] += 1
                elif resultado["acao"] == "copiado":
                    resultados["copiados"] += 1
                elif resultado["acao"] == "sobrescrito":
                    resultados["sobrescritos"] += 1
                elif resultado["acao"] == "ignorado_duplicata":
                    resultados["ignorados"] += 1
            else:
                resultados["erros"] += 1
                
            self.logger.info(f"📄 {arquivo.name}: {resultado['acao']} ({resultado['status']})")
            
        fim = datetime.now()
        resultados["fim"] = fim.isoformat()
        resultados["tempo_execucao"] = (fim - inicio).total_seconds()
        
        # Log resumo
        self.logger.info(f"✅ Sincronização concluída:")
        self.logger.info(f"   📊 Total: {resultados['total_arquivos']} arquivos")
        self.logger.info(f"   ✅ Sucessos: {resultados['sucessos']}")
        self.logger.info(f"   ❌ Erros: {resultados['erros']}")
        self.logger.info(f"   📄 Copiados: {resultados['copiados']}")
        self.logger.info(f"   🔄 Sobrescritos: {resultados['sobrescritos']}")
        self.logger.info(f"   ⏱️ Tempo: {resultados['tempo_execucao']:.2f}s")
        
        return resultados
        
    def limpar_arquivos_antigos(self, dias: int = 30) -> int:
        """Remove arquivos de backup mais antigos que X dias"""
        if not self.backup_dir or not self.backup_dir.exists():
            return 0
            
        limite = datetime.now() - timedelta(days=dias)
        arquivos_removidos = 0
        
        for arquivo in self.backup_dir.glob("*.pdf"):
            try:
                if datetime.fromtimestamp(arquivo.stat().st_mtime) < limite:
                    arquivo.unlink()
                    arquivos_removidos += 1
                    self.logger.debug(f"Backup antigo removido: {arquivo}")
            except Exception as e:
                self.logger.error(f"Erro ao remover backup {arquivo}: {e}")
                
        if arquivos_removidos > 0:
            self.logger.info(f"🧹 Removidos {arquivos_removidos} backups antigos")
            
        return arquivos_removidos
        
    def gerar_relatorio_detalhado(self, resultados: Dict, arquivo_saida: Optional[Path] = None) -> Path:
        """Gera relatório detalhado da sincronização"""
        if not arquivo_saida:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            arquivo_saida = Path(f"relatorio_sincronizacao_{timestamp}.json")
            
        try:
            import json
            with open(arquivo_saida, 'w', encoding='utf-8') as f:
                json.dump(resultados, f, ensure_ascii=False, indent=2)
                
            self.logger.info(f"📋 Relatório salvo: {arquivo_saida}")
            return arquivo_saida
            
        except Exception as e:
            self.logger.error(f"Erro ao gerar relatório: {e}")
            return None
