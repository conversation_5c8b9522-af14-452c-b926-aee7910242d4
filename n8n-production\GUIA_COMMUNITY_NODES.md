# 🔌 GUIA COMPLETO - COMMUNITY NODES N8N

## 📋 NODES SOLICITADOS

### 1. n8n-nodes-mcp
**Descrição:** Model Context Protocol integration
**NPM:** https://www.npmjs.com/package/n8n-nodes-mcp
**Status:** ✅ Configurado no ambiente

**Instalação Manual (se necessário):**
```bash
# Dentro do container N8N
npm install n8n-nodes-mcp
```

**Como usar:**
1. Acesse N8N: http://localhost:5678
2. Crie novo workflow
3. Procure por "MCP" nos nodes
4. Configure conexão MCP

### 2. n8n-nodes-evolution-api
**Descrição:** Evolution API WhatsApp integration
**NPM:** https://www.npmjs.com/package/n8n-nodes-evolution-api
**Status:** ✅ Configurado no ambiente

**Instalação Manual (se necessário):**
```bash
# Dentro do container N8N
npm install n8n-nodes-evolution-api
```

**Como usar:**
1. Configure Evolution API server
2. No N8N, procure por "Evolution" nos nodes
3. Configure credenciais da API
4. Crie workflows WhatsApp

## 🚀 INSTALAÇÃO VIA INTERFACE N8N

### Método 1: Interface Gráfica (Recomendado)
1. **Acesse N8N:** http://localhost:5678
2. **Login:** Use as credenciais configuradas
3. **Vá para Settings:** Clique no ícone de engrenagem
4. **Community Nodes:** Selecione a aba "Community Nodes"
5. **Install Package:** Digite o nome do pacote
6. **Confirme:** Clique em "Install"

### Método 2: Via Docker Exec
```bash
# Acesse o container N8N
docker exec -it n8n-production-n8n-1 /bin/sh

# Instale o pacote
npm install n8n-nodes-mcp
npm install n8n-nodes-evolution-api

# Reinicie o N8N
exit
docker-compose restart n8n n8n-worker
```

## 📦 OUTROS COMMUNITY NODES RECOMENDADOS

### Comunicação
- `n8n-nodes-telegram` - Telegram Bot API
- `n8n-nodes-discord` - Discord integration
- `n8n-nodes-slack-enhanced` - Slack avançado
- `n8n-nodes-whatsapp-business` - WhatsApp Business

### IA e Machine Learning
- `n8n-nodes-openai` - OpenAI GPT integration
- `n8n-nodes-anthropic` - Claude AI
- `n8n-nodes-huggingface` - Hugging Face models
- `n8n-nodes-stability-ai` - Stable Diffusion

### Produtividade
- `n8n-nodes-google-sheets-advanced` - Google Sheets avançado
- `n8n-nodes-notion-enhanced` - Notion melhorado
- `n8n-nodes-airtable-plus` - Airtable avançado
- `n8n-nodes-microsoft-365` - Office 365

### Desenvolvimento
- `n8n-nodes-github-enhanced` - GitHub avançado
- `n8n-nodes-docker` - Docker management
- `n8n-nodes-kubernetes` - Kubernetes integration
- `n8n-nodes-aws-enhanced` - AWS services

### E-commerce
- `n8n-nodes-shopify-plus` - Shopify avançado
- `n8n-nodes-woocommerce` - WooCommerce
- `n8n-nodes-stripe-enhanced` - Stripe payments
- `n8n-nodes-mercadopago` - MercadoPago

## 🔧 TROUBLESHOOTING

### Problema: Node não aparece após instalação
**Solução:**
```bash
# Reinicie os serviços N8N
docker-compose restart n8n n8n-worker

# Aguarde 30 segundos e recarregue a página
```

### Problema: Erro de instalação
**Solução:**
```bash
# Verifique logs
docker-compose logs n8n

# Limpe cache npm
docker exec -it n8n-production-n8n-1 npm cache clean --force

# Tente instalar novamente
```

### Problema: Node instalado mas com erro
**Solução:**
1. Verifique compatibilidade com versão N8N
2. Consulte documentação do node
3. Verifique logs de erro no N8N

## 📚 DOCUMENTAÇÃO ÚTIL

### Links Oficiais
- **N8N Community Nodes:** https://docs.n8n.io/integrations/community-nodes/
- **NPM Registry:** https://www.npmjs.com/search?q=n8n-nodes
- **N8N Forum:** https://community.n8n.io/

### Desenvolvimento de Nodes
- **Criar Node:** https://docs.n8n.io/integrations/creating-nodes/
- **Template:** https://github.com/n8n-io/n8n-nodes-starter
- **Publicar:** https://docs.n8n.io/integrations/community-nodes/publishing/

## ✅ VERIFICAÇÃO DE INSTALAÇÃO

### Checklist Pós-Instalação
- [ ] Node aparece na lista de nodes
- [ ] Configuração de credenciais funciona
- [ ] Teste básico executado com sucesso
- [ ] Documentação do node consultada
- [ ] Workflow de exemplo criado

### Comandos de Verificação
```bash
# Listar nodes instalados
docker exec -it n8n-production-n8n-1 npm list | grep n8n-nodes

# Verificar status dos serviços
docker-compose ps

# Ver logs em tempo real
docker-compose logs -f n8n
```

---
**Ambiente configurado com community nodes habilitados! 🎉**
