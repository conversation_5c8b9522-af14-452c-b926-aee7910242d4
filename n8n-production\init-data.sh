#!/bin/bash
set -e

# Script de inicializacao do PostgreSQL para n8n
echo "Inicializando banco de dados PostgreSQL para n8n..."

# Criar usuario e banco de dados para n8n
psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" <<-EOSQL
    CREATE USER $POSTGRES_NON_ROOT_USER WITH PASSWORD '$POSTGRES_NON_ROOT_PASSWORD';
    GRANT ALL PRIVILEGES ON DATABASE $POSTGRES_DB TO $POSTGRES_NON_ROOT_USER;
    ALTER USER $POSTGRES_NON_ROOT_USER CREATEDB;
EOSQL

echo "Usuario $POSTGRES_NON_ROOT_USER criado com sucesso!"

# Criar banco de dados para Evolution API
echo "Criando banco de dados para Evolution API..."
psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" <<-EOSQL
    CREATE DATABASE evolution;
    GRANT ALL PRIVILEGES ON DATABASE evolution TO $POSTGRES_NON_ROOT_USER;
EOSQL

echo "Banco de dados 'evolution' criado com sucesso!"
