# IMPLEMENTAÇÃO PRÁTICA - GAPS WEBAGENT - CÓDIGO COMPLETO

## RESUMO EXECUTIVO

**Data**: 2025-01-24
**Versão**: 1.0
**Status**: Implementação Prática Completa

### OBJETIVO
Implementação prática e exemplos de código detalhados para correção dos gaps identificados na base de conhecimento WebAgent, incluindo:
- **Ferramentas de Mídia**: FFmpeg, Remotion, OpenCV
- **Frameworks de Agentes IA**: LangGraph, CrewAI, AutoGen, Gemini SDK
- **MCP Tools Especializados**: Servidores e integrações avançadas

---

## 1. FERRAMENTAS DE MÍDIA - IMPLEMENTAÇÃO COMPLETA

### 1.1 FFmpeg-Python - Sistema de Processamento de Vídeo

#### Instalação e Configuração
```bash
# Instalação FFmpeg
sudo apt update
sudo apt install ffmpeg

# Instalação Python wrapper
pip install ffmpeg-python imageio-ffmpeg

# Verificação da instalação
ffmpeg -version
```

#### Classe Principal de Processamento
```python
import ffmpeg
import os
import json
from typing import Dict, List, Optional
from pathlib import Path

class ViralVideoProcessor:
    """Processador avançado de vídeos para análise viral"""

    def __init__(self, work_dir: str = "./video_processing"):
        self.work_dir = Path(work_dir)
        self.work_dir.mkdir(exist_ok=True)

    def extract_video_metadata(self, video_path: str) -> Dict:
        """Extrai metadados completos do vídeo"""
        try:
            probe = ffmpeg.probe(video_path)
            video_info = next(s for s in probe['streams'] if s['codec_type'] == 'video')
            audio_info = next((s for s in probe['streams'] if s['codec_type'] == 'audio'), None)

            return {
                'duration': float(probe['format']['duration']),
                'size': int(probe['format']['size']),
                'bitrate': int(probe['format']['bit_rate']),
                'video': {
                    'codec': video_info['codec_name'],
                    'width': int(video_info['width']),
                    'height': int(video_info['height']),
                    'fps': eval(video_info['r_frame_rate']),
                    'bitrate': int(video_info.get('bit_rate', 0))
                },
                'audio': {
                    'codec': audio_info['codec_name'] if audio_info else None,
                    'sample_rate': int(audio_info['sample_rate']) if audio_info else None,
                    'channels': int(audio_info['channels']) if audio_info else None
                } if audio_info else None
            }
        except Exception as e:
            raise Exception(f"Erro ao extrair metadados: {str(e)}")

    def extract_frames_for_analysis(self, video_path: str, fps: float = 1.0) -> List[str]:
        """Extrai frames em intervalos específicos para análise"""
        output_pattern = self.work_dir / f"frame_%04d.png"

        try:
            (
                ffmpeg
                .input(video_path)
                .filter('fps', fps=fps)
                .output(str(output_pattern))
                .overwrite_output()
                .run(capture_stdout=True, capture_stderr=True)
            )

            # Retorna lista de frames extraídos
            frames = sorted(self.work_dir.glob("frame_*.png"))
            return [str(frame) for frame in frames]

        except ffmpeg.Error as e:
            raise Exception(f"Erro na extração de frames: {e.stderr.decode()}")

    def extract_audio_for_analysis(self, video_path: str) -> str:
        """Extrai áudio para análise de sentimento e tendências"""
        output_path = self.work_dir / "extracted_audio.wav"

        try:
            (
                ffmpeg
                .input(video_path)
                .output(str(output_path), acodec='pcm_s16le', ac=1, ar='16000')
                .overwrite_output()
                .run(capture_stdout=True, capture_stderr=True)
            )

            return str(output_path)

        except ffmpeg.Error as e:
            raise Exception(f"Erro na extração de áudio: {e.stderr.decode()}")

    def create_viral_moments_compilation(self, video_path: str, moments: List[Dict]) -> str:
        """Cria compilação dos momentos mais virais do vídeo"""
        output_path = self.work_dir / "viral_moments.mp4"

        # Criar lista de segmentos para concatenação
        segments = []
        for i, moment in enumerate(moments):
            segment_path = self.work_dir / f"segment_{i}.mp4"

            # Extrair segmento específico
            (
                ffmpeg
                .input(video_path, ss=moment['start_time'], t=moment['duration'])
                .output(str(segment_path), vcodec='libx264', acodec='aac')
                .overwrite_output()
                .run(capture_stdout=True, capture_stderr=True)
            )

            segments.append(str(segment_path))

        # Concatenar segmentos
        if segments:
            inputs = [ffmpeg.input(segment) for segment in segments]
            (
                ffmpeg
                .concat(*inputs, v=1, a=1)
                .output(str(output_path), vcodec='libx264', acodec='aac')
                .overwrite_output()
                .run(capture_stdout=True, capture_stderr=True)
            )

        return str(output_path)

    def optimize_for_platform(self, video_path: str, platform: str) -> str:
        """Otimiza vídeo para plataformas específicas"""
        platform_configs = {
            'tiktok': {
                'size': '720x1280',
                'fps': 30,
                'bitrate': '1500k',
                'duration_limit': 60
            },
            'instagram': {
                'size': '1080x1080',
                'fps': 30,
                'bitrate': '2000k',
                'duration_limit': 60
            },
            'youtube': {
                'size': '1920x1080',
                'fps': 30,
                'bitrate': '5000k',
                'duration_limit': None
            },
            'twitter': {
                'size': '1280x720',
                'fps': 30,
                'bitrate': '2000k',
                'duration_limit': 140
            }
        }

        config = platform_configs.get(platform.lower())
        if not config:
            raise ValueError(f"Plataforma {platform} não suportada")

        output_path = self.work_dir / f"optimized_{platform}.mp4"

        stream = ffmpeg.input(video_path)

        # Aplicar limitação de duração se necessário
        if config['duration_limit']:
            stream = stream.filter('trim', duration=config['duration_limit'])

        # Aplicar filtros de otimização
        stream = (
            stream
            .filter('scale', config['size'])
            .filter('fps', fps=config['fps'])
        )

        (
            stream
            .output(
                str(output_path),
                vcodec='libx264',
                acodec='aac',
                video_bitrate=config['bitrate'],
                audio_bitrate='128k'
            )
            .overwrite_output()
            .run(capture_stdout=True, capture_stderr=True)
        )

        return str(output_path)

# Exemplo de uso
if __name__ == "__main__":
    processor = ViralVideoProcessor()

    # Processar vídeo viral
    video_path = "viral_video.mp4"

    # Extrair metadados
    metadata = processor.extract_video_metadata(video_path)
    print(f"Metadados: {json.dumps(metadata, indent=2)}")

    # Extrair frames para análise
    frames = processor.extract_frames_for_analysis(video_path, fps=0.5)
    print(f"Frames extraídos: {len(frames)}")

    # Extrair áudio
    audio_path = processor.extract_audio_for_analysis(video_path)
    print(f"Áudio extraído: {audio_path}")

    # Otimizar para TikTok
    tiktok_video = processor.optimize_for_platform(video_path, 'tiktok')
    print(f"Vídeo otimizado para TikTok: {tiktok_video}")
```

### 1.2 OpenCV - Sistema de Análise Visual Avançada

#### Classe de Análise Visual Completa
```python
import cv2
import numpy as np
from typing import Dict, List, Tuple, Optional
import json
from pathlib import Path

class ViralVisualAnalyzer:
    """Analisador visual avançado para conteúdo viral"""

    def __init__(self):
        # Carregar modelos pré-treinados
        self.face_cascade = cv2.CascadeClassifier(
            cv2.data.haarcascades + 'haarcascade_frontalface_default.xml'
        )
        self.eye_cascade = cv2.CascadeClassifier(
            cv2.data.haarcascades + 'haarcascade_eye.xml'
        )

        # Configurações para detecção de movimento
        self.bg_subtractor = cv2.createBackgroundSubtractorMOG2(
            detectShadows=True
        )

    def analyze_face_engagement(self, image_path: str) -> Dict:
        """Analisa engajamento baseado em detecção facial"""
        img = cv2.imread(image_path)
        if img is None:
            raise ValueError(f"Não foi possível carregar a imagem: {image_path}")

        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

        # Detectar faces
        faces = self.face_cascade.detectMultiScale(
            gray, scaleFactor=1.1, minNeighbors=5, minSize=(30, 30)
        )

        face_analysis = []
        for (x, y, w, h) in faces:
            face_roi = gray[y:y+h, x:x+w]

            # Detectar olhos na face
            eyes = self.eye_cascade.detectMultiScale(face_roi)

            # Calcular área da face em relação à imagem
            face_area_ratio = (w * h) / (img.shape[0] * img.shape[1])

            # Análise de posicionamento (regra dos terços)
            center_x, center_y = x + w//2, y + h//2
            img_h, img_w = img.shape[:2]

            # Verificar se está nos pontos de interesse (regra dos terços)
            third_x, third_y = img_w // 3, img_h // 3
            is_in_sweet_spot = (
                third_x <= center_x <= 2*third_x and
                third_y <= center_y <= 2*third_y
            )

            face_analysis.append({
                'position': {'x': int(x), 'y': int(y), 'width': int(w), 'height': int(h)},
                'area_ratio': float(face_area_ratio),
                'eyes_detected': len(eyes),
                'in_sweet_spot': is_in_sweet_spot,
                'engagement_score': self._calculate_face_engagement_score(
                    face_area_ratio, len(eyes), is_in_sweet_spot
                )
            })

        return {
            'total_faces': len(faces),
            'faces': face_analysis,
            'overall_engagement': np.mean([f['engagement_score'] for f in face_analysis]) if face_analysis else 0
        }

    def analyze_color_psychology(self, image_path: str) -> Dict:
        """Analisa psicologia das cores para viralidade"""
        img = cv2.imread(image_path)
        if img is None:
            raise ValueError(f"Não foi possível carregar a imagem: {image_path}")

        # Converter para HSV para melhor análise de cores
        hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)

        # Definir ranges de cores virais
        viral_colors = {
            'red': ([0, 50, 50], [10, 255, 255]),      # Urgência, paixão
            'orange': ([11, 50, 50], [25, 255, 255]),  # Energia, entusiasmo
            'yellow': ([26, 50, 50], [35, 255, 255]),  # Felicidade, atenção
            'green': ([36, 50, 50], [85, 255, 255]),   # Natureza, crescimento
            'blue': ([86, 50, 50], [125, 255, 255]),   # Confiança, calma
            'purple': ([126, 50, 50], [145, 255, 255]) # Criatividade, luxo
        }

        color_analysis = {}
        total_pixels = img.shape[0] * img.shape[1]

        for color_name, (lower, upper) in viral_colors.items():
            lower = np.array(lower)
            upper = np.array(upper)

            # Criar máscara para a cor
            mask = cv2.inRange(hsv, lower, upper)
            color_pixels = cv2.countNonZero(mask)

            color_percentage = (color_pixels / total_pixels) * 100

            color_analysis[color_name] = {
                'percentage': float(color_percentage),
                'viral_impact': self._get_color_viral_impact(color_name, color_percentage)
            }

        # Calcular contraste geral
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        contrast = gray.std()

        return {
            'colors': color_analysis,
            'contrast_score': float(contrast),
            'dominant_colors': sorted(
                color_analysis.items(),
                key=lambda x: x[1]['percentage'],
                reverse=True
            )[:3],
            'viral_color_score': sum(c['viral_impact'] for c in color_analysis.values())
        }

    def analyze_composition_rules(self, image_path: str) -> Dict:
        """Analisa regras de composição visual"""
        img = cv2.imread(image_path)
        if img is None:
            raise ValueError(f"Não foi possível carregar a imagem: {image_path}")

        h, w = img.shape[:2]
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

        # Análise da regra dos terços
        third_lines = {
            'vertical': [w//3, 2*w//3],
            'horizontal': [h//3, 2*h//3]
        }

        # Detectar pontos de interesse usando SIFT
        sift = cv2.SIFT_create()
        keypoints = sift.detect(gray, None)

        # Verificar quantos pontos estão nas linhas dos terços
        points_on_thirds = 0
        for kp in keypoints:
            x, y = int(kp.pt[0]), int(kp.pt[1])

            # Verificar proximidade com linhas dos terços (tolerância de 20 pixels)
            tolerance = 20
            on_vertical = any(abs(x - line) <= tolerance for line in third_lines['vertical'])
            on_horizontal = any(abs(y - line) <= tolerance for line in third_lines['horizontal'])

            if on_vertical or on_horizontal:
                points_on_thirds += 1

        # Análise de simetria
        left_half = gray[:, :w//2]
        right_half = cv2.flip(gray[:, w//2:], 1)

        # Redimensionar se necessário
        min_width = min(left_half.shape[1], right_half.shape[1])
        left_half = left_half[:, :min_width]
        right_half = right_half[:, :min_width]

        symmetry_score = cv2.matchTemplate(left_half, right_half, cv2.TM_CCOEFF_NORMED)[0][0]

        # Análise de leading lines usando detecção de bordas
        edges = cv2.Canny(gray, 50, 150)
        lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=100, minLineLength=50, maxLineGap=10)

        leading_lines_score = 0
        if lines is not None:
            # Calcular direcionamento das linhas
            for line in lines:
                x1, y1, x2, y2 = line[0]

                # Verificar se a linha aponta para pontos de interesse
                line_angle = np.arctan2(y2-y1, x2-x1) * 180 / np.pi

                # Linhas diagonais são mais interessantes visualmente
                if 30 <= abs(line_angle) <= 60 or 120 <= abs(line_angle) <= 150:
                    leading_lines_score += 1

        return {
            'rule_of_thirds': {
                'total_keypoints': len(keypoints),
                'points_on_thirds': points_on_thirds,
                'thirds_ratio': points_on_thirds / len(keypoints) if keypoints else 0
            },
            'symmetry_score': float(symmetry_score),
            'leading_lines': {
                'total_lines': len(lines) if lines is not None else 0,
                'strong_lines': leading_lines_score,
                'composition_score': leading_lines_score / 10  # Normalizado
            },
            'overall_composition_score': self._calculate_composition_score(
                points_on_thirds / len(keypoints) if keypoints else 0,
                symmetry_score,
                leading_lines_score / 10
            )
        }

    def _calculate_face_engagement_score(self, area_ratio: float, eyes: int, sweet_spot: bool) -> float:
        """Calcula score de engajamento facial"""
        score = 0

        # Área da face (faces maiores = mais engajamento)
        if area_ratio > 0.1:
            score += 30
        elif area_ratio > 0.05:
            score += 20
        elif area_ratio > 0.02:
            score += 10

        # Olhos detectados (contato visual)
        if eyes >= 2:
            score += 25
        elif eyes == 1:
            score += 15

        # Posicionamento (regra dos terços)
        if sweet_spot:
            score += 20

        # Bonus por face bem posicionada e visível
        if area_ratio > 0.05 and eyes >= 2 and sweet_spot:
            score += 25

        return min(score, 100)  # Máximo 100

    def _get_color_viral_impact(self, color: str, percentage: float) -> float:
        """Calcula impacto viral da cor"""
        viral_weights = {
            'red': 1.2,      # Alto impacto emocional
            'orange': 1.1,   # Energia e entusiasmo
            'yellow': 1.0,   # Atenção e felicidade
            'blue': 0.8,     # Calma, menos viral
            'green': 0.9,    # Natural, moderado
            'purple': 1.1    # Criatividade, luxo
        }

        weight = viral_weights.get(color, 0.5)

        # Percentagem ideal entre 10-40% para máximo impacto
        if 10 <= percentage <= 40:
            return percentage * weight
        elif percentage > 40:
            return (40 - (percentage - 40) * 0.5) * weight
        else:
            return percentage * weight * 0.5

    def _calculate_composition_score(self, thirds_ratio: float, symmetry: float, lines: float) -> float:
        """Calcula score geral de composição"""
        # Pesos para diferentes aspectos
        thirds_weight = 0.4
        symmetry_weight = 0.3
        lines_weight = 0.3

        score = (
            thirds_ratio * thirds_weight * 100 +
            abs(symmetry) * symmetry_weight * 100 +
            lines * lines_weight * 100
        )

        return min(score, 100)

# Exemplo de uso
if __name__ == "__main__":
    analyzer = ViralVisualAnalyzer()

    image_path = "viral_image.jpg"

    # Análise facial
    face_analysis = analyzer.analyze_face_engagement(image_path)
    print(f"Análise Facial: {json.dumps(face_analysis, indent=2)}")

    # Análise de cores
    color_analysis = analyzer.analyze_color_psychology(image_path)
    print(f"Análise de Cores: {json.dumps(color_analysis, indent=2)}")

    # Análise de composição
    composition_analysis = analyzer.analyze_composition_rules(image_path)
    print(f"Análise de Composição: {json.dumps(composition_analysis, indent=2)}")
```

---

## 2. FRAMEWORKS DE AGENTES IA - IMPLEMENTAÇÃO AVANÇADA

### 2.1 LangGraph - Sistema de Workflows Estatais

#### Instalação e Configuração
```bash
pip install langgraph langchain langchain-openai
```

#### Implementação de Agente Viral Completo
```python
from langgraph.graph import StateGraph, END
from langgraph.prebuilt import ToolExecutor
from typing import TypedDict, List, Dict, Any
import json
from datetime import datetime

class ViralAnalysisState(TypedDict):
    """Estado compartilhado entre nós do grafo"""
    input_content: Dict[str, Any]
    extracted_data: Dict[str, Any]
    sentiment_analysis: Dict[str, Any]
    visual_analysis: Dict[str, Any]
    trend_prediction: Dict[str, Any]
    final_report: Dict[str, Any]
    current_step: str
    errors: List[str]
    metadata: Dict[str, Any]

class ViralContentWorkflow:
    """Workflow completo de análise de conteúdo viral usando LangGraph"""

    def __init__(self):
        self.graph = self._build_workflow_graph()

    def _build_workflow_graph(self) -> StateGraph:
        """Constrói o grafo de workflow"""
        workflow = StateGraph(ViralAnalysisState)

        # Adicionar nós
        workflow.add_node("content_extractor", self.extract_content)
        workflow.add_node("sentiment_analyzer", self.analyze_sentiment)
        workflow.add_node("visual_analyzer", self.analyze_visual_elements)
        workflow.add_node("trend_predictor", self.predict_trends)
        workflow.add_node("report_generator", self.generate_final_report)
        workflow.add_node("error_handler", self.handle_errors)

        # Definir fluxo
        workflow.set_entry_point("content_extractor")

        # Fluxo principal
        workflow.add_edge("content_extractor", "sentiment_analyzer")
        workflow.add_edge("sentiment_analyzer", "visual_analyzer")
        workflow.add_edge("visual_analyzer", "trend_predictor")
        workflow.add_edge("trend_predictor", "report_generator")
        workflow.add_edge("report_generator", END)

        # Fluxo de erro
        workflow.add_conditional_edges(
            "content_extractor",
            self._should_handle_error,
            {
                "error": "error_handler",
                "continue": "sentiment_analyzer"
            }
        )

        return workflow.compile()

    def extract_content(self, state: ViralAnalysisState) -> ViralAnalysisState:
        """Extrai conteúdo de múltiplas fontes"""
        try:
            content = state["input_content"]

            extracted_data = {
                "timestamp": datetime.now().isoformat(),
                "source_platform": content.get("platform", "unknown"),
                "content_type": content.get("type", "unknown"),
                "raw_data": content.get("data", {}),
                "metadata": {
                    "extraction_method": "automated",
                    "quality_score": self._calculate_extraction_quality(content)
                }
            }

            # Processamento específico por tipo de conteúdo
            if content.get("type") == "video":
                extracted_data.update(self._extract_video_features(content))
            elif content.get("type") == "image":
                extracted_data.update(self._extract_image_features(content))
            elif content.get("type") == "text":
                extracted_data.update(self._extract_text_features(content))

            state["extracted_data"] = extracted_data
            state["current_step"] = "content_extraction_complete"

        except Exception as e:
            state["errors"].append(f"Content extraction error: {str(e)}")
            state["current_step"] = "content_extraction_failed"

        return state

    def analyze_sentiment(self, state: ViralAnalysisState) -> ViralAnalysisState:
        """Análise avançada de sentimento"""
        try:
            extracted_data = state["extracted_data"]

            sentiment_analysis = {
                "overall_sentiment": self._calculate_overall_sentiment(extracted_data),
                "emotion_breakdown": self._analyze_emotions(extracted_data),
                "engagement_indicators": self._identify_engagement_patterns(extracted_data),
                "viral_sentiment_score": 0,
                "confidence_level": 0
            }

            # Calcular score viral baseado em sentimento
            sentiment_analysis["viral_sentiment_score"] = self._calculate_viral_sentiment_score(
                sentiment_analysis
            )

            state["sentiment_analysis"] = sentiment_analysis
            state["current_step"] = "sentiment_analysis_complete"

        except Exception as e:
            state["errors"].append(f"Sentiment analysis error: {str(e)}")
            state["current_step"] = "sentiment_analysis_failed"

        return state

    def analyze_visual_elements(self, state: ViralAnalysisState) -> ViralAnalysisState:
        """Análise de elementos visuais"""
        try:
            extracted_data = state["extracted_data"]

            visual_analysis = {
                "color_psychology": self._analyze_color_impact(extracted_data),
                "composition_score": self._analyze_composition(extracted_data),
                "face_detection": self._analyze_faces(extracted_data),
                "object_detection": self._detect_viral_objects(extracted_data),
                "aesthetic_score": 0,
                "viral_visual_score": 0
            }

            # Calcular scores visuais
            visual_analysis["aesthetic_score"] = self._calculate_aesthetic_score(visual_analysis)
            visual_analysis["viral_visual_score"] = self._calculate_viral_visual_score(visual_analysis)

            state["visual_analysis"] = visual_analysis
            state["current_step"] = "visual_analysis_complete"

        except Exception as e:
            state["errors"].append(f"Visual analysis error: {str(e)}")
            state["current_step"] = "visual_analysis_failed"

        return state

    def predict_trends(self, state: ViralAnalysisState) -> ViralAnalysisState:
        """Predição de tendências baseada em análises anteriores"""
        try:
            sentiment = state["sentiment_analysis"]
            visual = state["visual_analysis"]
            extracted = state["extracted_data"]

            trend_prediction = {
                "viral_probability": self._calculate_viral_probability(sentiment, visual, extracted),
                "trend_alignment": self._analyze_trend_alignment(extracted),
                "platform_optimization": self._suggest_platform_optimization(extracted),
                "timing_recommendations": self._analyze_optimal_timing(extracted),
                "content_improvements": self._suggest_improvements(sentiment, visual),
                "predicted_reach": self._predict_reach(sentiment, visual, extracted),
                "confidence_interval": [0, 0]
            }

            # Calcular intervalo de confiança
            trend_prediction["confidence_interval"] = self._calculate_confidence_interval(
                trend_prediction["viral_probability"]
            )

            state["trend_prediction"] = trend_prediction
            state["current_step"] = "trend_prediction_complete"

        except Exception as e:
            state["errors"].append(f"Trend prediction error: {str(e)}")
            state["current_step"] = "trend_prediction_failed"

        return state

    def generate_final_report(self, state: ViralAnalysisState) -> ViralAnalysisState:
        """Gera relatório final consolidado"""
        try:
            final_report = {
                "analysis_summary": {
                    "overall_viral_score": self._calculate_overall_viral_score(state),
                    "key_strengths": self._identify_key_strengths(state),
                    "improvement_areas": self._identify_improvement_areas(state),
                    "recommendation_priority": self._prioritize_recommendations(state)
                },
                "detailed_metrics": {
                    "sentiment_metrics": state["sentiment_analysis"],
                    "visual_metrics": state["visual_analysis"],
                    "trend_metrics": state["trend_prediction"]
                },
                "actionable_insights": self._generate_actionable_insights(state),
                "next_steps": self._suggest_next_steps(state),
                "metadata": {
                    "analysis_timestamp": datetime.now().isoformat(),
                    "processing_time": self._calculate_processing_time(state),
                    "confidence_level": self._calculate_overall_confidence(state)
                }
            }

            state["final_report"] = final_report
            state["current_step"] = "analysis_complete"

        except Exception as e:
            state["errors"].append(f"Report generation error: {str(e)}")
            state["current_step"] = "report_generation_failed"

        return state

    def handle_errors(self, state: ViralAnalysisState) -> ViralAnalysisState:
        """Manipula erros e tenta recuperação"""
        errors = state.get("errors", [])

        error_report = {
            "error_count": len(errors),
            "error_details": errors,
            "recovery_attempts": [],
            "partial_results": self._extract_partial_results(state)
        }

        # Tentar recuperação baseada no tipo de erro
        for error in errors:
            if "extraction" in error.lower():
                recovery = self._attempt_extraction_recovery(state)
                error_report["recovery_attempts"].append(recovery)
            elif "sentiment" in error.lower():
                recovery = self._attempt_sentiment_recovery(state)
                error_report["recovery_attempts"].append(recovery)

        state["final_report"] = {
            "status": "partial_analysis",
            "error_report": error_report,
            "available_results": error_report["partial_results"]
        }

        return state

    def _should_handle_error(self, state: ViralAnalysisState) -> str:
        """Determina se deve ir para tratamento de erro"""
        return "error" if state.get("errors") else "continue"

    # Métodos auxiliares de análise
    def _calculate_extraction_quality(self, content: Dict) -> float:
        """Calcula qualidade da extração"""
        quality_factors = {
            "has_metadata": bool(content.get("metadata")),
            "has_content": bool(content.get("data")),
            "platform_identified": bool(content.get("platform")),
            "type_identified": bool(content.get("type"))
        }

        return sum(quality_factors.values()) / len(quality_factors)

    def _extract_video_features(self, content: Dict) -> Dict:
        """Extrai características específicas de vídeo"""
        return {
            "duration": content.get("duration", 0),
            "resolution": content.get("resolution", "unknown"),
            "fps": content.get("fps", 0),
            "audio_present": content.get("has_audio", False),
            "thumbnail_quality": self._assess_thumbnail_quality(content)
        }

    def _extract_image_features(self, content: Dict) -> Dict:
        """Extrai características específicas de imagem"""
        return {
            "dimensions": content.get("dimensions", {}),
            "file_size": content.get("file_size", 0),
            "format": content.get("format", "unknown"),
            "quality_assessment": self._assess_image_quality(content)
        }

    def _extract_text_features(self, content: Dict) -> Dict:
        """Extrai características específicas de texto"""
        text = content.get("text", "")
        return {
            "word_count": len(text.split()),
            "character_count": len(text),
            "hashtag_count": text.count("#"),
            "mention_count": text.count("@"),
            "emoji_count": self._count_emojis(text),
            "readability_score": self._calculate_readability(text)
        }

    def _calculate_overall_sentiment(self, data: Dict) -> Dict:
        """Calcula sentimento geral"""
        # Implementação simplificada - em produção usaria modelos avançados
        return {
            "polarity": 0.5,  # -1 a 1
            "subjectivity": 0.5,  # 0 a 1
            "dominant_emotion": "neutral"
        }

    def _analyze_emotions(self, data: Dict) -> Dict:
        """Analisa breakdown de emoções"""
        return {
            "joy": 0.2,
            "anger": 0.1,
            "fear": 0.05,
            "sadness": 0.1,
            "surprise": 0.3,
            "disgust": 0.05,
            "trust": 0.2
        }

    def _calculate_viral_probability(self, sentiment: Dict, visual: Dict, extracted: Dict) -> float:
        """Calcula probabilidade viral baseada em múltiplos fatores"""
        sentiment_score = sentiment.get("viral_sentiment_score", 0)
        visual_score = visual.get("viral_visual_score", 0)

        # Pesos para diferentes fatores
        weights = {
            "sentiment": 0.3,
            "visual": 0.4,
            "timing": 0.1,
            "platform": 0.2
        }

        probability = (
            sentiment_score * weights["sentiment"] +
            visual_score * weights["visual"] +
            0.5 * weights["timing"] +  # Placeholder
            0.5 * weights["platform"]  # Placeholder
        )

        return min(max(probability, 0), 1)

    def _calculate_overall_viral_score(self, state: ViralAnalysisState) -> float:
        """Calcula score viral geral"""
        scores = []

        if "sentiment_analysis" in state:
            scores.append(state["sentiment_analysis"].get("viral_sentiment_score", 0))

        if "visual_analysis" in state:
            scores.append(state["visual_analysis"].get("viral_visual_score", 0))

        if "trend_prediction" in state:
            scores.append(state["trend_prediction"].get("viral_probability", 0))

        return sum(scores) / len(scores) if scores else 0

    # Métodos auxiliares adicionais (implementação simplificada)
    def _assess_thumbnail_quality(self, content: Dict) -> float:
        return 0.7  # Placeholder

    def _assess_image_quality(self, content: Dict) -> float:
        return 0.8  # Placeholder

    def _count_emojis(self, text: str) -> int:
        # Implementação simplificada
        return sum(1 for char in text if ord(char) > 127)

    def _calculate_readability(self, text: str) -> float:
        # Implementação simplificada do Flesch Reading Ease
        words = len(text.split())
        sentences = text.count('.') + text.count('!') + text.count('?')
        if sentences == 0:
            sentences = 1

        avg_sentence_length = words / sentences
        return max(0, min(100, 206.835 - (1.015 * avg_sentence_length)))

# Exemplo de uso
if __name__ == "__main__":
    workflow = ViralContentWorkflow()

    # Estado inicial
    initial_state = {
        "input_content": {
            "platform": "tiktok",
            "type": "video",
            "data": {
                "url": "https://example.com/video.mp4",
                "duration": 30,
                "resolution": "1080x1920"
            }
        },
        "extracted_data": {},
        "sentiment_analysis": {},
        "visual_analysis": {},
        "trend_prediction": {},
        "final_report": {},
        "current_step": "initialized",
        "errors": [],
        "metadata": {"start_time": datetime.now().isoformat()}
    }

    # Executar workflow
    result = workflow.graph.invoke(initial_state)

    print("Análise Completa:")
    print(json.dumps(result["final_report"], indent=2))
```