# 🔌 APIs E ENDPOINTS CUSTOMIZADOS - SISTEMA WEBAGENT

**Data:** 2025-01-24  
**Versão:** v1.0 - APIs REST Customizadas e Endpoints Especializados  
**Autor:** Augment Code Orchestrator V5.0  
**Projeto:** WebAgent (Supabase ID: nnpxxdyhamnsxqljumun)  
**Escopo:** APIs REST customizadas e endpoints especializados para extração viral  

---

## 🎯 EXECUTIVE SUMMARY

Este documento especifica **APIs REST customizadas** e **endpoints especializados** para o sistema de extração viral WebAgent. As APIs são construídas sobre **PostgREST** e **Edge Functions**, oferecendo endpoints otimizados para análise de conteúdo viral, relatórios executivos e integração com ferramentas externas.

### PRINCIPAIS COMPONENTES:

**1. APIS POSTGREST CUSTOMIZADAS:**
- **Viral Content API** - Endpoints para busca e análise de conteúdo
- **Analytics API** - Métricas e relatórios em tempo real
- **Trends API** - Análise de tendências e predições
- **Authors API** - Analytics de criadores e influenciadores

**2. EDGE FUNCTIONS ESPECIALIZADAS:**
- **Extraction Orchestrator** - Coordenação de extrações
- **Real-time Analytics** - Processamento em tempo real
- **Report Generator** - Geração de relatórios customizados
- **Webhook Handler** - Integração com sistemas externos

**3. ENDPOINTS DE INTEGRAÇÃO:**
- **Social Media Connectors** - APIs para plataformas sociais
- **Export APIs** - Exportação de dados em múltiplos formatos
- **Webhook Endpoints** - Notificações e eventos
- **Health Check APIs** - Monitoramento de sistema

**4. AUTENTICAÇÃO E SEGURANÇA:**
- **JWT Authentication** - Tokens seguros
- **Rate Limiting** - Controle de uso
- **API Keys** - Acesso programático
- **CORS Configuration** - Segurança de origem

---

## 🔗 APIS POSTGREST CUSTOMIZADAS

### VIRAL CONTENT API:

```sql
-- =====================================================
-- VIEW: API VIRAL CONTENT
-- =====================================================

CREATE OR REPLACE VIEW public.api_viral_content AS
SELECT 
    vc.id,
    vc.platform,
    vc.content_type,
    vc.external_id,
    vc.url,
    vc.title,
    vc.description,
    vc.author_username,
    vc.author_display_name,
    vc.author_followers_count,
    vc.author_verified,
    vc.content_text,
    vc.hashtags,
    vc.mentions,
    vc.media_urls,
    vc.engagement_metrics,
    vc.viral_score,
    vc.sentiment_score,
    vc.sentiment_label,
    vc.language_code,
    vc.content_category,
    vc.keywords_extracted,
    vc.published_at,
    vc.extracted_at,
    
    -- Métricas calculadas
    CASE 
        WHEN vc.viral_score > 50 THEN 'viral'
        WHEN vc.viral_score > 25 THEN 'trending'
        WHEN vc.viral_score > 10 THEN 'popular'
        ELSE 'normal'
    END as virality_level,
    
    -- Engagement rate
    CASE 
        WHEN vc.author_followers_count > 0 THEN
            ROUND(((vc.engagement_metrics->>'likes_count')::BIGINT + 
                   (vc.engagement_metrics->>'shares_count')::BIGINT + 
                   (vc.engagement_metrics->>'comments_count')::BIGINT)::DECIMAL / 
                   vc.author_followers_count * 100, 4)
        ELSE 0
    END as engagement_rate_percent,
    
    -- Tempo desde publicação
    EXTRACT(EPOCH FROM (NOW() - vc.published_at)) / 3600 as hours_since_published,
    
    -- Projeto associado
    p.name as project_name,
    p.user_id as project_owner
    
FROM public.viral_content vc
JOIN public.extractions e ON vc.extraction_id = e.id
JOIN public.projects p ON e.project_id = p.id;

-- RLS para API
ALTER VIEW public.api_viral_content OWNER TO postgres;
GRANT SELECT ON public.api_viral_content TO anon, authenticated;

-- =====================================================
-- FUNCTION: BUSCA AVANÇADA DE CONTEÚDO VIRAL
-- =====================================================

CREATE OR REPLACE FUNCTION public.search_viral_content(
    search_query TEXT DEFAULT NULL,
    platforms TEXT[] DEFAULT NULL,
    min_viral_score DECIMAL DEFAULT 0,
    max_results INTEGER DEFAULT 100,
    sort_by TEXT DEFAULT 'viral_score',
    sort_order TEXT DEFAULT 'desc',
    date_from TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    date_to TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    content_types TEXT[] DEFAULT NULL,
    languages TEXT[] DEFAULT NULL,
    verified_only BOOLEAN DEFAULT FALSE
)
RETURNS TABLE(
    id UUID,
    platform platform_type,
    content_type content_type,
    title TEXT,
    author_username VARCHAR,
    viral_score DECIMAL,
    engagement_rate_percent DECIMAL,
    published_at TIMESTAMP WITH TIME ZONE,
    url TEXT,
    hashtags TEXT[],
    sentiment_label VARCHAR
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        avc.id,
        avc.platform,
        avc.content_type,
        avc.title,
        avc.author_username,
        avc.viral_score,
        avc.engagement_rate_percent,
        avc.published_at,
        avc.url,
        avc.hashtags,
        avc.sentiment_label
    FROM public.api_viral_content avc
    WHERE 
        (search_query IS NULL OR (
            avc.title ILIKE '%' || search_query || '%' OR
            avc.description ILIKE '%' || search_query || '%' OR
            avc.content_text ILIKE '%' || search_query || '%' OR
            search_query = ANY(avc.hashtags) OR
            search_query = ANY(avc.keywords_extracted)
        ))
        AND (platforms IS NULL OR avc.platform = ANY(platforms))
        AND avc.viral_score >= min_viral_score
        AND (date_from IS NULL OR avc.published_at >= date_from)
        AND (date_to IS NULL OR avc.published_at <= date_to)
        AND (content_types IS NULL OR avc.content_type = ANY(content_types))
        AND (languages IS NULL OR avc.language_code = ANY(languages))
        AND (NOT verified_only OR avc.author_verified = TRUE)
    ORDER BY 
        CASE 
            WHEN sort_by = 'viral_score' AND sort_order = 'desc' THEN avc.viral_score
        END DESC,
        CASE 
            WHEN sort_by = 'viral_score' AND sort_order = 'asc' THEN avc.viral_score
        END ASC,
        CASE 
            WHEN sort_by = 'published_at' AND sort_order = 'desc' THEN avc.published_at
        END DESC,
        CASE 
            WHEN sort_by = 'published_at' AND sort_order = 'asc' THEN avc.published_at
        END ASC,
        CASE 
            WHEN sort_by = 'engagement_rate' AND sort_order = 'desc' THEN avc.engagement_rate_percent
        END DESC,
        CASE 
            WHEN sort_by = 'engagement_rate' AND sort_order = 'asc' THEN avc.engagement_rate_percent
        END ASC
    LIMIT max_results;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- ANALYTICS API
-- =====================================================

CREATE OR REPLACE VIEW public.api_analytics_summary AS
SELECT 
    p.id as project_id,
    p.name as project_name,
    p.user_id,
    
    -- Contadores básicos
    COUNT(DISTINCT e.id) as total_extractions,
    COUNT(DISTINCT vc.id) as total_content,
    COUNT(DISTINCT vc.platform) as platforms_used,
    COUNT(DISTINCT vc.author_username) as unique_authors,
    
    -- Métricas de viral score
    AVG(vc.viral_score) as avg_viral_score,
    MAX(vc.viral_score) as max_viral_score,
    COUNT(vc.id) FILTER (WHERE vc.viral_score > 50) as viral_content_count,
    COUNT(vc.id) FILTER (WHERE vc.viral_score > 25) as trending_content_count,
    
    -- Métricas de engajamento
    SUM((vc.engagement_metrics->>'likes_count')::BIGINT) as total_likes,
    SUM((vc.engagement_metrics->>'shares_count')::BIGINT) as total_shares,
    SUM((vc.engagement_metrics->>'comments_count')::BIGINT) as total_comments,
    SUM((vc.engagement_metrics->>'views_count')::BIGINT) as total_views,
    
    -- Distribuição de sentimento
    COUNT(vc.id) FILTER (WHERE vc.sentiment_label = 'positive') as positive_content,
    COUNT(vc.id) FILTER (WHERE vc.sentiment_label = 'negative') as negative_content,
    COUNT(vc.id) FILTER (WHERE vc.sentiment_label = 'neutral') as neutral_content,
    
    -- Métricas temporais
    MIN(vc.published_at) as earliest_content,
    MAX(vc.published_at) as latest_content,
    COUNT(vc.id) FILTER (WHERE vc.published_at >= NOW() - INTERVAL '24 hours') as content_last_24h,
    COUNT(vc.id) FILTER (WHERE vc.published_at >= NOW() - INTERVAL '7 days') as content_last_7d,
    
    -- Top hashtags
    (
        SELECT array_agg(hashtag ORDER BY count DESC)
        FROM (
            SELECT hashtag, COUNT(*) as count
            FROM public.viral_content vc2, unnest(vc2.hashtags) as hashtag
            WHERE vc2.extraction_id IN (
                SELECT e2.id FROM public.extractions e2 WHERE e2.project_id = p.id
            )
            GROUP BY hashtag
            ORDER BY count DESC
            LIMIT 10
        ) top_hashtags
    ) as top_hashtags,
    
    -- Última atualização
    MAX(vc.extracted_at) as last_updated
    
FROM public.projects p
LEFT JOIN public.extractions e ON p.id = e.project_id
LEFT JOIN public.viral_content vc ON e.id = vc.extraction_id
WHERE p.is_active = true
GROUP BY p.id, p.name, p.user_id;

-- RLS para analytics
GRANT SELECT ON public.api_analytics_summary TO anon, authenticated;

-- =====================================================
-- FUNCTION: RELATÓRIO DE PERFORMANCE POR PLATAFORMA
-- =====================================================

CREATE OR REPLACE FUNCTION public.get_platform_performance(
    project_uuid UUID,
    days_back INTEGER DEFAULT 30
)
RETURNS TABLE(
    platform platform_type,
    content_count BIGINT,
    avg_viral_score DECIMAL,
    total_engagement BIGINT,
    avg_engagement_per_content DECIMAL,
    top_content_id UUID,
    top_content_title TEXT,
    top_content_viral_score DECIMAL,
    growth_rate_percent DECIMAL
) AS $$
BEGIN
    RETURN QUERY
    WITH platform_stats AS (
        SELECT 
            vc.platform,
            COUNT(vc.id) as content_count,
            AVG(vc.viral_score) as avg_viral_score,
            SUM(
                (vc.engagement_metrics->>'likes_count')::BIGINT + 
                (vc.engagement_metrics->>'shares_count')::BIGINT + 
                (vc.engagement_metrics->>'comments_count')::BIGINT
            ) as total_engagement
        FROM public.viral_content vc
        JOIN public.extractions e ON vc.extraction_id = e.id
        WHERE e.project_id = project_uuid
          AND vc.published_at >= NOW() - (days_back || ' days')::INTERVAL
        GROUP BY vc.platform
    ),
    top_content AS (
        SELECT DISTINCT ON (vc.platform)
            vc.platform,
            vc.id as top_content_id,
            vc.title as top_content_title,
            vc.viral_score as top_content_viral_score
        FROM public.viral_content vc
        JOIN public.extractions e ON vc.extraction_id = e.id
        WHERE e.project_id = project_uuid
          AND vc.published_at >= NOW() - (days_back || ' days')::INTERVAL
        ORDER BY vc.platform, vc.viral_score DESC
    ),
    growth_stats AS (
        SELECT 
            vc.platform,
            COUNT(vc.id) FILTER (WHERE vc.published_at >= NOW() - INTERVAL '7 days') as recent_count,
            COUNT(vc.id) FILTER (WHERE vc.published_at >= NOW() - INTERVAL '14 days' 
                                   AND vc.published_at < NOW() - INTERVAL '7 days') as previous_count
        FROM public.viral_content vc
        JOIN public.extractions e ON vc.extraction_id = e.id
        WHERE e.project_id = project_uuid
          AND vc.published_at >= NOW() - INTERVAL '14 days'
        GROUP BY vc.platform
    )
    SELECT 
        ps.platform,
        ps.content_count,
        ROUND(ps.avg_viral_score::NUMERIC, 2),
        ps.total_engagement,
        ROUND((ps.total_engagement::DECIMAL / ps.content_count), 2),
        tc.top_content_id,
        tc.top_content_title,
        tc.top_content_viral_score,
        CASE 
            WHEN gs.previous_count > 0 THEN 
                ROUND(((gs.recent_count - gs.previous_count)::DECIMAL / gs.previous_count * 100), 2)
            ELSE NULL
        END as growth_rate_percent
    FROM platform_stats ps
    LEFT JOIN top_content tc ON ps.platform = tc.platform
    LEFT JOIN growth_stats gs ON ps.platform = gs.platform
    ORDER BY ps.avg_viral_score DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

---

## ⚡ EDGE FUNCTIONS ESPECIALIZADAS

### EXTRACTION ORCHESTRATOR:

```typescript
// supabase/functions/extraction-orchestrator/index.ts

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

interface ExtractionRequest {
  project_id: string
  platforms: string[]
  keywords: string[]
  hashtags?: string[]
  max_results_per_platform: number
  extraction_options: {
    include_comments: boolean
    include_transcripts: boolean
    sentiment_analysis: boolean
    download_media: boolean
    real_time_processing: boolean
  }
  scheduling?: {
    is_scheduled: boolean
    frequency?: 'hourly' | 'daily' | 'weekly'
    next_run?: string
  }
}

interface ExtractionResponse {
  extraction_id: string
  status: 'queued' | 'processing' | 'completed' | 'failed'
  platforms_queued: string[]
  estimated_completion: string
  webhook_url?: string
}

const SUPABASE_URL = Deno.env.get('SUPABASE_URL')!
const SUPABASE_SERVICE_KEY = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!

serve(async (req) => {
  try {
    if (req.method !== 'POST') {
      return new Response('Method not allowed', { status: 405 })
    }

    const extractionRequest: ExtractionRequest = await req.json()
    
    // Validações
    if (!extractionRequest.project_id || !extractionRequest.platforms || extractionRequest.platforms.length === 0) {
      return new Response('Missing required fields', { status: 400 })
    }

    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY)

    // Verificar se o projeto existe e o usuário tem permissão
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('id, name, user_id')
      .eq('id', extractionRequest.project_id)
      .single()

    if (projectError || !project) {
      return new Response('Project not found', { status: 404 })
    }

    // Criar registro de extração principal
    const { data: extraction, error: extractionError } = await supabase
      .from('extractions')
      .insert({
        project_id: extractionRequest.project_id,
        user_id: project.user_id,
        platform: 'multi', // Indicador de extração multi-plataforma
        query_params: {
          platforms: extractionRequest.platforms,
          keywords: extractionRequest.keywords,
          hashtags: extractionRequest.hashtags || [],
          max_results_per_platform: extractionRequest.max_results_per_platform,
          options: extractionRequest.extraction_options
        },
        status: 'pending'
      })
      .select()
      .single()

    if (extractionError) {
      throw new Error(`Failed to create extraction: ${extractionError.message}`)
    }

    // Criar sub-extrações para cada plataforma
    const platformExtractions = []
    for (const platform of extractionRequest.platforms) {
      const { data: platformExtraction } = await supabase
        .from('extractions')
        .insert({
          project_id: extractionRequest.project_id,
          user_id: project.user_id,
          platform: platform,
          query_params: {
            keywords: extractionRequest.keywords,
            hashtags: extractionRequest.hashtags || [],
            max_results: extractionRequest.max_results_per_platform,
            options: extractionRequest.extraction_options
          },
          status: 'queued',
          parent_extraction_id: extraction.id
        })
        .select()
        .single()

      if (platformExtraction) {
        platformExtractions.push(platformExtraction)
      }
    }

    // Agendar processamento se necessário
    if (extractionRequest.scheduling?.is_scheduled) {
      await scheduleRecurringExtraction(extraction.id, extractionRequest.scheduling, supabase)
    }

    // Iniciar processamento assíncrono
    await triggerExtractionProcessing(platformExtractions, extractionRequest.extraction_options)

    // Calcular tempo estimado de conclusão
    const estimatedMinutes = extractionRequest.platforms.length * 5 + 
                           (extractionRequest.max_results_per_platform / 100) * 2
    const estimatedCompletion = new Date(Date.now() + estimatedMinutes * 60 * 1000).toISOString()

    const response: ExtractionResponse = {
      extraction_id: extraction.id,
      status: 'queued',
      platforms_queued: extractionRequest.platforms,
      estimated_completion: estimatedCompletion,
      webhook_url: `${SUPABASE_URL}/functions/v1/webhook-handler/${extraction.id}`
    }

    return new Response(JSON.stringify(response), {
      headers: { 'Content-Type': 'application/json' }
    })

  } catch (error) {
    console.error('❌ Extraction orchestrator error:', error)
    
    return new Response(JSON.stringify({
      success: false,
      error: error.message
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    })
  }
})

async function scheduleRecurringExtraction(extractionId: string, scheduling: any, supabase: any) {
  // Implementar agendamento recorrente
  // Pode usar pg_cron ou sistema externo de agendamento
  console.log(`📅 Scheduling recurring extraction ${extractionId} with frequency ${scheduling.frequency}`)
}

async function triggerExtractionProcessing(platformExtractions: any[], options: any) {
  // Disparar processamento assíncrono para cada plataforma
  for (const extraction of platformExtractions) {
    // Chamar Edge Function específica da plataforma ou sistema externo
    console.log(`🚀 Triggering ${extraction.platform} extraction: ${extraction.id}`)
    
    // Exemplo: chamar função específica da plataforma
    await fetch(`${SUPABASE_URL}/functions/v1/extract-${extraction.platform}`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        extraction_id: extraction.id,
        query_params: extraction.query_params,
        options
      })
    })
  }
}
```

### REAL-TIME ANALYTICS:

```typescript
// supabase/functions/realtime-analytics/index.ts

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

interface AnalyticsRequest {
  project_id: string
  metrics: string[] // ['viral_score', 'engagement', 'sentiment', 'trends']
  time_window: '1h' | '6h' | '24h' | '7d'
  real_time: boolean
  filters?: {
    platforms?: string[]
    min_viral_score?: number
    content_types?: string[]
  }
}

const SUPABASE_URL = Deno.env.get('SUPABASE_URL')!
const SUPABASE_SERVICE_KEY = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!

serve(async (req) => {
  try {
    if (req.method !== 'POST') {
      return new Response('Method not allowed', { status: 405 })
    }

    const analyticsRequest: AnalyticsRequest = await req.json()
    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY)

    // Calcular janela de tempo
    const timeWindow = calculateTimeWindow(analyticsRequest.time_window)
    
    // Buscar dados base
    let query = supabase
      .from('api_viral_content')
      .select('*')
      .eq('project_owner', analyticsRequest.project_id)
      .gte('published_at', timeWindow.start.toISOString())
      .lte('published_at', timeWindow.end.toISOString())

    // Aplicar filtros
    if (analyticsRequest.filters?.platforms) {
      query = query.in('platform', analyticsRequest.filters.platforms)
    }
    if (analyticsRequest.filters?.min_viral_score) {
      query = query.gte('viral_score', analyticsRequest.filters.min_viral_score)
    }
    if (analyticsRequest.filters?.content_types) {
      query = query.in('content_type', analyticsRequest.filters.content_types)
    }

    const { data: content, error } = await query

    if (error) {
      throw new Error(`Data fetch failed: ${error.message}`)
    }

    // Processar métricas solicitadas
    const analytics: any = {}

    if (analyticsRequest.metrics.includes('viral_score')) {
      analytics.viral_metrics = calculateViralMetrics(content)
    }

    if (analyticsRequest.metrics.includes('engagement')) {
      analytics.engagement_metrics = calculateEngagementMetrics(content)
    }

    if (analyticsRequest.metrics.includes('sentiment')) {
      analytics.sentiment_metrics = calculateSentimentMetrics(content)
    }

    if (analyticsRequest.metrics.includes('trends')) {
      analytics.trend_metrics = await calculateTrendMetrics(content, supabase)
    }

    // Adicionar metadados
    analytics.metadata = {
      project_id: analyticsRequest.project_id,
      time_window: analyticsRequest.time_window,
      total_content: content.length,
      generated_at: new Date().toISOString(),
      filters_applied: analyticsRequest.filters || {}
    }

    // Se real-time, configurar WebSocket ou Server-Sent Events
    if (analyticsRequest.real_time) {
      analytics.real_time_endpoint = `${SUPABASE_URL}/functions/v1/realtime-stream/${analyticsRequest.project_id}`
    }

    return new Response(JSON.stringify(analytics), {
      headers: { 
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    })

  } catch (error) {
    console.error('❌ Real-time analytics error:', error)
    
    return new Response(JSON.stringify({
      success: false,
      error: error.message
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    })
  }
})

function calculateTimeWindow(window: string) {
  const end = new Date()
  const start = new Date()

  switch (window) {
    case '1h':
      start.setHours(end.getHours() - 1)
      break
    case '6h':
      start.setHours(end.getHours() - 6)
      break
    case '24h':
      start.setDate(end.getDate() - 1)
      break
    case '7d':
      start.setDate(end.getDate() - 7)
      break
  }

  return { start, end }
}

function calculateViralMetrics(content: any[]) {
  const viralScores = content.map(c => c.viral_score || 0)
  
  return {
    average_viral_score: viralScores.reduce((a, b) => a + b, 0) / viralScores.length,
    max_viral_score: Math.max(...viralScores),
    min_viral_score: Math.min(...viralScores),
    viral_content_count: content.filter(c => c.viral_score > 50).length,
    trending_content_count: content.filter(c => c.viral_score > 25).length,
    viral_score_distribution: {
      '0-10': content.filter(c => c.viral_score >= 0 && c.viral_score < 10).length,
      '10-25': content.filter(c => c.viral_score >= 10 && c.viral_score < 25).length,
      '25-50': content.filter(c => c.viral_score >= 25 && c.viral_score < 50).length,
      '50+': content.filter(c => c.viral_score >= 50).length
    }
  }
}

function calculateEngagementMetrics(content: any[]) {
  const totalLikes = content.reduce((sum, c) => sum + (c.engagement_metrics?.likes_count || 0), 0)
  const totalShares = content.reduce((sum, c) => sum + (c.engagement_metrics?.shares_count || 0), 0)
  const totalComments = content.reduce((sum, c) => sum + (c.engagement_metrics?.comments_count || 0), 0)
  const totalViews = content.reduce((sum, c) => sum + (c.engagement_metrics?.views_count || 0), 0)

  return {
    total_engagement: totalLikes + totalShares + totalComments,
    total_likes: totalLikes,
    total_shares: totalShares,
    total_comments: totalComments,
    total_views: totalViews,
    avg_engagement_per_content: (totalLikes + totalShares + totalComments) / content.length,
    engagement_by_platform: groupEngagementByPlatform(content)
  }
}

function calculateSentimentMetrics(content: any[]) {
  const sentiments = content.filter(c => c.sentiment_label)
  
  return {
    total_analyzed: sentiments.length,
    positive_count: sentiments.filter(c => c.sentiment_label === 'positive').length,
    negative_count: sentiments.filter(c => c.sentiment_label === 'negative').length,
    neutral_count: sentiments.filter(c => c.sentiment_label === 'neutral').length,
    average_sentiment_score: sentiments.reduce((sum, c) => sum + (c.sentiment_score || 0), 0) / sentiments.length,
    sentiment_by_platform: groupSentimentByPlatform(sentiments)
  }
}

async function calculateTrendMetrics(content: any[], supabase: any) {
  // Análise de hashtags trending
  const hashtagCounts = new Map()
  content.forEach(c => {
    c.hashtags?.forEach((hashtag: string) => {
      hashtagCounts.set(hashtag, (hashtagCounts.get(hashtag) || 0) + 1)
    })
  })

  const trendingHashtags = Array.from(hashtagCounts.entries())
    .sort((a, b) => b[1] - a[1])
    .slice(0, 10)
    .map(([hashtag, count]) => ({ hashtag, count }))

  return {
    trending_hashtags: trendingHashtags,
    total_unique_hashtags: hashtagCounts.size,
    content_growth_rate: await calculateGrowthRate(content, supabase)
  }
}

function groupEngagementByPlatform(content: any[]) {
  const platforms = {}
  content.forEach(c => {
    if (!platforms[c.platform]) {
      platforms[c.platform] = { likes: 0, shares: 0, comments: 0, count: 0 }
    }
    platforms[c.platform].likes += c.engagement_metrics?.likes_count || 0
    platforms[c.platform].shares += c.engagement_metrics?.shares_count || 0
    platforms[c.platform].comments += c.engagement_metrics?.comments_count || 0
    platforms[c.platform].count++
  })
  return platforms
}

function groupSentimentByPlatform(content: any[]) {
  const platforms = {}
  content.forEach(c => {
    if (!platforms[c.platform]) {
      platforms[c.platform] = { positive: 0, negative: 0, neutral: 0 }
    }
    platforms[c.platform][c.sentiment_label]++
  })
  return platforms
}

async function calculateGrowthRate(content: any[], supabase: any) {
  // Implementar cálculo de taxa de crescimento
  return 0
}
```
