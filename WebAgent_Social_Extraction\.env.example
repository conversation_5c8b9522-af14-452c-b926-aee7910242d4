# WebAgent Social Extraction Platform - Environment Variables
# Copy this file to .env and fill in your actual values

# ===== APPLICATION SETTINGS =====
APP_NAME=WebAgent Social Extraction
APP_VERSION=2.0.0
APP_ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO

# ===== API CONFIGURATION =====
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=4
API_RELOAD=true

# ===== DATABASE CONFIGURATION (SUPABASE) =====
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here
DATABASE_URL=postgresql://postgres:password@localhost:5432/webagent

# ===== REDIS CONFIGURATION =====
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=
REDIS_DB=0
REDIS_MAX_CONNECTIONS=20

# ===== CELERY CONFIGURATION =====
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2
CELERY_WORKER_CONCURRENCY=4
CELERY_TASK_SERIALIZER=json
CELERY_RESULT_SERIALIZER=json

# ===== AI/ML API KEYS =====
GEMINI_API_KEY=your-gemini-api-key-here
OPENAI_API_KEY=your-openai-api-key-here
ANTHROPIC_API_KEY=your-anthropic-api-key-here

# ===== SOCIAL MEDIA APIS =====
# YouTube Data API
YOUTUBE_API_KEY=your-youtube-api-key-here

# Twitter/X API
TWITTER_BEARER_TOKEN=your-twitter-bearer-token-here
TWITTER_API_KEY=your-twitter-api-key-here
TWITTER_API_SECRET=your-twitter-api-secret-here
TWITTER_ACCESS_TOKEN=your-twitter-access-token-here
TWITTER_ACCESS_TOKEN_SECRET=your-twitter-access-token-secret-here

# Instagram (if using official API)
INSTAGRAM_ACCESS_TOKEN=your-instagram-access-token-here
INSTAGRAM_APP_ID=your-instagram-app-id-here
INSTAGRAM_APP_SECRET=your-instagram-app-secret-here

# ===== SECURITY SETTINGS =====
SECRET_KEY=your-super-secret-key-here-change-this-in-production
JWT_SECRET_KEY=your-jwt-secret-key-here
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# ===== RATE LIMITING =====
RATE_LIMIT_REQUESTS_PER_MINUTE=60
RATE_LIMIT_BURST=10

# ===== MONITORING & LOGGING =====
SENTRY_DSN=your-sentry-dsn-here
PROMETHEUS_PORT=9090
GRAFANA_PORT=3000

# ===== STORAGE CONFIGURATION =====
STORAGE_PROVIDER=supabase
STORAGE_BUCKET=webagent-media
STORAGE_MAX_FILE_SIZE=100MB

# ===== CACHE SETTINGS =====
CACHE_TTL_SECONDS=3600
CACHE_MAX_SIZE=1000
CACHE_VIRAL_ANALYSIS_TTL=86400

# ===== PERFORMANCE SETTINGS =====
MAX_CONCURRENT_EXTRACTIONS=10
MAX_CONCURRENT_ANALYSES=5
EXTRACTION_TIMEOUT_SECONDS=300
ANALYSIS_TIMEOUT_SECONDS=120

# ===== COMPLIANCE SETTINGS =====
LGPD_ENABLED=true
DATA_RETENTION_DAYS=90
AUDIT_LOG_ENABLED=true
PRIVACY_MODE=strict

# ===== DEVELOPMENT SETTINGS =====
RELOAD_ON_CHANGE=true
ENABLE_CORS=true
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080"]
ENABLE_DOCS=true
DOCS_URL=/docs
REDOC_URL=/redoc

# ===== TESTING SETTINGS =====
TEST_DATABASE_URL=postgresql://postgres:password@localhost:5432/webagent_test
TEST_REDIS_URL=redis://localhost:6379/15
PYTEST_WORKERS=4

# ===== PROXY SETTINGS (for social media scraping) =====
USE_PROXY=false
PROXY_HTTP=http://proxy-server:port
PROXY_HTTPS=https://proxy-server:port
PROXY_USERNAME=your-proxy-username
PROXY_PASSWORD=your-proxy-password

# ===== FEATURE FLAGS =====
ENABLE_YOUTUBE_EXTRACTION=true
ENABLE_INSTAGRAM_EXTRACTION=false
ENABLE_TWITTER_EXTRACTION=false
ENABLE_VIRAL_PREDICTION=true
ENABLE_MULTIMODAL_ANALYSIS=true
ENABLE_REAL_TIME_PROCESSING=false

# ===== COST CONTROL =====
MAX_DAILY_AI_COST_USD=100
MAX_MONTHLY_AI_COST_USD=2000
COST_ALERT_THRESHOLD_USD=50
ENABLE_COST_MONITORING=true

# ===== BACKUP SETTINGS =====
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_STORAGE_PROVIDER=supabase
