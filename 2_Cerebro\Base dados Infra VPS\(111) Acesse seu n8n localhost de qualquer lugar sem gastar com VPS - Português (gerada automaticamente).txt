﻿E aí Automatizadores tudo bem seja
bem-vindo a mais um vídeo aqui do nosso
canal do YouTube Hoje eu gostaria de
falar a respeito de um assunto
importante mas também muito sério tá eu
tenho acompanhado aí diversos membros né
de
grupos inscritos do YouTube também e
principalmente alunos né que estão
começando aí a dar os primeiros passos e
em sua maioria e acabam optando ali pelo
ntn local host né porque muitos às vezes
não tem condições ali de contratar uma
VPS né e ou então tem certa dificuldade
de configurar o ntn em um servidor
remoto e acabam pitando ali pelo entin
local né porque é simplesmente Abrir ali
o o comando o prompt de comando e
digitar ali npx ntn e pronto já baixa
ali o projeto do Git
e já executa ele localmente tá mas qual
tem sido o grande problema né ou o o
alerta né ao risco que eu tenho
percebido de muita gente usando antin
local rost é a questão do uso de do
serviço túnel né O Túnel é uma forma de
você expor o seu ntn na internet
eh por meio de um como se fosse um proxy
né um proxy reverso Não na verdade como
se fosse uma VPN né Ou seja você
consegue
eh gerar ali uma uma url né Um Domínio ã
ou um subdomínio né com conexão segura e
por meio desse domínio né Eh é feito o
apontamento ali paraa sua máquina local
host é como se fosse um apontamento ali
de um servidor remoto né sendo que em
vez de você tá apontando o servidor
remoto de alguma hospedagem você tá
apontando do seu próprio computador né
da sua própria máquina tá que acaba
fazendo essa conexão por meio de um
conector tá então o conector É de fato
como se fosse o túnel é ele que liga né
o o IP digamos assim do do domínio ali
né o o IP externo com o o IP da sua
máquina né o local host ali que
representa a sua máquina
consequentemente o ntm tá e o qual é a
vantagem disso né digamos assim é você
poder usar o seu ntn local para integrar
com serviços externos um exemplo é o
Google né hoje o Google ele exige que o
seu ntn ele tenha domínio tá E
principalmente com conexão segura né com
certificado SSL instalado e até então
quando não tinha essa questão de de
serviços de túnel né ou as pessoas não
tinham muito conhecimento sobre isso
ficava inviável né se elas fossem
integrar com algum serviço externo
Talvez né Eh teria que instalar ali um
serviço na própria máquina né ou seja
teria teria que ser também um serviço
self host né ou seja instalado na
própria máquina para então conseguir
usar em conjunto com NN agora quando era
um serviço externo aí realmente não
tinha como por quê Porque os serviços
externos eles não vão reconhecer eh o
local host porque o local host como o
nome já diz é uma coisa local que está
ali na sua própria máquina ou seja não
tá exposto à internet tá mas graças ao
serviço de túnel agora é possível de
fato você eh expor aí o seu ntn na
internet para poder integrar com serviço
externo não precisando pagar mensalidade
de hospedagem tá
eh e nem mensalidade aí de de ntn Cloud
né ou seja o único custo que você teria
é com luz porque você vai ter que deixar
o computador ligado eh para as suas
automações funcionarem né no NN local e
também você teria custo com internet né
porque você também além de luz né Você
precisa ter conexão com internet o seu
ntn ali tá com a a conexão ali
eh 24 horas né funcionando 24 hor tá mas
aí mesmo com essa vantagem n com essa
com essa coisa incrível né que o túnel
promove nós que usamos né a solução ali
local host o problema disso é que tem
tido casos de Invasões de computador por
conta do serviço túnel tá Inclusive eu
vou compartilhar com você uma
experiência não tão boa que eu tive com
o tá e pode ser que já tenha acontecido
com você ou então pode que acontecido
com outro serviço de túnel tá e eu acho
muito válido você compartilhar conosco
aqui nos comentários do vídeo tá então
se você já teve êxito né com algum
serviço de túnel comenta aí nos
comentários Qual é esse serviço você já
teve uma um uma experiência negativa né
ou um uma uma possível invasão né do seu
computador depois que você começou a
usar o túnel digita aí também nos
comentários pra gente poder eh
entender mais um pouco aí o que que o
pessoal está achando desses serviços de
túnel
Tá então vamos lá o primeiro serviço de
túnel que eu
basicamente usei né foi o próprio
serviço de túnel do ntn tá o n ele
dispõe e esse serviço para você tá sendo
que é um serviço um pouco instável tá já
tem algum tempo que eu não uso não sei
se hoje foi melhorado né se realmente
está mais estável mas eu lembro que na
época quando eu testei eh às vezes
funcionava às vezes não funcionava às
vezes recebia evento às vezes não
recebia né então Só servia mesmo para
fins de teste né às vezes nem teste eu
conseguia fazer direito então eu acabava
até desistindo aí eu procurava logo um
ntn self host e usava ali a versão eh
Auto hospedada mesmo tá eh então assim é
um serviço isso gratuito tá o ntn ele
gera ali uma url temporária uma url que
vem com subdomínio E domínio deles tá
eh mas a princípio funcional tá consegue
receber eventos ali externos né de
aplicações extern E você já consegue por
exemplo fazer algumas automações ali com
o seu ntn local Tá mas como eu falei
eh não é 100% estável tá bom
eh bom o próximo serviço foi o do grock
tá esse aqui infelizmente eu não
tive uma boa experiência tá
eh eu lembro que na época isso já deve
ter aí uns se meses mais ou menos eu
quis
eh trazer né esse tipo de serviço né de
túnel para o curso de setup do ntn né
porque lá a gente já em já ensinou né
sobre o o túnel do do propio NN e a
gente queria aproveitar e ensinar o
túnel do n grock enfim eu comecei a
estudar né aqui a solução comecei a a
aplicar ali a implementar e Sério depois
que eu coloquei o ntn no ar né o ntn
local usando o túnel aqui do n grock
pessoal sem mentira é porque assim eu
fiquei tão preocupado tão nervoso que
nem pensei em tirar print ou então
gravar um vídeo mas o meu Windows né do
meu computador do meu notebook ele
começou a dar uns erros doidos de
interface ícones da da pasta sumindo eh
a a o as thumbnails da imagem já não
estavam mais aparecendo Enfim no meio do
nada começou a dar um erro de interface
no meu Windows e justamente após eu ter
configurado o ntn com grock e ter
colocado no ar ou seja antes disso tava
tudo OK tá então assim eu acredito que
não foi coincidência acho que realmente
pode ter ocorrido alguma coisa ali nessa
essa configuração de túnel que talvez
algum hack ou alguém mal intencionado
conseguiu acessar minha máquina e
aplicou esse essa falha ali na interface
do meu Windows né então imediatamente eu
já desinstalei o Enock já passei ali
antivírus antimalware Enfim tudo que
tinha direito para ver se não tinha
tinha vírus e tal e se eu não me engano
ele encontrou ele encontrou dois
arquivos maliciosos que haviam Aparecido
dentro do meu computador e que
Possivelmente estavam causando esse
problema né tô aqui quando eu fui removi
né esses arquivos reiniciei o computador
o computador voltou ao normal tá então
assim o en grock infelizmente eu não
tive boa experiência por mais que várias
pessoas falam né Ah poxa eu usei gostei
funcionou nunca tive problema Mas eu
particularmente não tive boa experiência
tá por isso que eu estou falando se você
também teve uma experiência aí negativa
com a neg Rock seria interessante você
comentar aqui para realmente a gente
saber se é algo que tem acontecido com
mais pessoas tá algo não Comigo não
aconteceu nada dessas coisas funcionou
perfeitamente até hoje uso grock postea
aí também nos comentários a gente também
quer ouvir as opiniões positivas a
respeito dos serviço tá mas pessoal o
serviço mesmo né que
eh eu gostei muito mesmo tá foi o zero
trust do Cloud flare ou da Cloud flare
tá esse serviço aqui eu já uso para
fazer os apontamentos
eh dos meu do dos meus softwares
né das Ferramentas Open sce que eu uso
aqui na auto até mesmo dos clientes eh
eu uso uso o cloud FL para fazer os
apontamentos do DNS Tá e agora já estou
usando para fazer os apontamentos eh do
ntn local ou seja graças ao zero trust
do do Cloud FL eu já consigo subir a a
minha Instância né local do ntn para a
internet podendo já ser acessada ali com
subdomínio do meu próprio domínio esse
que é o legal eh
e funcionando perfeitamente ou seja 100%
estável nenhum tipo de ameaça de invasão
de computador né ou seja aquilo que eu
não consegui no ntn Tunnel aquilo que eu
não consegui no no n grock Tunnel eu
consegui aqui no Cloud flare realmente é
um serviço aprovado tá Eu recomendo de
olhos fechado mesmo tá E aí aqui no no
zer trust tá é basicamente você você vai
ter e ali no menu RS né você vai ter o
menu tunnels tá E aí como você pode ver
aqui eu criei um túnel né chamado ntn tá
lembrando que esse túnel ele tem um um
instalador né um executável que você tem
que rodar na sua máquina tá Pode ficar
tranquilo que é seguro onde ele vai ser
como se fosse a ponte ali entre aqui o
serviço da Cloud flare com a sua máquina
física para ocorrer esse tunelamento né
não sei se é assim que o pessoal fala né
então assim
é é bem tranquilo tá você pode instalar
aí sem medo e em seguida depois que você
instala você já coloca aqui né o
mapeamento do seu host tá que é
basicamente isso aqui ó que eu até eu
até Acabei fechando a janela né Vou
botar aqui ó túnel local host
V te mostrar aqui um exemplo ó essa essa
imagem aqui
mesmo então basicamente lá no cloudfare
assim como qualquer outro serviço de
tunelamento né você tem lá o apontamento
para local host juntamente com a porta
né então no caso você colocaria local
host né 2. 5678 que é a porta padrão do
NN e o Tunel Cent seria esse conector
que você você instala no seu computador
né é um executável bem pequenininho você
roda ele ele já ele já instala no seu
computador e aí você roda um comando né
que o próprio Cloud flare vai te passar
que vem com o seu Token para poder
autenticar esse conector Ok E aí sim
esse conector vai estar lá no Cloud
flare Como ativo né e eh dizendo o
seguinte Olha o túnel já está
configurado Ou seja a partir de agora
você já pode fazer o apontamento e a sua
aplicação já vai ficar exposta na
internet tá E aí você simplesmente faz a
configuração de domínio subdomínio né
Você pode botar lá um subdomínio por
exemplo ntn local né E aí o subdomínio
você pode e o domínio você pode usar da
sua própria empresa ou marca né sei lá
test.com ntn
local test.com tá
E e aí feito isso basta você
simplesmente abrir o seu ntn aliás
executar o comando de prompt tá passando
eh as variáveis de ambiente de
configuração de domínio do seu ntn tá ao
invés de você já sair rodando ali o npx
ntn você vai fazer um set né das
variáveis de de configuração de domínio
e em seguida você faz o o npx ntn tá
então por exemplo eu tenho um um exemplo
aqui de código tá que se você for
reparar ó ele tá setando na variável de
ambiente ntn protocol o protocolo https
né Por quê Porque o próprio Cloud flare
ele já força o redirecionamento para
https Ou seja a URL do seu ntn local ela
vai ser uma url segura Tá você também
seta aqui o host né que aí seria o RL
base do seu ntn no caso aqui eu coloquei
n8n local p aoti
pc.com.br e eh por último você também
seta aqui a a URL do seu web Hook tá
então você coloca aqui ó o protocolo que
é https 2is P bar Barra coloca o o
subdomínio né com domínio e finaliza
aqui colocando uma barrinha tá você tem
que colocar essa Barrinha aqui isso não
pode dar problema lá na sua na ur seu M
Hook E aí sim por fim você faz um npx
ntn tá então só rodando aqui para você
ver e como é que ele se comporta tá
então eu estou aqui com o prompt aberto
ó Colei o comando
tá enquanto isso deixa eu acessar aqui o
site da
Auto e vamos lá vou abrir aqui ó pode
ser que ele demore um
tá que às vezes ele vai tentar buscar né
a
atualização E aí se não tiver
atualização ele já vai eh iniciar o seu
ntn com as configurações né que você
definiu aqui nas variáveis de ambiente
tá lembrando que essa tela né esse
prompt aqui ele precisa estar aberto
para que o seu ntn funciona tá
eh posso dizer que seria aí uma
desvantagem né você realmente tem que
ficar com o computador ligado com promet
aberto e com conexão internet o seu ntn
local
e Man ali as automações manter as
integrações tá bom diferente de um nhn
self host que você roda uma VPS e não se
preocupa em ter que manter máquina
ligada com conexão internet gastando
energia não você compra lá um VPS às
vezes r$ 2 e você tem
ã mais facilidade né de ter ali suas
automações funcionando 24 horas tá Então
olha só que interessante ó ele já tá
inicializando aqui o ntn ó na porta 5678
tá que é a porta
padrão E aí ele já mostra aqui a versão
que tá rodando que é A
175.27 tá você pode simplesmente apertar
cont control aqui e clicar na URL tá ou
simplesmente apertar a tecla o que ele
vai abrir uma nova aba com já com acesso
ao seu ntn local sendo que tem uma
observação inclusive é algo que muitos
usuários reportam né mas eles esquecem
desse detalhe aqui e quando a gente ã
configura o ntn uma Instância do NN como
eu falei por padrão a porta dela é 5678
tá e a porta ela não precisa ser exposta
na URL tá então quando você por exemplo
só acessa o subdomínio com domínio né
seja no seu ntn com túnel ou um ntn self
host em um VPS em um servidor remoto
todos eles você só acessa
eh o domínio com o subdomínio com
domínio você já vai vai ver aqui ó que
ele já vai acessar direto
ó aqui ó Pronto já acessou o meu ntn
local tá Por quê Porque a porta ela fica
implícita né Ela não é uma coisa que
realmente você tem que informar na na
URL aqui do navegador tá E aí ó pra
gente poder testar aqui
rapidamente ó eu posso dar um Create
workflow tá E aí eu coloco aqui ó um web
Hook então se eu vir aqui por exemplo
pegar de teste tá ó pegar de
teste vou salvar ó vou executar aqui
acesso essa URL em uma nova aba e pronto
ó recebeu o evento aqui tá ó web Hook no
modo de execução teste Hugo e produção
funciona funciona também mesma coisa
pode pegar aqui tá repare que a URL de
web Hook ó já vem aqui configurado
direitinho exatamente como você definiu
lá no no seu túnel né do da Cloud flare
tá E aí você também pode usar o de
produção né Você só vai ativar aqui e
vai ficar acompanhando aqui pelo log
execu tá então se eu for lá acessar aqui
a URL novamente em uma nova aba ele já
vai gerar
eh um evento aqui tá vendo ó esse aqui
já foi de acesso
eh pelo modo produção Tá e por último
também né pra gente finalizar aqui a
gente pode testar um post também tá o
post funciona perfeitamente já vez você
pode estar olhando e fal assim Ah beleza
você só tá testando get aí tá mais um
post se realmente eu precisar fazer um
post de uma aplicação externa para o ntn
vai funcionar vai funcionar Tá mesmo
processo Ó você pode chegar aqui né ó
abre pega a URL de produção coloca aqui
no post tá ó salva já vou ficar aqui
monitorando a execução tá aí eu venho
aqui ó Cola a minha url de web Hook de
produção vou aqui emb e coloco aqui por
exemplo eh
um dado aqui ó de de teste né Por
exemplo botei aqui O Hugo e seria o meu
nome né então vou enviar isso aqui lá
pro meu ntn ó recebi a mensagem com
sucesso eu volto aqui ó ele vai gerar
mais um log desse post né que eu fiz
aqui de teste tá E vai estar lá ó no
Body o campo nome o meu nome aqui tá
e modo de execução produção ou seja
Parece que realmente eu tô usando aqui o
ntn self host né ou seja nem parece que
é um ntn rodando ali direto da minha
máquina então assim fica aí uma
alternativa
eh confiável para você tá Se você
realmente quiser usar o ntn eh exposto
na internet eu recomendo aí o serviço eh
Z trust tá do Cloud flare tá lembrando
que a gente fala isso com mais detalhes
e aqui no nosso curso de setap de ntn tá
então lá no módulo de node DS né você
vai aprender a instalar o ntn localmente
né no no seu computador Windows como
também fazer e as integrações externas
usando o serviço de túnel do prop NN e
agora o usando o serviço de túnel do
próprio Cloud flare tá aqui a gente
mostra passo a passo de como você fazer
a configuração aqui nesse tutorial a
gente só fez mesmo um resumo para você
ver as opções né E como funciona eh cada
uma delas tá bom Quais são os pros e e
os contras Ok então é isso aí muito
obrigado pela atenção não esqueça de
deixar um like aí nesse vídeo Para
apoiar o nosso trabalho e se inscreva
também no canal tá a gente tá crescendo
cada vez mais que a pouquinho a gente já
vai bater aí eh 277.000 inscritos né E a
sua inscrição é muito importante tá bom
E caso você tenha algo para comentar
sugestão de vídeo ou então dá a sua
opinião sobre o que foi falado aqui pode
usar os comentários aí que a gente vai
ter um maior prazer de se interagir Tá
bom muito obrigado pela adição grande
abraço e valeu