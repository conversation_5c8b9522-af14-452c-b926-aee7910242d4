﻿Hoje você vai ver o poder do MCP Server
"nativo no INAT, conectado à AP oficial"
"do WhatsApp, tudo 100% gratuito. Com"
"essa integração, você automatiza"
"atendimentos, utiliza a inteligência"
"artificial, escala o teu negócio com"
"liberdade total, sem depender de"
"terceiros. E o melhor, tem material"
pronto para download para você sair
aplicando agora mesmo. Se você quer
transformar teus processos com automação
"inteligente, fica até o fim, porque essa"
"combinação de MCP, N WhatsApp oficial é"
simplesmente poderosa. Bora. Eu sou o
"Pedril da NAS, você está no canal da"
comunidade ZDG.
[Música]
"Bora pra ação, galera. Nessa aula a"
gente vai conectar o MCP Server nativo
do NTIN diretamente com a API oficial do
WhatsApp. Recentemente o NTN lançou essa
atualização que permite com que a gente
crie um servidor MCP de forma nativa
"nele, conecte com qualquer uma das"
"ferramentas, qualquer um dos gatilhos"
que já estão disponíveis na ferramenta.
Todo o material que a gente vai utilizar
"nessa aula, ele vai estar disponível"
para download na página de apoio e você
encontra o link dessa página na
descrição desse vídeo. Lembrando que
"toda a instalação ela vai ser gratuita,"
você não precisa saber nada de
programação e você vai fazer tudo direto
no teu computador. Só que antes de
"continuar, deixa um like para mim nesse"
vídeo e deixa um comentário dizendo como
você pretende utilizar o MCP para
automatizar o teu atendimento no
"WhatsApp. Então, basicamente, eu tenho"
"dois workflows, tá bem? O primeiro deles"
"é um MCP Server, que já está conectado à"
p oficial do WhatsApp. Então vou ligar
ele aqui no ambiente teste e eu tenho um
segundo workflow que é o agente de A que
vai fazer as consultas para mim e fazer
"a chamada do MCP client, ou seja, vai a"
eh vai executar as ações que eu defini
"aqui no meu MCP server, que é uma ação"
de enviar uma mensagem. Então aqui eu já
"tenho o número, tá conectado a P"
"oficial, porque esse carinha aqui, ó,"
"ele já tem uma conversinha aberta, tá?"
"Eh, e vamos lá, né? Vamos eh fazer um um"
teste aqui. Vamos dar lá um oi. Tudo
"bem, né? Beleza."
Lá. Beleza. A inteligência artificial já
"me retornou. É, como posso ajudar você?"
Gostaria de enviar uma
mensagem para o
número.
"Beleza. Ah, tá rodando aqui. Vamos lá."
Á. Qual mensagem gostaria de enviar para
o número?
Gostaria de
enviar ah um bom
dia uma previsão do
tempo de hoje na cidade de
Alfenas
MG. Vai consultar e vai enviar. Beleza?
"Ó, enviou a mensagem, já executou. Que"
"que ele mandou para mim aqui, ó? Ó lá."
Bom dia. A previsão do tempo para
"ofendas hoje é de céu limpo, com"
temperaturas variando de tal a tal.
"Legal, né? E esse é um número oficial da"
API do WhatsApp que eu tô utilizando
"diretamente na ABA, na Cloud Pay, na na"
"minha conta do Facebook. Beleza, galera?"
a gente vai construir toda essa
"estrutura, né, do zero, para você"
entender como você pode aí também
replicar diretamente no teu PC e consiga
começar a fazer aí automações utilizando
"a AP oficial do WhatsApp, Net direto no"
"seu PC de forma gratuita, tá bem? Então"
"bora lá, galera. Primeira coisa e que a"
gente vai precisar para usar e o NN é o
Docker Desktop. Então você vai entrar e
baixar o Docker Desktop.
Tá? Lembrando que o link para download
"de todas essas ferramentas, tá pessoal,"
também tá disponível na página de apoio.
"Então entra aqui na página do Desktop,"
"roda para baixo, escola aqui, escolhe"
aqui qual que é o teu sistema
"operacional, faz o download, instala,"
tá? Assim que você instalar o Docker
"Desktop e executar, ele vai abrir essa"
"baleinha para você aqui, ó. Você abre"
"ele, ele vai estar zerado aqui de eh de"
"contêiners, tá? eh, não vai ter nenhum"
"contêiner aberto. Então, é isso que a"
gente precisa. Instalou o Docker
"Desktop, abriu a baleinha, a gente vai"
começar a instalar o nosso Nate aqui na
"barrinha de busca, você vai clicar nela"
e vai buscar por NHN. NH. Aqui ele vai
encontrar para você a imagem da NH. E a
tag que a gente vai utilizar é a
"1.88.0, tá? que é a versão que hoje"
permite com que a gente utilize o MCP
"Server, né, de forma nativa aqui no"
"nosso Net. Beleza? Ó, escolheu a versão"
"1.88, você vai fazer um pull dela,"
"aguarda ele baixar a imagem do Net, tá?"
"Baixou a imagem do NT, você pode vir no"
run. Legal. Assim que você clicar em
"run, você vai precisar abrir as opções,"
tá? Você vai dar um nome para ele. Vou
colocar aqui NT N.
"N aula, né, Zdg, tá? Uma porta que eu"
"vou utilizar, você pode utilizar a porta"
"padrão que é 5678 ou qualquer uma outra,"
tá bem? Eu vou colocar uma outra aqui
porque eu já tô usando a 5678 no exemplo
que eu tô rodando aqui no meu PC. No
"volume, que que você vai fazer, ó? Você"
"vai vir na tua área de trabalho, você"
"cria uma pastinha nova assim, ó, chamada"
ZDG aula ou com qualquer nome que você
"quiser, tá bem? a gente precisa criar"
essa pasta para apontar aqui no nas
"variáveis de ambiente, né, para que o"
NTN salve os arquivos nela. Então vamos
eh criar a pastinha e vai vir nesses
"três pontinhos, ó, vai vir lá na tua"
área de trabalho e vai encontrar a
"pasta, como é que chama? Meu ZDG aula,"
"né, que a gente criou. Seleciona a pasta"
e no caminho do contêiner você vai
simplesmente copiar e colar essa
informação que tá disponível na página
de apoio. Legal. Beleza. Variável de
"ambiente, você pode passar aqui o Time"
"Zone América São Paulo, vai ficar legal"
"também, tá? Show. E dá o run. Aguarda"
ele executar. Tá
executando. Belezinha. Belezinha. Você
pode clicar do lado de fora. Ele já tá
"dizendo para mim, ó. Pode voltar aqui,"
"ó, em contêiners, tá? E NTIN aula já"
"está rodando. Se você clicar aqui, ó,"
"ele vai abrir para você o NTIN. Então,"
"vamos passar as informações, ó."
"e-mail, um nome e uma"
senha. Belê? Deixa eu aumentar o zoom
"aqui. Você coloca alguma coisa qualquer,"
"get started e skip. Beleza, galera? O"
NTN já tá
instalado. Primeiro etapa está pronta.
"Agora, que que nós vamos fazer? Vamos"
"criar um flow aqui, ó. Criar um workflow"
que vai se
"chamar MCP Server, tá? Aqui dentro do"
"mcp server, a gente vai simplesmente"
"colocar um mcp server trigger. Olha só,"
ele vai funcionar para você da mesma
"forma que funciona um webhook, tá? Ele"
traz para você um RL de teste de
"produção. Eh, aqui nós não vamos"
utilizar autenticação nesse vídeo de
exemplo. E você pode colocar um caminho
"para ele. Posso colocar aqui ZDG, tá? ou"
qualquer caminho que você quiser para
que você acesse esse esse a URL do teu
"servidor de MCP de qualquer lugar,"
beleza? Então isso é importante para que
"a gente construa segundo workflow,"
beleza? E que que nós vamos adicionar
"aqui, ó? Você pode adicionar qualquer"
trigger que tá disponível dentro do do
"NHN. É incrível isso, né? Então nós"
vamos colocar aqui WhatsApp Business
"Cloud 2, tá legal, galera? Eh, para"
"conectar um número de WhatsApp aqui, é"
preciso que você já tenha o seu número
de WhatsApp na cloud API. Você pode
"criar esse número de forma gratuita. Eh,"
você vai criar uma conta no teu Facebook
"developers, ativar um número, então o"
processo ali que você vai precisar de um
número para colocar dentro da conta do
"Facebook, o número de WhatsApp. Então eu"
"vou deixar para você na página de apoio,"
"tá, nessa etapa aqui, um link para que"
você consiga criar o teu número de
WhatsApp e Business Cloud API oficial de
forma gratuita e depois você vai trazer
as tuas informações aqui. Mas
basicamente a gente vai configurar as
"credenciais da seguinte forma, tá? Então"
você vai colocar aqui um token de
acesso. Nesse vídeo eu vou ensinar como
você vai criar esse token de acesso e
"uma conta e uma ID, tá? uma uma business"
account aqui que é a conta da sua BM do
Facebook. Eu já vou copiar esse essas
"informações do meu sistema, tá? Que eu"
já tenho uma conta de AP oficial
rodando. Então vou jogar aqui só para
"exemplificar. Eh, deixa eu pegar aqui."
Esse aqui para quem não conhece é o
"Zepro, tá? Nosso sistema de"
multiatendimento e e multicanais aqui da
"ZDG, um white label que você pode"
"comprar e fornecer. Então, se você tiver"
"interesse, tá, em comercializar um"
"sistema muito avançado, robusto, com"
"suporte eh e atualizações frequentes,"
"clica no link da descrição desse vídeo,"
você vai encontrar aí o ZPR. Deixa eu
"pegar as informações que eu preciso, ó."
Vamos pegar os
tokens e identificação WhatsApp
Business. Beleza? Credenciais salvas.
"Eh, que que a gente vai configurar aqui,"
"pessoal? É, primeira coisa, a gente quer"
"um research message, tá? O que que a"
gente quer enviar? A gente quer enviar
"um send, tá? Você pode enviar qualquer"
"uma dessas ações aqui. Você pode,"
"inclusive, depois com as instruções"
"desse vídeo, manipular as operações."
Vamos enviar uma mensagem de texto
padrão. Beleza? O fun number id. Esse
"fun number id, pessoal, nós vamos"
"colocar aqui, ó, como expression. E a"
gente precisa passar a ID do número
"também, eh, do nosso número oficial."
"Então, vou pegar ele aqui, ó, que seria"
esse cara.
"Tá? Eh, que mais a gente precisa? É o"
"recipiente, que é o número que vai"
"receber, a gente vai deixar e a tratar"
isso. Tipo de mensagem que a gente vai
"enviar, vai ser uma mensagem de texto. E"
"o body, vou deixar a tratar isso também."
"Beleza? Ó, salvou. Primeiro passo tá"
"pronto, tá? Que é a construção do MCP"
Server. Agora a gente vai ir lá pro MCP
"client, tá? Deixa eu pausar aqui. Vamos"
voltar. Vamos criar um novo workflow.
"Vou chamar ele de MCP Cl, tá? Aqui no"
MCP client a gente vai colocar um agente
de
"A. Beleza? Eh, esse agente de A a gente"
precisa configurar. A primeira coisa que
"a gente vai colocar nele aqui, ó, vai"
"ser um chat model de open AI, tá? Ã,"
"beleza. Open AI, a gente precisa"
"configurar as credenciais, tá? Essas"
"credenciais, no vídeo anterior, eh, a"
"essa aula, eu ensino como pegála de"
dentro da Open AI. Eu vou buscar aqui
"dentro da minha do meu ZPR, que eu já"
tenho ali uma configuração de chat e ppt
pronta e eu vou pegar essas informações
dali para não precisar acessar minha
minha página da Open AI. Beleza?
Organization API aqui. Vamos salvar.
Validou. Legal. O tipo da do modelo que
a gente vai usar o 40 mesmo. Vamos
colocar uma memória. Memória simples
aqui até 50 interações. Beleza? E aqui a
"gente vai colocar a TU do MCP, tá? MCP"
"client tool. Eh, agora é muito"
"importante, tá, pessoal? Ó, a gente já"
"construiu tudo, né? O último passo e o"
"mais importante, como a gente tá rodando"
"localmente, né? Então, se eu pegar,"
"deixa eu abrir aqui novamente, ó, eh,"
vocês vão perceber que o nosso MCP
"server, agora deixa eu ver qual que é"
"esse aqui, a gente tá usando, ó, nosso"
"MPCP server, ele tem uma URL. Como a"
gente tá rodando esse serviço aqui eh
"localmente, né, é importante que a gente"
"exponha esse serviço na web, tá? Então"
ele só vai conseguir acessar esse end
point com um servidor https. Então é
importante que a gente exponha isso.
Como é que a gente pode fazer essa
"exposição do serviço? Existem três,"
"quatro, várias formas de fazer. Vou"
trazer aqui as mais fáceis para você.
"Primeira delas é usando NGOCK, tá? Então"
você pode baixar o NGock. O NG Rock é
esse terminalzinho que você baixa no seu
"computador, você executa ele. E para"
"expor a porta com NG rock, você escreve"
"assim, ó, NGock HTTP. É a porta que a"
gente tá utilizando aqui nesse serviço é
"a 5679, ó, que é aquela porta do"
começo. Beleza? Então quer dizer que
agora eu já tenho uma RL com https que
tá rodando o meu
"Net, né? Ó lá, meu Netn tá rodando aqui,"
ó.
Legal? Então tá exposto. Então eu posso
"usar ele, né? Então eu eu posso copiar"
"aqui, ó, o meu trigger, tá? Minha URL de"
produção. E aqui no lugar onde tá o
"local host, a gente passa, tá? Eh, o"
"nosso NG Rock. Então, pega tudo que tá"
"aqui para trás, ó, e passa o caminho do"
"NGock. Então, ficaria assim, ó. Nosso"
URL. Tem um segundo serviço que chama
"servio, tá? O server ele é mais simples."
"Como é que funciona? Ó, copia esse"
"comando, ó, que tá aqui, ó. E a porta a"
"gente vai expor a 5679, tá vendo? Então,"
"ó, é só trocar a porta no comando do"
"server, abrir um terminalzinho. Você"
pode vir na tua pasta lá que a gente
"criou o ZDG de aula,"
"ó, e colar esse comando aqui,"
ó. Ele vai expor também para você numa
"RL server. Então, tá lá, ó. Então, se eu"
"entrar aqui, ó, seleciona, botão"
"direito, copia as informações, ó. Se eu"
"entrar aqui, ó, a gente tem nosso Nit"
"também rodando com https na web, ó. Olha"
"só que legal. Beleza? Então, você pode"
"usar ele também ou você quiser também,"
"você pode usar o Visual Studio. Então,"
você tem o Visual Studio dentro do seu
PC. Se aqui eu tô rodando o nosso nosso
"ZPR, se você vier aqui no terminal, ver"
"em portas, você também consegue"
"encaminhar uma porta, ó,"
"5679. Ele encaminha a porta para você,"
ó. Você clica aqui para invisibilidade
"público e aqui, ó, ele já tem, tá?"
Copiou. Ele também tá expondo aqui pra
"gente o nosso serviço, tá bem? Na web."
"Então, três formas de você expor o"
serviço local na web. a gente pode usar
"o NG rock, tá bem? A gente pode utilizar"
o servio ou a gente pode utilizar o
"Visual Studio, tá? Você pode utilizar um"
uma CD Fair Tunel também para fazer
isso. Então é muito importante que
aconteça essa exposição de serviço.
Então esse é um ponto crítico desse
"vídeo, tá? Então você precisa expor o"
serviço na web para tá consumindo eh o
web hook localmente. Se você tem o NTIN
"já instalado na web, já exposto em"
"produção, é muito mais simples, tá?"
Porque aí você não vai precisar fazer
toda essa configuração que eu fiz aqui
agora. Tô fazendo local para você
"aprenda, teste, né, manipule isso sem"
gasto nenhum. E a partir do momento que
você entender que faz sentido pro seu
"negócio, você consiga aí"
"eh no numa VPS, no servidor, né, já"
fazer o fornecimento desse tipo de
"serviço. Beleza? Ó, aí tá pronto. Agora"
"é só testar, né? Você põe para testar e"
"conversar com a IA, né?"
"Beleza? Ele já vai identificar, já vai"
fazer toda aquela chamada que a gente
"permitiu, né? E vai permitir. Já já tá"
"me respondendo, ó, consumindo aqui. Eh,"
quero enviar uma
mensagem. Só acho que Deixa eu ver se eu
configurei aqui. Tá configurado no
"server, né? Quero enviar uma mensagem"
lá. Olá. Para qual número você deseja
"enviar a mensagem? Tá rodando aqui, ó, o"
"meu meu MCP server, né? Tá o"
número
"7. Beleza? Ó, ó lá. Aí ele vai perguntar"
"para mim, ó, qual o tipo de texto que"
"você quer enviar, né? Eh, envie um bom"
dia e informações sobre a cidade de
Alfenas MG. Beleza? Ele já vai
"consultar, já vai fazer o envio, já vai"
"fazer a chamada do nosso MCP server, já"
"vai mandar a mensagem, né? Então agora"
"tá pronto aqui pra gente utilizar, tá?"
"Ela já fez a chamada, ó. Ó lá, foi"
"enviada. Vamos ver que que ele fez, ó."
"Eh, deixa eu só"
"eh aqui, ó. Bom dia. Aqui estão algumas"
"informações, né, sobre alfenas. P pa pá"
p p pá. E ele tá mandando a mensagem.
"Então, dessa forma, pessoal, vocês"
conseguem construir direto no PC de
"vocês, sem saber nada de programação, de"
"forma gratuita, com MCP Server conectado"
"à API oficial do WhatsApp, beleza? E se"
"quiser saber mais sobre automações,"
"sobre sistemas de multiatendimento, vem"
"pra comunidade EDG, tá? A maior"
comunidade do mundo que trabalha com
APIs e automações para WhatsApp. São
"mais de, são quase 7.000 alunos, tá?"
"mais de 4 anos na estrada aí,"
transformando a vida de centenas de
milhares de pessoas todos os dias. Para
"entrar na comunidade, o link tá na"
descrição desse vídeo e se tem interesse
"também em entrar pra Zepro aqui, que é o"
nosso sistema White Label com
"multicanais, tá? Que se conecta"
"inclusive ao Nitn, tem várias"
"integrações nativas, olha só, só de"
"bote, inteligência artificial, olha o"
"tanto de integração que ele tem, né?"
"integrações com vários tipos de canais,"
vários tipos de eh de automações para
que você consiga fornecer também um
"sistema bem avançado aí, white label,"
né? Consiga revender esse sistema e
"rentabilizar, garantir uma renda extra,"
"não só uma renda extra, mas construir um"
"negócio, tá, em cima do nosso sistema."
"Isso aí. Forte abraço, beijo no coração"
"de todos, fique com Deus. Qualquer"
"dúvida é só chamar. Estamos junto,"
Pedrinho da NASA. Até a próxima. Ah.