{"level":"info","message":"Initializing n8n process","metadata":{"file":"start.js","function":"init","timestamp":"2025-07-25T23:27:14.134Z"}}
{"level":"info","message":"n8n ready on ::, port 5678","metadata":{"file":"abstract-server.js","function":"init","timestamp":"2025-07-25T23:27:14.748Z"}}
{"level":"warn","message":"Migrations in progress, please do NOT stop the process.","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:14.782Z"}}
{"level":"info","message":"Starting migration InitialMigration1587669153312","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:14.783Z"}}
{"level":"info","message":"Finished migration InitialMigration1587669153312","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:14.808Z"}}
{"level":"info","message":"Starting migration WebhookModel1589476000887","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:14.815Z"}}
{"level":"info","message":"Finished migration WebhookModel1589476000887","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:14.821Z"}}
{"level":"info","message":"Starting migration CreateIndexStoppedAt1594828256133","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:14.823Z"}}
{"level":"info","message":"Finished migration CreateIndexStoppedAt1594828256133","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:14.827Z"}}
{"level":"info","message":"Starting migration MakeStoppedAtNullable1607431743768","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:14.829Z"}}
{"level":"info","message":"Finished migration MakeStoppedAtNullable1607431743768","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:14.830Z"}}
{"level":"info","message":"Starting migration AddWebhookId1611144599516","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:14.832Z"}}
{"level":"info","message":"Finished migration AddWebhookId1611144599516","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:14.837Z"}}
{"level":"info","message":"Starting migration CreateTagEntity1617270242566","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:14.840Z"}}
{"level":"info","message":"Finished migration CreateTagEntity1617270242566","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:14.899Z"}}
{"level":"info","message":"Starting migration UniqueWorkflowNames1620824779533","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:14.907Z"}}
{"level":"info","message":"Finished migration UniqueWorkflowNames1620824779533","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:14.911Z"}}
{"level":"info","message":"Starting migration AddwaitTill1626176912946","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:14.913Z"}}
{"level":"info","message":"Finished migration AddwaitTill1626176912946","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:14.916Z"}}
{"level":"info","message":"Starting migration UpdateWorkflowCredentials1630419189837","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:14.918Z"}}
{"level":"info","message":"Finished migration UpdateWorkflowCredentials1630419189837","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:14.925Z"}}
{"level":"info","message":"Starting migration AddExecutionEntityIndexes1644422880309","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:14.927Z"}}
{"level":"info","message":"Finished migration AddExecutionEntityIndexes1644422880309","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:14.944Z"}}
{"level":"info","message":"Starting migration IncreaseTypeVarcharLimit1646834195327","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:14.946Z"}}
{"level":"info","message":"Finished migration IncreaseTypeVarcharLimit1646834195327","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:14.948Z"}}
{"level":"info","message":"Starting migration CreateUserManagement1646992772331","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:14.949Z"}}
{"level":"info","message":"Finished migration CreateUserManagement1646992772331","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:14.998Z"}}
{"level":"info","message":"Starting migration LowerCaseUserEmail1648740597343","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:15.003Z"}}
{"level":"info","message":"Finished migration LowerCaseUserEmail1648740597343","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:15.005Z"}}
{"level":"info","message":"Starting migration CommunityNodes1652254514002","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:15.008Z"}}
{"level":"info","message":"Finished migration CommunityNodes1652254514002","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:15.018Z"}}
{"level":"info","message":"Starting migration AddUserSettings1652367743993","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:15.021Z"}}
{"level":"info","message":"Finished migration AddUserSettings1652367743993","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:15.031Z"}}
{"level":"info","message":"Starting migration AddAPIKeyColumn1652905585850","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:15.034Z"}}
{"level":"info","message":"Finished migration AddAPIKeyColumn1652905585850","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:15.038Z"}}
{"level":"info","message":"Starting migration IntroducePinData1654090467022","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:15.040Z"}}
{"level":"info","message":"Finished migration IntroducePinData1654090467022","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:15.042Z"}}
{"level":"info","message":"Starting migration AddNodeIds1658932090381","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:15.043Z"}}
{"level":"info","message":"Finished migration AddNodeIds1658932090381","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:15.044Z"}}
{"level":"info","message":"Starting migration AddJsonKeyPinData1659902242948","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:15.046Z"}}
{"level":"info","message":"Finished migration AddJsonKeyPinData1659902242948","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:15.047Z"}}
{"level":"info","message":"Starting migration CreateCredentialsUserRole1660062385367","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:15.049Z"}}
{"level":"info","message":"Finished migration CreateCredentialsUserRole1660062385367","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:15.050Z"}}
{"level":"info","message":"Starting migration CreateWorkflowsEditorRole1663755770893","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:15.052Z"}}
{"level":"info","message":"Finished migration CreateWorkflowsEditorRole1663755770893","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:15.053Z"}}
{"level":"info","message":"Starting migration WorkflowStatistics1664196174001","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:15.055Z"}}
{"level":"info","message":"Finished migration WorkflowStatistics1664196174001","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:15.060Z"}}
{"level":"info","message":"Starting migration CreateCredentialUsageTable1665484192212","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:15.062Z"}}
{"level":"info","message":"Finished migration CreateCredentialUsageTable1665484192212","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:15.068Z"}}
{"level":"info","message":"Starting migration RemoveCredentialUsageTable1665754637025","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:15.071Z"}}
{"level":"info","message":"Finished migration RemoveCredentialUsageTable1665754637025","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:15.075Z"}}
{"level":"info","message":"Starting migration AddWorkflowVersionIdColumn1669739707126","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:15.077Z"}}
{"level":"info","message":"Finished migration AddWorkflowVersionIdColumn1669739707126","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:15.079Z"}}
{"level":"info","message":"Starting migration AddTriggerCountColumn1669823906995","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:15.080Z"}}
{"level":"info","message":"Finished migration AddTriggerCountColumn1669823906995","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:15.081Z"}}
{"level":"info","message":"Starting migration MessageEventBusDestinations1671535397530","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:15.083Z"}}
{"level":"info","message":"Finished migration MessageEventBusDestinations1671535397530","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:15.089Z"}}
{"level":"info","message":"Starting migration RemoveWorkflowDataLoadedFlag1671726148421","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:15.091Z"}}
{"level":"info","message":"Finished migration RemoveWorkflowDataLoadedFlag1671726148421","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:15.093Z"}}
{"level":"info","message":"Starting migration DeleteExecutionsWithWorkflows1673268682475","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:15.095Z"}}
{"level":"info","message":"Finished migration DeleteExecutionsWithWorkflows1673268682475","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:15.119Z"}}
{"level":"info","message":"Starting migration AddStatusToExecutions1674138566000","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:15.122Z"}}
{"level":"info","message":"Finished migration AddStatusToExecutions1674138566000","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:15.123Z"}}
{"level":"info","message":"Starting migration CreateLdapEntities1674509946020","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:15.126Z"}}
{"level":"info","message":"Finished migration CreateLdapEntities1674509946020","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:15.139Z"}}
{"level":"info","message":"Starting migration PurgeInvalidWorkflowConnections1675940580449","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:15.142Z"}}
{"level":"info","message":"Finished migration PurgeInvalidWorkflowConnections1675940580449","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:15.146Z"}}
{"level":"info","message":"Starting migration MigrateExecutionStatus1676996103000","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:15.147Z"}}
{"level":"info","message":"Finished migration MigrateExecutionStatus1676996103000","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:15.149Z"}}
{"level":"info","message":"Starting migration UpdateRunningExecutionStatus1677236854063","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:15.151Z"}}
{"level":"info","message":"Finished migration UpdateRunningExecutionStatus1677236854063","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:15.154Z"}}
{"level":"info","message":"Starting migration CreateVariables1677501636754","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:15.158Z"}}
{"level":"info","message":"Finished migration CreateVariables1677501636754","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:15.165Z"}}
{"level":"info","message":"Starting migration CreateExecutionMetadataTable1679416281778","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:15.167Z"}}
{"level":"info","message":"Finished migration CreateExecutionMetadataTable1679416281778","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:15.181Z"}}
{"level":"info","message":"Starting migration AddUserActivatedProperty1681134145996","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:15.183Z"}}
{"level":"info","message":"Finished migration AddUserActivatedProperty1681134145996","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:15.189Z"}}
{"level":"info","message":"Starting migration RemoveSkipOwnerSetup1681134145997","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:15.191Z"}}
{"level":"info","message":"Finished migration RemoveSkipOwnerSetup1681134145997","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:15.192Z"}}
{"level":"info","message":"Starting migration MigrateIntegerKeysToString1690000000000","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:15.194Z"}}
{"level":"info","message":"Finished migration MigrateIntegerKeysToString1690000000000","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:15.282Z"}}
{"level":"info","message":"Starting migration SeparateExecutionData1690000000020","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:15.286Z"}}
{"level":"info","message":"Finished migration SeparateExecutionData1690000000020","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:15.291Z"}}
{"level":"info","message":"Starting migration RemoveResetPasswordColumns1690000000030","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:15.293Z"}}
{"level":"info","message":"Finished migration RemoveResetPasswordColumns1690000000030","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:15.332Z"}}
{"level":"info","message":"Starting migration AddMfaColumns1690000000030","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:15.335Z"}}
{"level":"info","message":"Finished migration AddMfaColumns1690000000030","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:15.340Z"}}
{"level":"info","message":"Starting migration AddMissingPrimaryKeyOnExecutionData1690787606731","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:15.343Z"}}
{"level":"info","message":"Finished migration AddMissingPrimaryKeyOnExecutionData1690787606731","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:15.346Z"}}
{"level":"info","message":"Starting migration CreateWorkflowNameIndex1691088862123","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:15.348Z"}}
{"level":"info","message":"Finished migration CreateWorkflowNameIndex1691088862123","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:15.373Z"}}
{"level":"info","message":"Starting migration CreateWorkflowHistoryTable1692967111175","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:15.375Z"}}
{"level":"info","message":"Finished migration CreateWorkflowHistoryTable1692967111175","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:15.387Z"}}
{"level":"info","message":"Starting migration ExecutionSoftDelete1693491613982","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:15.389Z"}}
{"level":"info","message":"Finished migration ExecutionSoftDelete1693491613982","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:15.414Z"}}
{"level":"info","message":"Starting migration DisallowOrphanExecutions1693554410387","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:15.416Z"}}
{"level":"info","message":"Finished migration DisallowOrphanExecutions1693554410387","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:15.441Z"}}
{"level":"info","message":"Starting migration MigrateToTimestampTz1694091729095","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:15.443Z"}}
{"level":"info","message":"Finished migration MigrateToTimestampTz1694091729095","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:15.667Z"}}
{"level":"info","message":"Starting migration AddWorkflowMetadata1695128658538","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:15.676Z"}}
{"level":"info","message":"Finished migration AddWorkflowMetadata1695128658538","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:15.726Z"}}
{"level":"info","message":"Starting migration ModifyWorkflowHistoryNodesAndConnections1695829275184","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:15.728Z"}}
{"level":"info","message":"Finished migration ModifyWorkflowHistoryNodesAndConnections1695829275184","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:15.754Z"}}
{"level":"info","message":"Starting migration AddGlobalAdminRole1700571993961","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:15.758Z"}}
{"level":"info","message":"Finished migration AddGlobalAdminRole1700571993961","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:15.760Z"}}
{"level":"info","message":"Starting migration DropRoleMapping1705429061930","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:15.762Z"}}
{"level":"info","message":"Finished migration DropRoleMapping1705429061930","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:15.933Z"}}
{"level":"info","message":"Starting migration RemoveFailedExecutionStatus1711018413374","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:15.936Z"}}
{"level":"info","message":"Finished migration RemoveFailedExecutionStatus1711018413374","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:15.938Z"}}
{"level":"info","message":"Starting migration MoveSshKeysToDatabase1711390882123","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:15.941Z"}}
{"level":"info","message":"[MoveSshKeysToDatabase1711390882123] No SSH keys in filesystem, skipping","metadata":{"file":"1711390882123-MoveSshKeysToDatabase.js","function":"up","timestamp":"2025-07-25T23:27:15.942Z"}}
{"level":"info","message":"Finished migration MoveSshKeysToDatabase1711390882123","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:15.942Z"}}
{"level":"info","message":"Starting migration RemoveNodesAccess1712044305787","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:15.944Z"}}
{"level":"info","message":"Finished migration RemoveNodesAccess1712044305787","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:15.964Z"}}
{"level":"info","message":"Starting migration CreateProject1714133768519","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:15.967Z"}}
{"level":"info","message":"Finished migration CreateProject1714133768519","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:16.174Z"}}
{"level":"info","message":"Starting migration MakeExecutionStatusNonNullable1714133768521","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:16.178Z"}}
{"level":"info","message":"Finished migration MakeExecutionStatusNonNullable1714133768521","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:16.206Z"}}
{"level":"info","message":"Starting migration AddActivatedAtUserSetting1717498465931","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:16.210Z"}}
{"level":"info","message":"Finished migration AddActivatedAtUserSetting1717498465931","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:16.212Z"}}
{"level":"info","message":"Starting migration AddConstraintToExecutionMetadata1720101653148","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:16.215Z"}}
{"level":"info","message":"Finished migration AddConstraintToExecutionMetadata1720101653148","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:16.262Z"}}
{"level":"info","message":"Starting migration FixExecutionMetadataSequence1721377157740","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:16.265Z"}}
{"level":"info","message":"Finished migration FixExecutionMetadataSequence1721377157740","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:16.266Z"}}
{"level":"info","message":"Starting migration CreateInvalidAuthTokenTable1723627610222","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:16.268Z"}}
{"level":"info","message":"Finished migration CreateInvalidAuthTokenTable1723627610222","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:16.276Z"}}
{"level":"info","message":"Starting migration RefactorExecutionIndices1723796243146","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:16.277Z"}}
{"level":"info","message":"Finished migration RefactorExecutionIndices1723796243146","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:16.291Z"}}
{"level":"info","message":"Starting migration CreateAnnotationTables1724753530828","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:16.294Z"}}
{"level":"info","message":"Finished migration CreateAnnotationTables1724753530828","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:16.323Z"}}
{"level":"info","message":"Starting migration AddApiKeysTable1724951148974","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:16.325Z"}}
{"level":"info","message":"Finished migration AddApiKeysTable1724951148974","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:16.341Z"}}
{"level":"info","message":"Starting migration CreateProcessedDataTable1726606152711","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:16.343Z"}}
{"level":"info","message":"Finished migration CreateProcessedDataTable1726606152711","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:16.351Z"}}
{"level":"info","message":"Starting migration SeparateExecutionCreationFromStart1727427440136","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:16.354Z"}}
{"level":"info","message":"Finished migration SeparateExecutionCreationFromStart1727427440136","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:16.378Z"}}
{"level":"info","message":"Starting migration AddMissingPrimaryKeyOnAnnotationTagMapping1728659839644","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:16.382Z"}}
{"level":"info","message":"Finished migration AddMissingPrimaryKeyOnAnnotationTagMapping1728659839644","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:16.404Z"}}
{"level":"info","message":"Starting migration UpdateProcessedDataValueColumnToText1729607673464","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:16.409Z"}}
{"level":"info","message":"Finished migration UpdateProcessedDataValueColumnToText1729607673464","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:16.434Z"}}
{"level":"info","message":"Starting migration AddProjectIcons1729607673469","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:16.438Z"}}
{"level":"info","message":"Finished migration AddProjectIcons1729607673469","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:16.461Z"}}
{"level":"info","message":"Starting migration CreateTestDefinitionTable1730386903556","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:16.464Z"}}
{"level":"info","message":"Finished migration CreateTestDefinitionTable1730386903556","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:16.477Z"}}
{"level":"info","message":"Starting migration AddDescriptionToTestDefinition1731404028106","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:16.479Z"}}
{"level":"info","message":"Finished migration AddDescriptionToTestDefinition1731404028106","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:16.500Z"}}
{"level":"info","message":"Starting migration MigrateTestDefinitionKeyToString1731582748663","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:16.504Z"}}
{"level":"info","message":"Finished migration MigrateTestDefinitionKeyToString1731582748663","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:16.527Z"}}
{"level":"info","message":"Starting migration CreateTestMetricTable1732271325258","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:16.531Z"}}
{"level":"info","message":"Finished migration CreateTestMetricTable1732271325258","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:16.553Z"}}
{"level":"info","message":"Starting migration CreateTestRun1732549866705","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:16.557Z"}}
{"level":"info","message":"Finished migration CreateTestRun1732549866705","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:16.576Z"}}
{"level":"info","message":"Starting migration AddMockedNodesColumnToTestDefinition1733133775640","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:16.579Z"}}
{"level":"info","message":"Finished migration AddMockedNodesColumnToTestDefinition1733133775640","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:16.581Z"}}
{"level":"info","message":"Starting migration AddManagedColumnToCredentialsTable1734479635324","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:16.583Z"}}
{"level":"info","message":"Finished migration AddManagedColumnToCredentialsTable1734479635324","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:16.585Z"}}
{"level":"info","message":"Starting migration AddStatsColumnsToTestRun1736172058779","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:16.588Z"}}
{"level":"info","message":"Finished migration AddStatsColumnsToTestRun1736172058779","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:16.592Z"}}
{"level":"info","message":"Starting migration CreateTestCaseExecutionTable1736947513045","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:16.595Z"}}
{"level":"info","message":"Finished migration CreateTestCaseExecutionTable1736947513045","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:16.630Z"}}
{"level":"info","message":"Starting migration AddErrorColumnsToTestRuns1737715421462","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:16.635Z"}}
{"level":"info","message":"Finished migration AddErrorColumnsToTestRuns1737715421462","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:16.638Z"}}
{"level":"info","message":"Starting migration CreateFolderTable1738709609940","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:16.643Z"}}
{"level":"info","message":"Finished migration CreateFolderTable1738709609940","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:16.686Z"}}
{"level":"info","message":"Starting migration CreateAnalyticsTables1739549398681","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:16.694Z"}}
{"level":"info","message":"Finished migration CreateAnalyticsTables1739549398681","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:16.737Z"}}
{"level":"info","message":"Starting migration UpdateParentFolderIdColumn1740445074052","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:16.745Z"}}
{"level":"info","message":"Finished migration UpdateParentFolderIdColumn1740445074052","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:16.777Z"}}
{"level":"info","message":"Starting migration RenameAnalyticsToInsights1741167584277","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:16.782Z"}}
{"level":"info","message":"Finished migration RenameAnalyticsToInsights1741167584277","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:16.876Z"}}
{"level":"info","message":"Starting migration AddScopesColumnToApiKeys1742918400000","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:16.879Z"}}
{"level":"info","message":"Finished migration AddScopesColumnToApiKeys1742918400000","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:16.896Z"}}
{"level":"info","message":"Starting migration ClearEvaluation1745322634000","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:16.899Z"}}
{"level":"info","message":"Finished migration ClearEvaluation1745322634000","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:17.014Z"}}
{"level":"info","message":"Starting migration AddWorkflowStatisticsRootCount1745587087521","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:17.017Z"}}
{"level":"info","message":"Finished migration AddWorkflowStatisticsRootCount1745587087521","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:17.018Z"}}
{"level":"info","message":"Starting migration AddWorkflowArchivedColumn1745934666076","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:17.021Z"}}
{"level":"info","message":"Finished migration AddWorkflowArchivedColumn1745934666076","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:17.023Z"}}
{"level":"info","message":"Starting migration DropRoleTable1745934666077","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:17.026Z"}}
{"level":"info","message":"Finished migration DropRoleTable1745934666077","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:17.052Z"}}
{"level":"info","message":"Starting migration AddProjectDescriptionColumn1747824239000","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:17.055Z"}}
{"level":"info","message":"Finished migration AddProjectDescriptionColumn1747824239000","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:17.056Z"}}
{"level":"info","message":"Starting migration AddLastActiveAtColumnToUser1750252139166","metadata":{"file":"migration-helpers.js","function":"logMigrationStart","timestamp":"2025-07-25T23:27:17.058Z"}}
{"level":"info","message":"Finished migration AddLastActiveAtColumnToUser1750252139166","metadata":{"file":"migration-helpers.js","function":"logMigrationEnd","timestamp":"2025-07-25T23:27:17.058Z"}}
{"level":"warn","message":"\nThere is a deprecation related to your environment variables. Please take the recommended actions to update your configuration:\n - OFFLOAD_MANUAL_EXECUTIONS_TO_WORKERS -> Running manual executions in the main instance in scaling mode is deprecated. Manual executions will be routed to workers in a future version. Please set `OFFLOAD_MANUAL_EXECUTIONS_TO_WORKERS=true` to offload manual executions to workers and avoid potential issues in the future. Consider increasing memory available to workers and reducing memory available to main.\n","metadata":{"file":"deprecation.service.js","function":"warn","timestamp":"2025-07-25T23:27:17.060Z"}}
{"level":"info","message":"n8n Task Broker ready on 127.0.0.1, port 5679","metadata":{"file":"task-broker-server.js","function":"setupHttpServer","timestamp":"2025-07-25T23:27:17.099Z"}}
{"level":"info","message":"[license SDK] Skipping renewal on init because renewal is not due yet or cert is not initialized","metadata":{"file":"LicenseManager.js","function":"log","scopes":["license"],"timestamp":"2025-07-25T23:27:17.182Z"}}
{"level":"info","message":"Registered runner \"JS Task Runner\" (D_7KLVqdAuimZDDnVKjmA) ","metadata":{"file":"task-broker-ws-server.js","function":"onMessage","timestamp":"2025-07-25T23:27:19.547Z"}}
{"level":"info","message":"Version: 1.103.2","metadata":{"file":"abstract-server.js","function":"start","timestamp":"2025-07-25T23:27:19.822Z"}}
{"level":"info","message":"\nEditor is now accessible via:\nhttp://localhost:5678","metadata":{"file":"base-command.js","function":"log","timestamp":"2025-07-25T23:27:19.907Z"}}
{"level":"info","message":"Owner was set up successfully","metadata":{"file":"owner.controller.js","function":"setupOwner","timestamp":"2025-07-25T23:32:08.810Z"}}
{"level":"info","message":"User survey updated successfully","metadata":{"file":"me.controller.js","function":"storeSurveyAnswers","timestamp":"2025-07-25T23:32:38.944Z","userId":"2e2ef891-9c4d-4bec-9958-c860b1b68192"}}
{"level":"info","message":"[license SDK] license successfully activated","metadata":{"file":"LicenseManager.js","function":"log","scopes":["license"],"timestamp":"2025-07-25T23:33:51.777Z"}}
{"level":"info","message":"Received SIGTERM. Shutting down...","metadata":{"file":"base-command.js","timestamp":"2025-07-25T23:38:03.799Z"}}
{"level":"info","message":"[Task Runner]: Received SIGTERM signal, shutting down...","metadata":{"file":"forward-to-logger.js","scopes":["task-runner"],"timestamp":"2025-07-25T23:38:03.873Z"}}
{"level":"info","message":"[Task Runner]: Task runner stopped","metadata":{"file":"forward-to-logger.js","scopes":["task-runner"],"timestamp":"2025-07-25T23:38:03.884Z"}}
{"level":"info","message":"\nStopping n8n...","metadata":{"file":"start.js","function":"stopProcess","timestamp":"2025-07-25T23:38:03.981Z"}}
{"level":"info","message":"n8n Task Broker ready on 127.0.0.1, port 5679","metadata":{"file":"task-broker-server.js","function":"setupHttpServer","timestamp":"2025-07-25T23:39:09.526Z"}}
{"level":"error","message":"Last session crashed","metadata":{"file":"crash-journal.js","function":"init","timestamp":"2025-07-25T23:39:09.579Z"}}
{"level":"info","message":"[license SDK] Skipping renewal on init because renewal is not due yet or cert is not initialized","metadata":{"file":"LicenseManager.js","function":"log","scopes":["license"],"timestamp":"2025-07-25T23:39:09.839Z"}}
{"level":"info","message":"\nn8n worker is now ready","metadata":{"file":"worker.js","function":"run","scopes":["scaling"],"timestamp":"2025-07-25T23:39:10.190Z"}}
{"level":"info","message":" * Version: 1.103.2","metadata":{"file":"worker.js","function":"run","scopes":["scaling"],"timestamp":"2025-07-25T23:39:10.192Z"}}
{"level":"info","message":" * Concurrency: 10","metadata":{"file":"worker.js","function":"run","scopes":["scaling"],"timestamp":"2025-07-25T23:39:10.192Z"}}
{"level":"info","message":"","metadata":{"file":"worker.js","function":"run","scopes":["scaling"],"timestamp":"2025-07-25T23:39:10.194Z"}}
{"level":"info","message":"\nn8n worker server listening on port 5678","metadata":{"file":"worker-server.js","function":"init","scopes":["scaling"],"timestamp":"2025-07-25T23:39:10.281Z"}}
{"level":"info","message":"Registered runner \"JS Task Runner\" (b3yoYx_HWONqAXDqtox0m) ","metadata":{"file":"task-broker-ws-server.js","function":"onMessage","timestamp":"2025-07-25T23:39:11.787Z"}}
{"level":"info","message":"Initializing n8n process","metadata":{"file":"start.js","function":"init","timestamp":"2025-07-25T23:39:19.609Z"}}
{"level":"info","message":"n8n ready on ::, port 5678","metadata":{"file":"abstract-server.js","function":"init","timestamp":"2025-07-25T23:39:21.906Z"}}
{"level":"warn","message":"\nThere is a deprecation related to your environment variables. Please take the recommended actions to update your configuration:\n - OFFLOAD_MANUAL_EXECUTIONS_TO_WORKERS -> Running manual executions in the main instance in scaling mode is deprecated. Manual executions will be routed to workers in a future version. Please set `OFFLOAD_MANUAL_EXECUTIONS_TO_WORKERS=true` to offload manual executions to workers and avoid potential issues in the future. Consider increasing memory available to workers and reducing memory available to main.\n","metadata":{"file":"deprecation.service.js","function":"warn","timestamp":"2025-07-25T23:39:21.938Z"}}
{"level":"info","message":"n8n Task Broker ready on 127.0.0.1, port 5679","metadata":{"file":"task-broker-server.js","function":"setupHttpServer","timestamp":"2025-07-25T23:39:22.037Z"}}
{"level":"info","message":"[license SDK] Skipping renewal on init because renewal is not due yet or cert is not initialized","metadata":{"file":"LicenseManager.js","function":"log","scopes":["license"],"timestamp":"2025-07-25T23:39:22.296Z"}}
{"level":"info","message":"Registered runner \"JS Task Runner\" (6nu3laNYDjXhoRmfJ3og5) ","metadata":{"file":"task-broker-ws-server.js","function":"onMessage","timestamp":"2025-07-25T23:39:25.447Z"}}
{"level":"info","message":"Version: 1.103.2","metadata":{"file":"abstract-server.js","function":"start","timestamp":"2025-07-25T23:39:25.601Z"}}
{"level":"info","message":"\nEditor is now accessible via:\nhttp://localhost:5678","metadata":{"file":"base-command.js","function":"log","timestamp":"2025-07-25T23:39:25.707Z"}}
{"level":"info","message":"[license SDK] license successfully renewed","metadata":{"file":"LicenseManager.js","function":"log","scopes":["license"],"timestamp":"2025-07-25T23:41:45.470Z"}}
{"level":"info","message":"[license SDK] Skipping renewal on init because renewal is not due yet or cert is not initialized","metadata":{"file":"LicenseManager.js","function":"log","scopes":["license"],"timestamp":"2025-07-25T23:41:45.963Z"}}
{"level":"info","message":"[license SDK] license successfully renewed","metadata":{"file":"LicenseManager.js","function":"log","scopes":["license"],"timestamp":"2025-07-25T23:41:54.165Z"}}
{"level":"info","message":"[license SDK] Skipping renewal on init because renewal is not due yet or cert is not initialized","metadata":{"file":"LicenseManager.js","function":"log","scopes":["license"],"timestamp":"2025-07-25T23:41:54.490Z"}}
{"level":"info","message":"Received SIGTERM. Shutting down...","metadata":{"file":"base-command.js","scopes":["scaling"],"timestamp":"2025-07-26T00:02:04.949Z"}}
{"level":"info","message":"[Task Runner]: Received SIGTERM signal, shutting down...","metadata":{"file":"forward-to-logger.js","scopes":["task-runner"],"timestamp":"2025-07-26T00:02:05.697Z"}}
{"level":"info","message":"[Task Runner]: Task runner stopped","metadata":{"file":"forward-to-logger.js","scopes":["task-runner"],"timestamp":"2025-07-26T00:02:05.734Z"}}
{"level":"info","message":"Stopping worker...","metadata":{"file":"worker.js","function":"stopProcess","scopes":["scaling"],"timestamp":"2025-07-26T00:02:05.991Z"}}
{"level":"info","message":"Received SIGTERM. Shutting down...","metadata":{"file":"base-command.js","timestamp":"2025-07-26T00:02:08.336Z"}}
{"level":"info","message":"[Task Runner]: Received SIGTERM signal, shutting down...","metadata":{"file":"forward-to-logger.js","scopes":["task-runner"],"timestamp":"2025-07-26T00:02:08.524Z"}}
{"level":"info","message":"[Task Runner]: Task runner stopped","metadata":{"file":"forward-to-logger.js","scopes":["task-runner"],"timestamp":"2025-07-26T00:02:08.570Z"}}
{"level":"info","message":"\nStopping n8n...","metadata":{"file":"start.js","function":"stopProcess","timestamp":"2025-07-26T00:02:08.707Z"}}
{"level":"error","message":"Last session crashed","metadata":{"file":"crash-journal.js","function":"init","timestamp":"2025-07-26T00:02:55.699Z"}}
{"level":"warn","message":"\nThere is a deprecation related to your environment variables. Please take the recommended actions to update your configuration:\n - N8N_SKIP_WEBHOOK_DEREGISTRATION_SHUTDOWN -> n8n no longer deregisters webhooks at startup and shutdown. Remove this environment variable; it is no longer needed.\n","metadata":{"file":"deprecation.service.js","function":"warn","timestamp":"2025-07-26T00:02:55.752Z"}}
{"level":"info","message":"n8n Task Broker ready on 127.0.0.1, port 5679","metadata":{"file":"task-broker-server.js","function":"setupHttpServer","timestamp":"2025-07-26T00:02:55.929Z"}}
{"level":"info","message":"[license SDK] Skipping renewal on init because renewal is not due yet or cert is not initialized","metadata":{"file":"LicenseManager.js","function":"log","scopes":["license"],"timestamp":"2025-07-26T00:02:56.065Z"}}
{"level":"info","message":"\nn8n worker is now ready","metadata":{"file":"worker.js","function":"run","scopes":["scaling"],"timestamp":"2025-07-26T00:02:56.300Z"}}
{"level":"info","message":" * Version: 1.103.2","metadata":{"file":"worker.js","function":"run","scopes":["scaling"],"timestamp":"2025-07-26T00:02:56.301Z"}}
{"level":"info","message":" * Concurrency: 10","metadata":{"file":"worker.js","function":"run","scopes":["scaling"],"timestamp":"2025-07-26T00:02:56.301Z"}}
{"level":"info","message":"","metadata":{"file":"worker.js","function":"run","scopes":["scaling"],"timestamp":"2025-07-26T00:02:56.301Z"}}
{"level":"info","message":"\nn8n worker server listening on port 5678","metadata":{"file":"worker-server.js","function":"init","scopes":["scaling"],"timestamp":"2025-07-26T00:02:56.350Z"}}
{"level":"info","message":"Registered runner \"JS Task Runner\" (zlMucUCtmKdkQLD1gn4A7) ","metadata":{"file":"task-broker-ws-server.js","function":"onMessage","timestamp":"2025-07-26T00:02:57.415Z"}}
{"level":"info","message":"Initializing n8n process","metadata":{"file":"start.js","function":"init","timestamp":"2025-07-26T00:03:05.707Z"}}
{"level":"info","message":"n8n ready on ::, port 5678","metadata":{"file":"abstract-server.js","function":"init","timestamp":"2025-07-26T00:03:06.572Z"}}
{"level":"warn","message":"\nThere are deprecations related to your environment variables. Please take the recommended actions to update your configuration:\n - N8N_SKIP_WEBHOOK_DEREGISTRATION_SHUTDOWN -> n8n no longer deregisters webhooks at startup and shutdown. Remove this environment variable; it is no longer needed.\n - OFFLOAD_MANUAL_EXECUTIONS_TO_WORKERS -> Running manual executions in the main instance in scaling mode is deprecated. Manual executions will be routed to workers in a future version. Please set `OFFLOAD_MANUAL_EXECUTIONS_TO_WORKERS=true` to offload manual executions to workers and avoid potential issues in the future. Consider increasing memory available to workers and reducing memory available to main.\n","metadata":{"file":"deprecation.service.js","function":"warn","timestamp":"2025-07-26T00:03:06.602Z"}}
{"level":"info","message":"n8n Task Broker ready on 127.0.0.1, port 5679","metadata":{"file":"task-broker-server.js","function":"setupHttpServer","timestamp":"2025-07-26T00:03:06.709Z"}}
{"level":"info","message":"[license SDK] Skipping renewal on init because renewal is not due yet or cert is not initialized","metadata":{"file":"LicenseManager.js","function":"log","scopes":["license"],"timestamp":"2025-07-26T00:03:06.922Z"}}
{"level":"info","message":"Registered runner \"JS Task Runner\" (J2yA8dlR0HzBq_GUVB-Cs) ","metadata":{"file":"task-broker-ws-server.js","function":"onMessage","timestamp":"2025-07-26T00:03:09.790Z"}}
{"level":"info","message":"Version: 1.103.2","metadata":{"file":"abstract-server.js","function":"start","timestamp":"2025-07-26T00:03:12.290Z"}}
{"level":"info","message":"\nEditor is now accessible via:\nhttp://localhost:5678","metadata":{"file":"base-command.js","function":"log","timestamp":"2025-07-26T00:03:12.418Z"}}
{"level":"info","message":"[license SDK] license successfully renewed","metadata":{"file":"LicenseManager.js","function":"log","scopes":["license"],"timestamp":"2025-07-26T00:11:29.850Z"}}
{"level":"info","message":"[license SDK] Skipping renewal on init because renewal is not due yet or cert is not initialized","metadata":{"file":"LicenseManager.js","function":"log","scopes":["license"],"timestamp":"2025-07-26T00:11:30.364Z"}}
{"level":"info","message":"Received SIGTERM. Shutting down...","metadata":{"file":"base-command.js","timestamp":"2025-07-26T00:16:55.927Z"}}
{"level":"info","message":"Received SIGTERM. Shutting down...","metadata":{"file":"base-command.js","scopes":["scaling"],"timestamp":"2025-07-26T00:16:55.946Z"}}
{"level":"info","message":"[Task Runner]: Received SIGTERM signal, shutting down...","metadata":{"file":"forward-to-logger.js","scopes":["task-runner"],"timestamp":"2025-07-26T00:16:56.010Z"}}
{"level":"info","message":"[Task Runner]: Task runner stopped","metadata":{"file":"forward-to-logger.js","scopes":["task-runner"],"timestamp":"2025-07-26T00:16:56.063Z"}}
{"level":"info","message":"\nStopping n8n...","metadata":{"file":"start.js","function":"stopProcess","timestamp":"2025-07-26T00:16:56.152Z"}}
{"level":"info","message":"[Task Runner]: Received SIGTERM signal, shutting down...","metadata":{"file":"forward-to-logger.js","scopes":["task-runner"],"timestamp":"2025-07-26T00:16:56.586Z"}}
{"level":"info","message":"[Task Runner]: Task runner stopped","metadata":{"file":"forward-to-logger.js","scopes":["task-runner"],"timestamp":"2025-07-26T00:16:56.607Z"}}
{"level":"info","message":"Stopping worker...","metadata":{"file":"worker.js","function":"stopProcess","scopes":["scaling"],"timestamp":"2025-07-26T00:16:56.713Z"}}
{"level":"error","message":"Last session crashed","metadata":{"file":"crash-journal.js","function":"init","timestamp":"2025-07-26T00:17:12.007Z"}}
{"level":"warn","message":"\nThere is a deprecation related to your environment variables. Please take the recommended actions to update your configuration:\n - N8N_SKIP_WEBHOOK_DEREGISTRATION_SHUTDOWN -> n8n no longer deregisters webhooks at startup and shutdown. Remove this environment variable; it is no longer needed.\n","metadata":{"file":"deprecation.service.js","function":"warn","timestamp":"2025-07-26T00:17:13.742Z"}}
{"level":"info","message":"n8n Task Broker ready on 127.0.0.1, port 5679","metadata":{"file":"task-broker-server.js","function":"setupHttpServer","timestamp":"2025-07-26T00:17:14.100Z"}}
{"level":"info","message":"[license SDK] Skipping renewal on init because renewal is not due yet or cert is not initialized","metadata":{"file":"LicenseManager.js","function":"log","scopes":["license"],"timestamp":"2025-07-26T00:17:14.377Z"}}
{"level":"info","message":"\nn8n worker is now ready","metadata":{"file":"worker.js","function":"run","scopes":["scaling"],"timestamp":"2025-07-26T00:17:14.919Z"}}
{"level":"info","message":" * Version: 1.103.2","metadata":{"file":"worker.js","function":"run","scopes":["scaling"],"timestamp":"2025-07-26T00:17:14.920Z"}}
{"level":"info","message":" * Concurrency: 10","metadata":{"file":"worker.js","function":"run","scopes":["scaling"],"timestamp":"2025-07-26T00:17:14.922Z"}}
{"level":"info","message":"","metadata":{"file":"worker.js","function":"run","scopes":["scaling"],"timestamp":"2025-07-26T00:17:14.922Z"}}
{"level":"info","message":"\nn8n worker server listening on port 5678","metadata":{"file":"worker-server.js","function":"init","scopes":["scaling"],"timestamp":"2025-07-26T00:17:15.001Z"}}
{"level":"info","message":"Registered runner \"JS Task Runner\" (GRh8AeUTuX1IVHLJmZptH) ","metadata":{"file":"task-broker-ws-server.js","function":"onMessage","timestamp":"2025-07-26T00:17:16.528Z"}}
{"level":"info","message":"Initializing n8n process","metadata":{"file":"start.js","function":"init","timestamp":"2025-07-26T00:17:22.043Z"}}
{"level":"info","message":"n8n ready on ::, port 5678","metadata":{"file":"abstract-server.js","function":"init","timestamp":"2025-07-26T00:17:22.575Z"}}
{"level":"warn","message":"\nThere are deprecations related to your environment variables. Please take the recommended actions to update your configuration:\n - N8N_SKIP_WEBHOOK_DEREGISTRATION_SHUTDOWN -> n8n no longer deregisters webhooks at startup and shutdown. Remove this environment variable; it is no longer needed.\n - OFFLOAD_MANUAL_EXECUTIONS_TO_WORKERS -> Running manual executions in the main instance in scaling mode is deprecated. Manual executions will be routed to workers in a future version. Please set `OFFLOAD_MANUAL_EXECUTIONS_TO_WORKERS=true` to offload manual executions to workers and avoid potential issues in the future. Consider increasing memory available to workers and reducing memory available to main.\n","metadata":{"file":"deprecation.service.js","function":"warn","timestamp":"2025-07-26T00:17:22.594Z"}}
{"level":"info","message":"n8n Task Broker ready on 127.0.0.1, port 5679","metadata":{"file":"task-broker-server.js","function":"setupHttpServer","timestamp":"2025-07-26T00:17:22.643Z"}}
{"level":"info","message":"[license SDK] Skipping renewal on init because renewal is not due yet or cert is not initialized","metadata":{"file":"LicenseManager.js","function":"log","scopes":["license"],"timestamp":"2025-07-26T00:17:22.776Z"}}
{"level":"info","message":"Registered runner \"JS Task Runner\" (YUEnv5OsKZ4ZdPs5pTdkC) ","metadata":{"file":"task-broker-ws-server.js","function":"onMessage","timestamp":"2025-07-26T00:17:25.305Z"}}
{"level":"info","message":"Version: 1.103.2","metadata":{"file":"abstract-server.js","function":"start","timestamp":"2025-07-26T00:17:27.517Z"}}
{"level":"info","message":"\nEditor is now accessible via:\nhttp://localhost:5678","metadata":{"file":"base-command.js","function":"log","timestamp":"2025-07-26T00:17:27.635Z"}}
{"level":"info","message":"[license SDK] license successfully renewed","metadata":{"file":"LicenseManager.js","function":"log","scopes":["license"],"timestamp":"2025-07-26T00:38:49.647Z"}}
{"level":"info","message":"[license SDK] Skipping renewal on init because renewal is not due yet or cert is not initialized","metadata":{"file":"LicenseManager.js","function":"log","scopes":["license"],"timestamp":"2025-07-26T00:38:50.221Z"}}
{"level":"info","message":"Received SIGTERM. Shutting down...","metadata":{"file":"base-command.js","timestamp":"2025-07-26T00:46:59.076Z"}}
{"level":"info","message":"Received SIGTERM. Shutting down...","metadata":{"file":"base-command.js","scopes":["scaling"],"timestamp":"2025-07-26T00:46:59.092Z"}}
{"level":"info","message":"[Task Runner]: Received SIGTERM signal, shutting down...","metadata":{"file":"forward-to-logger.js","scopes":["task-runner"],"timestamp":"2025-07-26T00:46:59.166Z"}}
{"level":"info","message":"[Task Runner]: Task runner stopped","metadata":{"file":"forward-to-logger.js","scopes":["task-runner"],"timestamp":"2025-07-26T00:46:59.229Z"}}
{"level":"info","message":"\nStopping n8n...","metadata":{"file":"start.js","function":"stopProcess","timestamp":"2025-07-26T00:46:59.343Z"}}
{"level":"info","message":"[Task Runner]: Received SIGTERM signal, shutting down...","metadata":{"file":"forward-to-logger.js","scopes":["task-runner"],"timestamp":"2025-07-26T00:46:59.757Z"}}
{"level":"info","message":"[Task Runner]: Task runner stopped","metadata":{"file":"forward-to-logger.js","scopes":["task-runner"],"timestamp":"2025-07-26T00:46:59.780Z"}}
{"level":"info","message":"Stopping worker...","metadata":{"file":"worker.js","function":"stopProcess","scopes":["scaling"],"timestamp":"2025-07-26T00:46:59.955Z"}}
{"level":"error","message":"Last session crashed","metadata":{"file":"crash-journal.js","function":"init","timestamp":"2025-07-26T00:47:16.038Z"}}
{"level":"warn","message":"\nThere is a deprecation related to your environment variables. Please take the recommended actions to update your configuration:\n - N8N_SKIP_WEBHOOK_DEREGISTRATION_SHUTDOWN -> n8n no longer deregisters webhooks at startup and shutdown. Remove this environment variable; it is no longer needed.\n","metadata":{"file":"deprecation.service.js","function":"warn","timestamp":"2025-07-26T00:47:16.243Z"}}
{"level":"info","message":"n8n Task Broker ready on 127.0.0.1, port 5679","metadata":{"file":"task-broker-server.js","function":"setupHttpServer","timestamp":"2025-07-26T00:47:16.387Z"}}
{"level":"info","message":"[license SDK] Skipping renewal on init because renewal is not due yet or cert is not initialized","metadata":{"file":"LicenseManager.js","function":"log","scopes":["license"],"timestamp":"2025-07-26T00:47:16.546Z"}}
{"level":"info","message":"\nn8n worker is now ready","metadata":{"file":"worker.js","function":"run","scopes":["scaling"],"timestamp":"2025-07-26T00:47:16.869Z"}}
{"level":"info","message":" * Version: 1.103.2","metadata":{"file":"worker.js","function":"run","scopes":["scaling"],"timestamp":"2025-07-26T00:47:16.870Z"}}
{"level":"info","message":" * Concurrency: 10","metadata":{"file":"worker.js","function":"run","scopes":["scaling"],"timestamp":"2025-07-26T00:47:16.871Z"}}
{"level":"info","message":"","metadata":{"file":"worker.js","function":"run","scopes":["scaling"],"timestamp":"2025-07-26T00:47:16.871Z"}}
{"level":"info","message":"\nn8n worker server listening on port 5678","metadata":{"file":"worker-server.js","function":"init","scopes":["scaling"],"timestamp":"2025-07-26T00:47:16.947Z"}}
{"level":"info","message":"Registered runner \"JS Task Runner\" (3gyXAACYeHn-BQ5QEaJaM) ","metadata":{"file":"task-broker-ws-server.js","function":"onMessage","timestamp":"2025-07-26T00:47:18.199Z"}}
{"level":"info","message":"Initializing n8n process","metadata":{"file":"start.js","function":"init","timestamp":"2025-07-26T00:47:26.047Z"}}
{"level":"info","message":"n8n ready on ::, port 5678","metadata":{"file":"abstract-server.js","function":"init","timestamp":"2025-07-26T00:47:26.408Z"}}
{"level":"warn","message":"\nThere are deprecations related to your environment variables. Please take the recommended actions to update your configuration:\n - N8N_SKIP_WEBHOOK_DEREGISTRATION_SHUTDOWN -> n8n no longer deregisters webhooks at startup and shutdown. Remove this environment variable; it is no longer needed.\n - OFFLOAD_MANUAL_EXECUTIONS_TO_WORKERS -> Running manual executions in the main instance in scaling mode is deprecated. Manual executions will be routed to workers in a future version. Please set `OFFLOAD_MANUAL_EXECUTIONS_TO_WORKERS=true` to offload manual executions to workers and avoid potential issues in the future. Consider increasing memory available to workers and reducing memory available to main.\n","metadata":{"file":"deprecation.service.js","function":"warn","timestamp":"2025-07-26T00:47:26.421Z"}}
{"level":"info","message":"n8n Task Broker ready on 127.0.0.1, port 5679","metadata":{"file":"task-broker-server.js","function":"setupHttpServer","timestamp":"2025-07-26T00:47:26.453Z"}}
{"level":"info","message":"[license SDK] Skipping renewal on init because renewal is not due yet or cert is not initialized","metadata":{"file":"LicenseManager.js","function":"log","scopes":["license"],"timestamp":"2025-07-26T00:47:26.533Z"}}
{"level":"info","message":"Registered runner \"JS Task Runner\" (kcttvESHImUsgMIFdGVs-) ","metadata":{"file":"task-broker-ws-server.js","function":"onMessage","timestamp":"2025-07-26T00:47:28.699Z"}}
{"level":"info","message":"Version: 1.103.2","metadata":{"file":"abstract-server.js","function":"start","timestamp":"2025-07-26T00:47:28.935Z"}}
{"level":"info","message":"\nEditor is now accessible via:\nhttp://localhost:5678","metadata":{"file":"base-command.js","function":"log","timestamp":"2025-07-26T00:47:29.119Z"}}
{"level":"info","message":"[license SDK] license successfully renewed","metadata":{"file":"LicenseManager.js","function":"log","scopes":["license"],"timestamp":"2025-07-26T00:47:40.366Z"}}
{"level":"info","message":"[license SDK] Skipping renewal on init because renewal is not due yet or cert is not initialized","metadata":{"file":"LicenseManager.js","function":"log","scopes":["license"],"timestamp":"2025-07-26T00:47:40.679Z"}}
{"level":"info","message":"[license SDK] license successfully renewed","metadata":{"file":"LicenseManager.js","function":"log","scopes":["license"],"timestamp":"2025-07-26T00:48:03.107Z"}}
{"level":"info","message":"[license SDK] Skipping renewal on init because renewal is not due yet or cert is not initialized","metadata":{"file":"LicenseManager.js","function":"log","scopes":["license"],"timestamp":"2025-07-26T00:48:03.417Z"}}
{"level":"error","message":"Error loading package \"n8n-nodes-discord\" :The specified package could not be loaded\nCause: Cannot find module '@discordjs/rest'\nRequire stack:\n- /home/<USER>/.n8n/nodes/node_modules/n8n-nodes-discord/dist/nodes/Discord/bot/commands.js\n- /home/<USER>/.n8n/nodes/node_modules/n8n-nodes-discord/dist/nodes/Discord/bot/ipcEvents/credentials.ipc.js\n- /home/<USER>/.n8n/nodes/node_modules/n8n-nodes-discord/dist/nodes/Discord/bot/index.js\n- /home/<USER>/.n8n/nodes/node_modules/n8n-nodes-discord/dist/nodes/Discord/Discord.node.js\n- /usr/local/lib/node_modules/n8n/node_modules/.pnpm/n8n-core@file+packages+core_openai@5.8.1_ws@8.17.1_zod@3.25.67_/node_modules/n8n-core/dist/nodes-loader/load-class-in-isolation.js\n- /usr/local/lib/node_modules/n8n/node_modules/.pnpm/n8n-core@file+packages+core_openai@5.8.1_ws@8.17.1_zod@3.25.67_/node_modules/n8n-core/dist/nodes-loader/directory-loader.js\n- /usr/local/lib/node_modules/n8n/node_modules/.pnpm/n8n-core@file+packages+core_openai@5.8.1_ws@8.17.1_zod@3.25.67_/node_modules/n8n-core/dist/nodes-loader/index.js\n- /usr/local/lib/node_modules/n8n/node_modules/.pnpm/n8n-core@file+packages+core_openai@5.8.1_ws@8.17.1_zod@3.25.67_/node_modules/n8n-core/dist/index.js\n- /usr/local/lib/node_modules/n8n/node_modules/.pnpm/@n8n+db@file+packages+@n8n+db_@sentry+node@8.52.1_ioredis@5.3.2_mongodb@6.11.0_@aws-sdk_f7bd9984f5e38971ca100f511ef3ee0b/node_modules/@n8n/db/dist/repositories/execution.repository.js\n- /usr/local/lib/node_modules/n8n/node_modules/.pnpm/@n8n+db@file+packages+@n8n+db_@sentry+node@8.52.1_ioredis@5.3.2_mongodb@6.11.0_@aws-sdk_f7bd9984f5e38971ca100f511ef3ee0b/node_modules/@n8n/db/dist/repositories/index.js\n- /usr/local/lib/node_modules/n8n/node_modules/.pnpm/@n8n+db@file+packages+@n8n+db_@sentry+node@8.52.1_ioredis@5.3.2_mongodb@6.11.0_@aws-sdk_f7bd9984f5e38971ca100f511ef3ee0b/node_modules/@n8n/db/dist/index.js\n- /usr/local/lib/node_modules/n8n/dist/commands/start.js\n- /usr/local/lib/node_modules/n8n/dist/command-registry.js\nResponseError: Error loading package \"n8n-nodes-discord\" :The specified package could not be loaded\nCause: Cannot find module '@discordjs/rest'\nRequire stack:\n- /home/<USER>/.n8n/nodes/node_modules/n8n-nodes-discord/dist/nodes/Discord/bot/commands.js\n- /home/<USER>/.n8n/nodes/node_modules/n8n-nodes-discord/dist/nodes/Discord/bot/ipcEvents/credentials.ipc.js\n- /home/<USER>/.n8n/nodes/node_modules/n8n-nodes-discord/dist/nodes/Discord/bot/index.js\n- /home/<USER>/.n8n/nodes/node_modules/n8n-nodes-discord/dist/nodes/Discord/Discord.node.js\n- /usr/local/lib/node_modules/n8n/node_modules/.pnpm/n8n-core@file+packages+core_openai@5.8.1_ws@8.17.1_zod@3.25.67_/node_modules/n8n-core/dist/nodes-loader/load-class-in-isolation.js\n- /usr/local/lib/node_modules/n8n/node_modules/.pnpm/n8n-core@file+packages+core_openai@5.8.1_ws@8.17.1_zod@3.25.67_/node_modules/n8n-core/dist/nodes-loader/directory-loader.js\n- /usr/local/lib/node_modules/n8n/node_modules/.pnpm/n8n-core@file+packages+core_openai@5.8.1_ws@8.17.1_zod@3.25.67_/node_modules/n8n-core/dist/nodes-loader/index.js\n- /usr/local/lib/node_modules/n8n/node_modules/.pnpm/n8n-core@file+packages+core_openai@5.8.1_ws@8.17.1_zod@3.25.67_/node_modules/n8n-core/dist/index.js\n- /usr/local/lib/node_modules/n8n/node_modules/.pnpm/@n8n+db@file+packages+@n8n+db_@sentry+node@8.52.1_ioredis@5.3.2_mongodb@6.11.0_@aws-sdk_f7bd9984f5e38971ca100f511ef3ee0b/node_modules/@n8n/db/dist/repositories/execution.repository.js\n- /usr/local/lib/node_modules/n8n/node_modules/.pnpm/@n8n+db@file+packages+@n8n+db_@sentry+node@8.52.1_ioredis@5.3.2_mongodb@6.11.0_@aws-sdk_f7bd9984f5e38971ca100f511ef3ee0b/node_modules/@n8n/db/dist/repositories/index.js\n- /usr/local/lib/node_modules/n8n/node_modules/.pnpm/@n8n+db@file+packages+@n8n+db_@sentry+node@8.52.1_ioredis@5.3.2_mongodb@6.11.0_@aws-sdk_f7bd9984f5e38971ca100f511ef3ee0b/node_modules/@n8n/db/dist/index.js\n- /usr/local/lib/node_modules/n8n/dist/commands/start.js\n- /usr/local/lib/node_modules/n8n/dist/command-registry.js\n    at CommunityPackagesController.installPackage (/usr/local/lib/node_modules/n8n/src/controllers/community-packages.controller.ts:127:10)\n    at handler (/usr/local/lib/node_modules/n8n/src/controller.registry.ts:78:12)\n    at /usr/local/lib/node_modules/n8n/src/response-helper.ts:157:17\n","metadata":{"file":"error-reporter.js","function":"defaultReport","timestamp":"2025-07-26T00:49:19.919Z"}}
{"level":"info","message":"Community package installed: @jordanburke/n8n-nodes-discord","metadata":{"file":"community-packages.service.js","function":"installOrUpdatePackage","timestamp":"2025-07-26T00:52:18.073Z"}}
{"level":"info","message":"Community package installed: @jordanburke/n8n-nodes-discord","metadata":{"file":"community-packages.service.js","function":"installOrUpdateNpmPackage","timestamp":"2025-07-26T00:52:37.261Z"}}
{"level":"info","message":"[license SDK] license successfully renewed","metadata":{"file":"LicenseManager.js","function":"log","scopes":["license"],"timestamp":"2025-07-26T00:55:32.053Z"}}
{"level":"info","message":"[license SDK] Skipping renewal on init because renewal is not due yet or cert is not initialized","metadata":{"file":"LicenseManager.js","function":"log","scopes":["license"],"timestamp":"2025-07-26T00:55:32.404Z"}}
{"level":"info","message":"Community package installed: n8n-nodes-mcp-client","metadata":{"file":"community-packages.service.js","function":"installOrUpdatePackage","timestamp":"2025-07-26T00:56:13.506Z"}}
{"level":"info","message":"Community package installed: n8n-nodes-mcp-client","metadata":{"file":"community-packages.service.js","function":"installOrUpdateNpmPackage","timestamp":"2025-07-26T00:56:21.569Z"}}
{"level":"info","message":"Community package installed: n8n-nodes-mcp-client","metadata":{"file":"community-packages.service.js","function":"installOrUpdatePackage","timestamp":"2025-07-26T00:56:32.026Z"}}
{"level":"error","message":"nodes package n8n-nodes-mcp-client is already loaded.\n Please delete this second copy at path /home/<USER>/.n8n/nodes/node_modules/n8n-nodes-mcp-client","metadata":{"file":"error-reporter.js","function":"defaultReport","timestamp":"2025-07-26T00:56:34.869Z"}}
{"level":"error","message":"getaddrinfo ENOTFOUND postgres","metadata":{"file":"error-reporter.js","function":"defaultReport","timestamp":"2025-07-26T01:03:25.973Z"}}
{"level":"error","message":"getaddrinfo EAI_AGAIN postgres","metadata":{"file":"error-reporter.js","function":"defaultReport","timestamp":"2025-07-26T01:03:26.912Z"}}
{"level":"error","message":"getaddrinfo ENOTFOUND postgres","metadata":{"file":"error-reporter.js","function":"defaultReport","timestamp":"2025-07-26T01:03:30.734Z"}}
{"level":"error","message":"Received request for unknown webhook: The requested webhook \"GET a81bf21b-3c52-4afc-9bad-608b3f5b96b5/chat\" is not registered.","metadata":{"currentlyRegistered":[],"file":"webhook-request-handler.js","function":"handleRequest","timestamp":"2025-07-26T01:53:13.639Z"}}
{"level":"error","message":"Node does not have any credentials set","metadata":{"file":"error-reporter.js","function":"defaultReport","timestamp":"2025-07-26T02:45:27.434Z"}}
{"level":"error","message":"Authorization failed - please check your credentials: Invalid API key","metadata":{"file":"error-reporter.js","function":"defaultReport","timestamp":"2025-07-26T02:47:47.635Z"}}
{"level":"error","message":"Authorization failed - please check your credentials","metadata":{"file":"error-reporter.js","function":"defaultReport","timestamp":"2025-07-26T02:47:47.780Z"}}
{"level":"error","message":"Authorization failed - please check your credentials","metadata":{"file":"error-reporter.js","function":"defaultReport","timestamp":"2025-07-26T02:48:05.338Z"}}
{"level":"error","message":"Authorization failed - please check your credentials","metadata":{"file":"error-reporter.js","function":"defaultReport","timestamp":"2025-07-26T02:48:06.968Z"}}
{"level":"error","message":"Authorization failed - please check your credentials","metadata":{"file":"error-reporter.js","function":"defaultReport","timestamp":"2025-07-26T02:49:09.764Z"}}
{"level":"error","message":"Authorization failed - please check your credentials","metadata":{"file":"error-reporter.js","function":"defaultReport","timestamp":"2025-07-26T02:49:12.588Z"}}
{"level":"error","message":"Authorization failed - please check your credentials","metadata":{"file":"error-reporter.js","function":"defaultReport","timestamp":"2025-07-26T02:49:13.733Z"}}
{"level":"info","message":"[license SDK] license successfully renewed","metadata":{"file":"LicenseManager.js","function":"log","scopes":["license"],"timestamp":"2025-07-26T02:52:44.542Z"}}
{"level":"info","message":"[license SDK] Skipping renewal on init because renewal is not due yet or cert is not initialized","metadata":{"file":"LicenseManager.js","function":"log","scopes":["license"],"timestamp":"2025-07-26T02:52:45.301Z"}}
{"level":"error","message":"Error loading package \"oriondesign2015/n8n-nodes-evolution-api\" :Command failed: npm pack oriondesign2015/n8n-nodes-evolution-api@latest --registry=https://registry.npmjs.org --quiet\nnpm error code ENOENT\nnpm error syscall open\nnpm error path /home/<USER>/.n8n/nodes/oriondesign2015/n8n-nodes-evolution-api@latest/package.json\nnpm error errno -2\nnpm error enoent Could not read package.json: Error: ENOENT: no such file or directory, open '/home/<USER>/.n8n/nodes/oriondesign2015/n8n-nodes-evolution-api@latest/package.json'\nnpm error enoent This is related to npm not being able to find a file.\nnpm error enoent\nnpm error A complete log of this run can be found in: /home/<USER>/.npm/_logs/2025-07-26T02_54_39_032Z-debug-0.log\n\nResponseError: Error loading package \"oriondesign2015/n8n-nodes-evolution-api\" :Command failed: npm pack oriondesign2015/n8n-nodes-evolution-api@latest --registry=https://registry.npmjs.org --quiet\nnpm error code ENOENT\nnpm error syscall open\nnpm error path /home/<USER>/.n8n/nodes/oriondesign2015/n8n-nodes-evolution-api@latest/package.json\nnpm error errno -2\nnpm error enoent Could not read package.json: Error: ENOENT: no such file or directory, open '/home/<USER>/.n8n/nodes/oriondesign2015/n8n-nodes-evolution-api@latest/package.json'\nnpm error enoent This is related to npm not being able to find a file.\nnpm error enoent\nnpm error A complete log of this run can be found in: /home/<USER>/.npm/_logs/2025-07-26T02_54_39_032Z-debug-0.log\n\n    at CommunityPackagesController.installPackage (/usr/local/lib/node_modules/n8n/src/controllers/community-packages.controller.ts:127:10)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at handler (/usr/local/lib/node_modules/n8n/src/controller.registry.ts:78:12)\n    at /usr/local/lib/node_modules/n8n/src/response-helper.ts:157:17\n","metadata":{"file":"error-reporter.js","function":"defaultReport","timestamp":"2025-07-26T02:54:39.378Z"}}
{"level":"error","message":"Error loading package \"@oriondesign2015/n8n-nodes-evolution-api\" :Command failed: npm pack @oriondesign2015/n8n-nodes-evolution-api@latest --registry=https://registry.npmjs.org --quiet\nnpm error code E404\nnpm error 404 Not Found - GET https://registry.npmjs.org/@oriondesign2015%2fn8n-nodes-evolution-api - Not found\nnpm error 404\nnpm error 404  '@oriondesign2015/n8n-nodes-evolution-api@latest' is not in this registry.\nnpm error 404\nnpm error 404 Note that you can also install from a\nnpm error 404 tarball, folder, http url, or git url.\nnpm error A complete log of this run can be found in: /home/<USER>/.npm/_logs/2025-07-26T02_54_49_856Z-debug-0.log\n\nResponseError: Error loading package \"@oriondesign2015/n8n-nodes-evolution-api\" :Command failed: npm pack @oriondesign2015/n8n-nodes-evolution-api@latest --registry=https://registry.npmjs.org --quiet\nnpm error code E404\nnpm error 404 Not Found - GET https://registry.npmjs.org/@oriondesign2015%2fn8n-nodes-evolution-api - Not found\nnpm error 404\nnpm error 404  '@oriondesign2015/n8n-nodes-evolution-api@latest' is not in this registry.\nnpm error 404\nnpm error 404 Note that you can also install from a\nnpm error 404 tarball, folder, http url, or git url.\nnpm error A complete log of this run can be found in: /home/<USER>/.npm/_logs/2025-07-26T02_54_49_856Z-debug-0.log\n\n    at CommunityPackagesController.installPackage (/usr/local/lib/node_modules/n8n/src/controllers/community-packages.controller.ts:127:10)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at handler (/usr/local/lib/node_modules/n8n/src/controller.registry.ts:78:12)\n    at /usr/local/lib/node_modules/n8n/src/response-helper.ts:157:17\n","metadata":{"file":"error-reporter.js","function":"defaultReport","timestamp":"2025-07-26T02:54:52.072Z"}}
{"level":"error","message":"Error loading package \"@oriondesign2015/n8n-nodes-evolution-api\" :Command failed: npm pack @oriondesign2015/n8n-nodes-evolution-api@latest --registry=https://registry.npmjs.org --quiet\nnpm error code E404\nnpm error 404 Not Found - GET https://registry.npmjs.org/@oriondesign2015%2fn8n-nodes-evolution-api - Not found\nnpm error 404\nnpm error 404  '@oriondesign2015/n8n-nodes-evolution-api@latest' is not in this registry.\nnpm error 404\nnpm error 404 Note that you can also install from a\nnpm error 404 tarball, folder, http url, or git url.\nnpm error A complete log of this run can be found in: /home/<USER>/.npm/_logs/2025-07-26T02_54_56_304Z-debug-0.log\n\nResponseError: Error loading package \"@oriondesign2015/n8n-nodes-evolution-api\" :Command failed: npm pack @oriondesign2015/n8n-nodes-evolution-api@latest --registry=https://registry.npmjs.org --quiet\nnpm error code E404\nnpm error 404 Not Found - GET https://registry.npmjs.org/@oriondesign2015%2fn8n-nodes-evolution-api - Not found\nnpm error 404\nnpm error 404  '@oriondesign2015/n8n-nodes-evolution-api@latest' is not in this registry.\nnpm error 404\nnpm error 404 Note that you can also install from a\nnpm error 404 tarball, folder, http url, or git url.\nnpm error A complete log of this run can be found in: /home/<USER>/.npm/_logs/2025-07-26T02_54_56_304Z-debug-0.log\n\n    at CommunityPackagesController.installPackage (/usr/local/lib/node_modules/n8n/src/controllers/community-packages.controller.ts:127:10)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at handler (/usr/local/lib/node_modules/n8n/src/controller.registry.ts:78:12)\n    at /usr/local/lib/node_modules/n8n/src/response-helper.ts:157:17\n","metadata":{"file":"error-reporter.js","function":"defaultReport","timestamp":"2025-07-26T02:54:57.074Z"}}
{"level":"error","message":"Sheet with ID 2007238977 not found","metadata":{"file":"error-reporter.js","function":"defaultReport","timestamp":"2025-07-26T02:56:19.946Z"}}
{"level":"error","message":"Sheet with ID 2007238977 not found","metadata":{"file":"error-reporter.js","function":"defaultReport","timestamp":"2025-07-26T02:56:20.215Z"}}
{"level":"info","message":"Received SIGTERM. Shutting down...","metadata":{"file":"base-command.js","scopes":["scaling"],"timestamp":"2025-07-26T05:22:03.401Z"}}
{"level":"info","message":"Received SIGTERM. Shutting down...","metadata":{"file":"base-command.js","timestamp":"2025-07-26T05:22:03.407Z"}}
{"level":"error","message":"terminating connection due to administrator command","metadata":{"file":"error-reporter.js","function":"defaultReport","timestamp":"2025-07-26T05:22:03.581Z"}}
{"level":"error","message":"Connection terminated unexpectedly","metadata":{"file":"error-reporter.js","function":"defaultReport","timestamp":"2025-07-26T05:22:03.752Z"}}
{"level":"info","message":"[Task Runner]: Received SIGTERM signal, shutting down...","metadata":{"file":"forward-to-logger.js","scopes":["task-runner"],"timestamp":"2025-07-26T05:22:03.768Z"}}
{"level":"error","message":"Failed to shutdown gracefully\nError: Failed to shutdown gracefully\n    at ShutdownService.shutdownComponent (/usr/local/lib/node_modules/n8n/src/shutdown/shutdown.service.ts:95:29)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at /usr/local/lib/node_modules/n8n/src/shutdown/shutdown.service.ts:81:41\n    at async Promise.allSettled (index 0)\n    at ShutdownService.startShutdown (/usr/local/lib/node_modules/n8n/src/shutdown/shutdown.service.ts:80:4)\n    at ShutdownService.waitForShutdown (/usr/local/lib/node_modules/n8n/src/shutdown/shutdown.service.ts:69:3)\n    at process.<anonymous> (/usr/local/lib/node_modules/n8n/src/commands/base-command.ts:306:4)\n","metadata":{"component":"ScalingService.stop()","file":"error-reporter.js","function":"defaultReport","timestamp":"2025-07-26T05:22:03.822Z"}}
{"level":"error","message":"Socket closed unexpectedly","metadata":{"file":"error-reporter.js","function":"defaultReport","timestamp":"2025-07-26T05:22:03.827Z"}}
{"level":"info","message":"[Task Runner]: Task runner stopped","metadata":{"file":"forward-to-logger.js","scopes":["task-runner"],"timestamp":"2025-07-26T05:22:03.849Z"}}
{"level":"error","message":"Connection is closed.","metadata":{"file":"error-reporter.js","function":"defaultReport","timestamp":"2025-07-26T05:22:03.822Z"}}
{"level":"info","message":"[Task Runner]: Received SIGTERM signal, shutting down...","metadata":{"file":"forward-to-logger.js","scopes":["task-runner"],"timestamp":"2025-07-26T05:22:03.873Z"}}
{"level":"info","message":"[Task Runner]: Task runner stopped","metadata":{"file":"forward-to-logger.js","scopes":["task-runner"],"timestamp":"2025-07-26T05:22:03.939Z"}}
{"level":"info","message":"\nStopping n8n...","metadata":{"file":"start.js","function":"stopProcess","timestamp":"2025-07-26T05:22:04.002Z"}}
{"level":"info","message":"Stopping worker...","metadata":{"file":"worker.js","function":"stopProcess","scopes":["scaling"],"timestamp":"2025-07-26T05:22:04.023Z"}}
{"level":"warn","message":"Lost Redis connection. Trying to reconnect in 1s... (0s/10s)","metadata":{"file":"redis-client.service.js","scopes":["redis","scaling"],"timestamp":"2025-07-26T05:22:04.654Z"}}
{"level":"warn","message":"Lost Redis connection. Trying to reconnect in 1s... (0s/10s)","metadata":{"file":"redis-client.service.js","scopes":["redis","scaling"],"timestamp":"2025-07-26T05:22:04.815Z"}}
{"level":"error","message":"[Redis client] getaddrinfo ENOTFOUND redis","metadata":{"error":{"message":"getaddrinfo ENOTFOUND redis","name":"Error","stack":"Error: getaddrinfo ENOTFOUND redis\n    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:111:26)"},"file":"redis-client.service.js","scopes":["redis","scaling"],"timestamp":"2025-07-26T05:22:08.641Z"}}
{"level":"error","message":"[Redis client] getaddrinfo ENOTFOUND redis","metadata":{"error":{"message":"getaddrinfo ENOTFOUND redis","name":"Error","stack":"Error: getaddrinfo ENOTFOUND redis\n    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:111:26)"},"file":"redis-client.service.js","scopes":["redis","scaling"],"timestamp":"2025-07-26T05:22:08.642Z"}}
{"level":"error","message":"[Redis client] getaddrinfo ENOTFOUND redis","metadata":{"error":{"message":"getaddrinfo ENOTFOUND redis","name":"Error","stack":"Error: getaddrinfo ENOTFOUND redis\n    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:111:26)\n    at GetAddrInfoReqWrap.callbackTrampoline (node:internal/async_hooks:130:17)"},"file":"redis-client.service.js","scopes":["redis","scaling"],"timestamp":"2025-07-26T05:22:08.798Z"}}
{"level":"error","message":"Queue errored","metadata":{"error":{"message":"getaddrinfo ENOTFOUND redis","name":"Error","stack":"Error: getaddrinfo ENOTFOUND redis\n    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:111:26)\n    at GetAddrInfoReqWrap.callbackTrampoline (node:internal/async_hooks:130:17)"},"file":"scaling.service.js","scopes":["scaling"],"timestamp":"2025-07-26T05:22:08.799Z"}}
{"level":"error","message":"getaddrinfo ENOTFOUND redis","metadata":{"file":"error-reporter.js","function":"defaultReport","timestamp":"2025-07-26T05:22:08.801Z"}}
{"level":"error","message":"[Redis client] getaddrinfo ENOTFOUND redis","metadata":{"error":{"message":"getaddrinfo ENOTFOUND redis","name":"Error","stack":"Error: getaddrinfo ENOTFOUND redis\n    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:111:26)\n    at GetAddrInfoReqWrap.callbackTrampoline (node:internal/async_hooks:130:17)"},"file":"redis-client.service.js","scopes":["redis","scaling"],"timestamp":"2025-07-26T05:22:08.802Z"}}
{"level":"warn","message":"Lost Redis connection. Trying to reconnect in 1s... (5s/10s)","metadata":{"file":"redis-client.service.js","scopes":["redis","scaling"],"timestamp":"2025-07-26T05:22:09.642Z"}}
{"level":"warn","message":"Lost Redis connection. Trying to reconnect in 1s... (5s/10s)","metadata":{"file":"redis-client.service.js","scopes":["redis","scaling"],"timestamp":"2025-07-26T05:22:09.811Z"}}
{"level":"error","message":"[Redis client] getaddrinfo ENOTFOUND redis","metadata":{"error":{"message":"getaddrinfo ENOTFOUND redis","name":"Error","stack":"Error: getaddrinfo ENOTFOUND redis\n    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:111:26)"},"file":"redis-client.service.js","scopes":["redis","scaling"],"timestamp":"2025-07-26T05:22:12.544Z"}}
{"level":"error","message":"Queue errored","metadata":{"error":{"message":"getaddrinfo ENOTFOUND redis","name":"Error","stack":"Error: getaddrinfo ENOTFOUND redis\n    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:111:26)"},"file":"scaling.service.js","scopes":["scaling"],"timestamp":"2025-07-26T05:22:12.587Z"}}
{"level":"error","message":"getaddrinfo ENOTFOUND redis","metadata":{"file":"error-reporter.js","function":"defaultReport","timestamp":"2025-07-26T05:22:12.591Z"}}
{"level":"error","message":"[Redis client] getaddrinfo ENOTFOUND redis","metadata":{"error":{"message":"getaddrinfo ENOTFOUND redis","name":"Error","stack":"Error: getaddrinfo ENOTFOUND redis\n    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:111:26)"},"file":"redis-client.service.js","scopes":["redis","scaling"],"timestamp":"2025-07-26T05:22:12.603Z"}}
{"level":"error","message":"Queue errored","metadata":{"error":{"message":"getaddrinfo ENOTFOUND redis","name":"Error","stack":"Error: getaddrinfo ENOTFOUND redis\n    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:111:26)"},"file":"scaling.service.js","scopes":["scaling"],"timestamp":"2025-07-26T05:22:12.608Z"}}
{"level":"error","message":"getaddrinfo ENOTFOUND redis","metadata":{"file":"error-reporter.js","function":"defaultReport","timestamp":"2025-07-26T05:22:12.613Z"}}
{"level":"error","message":"[Redis client] getaddrinfo ENOTFOUND redis","metadata":{"error":{"message":"getaddrinfo ENOTFOUND redis","name":"Error","stack":"Error: getaddrinfo ENOTFOUND redis\n    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:111:26)\n    at GetAddrInfoReqWrap.callbackTrampoline (node:internal/async_hooks:130:17)"},"file":"redis-client.service.js","scopes":["redis","scaling"],"timestamp":"2025-07-26T05:22:12.689Z"}}
{"level":"error","message":"Queue errored","metadata":{"error":{"message":"getaddrinfo ENOTFOUND redis","name":"Error","stack":"Error: getaddrinfo ENOTFOUND redis\n    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:111:26)\n    at GetAddrInfoReqWrap.callbackTrampoline (node:internal/async_hooks:130:17)"},"file":"scaling.service.js","scopes":["scaling"],"timestamp":"2025-07-26T05:22:12.692Z"}}
{"level":"error","message":"getaddrinfo ENOTFOUND redis","metadata":{"file":"error-reporter.js","function":"defaultReport","timestamp":"2025-07-26T05:22:12.695Z"}}
{"level":"warn","message":"Lost Redis connection. Trying to reconnect in 1s... (9s/10s)","metadata":{"file":"redis-client.service.js","scopes":["redis","scaling"],"timestamp":"2025-07-26T05:22:13.663Z"}}
{"level":"warn","message":"Lost Redis connection. Trying to reconnect in 1s... (8.9s/10s)","metadata":{"file":"redis-client.service.js","scopes":["redis","scaling"],"timestamp":"2025-07-26T05:22:13.697Z"}}
{"level":"error","message":"[Redis client] connect ETIMEDOUT","metadata":{"error":{"message":"connect ETIMEDOUT","name":"Error","stack":"Error: connect ETIMEDOUT\n    at Socket.<anonymous> (/usr/local/lib/node_modules/n8n/node_modules/.pnpm/ioredis@5.3.2/node_modules/ioredis/built/Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:604:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at processTimers (node:internal/timers:523:7)"},"file":"redis-client.service.js","scopes":["redis","scaling"],"timestamp":"2025-07-26T05:22:14.656Z"}}
{"level":"error","message":"[Redis client] getaddrinfo EAI_AGAIN redis","metadata":{"error":{"message":"getaddrinfo EAI_AGAIN redis","name":"Error","stack":"Error: getaddrinfo EAI_AGAIN redis\n    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:111:26)\n    at GetAddrInfoReqWrap.callbackTrampoline (node:internal/async_hooks:130:17)"},"file":"redis-client.service.js","scopes":["redis","scaling"],"timestamp":"2025-07-26T05:22:14.820Z"}}
{"level":"error","message":"Last session crashed","metadata":{"file":"crash-journal.js","function":"init","timestamp":"2025-07-27T02:26:51.338Z"}}
{"level":"error","message":"ENOENT: no such file or directory, open '/home/<USER>/.n8n/nodes/node_modules/@oriondesign2015/n8n-nodes-evolution-api/package.json'","metadata":{"file":"load-nodes-and-credentials.js","function":"loadNodesFromNodeModules","timestamp":"2025-07-27T02:26:53.075Z"}}
{"level":"error","message":"ENOENT: no such file or directory, open '/home/<USER>/.n8n/nodes/node_modules/@oriondesign2015/n8n-nodes-evolution-api/package.json'","metadata":{"file":"error-reporter.js","function":"defaultReport","timestamp":"2025-07-27T02:26:53.083Z"}}
{"level":"warn","message":"\nThere is a deprecation related to your environment variables. Please take the recommended actions to update your configuration:\n - N8N_SKIP_WEBHOOK_DEREGISTRATION_SHUTDOWN -> n8n no longer deregisters webhooks at startup and shutdown. Remove this environment variable; it is no longer needed.\n","metadata":{"file":"deprecation.service.js","function":"warn","timestamp":"2025-07-27T02:26:53.477Z"}}
{"level":"info","message":"n8n Task Broker ready on 127.0.0.1, port 5679","metadata":{"file":"task-broker-server.js","function":"setupHttpServer","timestamp":"2025-07-27T02:26:53.631Z"}}
{"level":"info","message":"[license SDK] Skipping renewal on init because renewal is not due yet or cert is not initialized","metadata":{"file":"LicenseManager.js","function":"log","scopes":["license"],"timestamp":"2025-07-27T02:26:53.832Z"}}
{"level":"info","message":"\nn8n worker is now ready","metadata":{"file":"worker.js","function":"run","scopes":["scaling"],"timestamp":"2025-07-27T02:26:54.199Z"}}
{"level":"info","message":" * Version: 1.103.2","metadata":{"file":"worker.js","function":"run","scopes":["scaling"],"timestamp":"2025-07-27T02:26:54.200Z"}}
{"level":"info","message":" * Concurrency: 10","metadata":{"file":"worker.js","function":"run","scopes":["scaling"],"timestamp":"2025-07-27T02:26:54.200Z"}}
{"level":"info","message":"","metadata":{"file":"worker.js","function":"run","scopes":["scaling"],"timestamp":"2025-07-27T02:26:54.201Z"}}
{"level":"info","message":"\nn8n worker server listening on port 5678","metadata":{"file":"worker-server.js","function":"init","scopes":["scaling"],"timestamp":"2025-07-27T02:26:54.304Z"}}
{"level":"info","message":"Registered runner \"JS Task Runner\" (lxUc412nl2SfESoLb6JgS) ","metadata":{"file":"task-broker-ws-server.js","function":"onMessage","timestamp":"2025-07-27T02:26:55.328Z"}}
{"level":"info","message":"Initializing n8n process","metadata":{"file":"start.js","function":"init","timestamp":"2025-07-27T02:27:01.349Z"}}
{"level":"error","message":"ENOENT: no such file or directory, open '/home/<USER>/.n8n/nodes/node_modules/@oriondesign2015/n8n-nodes-evolution-api/package.json'","metadata":{"file":"load-nodes-and-credentials.js","function":"loadNodesFromNodeModules","timestamp":"2025-07-27T02:27:02.287Z"}}
{"level":"error","message":"ENOENT: no such file or directory, open '/home/<USER>/.n8n/nodes/node_modules/@oriondesign2015/n8n-nodes-evolution-api/package.json'","metadata":{"file":"error-reporter.js","function":"defaultReport","timestamp":"2025-07-27T02:27:02.288Z"}}
{"level":"info","message":"n8n ready on ::, port 5678","metadata":{"file":"abstract-server.js","function":"init","timestamp":"2025-07-27T02:27:02.576Z"}}
{"level":"warn","message":"\nThere are deprecations related to your environment variables. Please take the recommended actions to update your configuration:\n - N8N_SKIP_WEBHOOK_DEREGISTRATION_SHUTDOWN -> n8n no longer deregisters webhooks at startup and shutdown. Remove this environment variable; it is no longer needed.\n - OFFLOAD_MANUAL_EXECUTIONS_TO_WORKERS -> Running manual executions in the main instance in scaling mode is deprecated. Manual executions will be routed to workers in a future version. Please set `OFFLOAD_MANUAL_EXECUTIONS_TO_WORKERS=true` to offload manual executions to workers and avoid potential issues in the future. Consider increasing memory available to workers and reducing memory available to main.\n","metadata":{"file":"deprecation.service.js","function":"warn","timestamp":"2025-07-27T02:27:02.590Z"}}
{"level":"info","message":"n8n Task Broker ready on 127.0.0.1, port 5679","metadata":{"file":"task-broker-server.js","function":"setupHttpServer","timestamp":"2025-07-27T02:27:02.623Z"}}
{"level":"info","message":"[license SDK] Skipping renewal on init because renewal is not due yet or cert is not initialized","metadata":{"file":"LicenseManager.js","function":"log","scopes":["license"],"timestamp":"2025-07-27T02:27:02.726Z"}}
{"level":"info","message":"Registered runner \"JS Task Runner\" (STcqILdQMLeZmpXCKp4Ya) ","metadata":{"file":"task-broker-ws-server.js","function":"onMessage","timestamp":"2025-07-27T02:27:05.019Z"}}
{"level":"info","message":"Version: 1.103.2","metadata":{"file":"abstract-server.js","function":"start","timestamp":"2025-07-27T02:27:05.288Z"}}
{"level":"info","message":"Start Active Workflows:","metadata":{"file":"active-workflow-manager.js","function":"addActiveWorkflows","scopes":["workflow-activation"],"timestamp":"2025-07-27T02:27:05.368Z"}}
{"level":"info","message":"Activated workflow \"Agente BigFoot\" (ID: tHU9HJxY9sOOSlQT)","metadata":{"file":"active-workflow-manager.js","function":"activateWorkflow","scopes":["workflow-activation"],"timestamp":"2025-07-27T02:27:06.182Z","workflowId":"tHU9HJxY9sOOSlQT","workflowName":"Agente BigFoot"}}
{"level":"info","message":"\nEditor is now accessible via:\nhttp://localhost:5678","metadata":{"file":"base-command.js","function":"log","timestamp":"2025-07-27T02:27:06.183Z"}}
{"level":"info","message":"Received SIGTERM. Shutting down...","metadata":{"file":"base-command.js","timestamp":"2025-07-27T02:27:36.774Z"}}
{"level":"info","message":"[Task Runner]: Received SIGTERM signal, shutting down...","metadata":{"file":"forward-to-logger.js","scopes":["task-runner"],"timestamp":"2025-07-27T02:27:36.779Z"}}
{"level":"info","message":"[Task Runner]: Task runner stopped","metadata":{"file":"forward-to-logger.js","scopes":["task-runner"],"timestamp":"2025-07-27T02:27:36.782Z"}}
{"level":"info","message":"\nStopping n8n...","metadata":{"file":"start.js","function":"stopProcess","timestamp":"2025-07-27T02:27:36.796Z"}}
{"level":"info","message":"Received SIGTERM. Shutting down...","metadata":{"file":"base-command.js","scopes":["scaling"],"timestamp":"2025-07-27T02:27:37.619Z"}}
{"level":"info","message":"[Task Runner]: Received SIGTERM signal, shutting down...","metadata":{"file":"forward-to-logger.js","scopes":["task-runner"],"timestamp":"2025-07-27T02:27:38.126Z"}}
{"level":"info","message":"[Task Runner]: Task runner stopped","metadata":{"file":"forward-to-logger.js","scopes":["task-runner"],"timestamp":"2025-07-27T02:27:38.128Z"}}
{"level":"info","message":"Stopping worker...","metadata":{"file":"worker.js","function":"stopProcess","scopes":["scaling"],"timestamp":"2025-07-27T02:27:38.136Z"}}
{"level":"error","message":"Last session crashed","metadata":{"file":"crash-journal.js","function":"init","timestamp":"2025-07-27T02:27:44.120Z"}}
{"level":"error","message":"ENOENT: no such file or directory, open '/home/<USER>/.n8n/nodes/node_modules/@oriondesign2015/n8n-nodes-evolution-api/package.json'","metadata":{"file":"load-nodes-and-credentials.js","function":"loadNodesFromNodeModules","timestamp":"2025-07-27T02:27:45.171Z"}}
{"level":"error","message":"ENOENT: no such file or directory, open '/home/<USER>/.n8n/nodes/node_modules/@oriondesign2015/n8n-nodes-evolution-api/package.json'","metadata":{"file":"error-reporter.js","function":"defaultReport","timestamp":"2025-07-27T02:27:45.176Z"}}
{"level":"warn","message":"\nThere is a deprecation related to your environment variables. Please take the recommended actions to update your configuration:\n - N8N_SKIP_WEBHOOK_DEREGISTRATION_SHUTDOWN -> n8n no longer deregisters webhooks at startup and shutdown. Remove this environment variable; it is no longer needed.\n","metadata":{"file":"deprecation.service.js","function":"warn","timestamp":"2025-07-27T02:27:45.550Z"}}
{"level":"warn","message":"n8n detected that some packages are missing. For more information, visit https://docs.n8n.io/integrations/community-nodes/troubleshooting/","metadata":{"file":"community-packages.service.js","function":"checkForMissingPackages","timestamp":"2025-07-27T02:27:45.588Z"}}
{"level":"info","message":"n8n Task Broker ready on 127.0.0.1, port 5679","metadata":{"file":"task-broker-server.js","function":"setupHttpServer","timestamp":"2025-07-27T02:27:45.722Z"}}
{"level":"info","message":"[license SDK] Skipping renewal on init because renewal is not due yet or cert is not initialized","metadata":{"file":"LicenseManager.js","function":"log","scopes":["license"],"timestamp":"2025-07-27T02:27:45.811Z"}}
{"level":"info","message":"\nn8n worker is now ready","metadata":{"file":"worker.js","function":"run","scopes":["scaling"],"timestamp":"2025-07-27T02:27:46.071Z"}}
{"level":"info","message":" * Version: 1.103.2","metadata":{"file":"worker.js","function":"run","scopes":["scaling"],"timestamp":"2025-07-27T02:27:46.071Z"}}
{"level":"info","message":" * Concurrency: 10","metadata":{"file":"worker.js","function":"run","scopes":["scaling"],"timestamp":"2025-07-27T02:27:46.072Z"}}
{"level":"info","message":"","metadata":{"file":"worker.js","function":"run","scopes":["scaling"],"timestamp":"2025-07-27T02:27:46.072Z"}}
{"level":"info","message":"\nn8n worker server listening on port 5678","metadata":{"file":"worker-server.js","function":"init","scopes":["scaling"],"timestamp":"2025-07-27T02:27:46.133Z"}}
{"level":"info","message":"Registered runner \"JS Task Runner\" (xG0MECytqwsU21RFTGWyC) ","metadata":{"file":"task-broker-ws-server.js","function":"onMessage","timestamp":"2025-07-27T02:27:47.854Z"}}
{"level":"info","message":"Initializing n8n process","metadata":{"file":"start.js","function":"init","timestamp":"2025-07-27T02:27:54.129Z"}}
{"level":"error","message":"ENOENT: no such file or directory, open '/home/<USER>/.n8n/nodes/node_modules/@oriondesign2015/n8n-nodes-evolution-api/package.json'","metadata":{"file":"load-nodes-and-credentials.js","function":"loadNodesFromNodeModules","timestamp":"2025-07-27T02:27:54.879Z"}}
{"level":"error","message":"ENOENT: no such file or directory, open '/home/<USER>/.n8n/nodes/node_modules/@oriondesign2015/n8n-nodes-evolution-api/package.json'","metadata":{"file":"error-reporter.js","function":"defaultReport","timestamp":"2025-07-27T02:27:54.879Z"}}
{"level":"info","message":"n8n ready on ::, port 5678","metadata":{"file":"abstract-server.js","function":"init","timestamp":"2025-07-27T02:27:55.105Z"}}
{"level":"warn","message":"\nThere are deprecations related to your environment variables. Please take the recommended actions to update your configuration:\n - N8N_SKIP_WEBHOOK_DEREGISTRATION_SHUTDOWN -> n8n no longer deregisters webhooks at startup and shutdown. Remove this environment variable; it is no longer needed.\n - OFFLOAD_MANUAL_EXECUTIONS_TO_WORKERS -> Running manual executions in the main instance in scaling mode is deprecated. Manual executions will be routed to workers in a future version. Please set `OFFLOAD_MANUAL_EXECUTIONS_TO_WORKERS=true` to offload manual executions to workers and avoid potential issues in the future. Consider increasing memory available to workers and reducing memory available to main.\n","metadata":{"file":"deprecation.service.js","function":"warn","timestamp":"2025-07-27T02:27:55.125Z"}}
{"level":"warn","message":"n8n detected that some packages are missing. For more information, visit https://docs.n8n.io/integrations/community-nodes/troubleshooting/","metadata":{"file":"community-packages.service.js","function":"checkForMissingPackages","timestamp":"2025-07-27T02:27:55.144Z"}}
{"level":"info","message":"n8n Task Broker ready on 127.0.0.1, port 5679","metadata":{"file":"task-broker-server.js","function":"setupHttpServer","timestamp":"2025-07-27T02:27:55.157Z"}}
{"level":"info","message":"[license SDK] Skipping renewal on init because renewal is not due yet or cert is not initialized","metadata":{"file":"LicenseManager.js","function":"log","scopes":["license"],"timestamp":"2025-07-27T02:27:55.228Z"}}
{"level":"info","message":"Registered runner \"JS Task Runner\" (E43fzu2KjSUilqnS512vH) ","metadata":{"file":"task-broker-ws-server.js","function":"onMessage","timestamp":"2025-07-27T02:27:56.964Z"}}
{"level":"info","message":"Version: 1.103.2","metadata":{"file":"abstract-server.js","function":"start","timestamp":"2025-07-27T02:27:57.063Z"}}
{"level":"info","message":"Start Active Workflows:","metadata":{"file":"active-workflow-manager.js","function":"addActiveWorkflows","scopes":["workflow-activation"],"timestamp":"2025-07-27T02:27:57.113Z"}}
{"level":"info","message":"Activated workflow \"Agente BigFoot\" (ID: tHU9HJxY9sOOSlQT)","metadata":{"file":"active-workflow-manager.js","function":"activateWorkflow","scopes":["workflow-activation"],"timestamp":"2025-07-27T02:27:57.553Z","workflowId":"tHU9HJxY9sOOSlQT","workflowName":"Agente BigFoot"}}
{"level":"info","message":"\nEditor is now accessible via:\nhttp://localhost:5678","metadata":{"file":"base-command.js","function":"log","timestamp":"2025-07-27T02:27:57.554Z"}}
{"level":"info","message":"Received SIGTERM. Shutting down...","metadata":{"file":"base-command.js","timestamp":"2025-07-27T07:13:34.164Z"}}
{"level":"info","message":"Received SIGTERM. Shutting down...","metadata":{"file":"base-command.js","scopes":["scaling"],"timestamp":"2025-07-27T07:13:34.253Z"}}
{"level":"info","message":"[Task Runner]: Received SIGTERM signal, shutting down...","metadata":{"file":"forward-to-logger.js","scopes":["task-runner"],"timestamp":"2025-07-27T07:13:34.773Z"}}
{"level":"info","message":"[Task Runner]: Task runner stopped","metadata":{"file":"forward-to-logger.js","scopes":["task-runner"],"timestamp":"2025-07-27T07:13:34.902Z"}}
{"level":"error","message":"connect ECONNREFUSED ***********:5432","metadata":{"file":"error-reporter.js","function":"defaultReport","timestamp":"2025-07-27T07:13:34.989Z"}}
{"level":"info","message":"\nStopping n8n...","metadata":{"file":"start.js","function":"stopProcess","timestamp":"2025-07-27T07:13:35.244Z"}}
{"level":"warn","message":"Lost Redis connection. Trying to reconnect in 1s... (0s/10s)","metadata":{"file":"redis-client.service.js","scopes":["redis","scaling"],"timestamp":"2025-07-27T07:13:35.662Z"}}
{"level":"warn","message":"Lost Redis connection. Trying to reconnect in 1s... (0s/10s)","metadata":{"file":"redis-client.service.js","scopes":["redis","scaling"],"timestamp":"2025-07-27T07:13:35.781Z"}}
{"level":"error","message":"[Redis client] getaddrinfo ENOTFOUND redis","metadata":{"error":{"message":"getaddrinfo ENOTFOUND redis","name":"Error","stack":"Error: getaddrinfo ENOTFOUND redis\n    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:111:26)"},"file":"redis-client.service.js","scopes":["redis","scaling"],"timestamp":"2025-07-27T07:13:39.681Z"}}
{"level":"error","message":"[Redis client] getaddrinfo EAI_AGAIN redis","metadata":{"error":{"message":"getaddrinfo EAI_AGAIN redis","name":"Error","stack":"Error: getaddrinfo EAI_AGAIN redis\n    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:111:26)"},"file":"redis-client.service.js","scopes":["redis","scaling"],"timestamp":"2025-07-27T07:13:40.659Z"}}
{"level":"warn","message":"Lost Redis connection. Trying to reconnect in 1s... (6.1s/10s)","metadata":{"file":"redis-client.service.js","scopes":["redis","scaling"],"timestamp":"2025-07-27T07:13:41.670Z"}}
{"level":"error","message":"[Redis client] getaddrinfo ENOTFOUND redis","metadata":{"error":{"message":"getaddrinfo ENOTFOUND redis","name":"Error","stack":"Error: getaddrinfo ENOTFOUND redis\n    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:111:26)"},"file":"redis-client.service.js","scopes":["redis","scaling"],"timestamp":"2025-07-27T07:13:43.504Z"}}
{"level":"error","message":"Queue errored","metadata":{"error":{"message":"getaddrinfo ENOTFOUND redis","name":"Error","stack":"Error: getaddrinfo ENOTFOUND redis\n    at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:111:26)"},"file":"scaling.service.js","scopes":["scaling"],"timestamp":"2025-07-27T07:13:43.511Z"}}
{"level":"error","message":"getaddrinfo ENOTFOUND redis","metadata":{"file":"error-reporter.js","function":"defaultReport","timestamp":"2025-07-27T07:13:43.521Z"}}
{"level":"warn","message":"Lost Redis connection. Trying to reconnect in 1s... (8.9s/10s)","metadata":{"file":"redis-client.service.js","scopes":["redis","scaling"],"timestamp":"2025-07-27T07:13:44.524Z"}}
{"level":"error","message":"Failed to shutdown gracefully\nError: Failed to shutdown gracefully\n    at ShutdownService.shutdownComponent (/usr/local/lib/node_modules/n8n/src/shutdown/shutdown.service.ts:95:29)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at /usr/local/lib/node_modules/n8n/src/shutdown/shutdown.service.ts:81:41\n    at async Promise.allSettled (index 0)\n    at ShutdownService.startShutdown (/usr/local/lib/node_modules/n8n/src/shutdown/shutdown.service.ts:80:4)\n    at ShutdownService.waitForShutdown (/usr/local/lib/node_modules/n8n/src/shutdown/shutdown.service.ts:69:3)\n    at process.<anonymous> (/usr/local/lib/node_modules/n8n/src/commands/base-command.ts:306:4)\n","metadata":{"component":"ScalingService.stop()","file":"error-reporter.js","function":"defaultReport","timestamp":"2025-07-27T07:13:45.015Z"}}
{"level":"error","message":"Connection is closed.","metadata":{"file":"error-reporter.js","function":"defaultReport","timestamp":"2025-07-27T07:13:45.015Z"}}
{"level":"info","message":"[Task Runner]: Received SIGTERM signal, shutting down...","metadata":{"file":"forward-to-logger.js","scopes":["task-runner"],"timestamp":"2025-07-27T07:13:45.147Z"}}
{"level":"info","message":"[Task Runner]: Task runner stopped","metadata":{"file":"forward-to-logger.js","scopes":["task-runner"],"timestamp":"2025-07-27T07:13:45.230Z"}}
{"level":"info","message":"Stopping worker...","metadata":{"file":"worker.js","function":"stopProcess","scopes":["scaling"],"timestamp":"2025-07-27T07:13:45.453Z"}}
{"level":"error","message":"[Redis client] connect ETIMEDOUT","metadata":{"error":{"message":"connect ETIMEDOUT","name":"Error","stack":"Error: connect ETIMEDOUT\n    at Socket.<anonymous> (/usr/local/lib/node_modules/n8n/node_modules/.pnpm/ioredis@5.3.2/node_modules/ioredis/built/Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:604:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at processTimers (node:internal/timers:523:7)"},"file":"redis-client.service.js","scopes":["redis","scaling"],"timestamp":"2025-07-27T07:13:45.669Z"}}
{"level":"error","message":"Queue errored","metadata":{"error":{"message":"connect ETIMEDOUT","name":"Error","stack":"Error: connect ETIMEDOUT\n    at Socket.<anonymous> (/usr/local/lib/node_modules/n8n/node_modules/.pnpm/ioredis@5.3.2/node_modules/ioredis/built/Redis.js:170:41)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket._onTimeout (node:net:604:8)\n    at listOnTimeout (node:internal/timers:588:17)\n    at processTimers (node:internal/timers:523:7)"},"file":"scaling.service.js","scopes":["scaling"],"timestamp":"2025-07-27T07:13:45.670Z"}}
{"level":"error","message":"connect ETIMEDOUT","metadata":{"file":"error-reporter.js","function":"defaultReport","timestamp":"2025-07-27T07:13:45.670Z"}}
{"level":"error","message":"Last session crashed","metadata":{"file":"crash-journal.js","function":"init","timestamp":"2025-07-27T16:19:33.060Z"}}
{"level":"error","message":"ENOENT: no such file or directory, open '/home/<USER>/.n8n/nodes/node_modules/@oriondesign2015/n8n-nodes-evolution-api/package.json'","metadata":{"file":"load-nodes-and-credentials.js","function":"loadNodesFromNodeModules","timestamp":"2025-07-27T16:19:34.582Z"}}
{"level":"error","message":"ENOENT: no such file or directory, open '/home/<USER>/.n8n/nodes/node_modules/@oriondesign2015/n8n-nodes-evolution-api/package.json'","metadata":{"file":"error-reporter.js","function":"defaultReport","timestamp":"2025-07-27T16:19:34.589Z"}}
{"level":"warn","message":"\nThere is a deprecation related to your environment variables. Please take the recommended actions to update your configuration:\n - N8N_SKIP_WEBHOOK_DEREGISTRATION_SHUTDOWN -> n8n no longer deregisters webhooks at startup and shutdown. Remove this environment variable; it is no longer needed.\n","metadata":{"file":"deprecation.service.js","function":"warn","timestamp":"2025-07-27T16:19:34.935Z"}}
{"level":"warn","message":"n8n detected that some packages are missing. For more information, visit https://docs.n8n.io/integrations/community-nodes/troubleshooting/","metadata":{"file":"community-packages.service.js","function":"checkForMissingPackages","timestamp":"2025-07-27T16:19:34.972Z"}}
{"level":"info","message":"n8n Task Broker ready on 127.0.0.1, port 5679","metadata":{"file":"task-broker-server.js","function":"setupHttpServer","timestamp":"2025-07-27T16:19:35.076Z"}}
{"level":"info","message":"[license SDK] Skipping renewal on init because renewal is not due yet or cert is not initialized","metadata":{"file":"LicenseManager.js","function":"log","scopes":["license"],"timestamp":"2025-07-27T16:19:35.264Z"}}
{"level":"info","message":"\nn8n worker is now ready","metadata":{"file":"worker.js","function":"run","scopes":["scaling"],"timestamp":"2025-07-27T16:19:35.594Z"}}
{"level":"info","message":" * Version: 1.103.2","metadata":{"file":"worker.js","function":"run","scopes":["scaling"],"timestamp":"2025-07-27T16:19:35.594Z"}}
{"level":"info","message":" * Concurrency: 10","metadata":{"file":"worker.js","function":"run","scopes":["scaling"],"timestamp":"2025-07-27T16:19:35.595Z"}}
{"level":"info","message":"","metadata":{"file":"worker.js","function":"run","scopes":["scaling"],"timestamp":"2025-07-27T16:19:35.595Z"}}
{"level":"info","message":"\nn8n worker server listening on port 5678","metadata":{"file":"worker-server.js","function":"init","scopes":["scaling"],"timestamp":"2025-07-27T16:19:35.696Z"}}
{"level":"info","message":"Registered runner \"JS Task Runner\" (aheEFn2AGvpmIN_0F8jh9) ","metadata":{"file":"task-broker-ws-server.js","function":"onMessage","timestamp":"2025-07-27T16:19:36.855Z"}}
{"level":"info","message":"Initializing n8n process","metadata":{"file":"start.js","function":"init","timestamp":"2025-07-27T16:19:43.067Z"}}
{"level":"error","message":"ENOENT: no such file or directory, open '/home/<USER>/.n8n/nodes/node_modules/@oriondesign2015/n8n-nodes-evolution-api/package.json'","metadata":{"file":"load-nodes-and-credentials.js","function":"loadNodesFromNodeModules","timestamp":"2025-07-27T16:19:44.180Z"}}
{"level":"error","message":"ENOENT: no such file or directory, open '/home/<USER>/.n8n/nodes/node_modules/@oriondesign2015/n8n-nodes-evolution-api/package.json'","metadata":{"file":"error-reporter.js","function":"defaultReport","timestamp":"2025-07-27T16:19:44.181Z"}}
{"level":"info","message":"n8n ready on ::, port 5678","metadata":{"file":"abstract-server.js","function":"init","timestamp":"2025-07-27T16:19:44.502Z"}}
{"level":"warn","message":"\nThere are deprecations related to your environment variables. Please take the recommended actions to update your configuration:\n - N8N_SKIP_WEBHOOK_DEREGISTRATION_SHUTDOWN -> n8n no longer deregisters webhooks at startup and shutdown. Remove this environment variable; it is no longer needed.\n - OFFLOAD_MANUAL_EXECUTIONS_TO_WORKERS -> Running manual executions in the main instance in scaling mode is deprecated. Manual executions will be routed to workers in a future version. Please set `OFFLOAD_MANUAL_EXECUTIONS_TO_WORKERS=true` to offload manual executions to workers and avoid potential issues in the future. Consider increasing memory available to workers and reducing memory available to main.\n","metadata":{"file":"deprecation.service.js","function":"warn","timestamp":"2025-07-27T16:19:44.540Z"}}
{"level":"warn","message":"n8n detected that some packages are missing. For more information, visit https://docs.n8n.io/integrations/community-nodes/troubleshooting/","metadata":{"file":"community-packages.service.js","function":"checkForMissingPackages","timestamp":"2025-07-27T16:19:44.581Z"}}
{"level":"info","message":"n8n Task Broker ready on 127.0.0.1, port 5679","metadata":{"file":"task-broker-server.js","function":"setupHttpServer","timestamp":"2025-07-27T16:19:44.622Z"}}
{"level":"info","message":"[license SDK] Skipping renewal on init because renewal is not due yet or cert is not initialized","metadata":{"file":"LicenseManager.js","function":"log","scopes":["license"],"timestamp":"2025-07-27T16:19:44.829Z"}}
{"level":"info","message":"Registered runner \"JS Task Runner\" (yUj2JQsek4laG35-_nWvm) ","metadata":{"file":"task-broker-ws-server.js","function":"onMessage","timestamp":"2025-07-27T16:19:47.722Z"}}
{"level":"info","message":"Version: 1.103.2","metadata":{"file":"abstract-server.js","function":"start","timestamp":"2025-07-27T16:19:47.889Z"}}
{"level":"info","message":"Start Active Workflows:","metadata":{"file":"active-workflow-manager.js","function":"addActiveWorkflows","scopes":["workflow-activation"],"timestamp":"2025-07-27T16:19:47.985Z"}}
{"level":"info","message":"Activated workflow \"Agente BigFoot\" (ID: tHU9HJxY9sOOSlQT)","metadata":{"file":"active-workflow-manager.js","function":"activateWorkflow","scopes":["workflow-activation"],"timestamp":"2025-07-27T16:19:49.158Z","workflowId":"tHU9HJxY9sOOSlQT","workflowName":"Agente BigFoot"}}
{"level":"info","message":"\nEditor is now accessible via:\nhttp://localhost:5678","metadata":{"file":"base-command.js","function":"log","timestamp":"2025-07-27T16:19:49.159Z"}}
{"level":"error","message":"Last session crashed","metadata":{"file":"crash-journal.js","function":"init","timestamp":"2025-07-28T16:13:23.223Z"}}
{"level":"error","message":"Last session crashed","metadata":{"file":"crash-journal.js","function":"init","timestamp":"2025-07-28T16:13:24.112Z"}}
{"level":"info","message":"Initializing n8n process","metadata":{"file":"start.js","function":"init","timestamp":"2025-07-28T16:13:34.115Z"}}
{"level":"error","message":"ENOENT: no such file or directory, open '/home/<USER>/.n8n/nodes/node_modules/@oriondesign2015/n8n-nodes-evolution-api/package.json'","metadata":{"file":"load-nodes-and-credentials.js","function":"loadNodesFromNodeModules","timestamp":"2025-07-28T16:13:36.461Z"}}
{"level":"error","message":"ENOENT: no such file or directory, open '/home/<USER>/.n8n/nodes/node_modules/@oriondesign2015/n8n-nodes-evolution-api/package.json'","metadata":{"file":"load-nodes-and-credentials.js","function":"loadNodesFromNodeModules","timestamp":"2025-07-28T16:13:36.462Z"}}
{"level":"error","message":"ENOENT: no such file or directory, open '/home/<USER>/.n8n/nodes/node_modules/@oriondesign2015/n8n-nodes-evolution-api/package.json'","metadata":{"file":"error-reporter.js","function":"defaultReport","timestamp":"2025-07-28T16:13:36.462Z"}}
{"level":"error","message":"ENOENT: no such file or directory, open '/home/<USER>/.n8n/nodes/node_modules/@oriondesign2015/n8n-nodes-evolution-api/package.json'","metadata":{"file":"error-reporter.js","function":"defaultReport","timestamp":"2025-07-28T16:13:36.463Z"}}
{"level":"info","message":"n8n ready on ::, port 5678","metadata":{"file":"abstract-server.js","function":"init","timestamp":"2025-07-28T16:13:36.885Z"}}
{"level":"warn","message":"\nThere are deprecations related to your environment variables. Please take the recommended actions to update your configuration:\n - N8N_SKIP_WEBHOOK_DEREGISTRATION_SHUTDOWN -> n8n no longer deregisters webhooks at startup and shutdown. Remove this environment variable; it is no longer needed.\n - OFFLOAD_MANUAL_EXECUTIONS_TO_WORKERS -> Running manual executions in the main instance in scaling mode is deprecated. Manual executions will be routed to workers in a future version. Please set `OFFLOAD_MANUAL_EXECUTIONS_TO_WORKERS=true` to offload manual executions to workers and avoid potential issues in the future. Consider increasing memory available to workers and reducing memory available to main.\n","metadata":{"file":"deprecation.service.js","function":"warn","timestamp":"2025-07-28T16:13:36.934Z"}}
{"level":"warn","message":"\nThere is a deprecation related to your environment variables. Please take the recommended actions to update your configuration:\n - N8N_SKIP_WEBHOOK_DEREGISTRATION_SHUTDOWN -> n8n no longer deregisters webhooks at startup and shutdown. Remove this environment variable; it is no longer needed.\n","metadata":{"file":"deprecation.service.js","function":"warn","timestamp":"2025-07-28T16:13:36.934Z"}}
{"level":"warn","message":"n8n detected that some packages are missing. For more information, visit https://docs.n8n.io/integrations/community-nodes/troubleshooting/","metadata":{"file":"community-packages.service.js","function":"checkForMissingPackages","timestamp":"2025-07-28T16:13:36.973Z"}}
{"level":"warn","message":"n8n detected that some packages are missing. For more information, visit https://docs.n8n.io/integrations/community-nodes/troubleshooting/","metadata":{"file":"community-packages.service.js","function":"checkForMissingPackages","timestamp":"2025-07-28T16:13:36.973Z"}}
{"level":"info","message":"n8n Task Broker ready on 127.0.0.1, port 5679","metadata":{"file":"task-broker-server.js","function":"setupHttpServer","timestamp":"2025-07-28T16:13:37.009Z"}}
{"level":"info","message":"n8n Task Broker ready on 127.0.0.1, port 5679","metadata":{"file":"task-broker-server.js","function":"setupHttpServer","timestamp":"2025-07-28T16:13:37.085Z"}}
{"level":"info","message":"[license SDK] Skipping renewal on init because renewal is not due yet or cert is not initialized","metadata":{"file":"LicenseManager.js","function":"log","scopes":["license"],"timestamp":"2025-07-28T16:13:37.244Z"}}
{"level":"info","message":"[license SDK] Skipping renewal on init because renewal is not due yet or cert is not initialized","metadata":{"file":"LicenseManager.js","function":"log","scopes":["license"],"timestamp":"2025-07-28T16:13:37.245Z"}}
{"level":"info","message":"\nn8n worker is now ready","metadata":{"file":"worker.js","function":"run","scopes":["scaling"],"timestamp":"2025-07-28T16:13:37.693Z"}}
{"level":"info","message":" * Version: 1.103.2","metadata":{"file":"worker.js","function":"run","scopes":["scaling"],"timestamp":"2025-07-28T16:13:37.695Z"}}
{"level":"info","message":" * Concurrency: 10","metadata":{"file":"worker.js","function":"run","scopes":["scaling"],"timestamp":"2025-07-28T16:13:37.696Z"}}
{"level":"info","message":"","metadata":{"file":"worker.js","function":"run","scopes":["scaling"],"timestamp":"2025-07-28T16:13:37.697Z"}}
{"level":"info","message":"\nn8n worker server listening on port 5678","metadata":{"file":"worker-server.js","function":"init","scopes":["scaling"],"timestamp":"2025-07-28T16:13:37.831Z"}}
{"level":"info","message":"Registered runner \"JS Task Runner\" (bihanIqQMhELkaG8hv94E) ","metadata":{"file":"task-broker-ws-server.js","function":"onMessage","timestamp":"2025-07-28T16:13:40.180Z"}}
{"level":"info","message":"Registered runner \"JS Task Runner\" (dmqFOFQyRrpvbU-NT9MGl) ","metadata":{"file":"task-broker-ws-server.js","function":"onMessage","timestamp":"2025-07-28T16:13:41.407Z"}}
{"level":"info","message":"Version: 1.103.2","metadata":{"file":"abstract-server.js","function":"start","timestamp":"2025-07-28T16:13:41.489Z"}}
{"level":"info","message":"Start Active Workflows:","metadata":{"file":"active-workflow-manager.js","function":"addActiveWorkflows","scopes":["workflow-activation"],"timestamp":"2025-07-28T16:13:41.589Z"}}
{"level":"info","message":"Activated workflow \"Agente BigFoot\" (ID: tHU9HJxY9sOOSlQT)","metadata":{"file":"active-workflow-manager.js","function":"activateWorkflow","scopes":["workflow-activation"],"timestamp":"2025-07-28T16:13:42.687Z","workflowId":"tHU9HJxY9sOOSlQT","workflowName":"Agente BigFoot"}}
{"level":"info","message":"\nEditor is now accessible via:\nhttp://localhost:5678","metadata":{"file":"base-command.js","function":"log","timestamp":"2025-07-28T16:13:42.688Z"}}
{"level":"error","message":"Problem with execution 6: Connect a trigger to run this node. Aborting.","metadata":{"file":"workflow-runner.js","function":"processError","timestamp":"2025-07-28T17:10:07.590Z"}}
{"level":"error","message":"Connect a trigger to run this node (execution 6)","metadata":{"file":"error-reporter.js","function":"defaultReport","timestamp":"2025-07-28T17:10:07.591Z"}}
{"level":"error","message":"Connect a trigger to run this node","metadata":{"file":"error-reporter.js","function":"defaultReport","timestamp":"2025-07-28T17:10:07.632Z"}}
{"level":"error","message":"Last session crashed","metadata":{"file":"crash-journal.js","function":"init","timestamp":"2025-07-28T20:10:25.199Z"}}
{"level":"error","message":"Last session crashed","metadata":{"file":"crash-journal.js","function":"init","timestamp":"2025-07-28T20:10:26.027Z"}}
{"level":"info","message":"Initializing n8n process","metadata":{"file":"start.js","function":"init","timestamp":"2025-07-28T20:10:36.032Z"}}
{"level":"error","message":"ENOENT: no such file or directory, open '/home/<USER>/.n8n/nodes/node_modules/@oriondesign2015/n8n-nodes-evolution-api/package.json'","metadata":{"file":"load-nodes-and-credentials.js","function":"loadNodesFromNodeModules","timestamp":"2025-07-28T20:10:36.967Z"}}
{"level":"error","message":"ENOENT: no such file or directory, open '/home/<USER>/.n8n/nodes/node_modules/@oriondesign2015/n8n-nodes-evolution-api/package.json'","metadata":{"file":"error-reporter.js","function":"defaultReport","timestamp":"2025-07-28T20:10:36.968Z"}}
{"level":"error","message":"ENOENT: no such file or directory, open '/home/<USER>/.n8n/nodes/node_modules/@oriondesign2015/n8n-nodes-evolution-api/package.json'","metadata":{"file":"load-nodes-and-credentials.js","function":"loadNodesFromNodeModules","timestamp":"2025-07-28T20:10:37.037Z"}}
{"level":"error","message":"ENOENT: no such file or directory, open '/home/<USER>/.n8n/nodes/node_modules/@oriondesign2015/n8n-nodes-evolution-api/package.json'","metadata":{"file":"error-reporter.js","function":"defaultReport","timestamp":"2025-07-28T20:10:37.038Z"}}
{"level":"info","message":"n8n ready on ::, port 5678","metadata":{"file":"abstract-server.js","function":"init","timestamp":"2025-07-28T20:10:37.209Z"}}
{"level":"warn","message":"\nThere are deprecations related to your environment variables. Please take the recommended actions to update your configuration:\n - N8N_SKIP_WEBHOOK_DEREGISTRATION_SHUTDOWN -> n8n no longer deregisters webhooks at startup and shutdown. Remove this environment variable; it is no longer needed.\n - OFFLOAD_MANUAL_EXECUTIONS_TO_WORKERS -> Running manual executions in the main instance in scaling mode is deprecated. Manual executions will be routed to workers in a future version. Please set `OFFLOAD_MANUAL_EXECUTIONS_TO_WORKERS=true` to offload manual executions to workers and avoid potential issues in the future. Consider increasing memory available to workers and reducing memory available to main.\n","metadata":{"file":"deprecation.service.js","function":"warn","timestamp":"2025-07-28T20:10:37.278Z"}}
{"level":"warn","message":"\nThere is a deprecation related to your environment variables. Please take the recommended actions to update your configuration:\n - N8N_SKIP_WEBHOOK_DEREGISTRATION_SHUTDOWN -> n8n no longer deregisters webhooks at startup and shutdown. Remove this environment variable; it is no longer needed.\n","metadata":{"file":"deprecation.service.js","function":"warn","timestamp":"2025-07-28T20:10:37.278Z"}}
{"level":"warn","message":"n8n detected that some packages are missing. For more information, visit https://docs.n8n.io/integrations/community-nodes/troubleshooting/","metadata":{"file":"community-packages.service.js","function":"checkForMissingPackages","timestamp":"2025-07-28T20:10:37.305Z"}}
{"level":"warn","message":"n8n detected that some packages are missing. For more information, visit https://docs.n8n.io/integrations/community-nodes/troubleshooting/","metadata":{"file":"community-packages.service.js","function":"checkForMissingPackages","timestamp":"2025-07-28T20:10:37.305Z"}}
{"level":"info","message":"n8n Task Broker ready on 127.0.0.1, port 5679","metadata":{"file":"task-broker-server.js","function":"setupHttpServer","timestamp":"2025-07-28T20:10:37.327Z"}}
{"level":"info","message":"n8n Task Broker ready on 127.0.0.1, port 5679","metadata":{"file":"task-broker-server.js","function":"setupHttpServer","timestamp":"2025-07-28T20:10:37.932Z"}}
{"level":"info","message":"[license SDK] Skipping renewal on init because renewal is not due yet or cert is not initialized","metadata":{"file":"LicenseManager.js","function":"log","scopes":["license"],"timestamp":"2025-07-28T20:10:38.054Z"}}
{"level":"info","message":"[license SDK] Skipping renewal on init because renewal is not due yet or cert is not initialized","metadata":{"file":"LicenseManager.js","function":"log","scopes":["license"],"timestamp":"2025-07-28T20:10:38.070Z"}}
{"level":"info","message":"\nn8n worker is now ready","metadata":{"file":"worker.js","function":"run","scopes":["scaling"],"timestamp":"2025-07-28T20:10:38.412Z"}}
{"level":"info","message":" * Version: 1.103.2","metadata":{"file":"worker.js","function":"run","scopes":["scaling"],"timestamp":"2025-07-28T20:10:38.412Z"}}
{"level":"info","message":" * Concurrency: 10","metadata":{"file":"worker.js","function":"run","scopes":["scaling"],"timestamp":"2025-07-28T20:10:38.413Z"}}
{"level":"info","message":"","metadata":{"file":"worker.js","function":"run","scopes":["scaling"],"timestamp":"2025-07-28T20:10:38.413Z"}}
{"level":"info","message":"\nn8n worker server listening on port 5678","metadata":{"file":"worker-server.js","function":"init","scopes":["scaling"],"timestamp":"2025-07-28T20:10:38.516Z"}}
{"level":"info","message":"Registered runner \"JS Task Runner\" (tdA4eERmQZ4Aj6aazDCQD) ","metadata":{"file":"task-broker-ws-server.js","function":"onMessage","timestamp":"2025-07-28T20:10:39.445Z"}}
{"level":"info","message":"Registered runner \"JS Task Runner\" (k6jMpzuC-4TST_d3vzMFU) ","metadata":{"file":"task-broker-ws-server.js","function":"onMessage","timestamp":"2025-07-28T20:10:40.002Z"}}
{"level":"info","message":"Version: 1.103.2","metadata":{"file":"abstract-server.js","function":"start","timestamp":"2025-07-28T20:10:40.121Z"}}
{"level":"info","message":"Start Active Workflows:","metadata":{"file":"active-workflow-manager.js","function":"addActiveWorkflows","scopes":["workflow-activation"],"timestamp":"2025-07-28T20:10:40.174Z"}}
{"level":"info","message":"Activated workflow \"Agente BigFoot\" (ID: tHU9HJxY9sOOSlQT)","metadata":{"file":"active-workflow-manager.js","function":"activateWorkflow","scopes":["workflow-activation"],"timestamp":"2025-07-28T20:10:40.899Z","workflowId":"tHU9HJxY9sOOSlQT","workflowName":"Agente BigFoot"}}
{"level":"info","message":"\nEditor is now accessible via:\nhttp://localhost:5678","metadata":{"file":"base-command.js","function":"log","timestamp":"2025-07-28T20:10:40.900Z"}}
