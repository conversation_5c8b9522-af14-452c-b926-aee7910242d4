﻿Olá pessoal tudo joia aqui Luiz nesse
curso eu vou apresentar para vocês o
painel da rner que é um Provedor
Fantástico e que oferece serviços de VPS
máquinas dedicadas e que eu tenho
certeza que vai ajudar muito você a
hospedar aí as suas aplicações com
segurança com um ótimo desempenho e um
custo bem acessível um dos principais
pontos da hetzner é justamente a questão
do preço são máquinas muito acessíveis
tá só muito acessíveis é um preço ótimo
principalmente para quem tá dando os
primeiros passos né porque eles como
eles têm um preço extremamente vantajoso
vocês vão ver daqui a pouco aqui como é
que é o preço deles é muito bom né acaba
que se torna muito mais fácil para você
poder iniciar os seus projetos começar a
ganhar atração a ganhar mesmo a
escalabilidade suas aplicações tá então
é um host Fantástico eu quero que vocês
façam esse treinamento com muita atenção
mesmo tá pessoal em cada detalhe que eu
vou mostrar ali o painel deles é bem bem
completo tem muito recurso Fantástico
que tá bem escondidinho ali então faz
com atenção para você poder tirar o
maior proveito possível desse provedor
que eu tenho certeza que vai ajudar
muito vocês aí nos seus projetos pessoal
então eu tô aqui no painel da hetzner no
site da hetzner para fazer um overview
com vocês dos serviços que eles oferecem
como que eles podem ajudar vocês aí nas
suas estratégias com as VPS tá pessoal o
hetzner ele foca muito em máquinas mesmo
em VPS então é um Provedor totalmente
voltado para esse fim tá todos os
serviços dele gira em torno disso né de
oferecer para você a computação então
aqui pessoal como vocês podem ver no no
product overview deles aqui eles TM uma
listagem dos produtos dele você vai
encontrar máquinas dedicadas e são
dedicadas mesmo tá você vai ter um
espacinho ali num hack que é inteiro seu
e também vai ter o que a gente vai usar
aqui que é a máquina Cloud né a máquina
em nuvem da HN então nos dois modelos
pessoal eles possuem ótimos serviços eu
vou focar um pouco mais na parte de
nuvem que é o que interessa mais aqui
pra gente né E você vai ter vários tipos
de opções aí de máquinas que vocês podem
escolher e que formam aí a questão do
preço da rner tá então aqui por exemplo
pessoal se você for lar no site deles
uma máquina comum tá ó uma máquina com
uma uma CPU dedicada tá duas cpus 8 GB
tá saindo em torno de
14€ aqui tá pessoal é muito barato é um
preço muito bom se você partir aqui para
máquinas comuns e que vai rodar ali num
hack né compartilhando recursos com
outras máquinas que é o normal que é o
que a digital o oferece é o que a
maioria dos provedores oferecem eu tô
falando pessoal de uma máquina de 4€ uma
CPU e 2 GB de memória o equivalente no
digital otion por exemplo a do digital
que eu sugeri para vocês lá no curso do
digital é uma de 10 com com 11 aqui na H
você tem duas cpus e 8 GB lá no digital
Ocean é uma CPU e 2 GB Então vale muito
a pena a questão aqui da precificação da
rner é um dos grandes atrativos da rner
é justamente aqui o preço mas não é só o
preço tá pessoal é uma é um serviço
Fantástico e muito mais customizável do
que a grande maioria dos provedores aqui
de de de
VPS eu não classificaria a hetzner como
Classe A pelo seguinte fator dela
oferecer un exclusivamente a parte de de
de computação ela não oferece um banco
de dados gerenciado ela não oferece
alguns recursos que geralmente e
provedores de Classe A como Amazon e eh
a Microsoft o Google oferece Tá mas a
qualidade da da herner é muito parecida
tá então eu só não vou encaixar por uma
questão mesmo ali de de serviço isso que
oferece mas em termos de qualidade é
fantástico igual a digital oan Luiz
Então qual que o qual que é o escolho eu
não vou escolher por vocês pessoal mas a
rner é uma ótima escolha para você fazer
ainda mais se você quer gerenciar o seu
swarm quer ter várias máquinas no seu
swarm a rner aqui é uma ótima opção para
vocês tá o digital oan tem as suas
facilidades é mais amigável Porém Aqui
na header você vai ter um custo
benefício muito melhor do que no digital
Ocean e uma máquina mais potente isso aí
impacta diretamente na sua operação tá E
aqui no aqui na hestner você vai ter
load balancer a gente não vai utilizar
porque a gente vai utilizar isso pelo
docker né IPS primários Mas eu posso
gerenciar os IPS primários eu posso
configurar minha rede eu tenho Firewall
de borda eu tenho volumes para poder
adicionar na minha máquina são máquinas
extremamente rápidas pessoal realmente a
hler tem um desempenho muito grande e
também tem backup e SnapShot tá tem
float IP que a gente já viu também aqui
sobre float IP né ddos protection todos
os provedores oferecem isso hoje em dia
né então você tem uma camada boa ali de
proteção então é um serviço Fantástico
tá Fantástico mesmo pessoal eu recomendo
muito que vocês olhem com carinho aqui a
questão da rner tá bom pessoal você vai
poder rodar as suas máquinas na Alemanha
na Finlândia ou nos Estados Unidos e
aqui vem a primeira dica pessoal
Alemanha é a melhor opção
porém está em outro continente você vai
ter uma questão ali de latência nos
Estados Unidos Eu também acho muito bom
tá pessoal mas como na Alemanha é onde
fic a sede com certeza é onde tá o maior
data center deles né mas evitar um pouco
a Finlândia tá muito longe pessoal então
dá muito delay dá um Ping Alto dá uma
certa latência para quem mora na Europa
eu acho que é uma ótima opção mas pra
gente que mora aqui na América do Sul o
Estados Unidos tá mais próximo então vai
ser melhor o tráfego de rede ali faz
menos hops até chegar lá tá bom então se
quiser escolher a Alemanha Ok mas se
possível escolhe aqui o Estados Unidos
tá pessoal um ótimo lugar para você
poder hospedar aí a sua máquina também
tá como tá mais próximo aqui tá muito
mais mais tranquilo tem uma latência bem
menor também tá Então pessoal é um
Provedor Fantástico que oferece para
vocês todos os recursos que vocês
precisam para lidar com as aplicações
aqui da promov web as aplicações que a
gente vai utilizar aqui tá bom
e realmente eu recomendo que vocês
prestem atenção aqui na H com um pouco
mais de carinho aqui pessoal eu vou
fazer o login eu já tenho uma conta aqui
na H tá bom e o processo de conta da H é
o seguinte eu vou logar aqui ó na minha
tela na opção Cloud tá então vou acessar
o cloud que é onde eu vou acessar aqui
as minhas VPS tá ou as minhas máquinas
dedicadas virtuais tá bom E aqui caso
você não tenha uma conta na hestner Você
vai clicar aqui então em register Now é
o Snap famoso Snap só que a hestner ela
é diferente do digital oan em alguns
aspectos em relação à sua conta pessoal
e tem que ficar muito atento a isso tá
então aqui vou fazer login se você criar
sua conta você vai criar sua conta e
você vai passar por um processo de
ativação da sua conta no digital o a
gente ativa a conta colocando lá um
cartão e fazendo um depósito de 5 né na
rner não na rner você não precisa
colocar cartão agora você não precisa
fazer depósito agora mas você precisa de
toda a questão de documentação Então
você tem que mandar por exemplo uma foto
da carteira deh habilitação sua de
passaporte eles vão você vai ser guiado
aqui pelo painel deles para poder eu não
vou não vou fazer aqui porque as
formações pessoais né Não não pode fazer
isso mas é um processo super simples de
vocês concluírem a dica que eu dou para
vocês pessoal é ter muito cuidado enviar
uma foto legível uma foto sua nova de
preferência da sua habilitação quem tem
tem passaporte envie também a foto do
passaporte fica muito mais prático e
seja eh criterioso nas suas respostas
pessoal o que eles perguntaram no painel
você responde com a verdade tá bom E aí
você não vai ter problema quem tem
problema pessoal é que o a gente sempre
tem os espertinhos ali que quer dar uma
burlada aqui quer não fornecer uma
informação ali hoje não tem mais isso
pessoal infelizmente a gente não pode
ludar com isso a h me dá uma regra eu
vou dançar a regra deles é assim que
funciona eles que tocam a música lá
então a gente tem que dançar a música
quees Estão tocando então não é um
processo difícil mas é um processo que
você tem que fazer com atenção verifica
se o nome seu aqui pessoal tá igual o
nome do documento é o básico que um
programador que uma pessoa que quer ter
uma empresa uma pessoa que quer
trabalhar quer ser um autônomo é o
básico que tem que fazer saber preencher
os próprios dados tá pessoal então tem
que ter esse esse cuidado tá aqui na
minha conta eu vou acessar aqui a minha
conta é é importante pessoal que vocês
se atentem que na rner você vai ter que
habilitar aqui o
otp Então quando você habilitar um otp
ele vai pedir para você esse código Toda
vez que você for fazer um login no pel
aqui ele vai pedir para você um código
aqui tá esse código é único não tem
problema mostrar para vocês esse código
aqui mas é importante que vocês
habilitem o otp aqui tá bom então fica
essa dica para você poder fazer a sua
conta pessoal preenche o cadastro com
bastante cuidado com bastante atenção
uma vez que você entrou aqui no painel
da rner você vai vir aqui ó nesse nesse
menuzinho aqui de cima que tem um um de
usuário e você vai clicar aqui por
exemplo em
preferências aqui você pode escolher Se
você quer a versão clara ou a versão
escura isso aqui é muito bom tá pessoal
porque muita gente mexe de noite né E aí
você f vou mexer de noite tudo então eu
eu gosto muito do do Dark mode tá então
legal o h ter isso daqui porque às vezes
você tá de madrugada você tá mais de
noite depende do seu da maneira como
você quer trabalhar aí e aquele pelz
branco na cara atrapalha um pouco então
aqui em preferências você vai ter essa
opção aí E o mais importante aqui
pessoal é esse user account Você vai
clicar aqui em user account é aqui que
você vai fornecer todos os seus dados
para hetzner tá então você vai informar
o seu nome seu nome completo nome
sobrenome o endereço que você mora o seu
CP todas as suas informações celular
telefone de novo preenche isso aqui
pessoal com informações verd iras tá
aqui não tem como ter espertinho pessoal
se for espertinho aqui eles vão bloquear
sua conta preenche com informações
verdadeiras aqui no seu endereço de
e-mail a mesma coisa Coloca o endereço
de e-mail que que que existe que
funcione tá porque eles podem mandar
algum tipo de confirmação algum tipo de
alerta para você no seu e-mail tá e você
vai ter que aceitar aqui o a as
informações de processamento de dados
dele né que eles vão ter informações
suas aqui tá você vai você aceita que
eles processem os seus dados não tem
como eles oferecer um serviço para você
sem processar os seus dados tá gente
pode parcer Óbvio mas é assim que
funciona tá aqui ainda pessoal que eu
recomendo que vocês façam logo de cara
criou a conta pessoal fez o srap ali vem
aqui ó suport otp e você vai vir aqui e
você vai ativar o seu otp é só você
baixar o aplicativo do Google de
gerenciamento de otp ali até vou deixar
o link para vocês aqui na aula também tá
para quem tem Iphone quem tem Android
tem para todo mundo mas é importante
porque é uma maneira que vocês têm de
mostrar para eles que você tá preocupado
com a sua conta porque é uma segurança a
mais que você põe então a pessoa tem que
saber o seu usuário e senha e também tem
que ter acesso ao seu celular para poder
para poder colocar ali o otp tá então
muito importante isso não pode descartar
essa opção tá gente entrou vem aqui e
habilita o otp se possível você pode vir
aqui ainda e habilitar né no caso eu
habilitei o otp ali né então eu tenho
aqui o meu two step né você vai
habilitar o seu two step e você pode
inclusive adicionar outras maneiras aí
tá então protege a sua conta pessoal é
muito importante que você proteja a sua
conta para que você inclusive seja bem
visto pelo provedor como a pessoa que tá
preocupada mesmo ali tá então muito
importante isso caso você precise do seu
customer ID Ele tá aqui em cima ó client
Number esse aqui é o seu customer ID
você pode usar ele para logar né E às
vezes você pode também caso você for
abrir um Ticket for mandar um e-mail
para eles vão solicitar para você esse
número aqui do customer id tá então é
muito importante Pessoal vocês TM de que
anotado tá bom para você poder se
comunicar com eles caso precise ainda
aqui pessoal na tela de preferência do
usuário você vai ter aqui o invoices né
então aqui no invoices você vai poder
acompanhar todas as cobranças que a
hestner faz na sua conta então aqui ele
você vai baixar poder baixar as suas
faturas Tá bom então Ó aqui você vai
poder aqui no meu no meu caso ele me
informa que todo dia primeiro ele me
envia um invoice né eu vou poder
acompanhar o meu o meu balancete aqui de
créditos tá ó Conforme eu for utilizando
aqui ele vai mostrando para mim o meu
meu meu balancete de crédito e aqui você
vai especificar os seus dados de
pagamento Tá pessoal não vou mostrar
aqui para não aparecer ali os meu dados
de pagamento mas informa ali o seu
cartão pessoal cartão não dá para ser
pré-pago não dá para ser cartão e
virtual ponha mesmo ali um cartão de de
verdade de banco né porque não tem
pessoal a empresa de Fora eles enxergam
é essa questão de maneira diferente como
a gente enxerga aqui no Brasil aqui é
uma outra realidade né pessoal
infelizmente Então é bom ficar atento a
isso para que você tenha controle aqui
dos seus invoices tá então preenche
direitinho ali os seus dados de
pagamento coloca um cartão válido não
pode ser pré-pago não pode ser cartão do
algodão doce nada disso pessoal tem que
ser um cartão de verdade de banco mesmo
que você utiliza aí porque ele a a
cobrança Não Pode falhar tá pessoal a
rner ela é muito rigorosa em relação à
cobrança em relação à fatura em relação
aos seus dados então você não pode
brincar a nesse sentido tá evitar o
máximo de novo né de ser o espertinho
ali e você vai acabar se prejudicando
tendo a sua conta bloqueada porque você
garote mesmo ali tá então ficar bem
atento nessa questão de colocar
informações verdadeiras aqui no seu
painel da
herner pessoal aqui no painel da herner
assim que você loga nele você vai ter
essa tela aqui esse dashboard é o que
ele chama de cloud console né então aqui
você vai ter uma visão geral
da das suas dos seus recursos né dos
seus assets aqui com a herner e aqui é
importante pessoal vou passar com vocês
aqui esse menu aqui por exemplo ó de uso
usage Ali vai mostrar para vocês a
questão So Do saldo né o quanto que
quanto que tá o seu balancete mensal
aqui na R no meu caso aqui ó 2 né eu
tenho uma máquina simples rodando ali né
É É muito bom mesm essa questão aqui do
do da rner Mas é bom você ficar você
acompanhar aqui inclusive você pode
clicar aqui nesse generate
preview ele te dá ó item a item da sua
cobrança então caso você ten que prestar
conta para algum cliente caso você tenha
que prestar conta no seu projeto aqui
você tem item a item de de valores do
que que é que eles estão cobrando tá
então muito legal a h t ISO daqui ela
mostrar PR vocês dessa maneira bem mais
detalhada o seu consumo mensal aqui tá
bom Aqui também tem essa aba de
atividades que é uma aba de auditoria
então tudo que que acontecer ó quando
você criar uma máquina quando você fizer
um backup quando você restaurar um
backup quando você logar no painel tudo
vai ficar aqui tá nessa auditoria aqui
que também é fantástico caso você tenha
que prestar conta para algum cliente
caso você tenha que trabalhar aí com
essa questão de auditoria tá pessoal
então isso aqui ponto positivo demais PR
rner a outra AB é muito importante que é
a de limite você pode inclusive vir aqui
e pedir para eles ó eles deixam bem
facinho aqui para para você poder pedir
para aumentar o limite e você vir aqui e
pedir para desbloquear alguma porta já
vou falar para vocês sobre isso daqui
mas olha só a minha conta padrão eu
tenho uma conta padrão na headster eu
posso ter 10 servidores Eu Posso
contratar oito máquinas com CPU dedicada
eu posso ter 30 snapshots 10 float IP eu
posso ter um volume de um tera eu posso
ter cinco load balancers e 20 IPS
primários é um volume bem o pessoal uma
quantidade bem Generosa para uma conta
nova conforme você vai avançando no uso
da da rner você vai podendo solicitar
ali você pode solicitar para eles
aumentar esses limites no caso aqui por
exemplo né ó limite increase aqui ó você
pode pedir para eles Olha eu quero mais
máquina dedicada eu quero mais e você
vai ter que se justificar esse seu
pedido de aumento ponto importante
pessoal todo provedor todo sem exceção
bloqueia envio de e-mail todos tá é uma
é uma regra em 2020 aconteceu esse
acordo e a partir de 2021 todos os
provedores começaram a a a a bloquear em
video mail Então você tem que abrir um
ticket no suporte para solicitar para
eles para liberar aqui a sua porta tá
bom então você vai vir aqui ó na hestner
Você vai clicar aqui ó unblock ports e
você vai vai ele ele já fala para você
aqui né Ó que a porta 25 e 465 já são
bloqueadas por padrão né E você pode vir
aqui solicitar ó preciso de tal porta
587 465 não sei a porta do seu do smtp
para poder disparar aqui e-mail coloca
lá o o o endereço do smtp que você vai
utilizar o serviço que você vai utilizar
também é bom tá Ah eu quero usar o chat
útil por exemplo e quero acessar o meu
emap então você pode vir aqui e
solicitar tudo isso para ele tá pessoal
mas é importante vocês entenderem que
por padrão bem bloqueado você vai ter
que abrir aqui e solicitar para eles
desbloquearem tá bom Aqui ainda você
pode OB ter suporte né e aqui tá o link
de afiliado pessoal muito legal esse
link de afiliado da HN Eu já falei para
vocês que eu sou totalmente a favor de
link de afiliado eu não faço na promov
weeb afiliação com provedores né pros
alunos Por uma questão que os alunos
estão me contratando então não acho
justo fazer isso mas eu sou a favor eu
recomendo que vocês todos façam tá de
vocês virm aqui ó é só vocês acertar
assinar o termo aqui e você vai poder
ter acesso ao seu link aí tá bom ele vai
nesse painel aqui ele vai mostrar para
você essa questão aí de de de você poder
compartilhar o seu link tá então é muito
importante aqui pessoal Olha só no meu
caso aqui eu deixei nesse ponto para
vocês verem que ó lá a minha conta é
muito nova para participar do referer
ainda então eu preciso ter pago pelo
menos uma uma uma uma conta na hestner
né á então é importante ou ter pago no
total aqui de 100 100€ para eles para
poder ter acesso aqui ao meu ao meu link
de afiliado Então é só você quando
passar o primeiro mês aí e você tiver
pago a sua conta beleza Tá bom pessoal
então aqui é muito importante essa
questão do referer eu acho que é muito
legal você oferecer isso pros seus
clientes vocês que compartilhou muito em
grupo compartilhar aqui todo mundo ganha
a pessoa ganha você também ganha tá
então a a pessoa que clicar no seu link
ganha 20€ e você ganhar 10 você não vai
sacar esse dinheiro né Ó mas você vai
poder utilizar como crédito aqui
dependendo aí Da Da sua rede você vai
poder utilizar Hat por muito tempo sem
pagar nada só com os créditos aqui de de
afiliação então recomendo que vocês
façam isso e aqui pessoal daqui a pouco
a gente vai ver com mais calma mas tudo
na hestner é baseado em projetos tá é
cada vez mais comum que os provedores
adotem essa nomenclatura adotem Essa
maneira para você poder organizar melhor
os seus recursos dentro de projeto Já
falei com vocês so o projeto daqui a
pouquinho a gente vai entrar mais a
fundo nesse nessa questão aí de projetos
tá Tá mas é muito importante que vocês
fiquem atentos aí a que hoje você tem
que organizar você tem que adicionar
pessoas no seu projeto você vai ter que
ter essa gestão dos seus
recursos pessoal falando um pouco mais
sobre suporte aqui na H aqui nessa aba
suporte você pode pedir um suporte
técnico tá então ele vai ele vai pedir
para você ó que tipo de problema que é é
um problema no servidor ó lá no caso
aqui do e-mails não sendo enviado né que
você pede ali pelo outro botão ou um
problema de rede ou outro problema então
você vai vir aqui você vai escolher o
projeto você vai informar qual servidor
que é E vai vir aqui vai descrever o seu
problema em inglês Então abre lá o
Google Translator escreve lá em
português copia a versão inglês e cola
aqui ou então pede pro GPT traduzir para
vocês de novo pessoal aqui você pode
anexar arquivo tá com problema pessoal
não não fale só olha Estou com problema
tá Seja claro seja descritivo ofereça
contexto se possível baixa um log tira
um print mostra mais detalhes do seu
erro que não tem como adivinhar Pessoal
vocês estão lidando com uma com uma
empresa de um outro continente eh é uma
outra regra de atendimento Se você não
for Claro eles vão fechar o seu ticket e
você pode não ter uma boa experiência
muito mais por culpa nossa de não não
expressar direito do que por culpa deles
de ter um mau atendimento tá pessoal
muita gente eu vejo muita gente
criticando o provedor que me contrata de
consultoria e eu vou olhar lá os ticket
falar ó cara mas não não é assim que
você eu já cheguei a pegar ticket Estou
com problema era só isso que tinha no
Ticket o cara não vai resolver pessoal
conta o que que você fez o que aconteceu
é igual no médico você tem que dar o
contexto para ele aqui a mesma coisa fui
atualizar uma aplicação eu fui fazer tal
coisa eu fui acessar eu fiz isso eu fiz
aquilo conta o seu caso seja o mais
descritivo possível para você ter uma
boa experiência aqui com a o suporte tá
aqui no account pessoal geralmente você
vai poder eh pedir para eles aqui algum
comprovante né você vai poder eh indicar
aqui para ele por exemplo ó enviar para
eles né algum item que eles possam
solicitar para você então você vai usar
aqui o account e no melanos aqui já é
uma Um item mais aberto tá já vai ter um
outro sla aqui na rner pessoal o sla
também é de um dia tá um dia útil tá
então de segunda a sexta né se você
abrir por exemplo um um chamado mais fim
do dia lembrar que eles estão na
Alemanha Então tem um fuso horário bem
diferente pelo menos 5 horas em relação
à gente aqui então entenda que vai ter
um dia aí para eles no mínimo para que
eles te respondam tá então é importante
ter essa noção aqui E esse aqui é a
visão Geral do painel da
rner pessoal ainda aqui em relação a
suporte se você acessar a AB suporte tem
um link aqui ó que é o rner status Você
vai clicar aqui você vai acessar a
página de status da rner você também
pode acessar por
status.com tá e aqui você vai ter uma
uma visão Geral de tudo que tá
acontecendo no provedor se tiver
alguma algum alguma notícia que você tem
que saber se tiver algum alguma
localização com problema problema de
rede problema enfim qualquer problema
que acontecer do lado da rner eles vão
informar vocês por aqui em tempo real tá
bom além disso por aqui você também pode
filtrar as manutenções programadas tá
pessoal então caso você eh esteja
planejando aí um lançamento coisa nesse
sentido é muito bom ficar de olho aqui
para ver se não vai ter nenhuma nenhuma
nada programado para esse período tá E
aqui por exemplo ele mostra todos os os
itens que estão que estavam com problema
que voltaram tá então é muito importante
ficar de olho nisso daqui tá bom Ó de
você poder ter essa questão aqui de ter
um lugar ter um painel para você vi
encontrar uma uma visão Geral do que que
tá acontecendo com seu provedor tá muito
importante recomendo que vocês criem uma
rotina de acessar essa página todos os
dias tá se possível todos os dias para
vocês acompanharem aqui o que que tá
acontecendo na
headster pessoal então aqui na herner
tudo gira em torno de um projeto então
tudo que você fizer na rner você vai
fazer aqui através de projetos eu já
tenho aqui um projeto que eu uso de de
exemplo mesmo para fingir D né mas a
regra para projetos pessoal é o seguinte
você pode criar um projeto para cada
cliente seu dentro de um cliente Você
pode ter um projeto para cada área de
atuação aí desse cliente então por
exemplo você pode ter lá um projeto para
poder colocar o servidores do
chat do da Evolution da parte de
comunicação suporte Você pode ter um
outro projeto para poder ficar ali com a
parte de de de integração com nhn o cong
e outros sistemas né você pode pode
então um projeto para ficar recursos de
marketing como por exemplo mtic etc né
então é é muito importante pessoal que
vocês organizem bem isso daqui eh eu até
Contei no curso do digital ho que
recentemente eu atendi ali um um um um
aluno ali que apagou sem querer uma
máquina que não era para apagar né
justamente pessoal por um pouco de
desorganização então é muito importante
que vocês prestem bastante atenção nessa
questão de de projetos Você pode ter
vários projetos aqui na sua conta então
eu vou criar aqui um projeto chamado
aula só para exemplificar para vocês
aqui né então agora eu tenho aqui dois
projetos o meu cinca e o aula tá eu vou
clicar aqui vou abrir meu projeto e o
mais importante aqui pessoal na questão
aqui de projeto é você poder a primeira
coisa a se fazer é você poder adicionar
membros em um projeto então a regra
principal pessoal é a seguinte não dê
senha para ninguém não oferece senha
para ninguém eu já falei que no começo
do curso para você proteger a sua conta
então você não vai oferecer o seu e-mail
o seu two step etc para outra pessoa
para um terceiro Ah luí eu ten um
consultor eu tenho outro colega de
trabalho aqui que vai me ajudar tem um
frila é só você acessar aqui no seu
projeto aqui na aqui em segurança você
vai ter aqui ó membros e você pode vir
aqui e convidar ele para participar do
seu projeto e você pode dizer o que que
ele vai fazer né se ele é um membro né
se ele é um admin ou se ele é mais
restrito tá o restrito pessoal não vai
poder acessar nada vai poder ver as
coisas acompanhar as coisas o membro já
pode fazer modificações o admin pode
mexer em tudo inclusive adicionar outras
outras pessoas tá então é importante
entender isso aí recomendo que você
coloque como membro os terceiros que
trabalham com você ah luí contratei o
consultor ele fez aqui um trabalho
pessoal acabou ali ele entregou para
você vem aqui e remove não deixa aqui
não tá eh o seu seu servidor de sua
responsabilidade Tá bom então é muito
importante essa questão aqui e aqui
pessoal tudo que acontecer todos os
servidores IPS volumes backup SnapShot
tudo que você tiver vai ficar associado
ao seu projeto por isso que é importante
você agrupar dessa maneira e não ficar
misturando sen não chega a hora que você
não sabe qual qual backup é do que Qual
o ip é da onde né E aí você consegue é
muito mais fácil de fazer gestão pessoal
hoje o mercado tá todo mundo fazer a
mesma coisa todo mundo instalando todo
mundo configurando tá todo mundo todo
mundo é mestre em fazer isso daqui e
realmente não é difícil vocês vão ver
aqui no curso de docker que não é um
bicho Sete Cabeças o que vai diferenciar
você de outras pessoas é o cuidado que
você tem em organizar em você evitar
muitas vezes os erros né E
principalmente pessoal em você manter um
padrão então se você mantém padrão de
nomenclatura eu vou mostrar para vocês
aqui na head que dá para mexer bastante
com isso padrão de nomenclatura padrão
de de organização por equipe separar em
equipes diferentes setores diferentes do
projeto colocar as pessoas adequadas e
remover depois que você não precisa mais
as pessoas do seu projeto tudo isso vai
ajudar você a manter o seu projeto
saudável tá então é muito importante
pessoal prestar atenção aqui ah luí mas
é só um projeto ali no se você não tem
benefício nenhum pessoal em usar isso
daqui você tem que ter um projeto porque
não tem como criar uma máquina aqui sem
ter um projeto isso vai tá cada vez mais
ativo em todos os provedores né mas você
poderia ter um projeto só e colocar tudo
lá dentro você vai ser igual os outros
pessoal você não pode esperar um
resultado diferente se você faz a mesma
coisa então se você quer despontar se
você quer cobrar melhor pela sua
instalação se você quer se destacar
nesse mercado se você quer viver disso
quanto mais organizado quanto mais
profissional você for melhor e as
pessoas estão te avaliando quando você
entregar esse aqui para um cliente ele
vai obser Observar isso aqui falou nossa
organizou certinho aqui os recursos
nomeou os recursos certinho então é
muito importante que vocês prestem muita
atenção e dê muito valor para essa para
essa questão aqui da sua
organização pessoal então uma vez que
você criou aqui o seu projeto né você tá
aqui com seu projeto você vai abrir ele
e aqui na barrinha lateral você vai vai
ter acesso a tudo que a rner oferece
para você em relação a cada item que é
um assim relacionado a a VPS a sua
máquina então aqui nessa primeiro item
são servidores volumes Tá eu vou
explicar todos para vocês load balancers
float IP Network Firewall e grupo e a
questão de segurança tá então eu vou
começar com vocês aqui pela aba
servidores tá paraessa esse item
servidores aqui aqui na hler pessoal tem
uma grande vantagem em utilizar hetzner
que e que eu achei Fantástico é o único
provedor que eu já vi isso até hoje você
pode definir esse placement group que
ele tem aqui então se você vem aqui ó
tem aqui ó placement groups ele até
explica né Ó você pode agrupar múltiplos
servidores para otimizar a aqui o a
ability né você vai poder a
disponibilidade desses desses servidores
então basicamente pessoal O que que a
rner oferece para você e olha que
fantástica é isso daqui você vai criar
três servidores no seu swarm por exemplo
geralmente sua arma tem tem número ímpar
né mas fica seu critério mas geralmente
são número ímpar você vai ter um Hack
você vai ter uma máquina lá na rner
quando você habilita um placement group
você Tá informando PR hestner que cada
vez que você criar uma máquina naquele
placement group é para ele criar essa
máquina num hack
diferente porque senão pessoal imagina
uma situação que pode acontecer com
qualquer provedor com a haer também
porque não mas qualquer provedor de dar
um pau ali naquele h então vai ficar lá
uma hora fora do ar para acontecer uma
uma manutenção se você não habilitar o
placement group é provável que todas as
suas VPS fiquem dentro desse hack se der
um problema nele você fica totalmente
fora do ar quando você põe o placement
group você Tá informando para ele que
cada máquina sua é para ir para um Hack
diferente então se dá um problema nesse
hack basicamente só aquela VPS vai ficar
um pouquinho Fora as outras estão
funcionando é uma maneira que eles têm
de garantir para você mais
disponibilidade muitos provedores
pessoal não não oferece essa essa opção
não mesmo oferece opção tá isso aqui é
de muito valor e é muito legal ver esse
cuidado com a hestner em relação aí aos
seus servidores que no swarm por exemplo
você pode ter dois managers e você pode
ter dois workers então você tem a
garantia que cada worker tá funcionando
numa máquina diferente dá um problema
você sabe que usar um problema numa
máquina só a não ser que caia todo da
rede por exemplo aí não tem muito o que
fazer mas olha que detalhe interessante
que é esse placement group aqui tá Então
a primeira coisa que que eu recomendo
que vocês façam é Criar o seu placement
group e geralmente pessoal você pode
nomear ele e de acordo com o uso dele
então por exemplo né esse meu placement
group aqui eu vou chamar de
managers Então vou vir aqui e eu vou
criar um placement group managers eu
também posso criar um outro placement
Group aqui chamado
workers então eu tô garantindo né eu vou
organizar melhor eu tô garantindo que os
meus managers e os meus workers estejam
em máquinas diferentes dentro da
estrutura da rner para caso de um
problema na máquina não afete toda a
minha operação Fantástico Pessoal esse
aqui é um recurso ó Fantástico da herner
eu tenho certeza que vocês vão fazer
muito uso aí desses recursos da
hler além aqui do placement Group Você
pode ter gerenciar os seus IPS primários
A grande maioria dos provedores pessoal
Eles te dão o IP primário na hora que
você cria a a sua VPS aqui na rer não
você você pode fazer a gestão disso isso
aqui pessoal é muito legal então antes
mesmo de eu criar minha máquina eu posso
vir aqui e alocar o meu IP então por
exemplo e eu posso criar
aqui um IP primário chamado Manager
1 ele ele pede para min aqui uma um um
local no caso que eu vou usar os Estados
Unidos né então eu vou ficar em ashburn
e eu vou sempre escolher um
ipv4 pessoal o ipv6 é um padrão legal
mas ele é muito utilizado para quando
pessoas se comunicam com os sistemas um
sistema se comunicar com outro sistema é
muito mais efetivo que você use pv4
então por exemplo né aqui no curso mais
paraa frente a gente vai utilizar o
cloud flare o cloud flare ele é um proxy
então assim que a pessoa conectar com
Cloud flare ela pode conectar com Cloud
flare tanto por ipv6 quanto por ipv4
porém o cloud flare vai sempre se
comunicar via ipv4 com a sua estrutura
então do clod flare pra frente é
interessante ter o ipv6 do clod Fair
para trás já não é interessante ter ipv6
então a dica dou para vocês é sempre que
for criar uma máquina em qualquer
provedor que seja opte sempre pelo ipv4
não utilizem ipv6 tá até porque não vai
acontecer nenhuma acesso direto a essa
máquina vai sempre passar pelo Cloud
flare aqui é importante notar pessoal
que na herner você é cobrado pelo seu IP
primário beleza muitos provedores eles
embutem o preço ali e não te falam
Quanto custa aqu eles são transparente
aqui tudo que você utilizar você vai ser
cobrado aqui no caso né você pode ver
que são frações ali de euros Mas eles
vão te cobrar aqui tá bom então eu vou
criar aqui o meu IP pro meu Manager um
né tá lá o meu IP então associado pro
meu Manager 1 isso aqui pessoal é muito
muito legal por eu posso pegar esse IP
aqui e passar por exemplo aqui no spam
House por pessoal os IPS eles são
rotativos né o provedor não dá um IP
para qualquer para todo mundo assim para
esse IP nunca foi só meu esse IP já
esteve com outra empresa com outra
pessoa então eu quero saber qual é a
reputação dele então aqui no span housee
você vai acessar aqui o span housee você
vai achar aqui quer ver ó fica bem
escondidinho o dbl
ã aqui ó blocklist removal
Center você vai cair nesse domínio aqui
ó Check ppan
house.org e você vai colar aqui o seu IP
vamos ver se ele tem algum histórico
negativo ele falou ó o IP esse IP não
tem problemas beleza pessoal muita gente
ainda gosta de fazer algum tipo de
quarentena então você pode pode vir aqui
e alocar os seus IPS então você pode
alugar alocar aí os IPS que você for
utilizar lembrando pessoal que você só
pode alocar um IP por VPS tá então se
você planeja ter três máquinas você pode
alocar três IPS aqui tá não
necessariamente você tem que ter um IP
público mas você pode fazer isso daí
isso aí vai ser lá na frente no curso de
de swarm que eu vou mostrar uma boa
estratégia para você manter em segurança
suas máquinas tá mas aqui por exemplo
você já pode deixar o seu IP em
quarentena Então você já cria sua conta
você já passa pela parte burocrática de
ativação da sua conta da hester você já
cria o seu projeto já cria o seu
placement group e vem aqui e já reserva
o seu IP e deixa uma semana duas semanas
é uma garantia que não tem ninguém
usando esse IP esse IP tá parado com
você tá eu vejo muita gente usando essa
estratégia e é uma boa estratégia
pessoal de você poder fazer isso porque
você tem certeza que pelo menos os
últimos 30 dias os últimos 2 3 meses
esse IP aqui ele não tem um histórico
negativo então é muito legal isso daqui
aqui tá pessoal muito legal mesmo tá
lembrando que você vai ter que criar uma
máquina na mesma região que tá o seu IP
Tá bom então isso aqui é um ponto
importante IP primário só pode alocar um
por máquina e ele tem que tá na mesma
região que a sua máquina você pode vir
aqui você pode renomear o seu IP você
pode habilitar aqui a proteção esse essa
proteção pessoal ele vai
eh impedir que você apague o IP
facilmente você vai ter que fazer uma
série de confirmações ali para você
poder pagar o IP muito importante isso
aqui pessoal principalmente se você tá
configurando a máquina de um terceiro
Então você quer garantir que o cliente
não vai fazer bobeira aqui né Então vem
aqui e e Marc tá pessoal habilita aqui
essa proteção tá você pode
ainda apagar o seu IP ou vir aqui e
associar ele a alguma máquina dentro
aqui do seu projeto não tem nenhuma
máquina ainda mas eu poderia por aqui
associar o IP primário de um projeto
quando você cria uma máquina e já
associa é a melhor a melhor maneira
pessoal e aqui interessante porque você
pode até utilizar essa questão aqui do
IP primário como da mesma moda que a
gente usou ali no digital ho iper
flutuante né como eu tenho controle
sobre os IPS primários eu posso reservar
ele e passar ele pro meu cliente poder
acessar então se eu tiver que por
exemplo criar uma máquina nova com um
SnapShot de uma máquina se eu tiver que
fazer algum problema nesse sentido assim
tiver um problema de criar máquina nova
eu já vou poder criar a máquina nova com
o mesmo ip da máquina antiga isso aqui
pessoal ó pra gestão é maravilhoso isso
aqui é ponto demais para rner eu queria
que todos os provedores fossem assim
infelizmente não é Tá mas falei PR vocês
é um Provedor Fantástico são pequenos
recursos pequenos detalhes mas que fazem
toda a diferença então se você planeja
ter o seu swarm aí Já reserva o IP dele
e deixa o IP quietinho ali um pouquinho
tá você sabe que pelo menos num período
de tempo ali aquele IP foi seu ele não
foi usado mesmo tá E tá sob o seu
controle passa aí pelo sistema de
validação de IP para analisar reputação
tudo e fica legal esse IP aqui ele pode
ser agora sempre seu tá então você pode
adicionar máquinas remover máquinas do
seu swarm e você pode utilizar o mesmo
IP em máquinas novas isso aqui pessoal ó
fantástico ponto positivo demais para
rner pessoal Outro ponto importante aqui
da rner na barra lateral que antes mesmo
de você criar aqui a sua VPS é você
acessar aqui a aba de Network então aqui
em Network você vai poder criar uma rede
interna pras suas VPS Então graças lá ao
placement group eu posso dizer PR H que
eu quero que as minhas VPS fiquem em
hacks diferentes então a comunicação não
vai ser local a comunicação vai ser
através da rede interna da hetzner e
para você ganhar desempenho e pode ver
aqui ó as redes internas permite que os
seus servidores comunique um com o outro
através de um link dedicado Então dentro
dessa estrutura da h a comunicação vai
acontecer através de um link só deles
extremamente rápido com ping perto do
zero né extremamente prático mesmo
latência baixíssima Então vale muito a
pena você utilizar isso aqui para
questão de desempenho e principalmente
pessoal questão de segurança e
organização mas antes de você criar aqui
ó eu criei na aula anterior um IP
privado eu escolhi aqui ashburn eu vou
deixar aqui na aula para você vocês tá
esse esse essa esse vídeo aqui ó os
Locations da herner então como eu tô em
ashburn ele ela vai se referenciar como
Ash ashburn ou East um no caso né West
East então se eu utilizar eu Central são
esses três aqui mas como eles têm duas
localizações nos Estados Unidos eles vão
eu vou ter que criar a minha rede nesse
US East tá então eu vou vir aqui ó como
eu peguei em ashburn é o West East Então
vou vir aqui Networks eu vou clicar CC
em criar nova rede e aqui eu vou vou
escolher ó o s East né que é onde tá
ashburn que é onde eu aloquei lá o meu
IP primário pessoal aqui nesse caso
geralmente você vai usar uma rede só no
seu projeto então não tem necessidade
você quer ficar criando várias redes tá
bom
eh então não tem problema essa rede
minha chamar aqui de Network um não tem
problema tá não fica não fica bagunçado
dessa maneira é mas você pode dar o nome
que você quiser pode chamar de rede mpal
rede primária pessoal é criativo da nome
de rede eu vou usar crw 1 que é o que eu
gosto mesmo de utilizar tá então já
tenho o meu IP alocado nessa região e
agora eu criei uma rede para que todas
as minhas VPS se comuniquem dentro dessa
região dessa maneira aqui e aí pessoal
fica Fantástico fica Além da questão de
desempenho né fica muito bem organizado
e muito seguro e a comunicação das suas
máquinas
aqui na H você também pode ter o float
IP tá então o float IP ele é um IP que
ele é desconectado da sua máquina né Ele
é um IP flutuante porque tem esse nome e
você geralmente usa float IP quando você
quer oferecer um acesso mais seguro mais
estável externamente na maioria dos
provedores pessoal eu não controlo o IP
primário Então eu preciso do float IP
como se fosse um IP primário porque eu
vou poder criar máquinas eu vou poder
criar uma nova máquina voltar um backup
e utilizar o mesmo float IP nela como
aqui na HN você tem controle sobre o seu
IP primário eu acho que é um pouco
demais um pouco de over Engine você você
ainda utilizar o flat IP É claro que vai
ter muita aplicação boa para isso
Lembrando que você pode ter um float IP
por máquina também tá então você pode
vir aqui caso você queira e reservar o
seu float IP você vai pagar 3 e euros
para ter um ipv4 aqui de fluat IP tá é
importante pessoal que ele só só tá
disponível para servidores que estejam
na mesma zona que o seu fluat IP então
eu poderia aqui no caso criar um um
fluat IP aqui na aqui em ashburn né vou
chamar ele de web tá E aí eu vou clicar
aqui nesse banho ele vai comprar esse
meu float P então então eu vou pagar 3€
por mês só para ter esse float IP de
novo pessoal quem gosta de organizar
mesmo assim no máximo da organização
beleza at o float IP o IP primário aqui
na H faz o mesmo papel Então eu acho que
compensa muito mais você ter um IP
primário gerenciar o seu IP primário do
que você ter um float IP mas aí fica seu
critério pessoal é importante mostrar
que tem esse recurso para quem gosta de
utilizar ok né Então como que
funcionaria né esse IP aqui eu vou
associar ele a uma VPS então no float IP
a informação só entra então eu poderia I
por exemplo é colocar esse float IP aqui
no meu DNS no meu clod flare né e eu
posso apontar esse float IP para onde eu
quiser aqui dentro da hetzner Então você
tem uma garantia uma uma segurança muito
maior em relação a isso Tá Mas como que
na hetzner eu tenho controle do IP
primário eu prefiro controlar o meu IP
primário tá você só vai controlar o
float IP caso você realmente queira
criar uma camada super camada de
segurança aqui na rer né então você vai
ter esse float IP que no fim das contas
vai funcionar muito mais como um IP
primário e o IP primário já resolve
totalmente esse problema para você então
aqui pessoal eu posso e habilitar a
proteção para que ninguém apague esse
float IP eu posso transferir ele para
outro projeto eu posso renomear eu posso
editar o editar o DNS reverso vamos
fazer isso aí mais pra frente no curso
muito legal aqui a a h oferece essa
opção de editar o DNS reverso e eu posso
apagar o meu float IP então tá então
você vai ser cobrado 3€ por mês para
poder ter o seu float IP aqui na header
ele é totalmente opcional por causa da
maneira como a h a maneira fantástica
que a h oferece pra gestão do IP
primário lembrando pessoal que no IP
primário a informação entra e sai no
float IP a informação só entra então é
uma maneira que você tem de entre aspas
né utilizar um IP falso na sua máquina
não é o IP mesmo primário da sua máquina
tá muita gente usa esse float IP dessa
maneira para você poder ocultar mesmo
ali o IP real da sua máquina tá mas seu
critério Como que você vai utilizar isso
daí recomendo se você quer criar uma
camada mais de proteção legal aqui no
curso a gente vai usar sempre o float IP
Mas como eu disse para vocês tem
gerenciamento de IP primário na H então
eles fazem praticamente a mesma coisa se
você quiser ficar só com o primário tá
tudo ok também e aqui no painel também
temos o nosso amigo Firewall esquecido
né aquele amigo que quando você monta
aquela lista para jogar bola você fala
assim cara se alguém falar que não
vai aí eu vou chamar ele né o O fol é
esse cara né Ninguém liga para ele ele é
o cara mais importante Ele é aquele cara
que no jogo lá vai fazer o gol da
vitória mas ele é sempre deixado de lado
tá pessoal importantíssimo que você
tenha um fal na sua na sua VPS na sua
rede aí tá é importantíssimo a internet
é uma guerra é o tempo inteiro conflito
ali robozinho querendo querendo acessar
servidor é rastr mamento de porta a
gente vai ter no curso do cloue flir né
aqui na Prom web tem já o curso lod
flare mas no curso de docker vou abordar
também vocês não tem noção da quantidade
de requisição que chega no sistema de
vocês né e é importantíssimo ter o fal
para proteger isso daí então aqui o fal
ele funciona como todo fal em todo
provedor ele é um Firewall de borda
então isso aí é ótimo porque você não
vai gastar recurso da sua máquina para
proteger a sua máquina você vai gastar
recursos da rner para ela proteger a sua
máquina então a sua CPU e memória tão
preciosa não vai ser usada para esse fim
aqui deixa que a hestner se vira eles
têm lá computação de de sobra para poder
fazer isso daí e aqui pessoal como todo
Firewall assim que você aplicar um
Firewall numa máquina tá tudo bloqueado
você vai vir aqui explicitamente e vai
lançar as exceções tudo bloqueado exceto
a porta 80 a porta 443 PR as pessoas
poderem Acessar meu site a porta 22 da
csh uma portinha aqui do post gri uma
outra portinha Mas você vai limitar o
acesso para máquinas específicas as
máquinas do seu swarm é claro né E
também outras máquinas externas que não
estão no seu swarm com por exemplo a sua
própria máquina aí para você poder
acessar a sua h tá então aqui pessoal
criar um Firewall aqui é super simples e
por padrão pode ver que ele já vem para
você com a porta 22 aberta pro mundo e o
icmp para você poder fazer Ping se você
não habilitar aqui o smmp pessoal não
faz nem ping na máquina eh aqui no curso
eu conforme ao longo do curso eu vou
passando para vocês em todas as aulas
Quais são as portas que vocês devem
liberar no Firo tá para você já deixar o
seu F pronto mas geralmente pessoal você
vai querer bloquear o SSH Então você vai
vir aqui você vai acessar aquela página
clássica que é o meu
IP você vai pegar o IP da sua máquina aí
tá depende muito região tá pessoal eu tô
numa região aqui que o IP ele até
relativamente fixo a gente fica um bom
tempo com o IP aqui aonde eu moro né
então mas tem gente que pode comprar aí
um IP fixo mesmo né Ó então eu vou
permitir ó que a minha máquina a minha
máquina e eu vou remover esse ni pv4 n
pv6 Então somente a minha máquina pode
fazer acesso à porta 22 ou seja por mais
que alguém Descubra o meu usuário sem da
minha máquina se ele não tiver aqui na
minha casa ele não acessa a VPS então é
um bloqueio que você faz dos melhores
que tem Luiz eh qual que é o melhor
bloqueio que eu posso fazer para S sh o
melhor bloqueio pessoal é você vir aqui
ó e e remover a regra ou seja tá
bloqueado para todo mundo ninguém acessa
nem eu então geralmente quando você não
tem que fazer um acesso s é isso que
você você vai fazer você vai abrir aqui
e vai remover a regra do da porta 22 Tá
bom você só vai criar a regrinha aqui da
porta
22 quando você for acessar o SSH tá bom
só isso né só só nesse período Fora esse
período Remove a regra Então ninguém
realmente ninguém acessa a porta 22 aí
da sua VPS então vou deixar o ping eu
vou deixar o 22 com meu IP e aqui eu vou
colocar pessoal ó se você vem aqui você
vai ter a porta aqui ó ele vai te
mostrar né htp 80 e a porta
443 que é a porta de SSL da htps e aqui
esse daqui vai ficar aberto pro mundo
todo mundo pode vir aqui e pode acessar
essa essa porta aí a porta 80 e a porta
443 tá pessoal então aqui ó eu permiti
acesso htp para todo
mundo se eu quiser fazer algum filtro eu
vou fazer esse filtro no Cloud flare tá
e eu tô permitindo também aqui ó que
somente o meu IP acesse a porta 22 e
também tô permitindo ali o ping outbound
roles pessoal você não precisa criar
essas regras de saída A não ser que você
realmente queira ter um controle muito
grande mas aqui é difícil pessoal porque
você vai ter que ver cada sistema tem na
sua máquina cada porta que ele usa então
para quem é mais e aficionada aí nessa
questão de segurança beleza Mas no geral
não precisa tá você você protege a sua
máquina do mundo não o seu mundo da
internet tá não não o contrário tá bom E
aqui ó você caso você já tenha né um
servidor criado você já poderia aplicar
ele a gente não vai fazer isso não tenho
nenhum servidor eu não preciso criar o
Firewall aplicando Nada também tá
recomendo que você faça isso e aqui ó Eu
Vou Chamar esse meu Firewall de
base Então vai ser o meu Firewall base
deixa eu remover aqui eu da tela aqui no
cantinho fica o botão para você poder
criar o Firewall ó então vou clicar aqui
e ele vai criar o Firewall para mim e tá
lá criado o Firewall para mim aqui ó
Então já tenho uma Firewall com quatro
regras é o meu Firewall base tá E aí
você vai poder criar eh firewalls
específicos para poder aplicar nas VPS
lembrando sempre que rega padrão no
Firewall bloquear tudo né E você vai com
com base em regras liberando algumas
portas ali para acesso pessoal eu vejo
muita gente utilizando né
Eh usando portas abertas portas expostas
no mundo do docker a gente não usa assim
né o docker ele tem uma rede interna a
gente vai ver isso no curso né eles
comunica de uma outra maneira por
questões de segurança né um dos
benefícios do docker é que ele acabou
com esse essa questão do IP mesh que é
você ter que se preocupar com o IP da
máquina e acabou também com o port MH
que é você ter que se preocupar com a
bagunça de porta do Servidor o docker
ele já evol voluiu nesse sentido mas
quem ainda tá lá atrás e ainda fica
brigando com porta é mais importante
ainda que você configura o seu Fire tá
pessoal porque senão você tá correndo
sérios riscos aí de problema de
segurança na sua máquina tá então super
simples criar o firal ao longo do curso
a gente vai criar mais Fir mais regras
de Fir tá bom para que vocês possam
aplicar na sua máquina aí então fica
essa dica para vocês de vocês poderem
configurar adequadamente o Firewall
geralmente por padrão você vai deixar o
22 com seu IP o 80 e o 443 aberto pro
mundo e o CMP aberto pro mundo PR você
poder fazer Ping ah Luiz não faço o ping
então você pode vir aqui e remover até o
icmp Se você quiser né se você for usar
otime Robot você deixa aqui o ismp né e
e e libera ali pro ip da sua própria
máquina né E você pode criar aqui outras
portas né ó ele até mostra vocês aqui ó
mais KL poch gri é tem outras portas que
você pode até ser interessante
que vocês liberem aí tá mas ficar bem
atento aqui pessoal na na questão do
Firewall porque é uma proteção que você
tem que ter você não pode descartar você
pode vir aqui e apagar o seu Firewall n
e você pode depois associar eles na
criação da VPS ali você já a gente já
cria VPS já associa o Firewall para
poder proteger ela pessoal aqui na no
menu segurança você vai poder adicionar
suas chaves aqui no pel da H Eu já falei
PR vocês em outros cursos eu não gosto
de colocar meu a minha chave em lugar
nenhum né minha chave fica comigo aqui
na minha máquina tá então fica seu
critério também se você vai adicionar
aqui luí quer dizer que você não confia
na Hater não pessoal quer dizer que é
uma política minha de não colocar minha
chave em lugar nenhum as minhas chaves
estão comigo então se vazar foi comigo é
uma é uma regra minha porque a chave
pessoal a gente vai ver isso no curso
mais pra frente também a chave ela vai
permitir que você se conecte da sua
máquina essa máquina é um é uma é um PA
de Chaves Então você tem uma chave com
você e você vai adicionar uma versão
pública dela no servidor quando você
pedir para se conectar o servidor vai
bater se a versão que ele tem lá
corresponde com a sua aqui se
corresponder ele permite acesso sem
senha um acesso direto ao servidor isso
aí é muito prático Nossa eu eu uso
demais isso daí só que é um baita perigo
porque vai permitir acesso qualquer
máquina sua aí então eu não gosto de
compartilhar minha chave por questões
mesmo aqui de segurança por políticas
mes tá eu não eu não vou recomendar
porque é uma coisa muito pessoal mas eu
faço isso tá eu não não deixo minha
chave com ninguém aí então você vai
gerar a sua chave na sua máquina aí e
você vai colocar aqui a sua chave né e
toda vez que você for criar uma máquina
você vai
poder atribuir essa chave aqui ele fala
né ó isso aqui não tem efeito sobre
servidores existentes você vai ter que
adicionar manualmente a gente vai ver
isso no curso né Em algum momento no
curso a gente vai adicionar manualmente
a chave no servidor tá então fica
totalmente a seu critério pessoal a HN
tem api então através de api você pode
manipular tudo aqui na rner e aqui que
você gera mas pessoal pelo amor de Deus
muito cuidado com isso daqui tá não é
brincadeira né de perder esse api
compartilhar um workflow NN com api já
vi demais isso daí Não façam isso tá
você pode caso você tenha e certificado
SSL de comprados né de de de
registradores aí você pode subir aqui o
seu certificado a gente também não vai
usar que a gente vai usar o leds encrypt
no curso e já mostrei para vocês aqui de
vocês adicionarem membros na sua na sua
no seu projeto tá pessoal então essa
abinha aqui é uma Binha importante quem
quiser colocar aqui a chave e quando for
criar a máquina já criar com a chave
fica à vontade tá pessoal mas a gente
vai fazer isso no curso manualmente
depois pessoal então agora a gente já
fez aqui um overview eu passei com vocês
pelo placement group pelo IP primário
criamos a rede criamos o fal já comprei
um float IP já gastei dinheiro até mais
que eu queria gastar aqui com float IP
load balancer a gente não vai utilizar
porque a gente vai usar o o próprio
docker swarm para fazer isso pra gente
já passei com vocês aqui em relação
também à Chaves é hora então de criar
uma máquina aqui na headster então vou
clicar aqui em AD server e como eu criei
o meu IP e já
pré-configurada a minha estrutura vai
ficar locada aqui em
ashburn Lu quer dizer então que eu só
vou poder ficar amarrado aí não você
pode criar os seus IPS primários em
outras regiões configurar redes para
outras regiões então você pode est no
mesmo projeto máquinas em regiões
diferentes mas lembrar sempre que o seu
IP tudo vai ficar sempre amarrado na
região tá então eu criei comprei um IP
primário comprei um float IP todos eles
aqui em ashb Tá eu vou então você vai
escolher aqui a região eu vou escolher
pessoal o Debian 12 porque na promov web
a gente sempre usa Debian não é uma
questão de guerra de disco não é uma
questão de gosto pessoal nada disso
pessoal o deban é a distro que menos
mexe né Cada passo que o debia andar é
muito calculado Então nada mais justo do
que optar pelo Deb né algumas distros
elas mudam muito elas são muito hypada
elas estão sempre seguindo novidade para
quem vai lidar com o servidor pessoal
para quem vai ter sempre aquela
receitinha para fazer as coisas para
quem precisa de organização porque
precisa saber onde tá cada arquivo onde
estão as configurações o Deb É de longe
é a melhor distro nesse sentido porque
ele dificilmente muda sua estrutura Ele
simplesmente atualiza mas ele atualiza
sem quebrar tudo né outras distros não
seguem muito esse caminho tá aqui ele
pergunta para mim o tipo de máquina e é
muito importante Então cpus
compartilhadas pessoal já falei para
vocês né até falei no outro curso lá que
você vai ter um Hack esse hack vai ter
uma quantidade de trads de CPU e você
vai vai reservar para você um espacinho
ali só que esse espacinho não é só seu
Você vai ter outras VPS de outros
clientes da Hat na mesma máquina né
então você tá dividindo ali tá numa
briga de ombro ali e existe uma teoria
grande da Conspiração que diz que se
você não usar alguém vai usar no seu
lugar né então é importante ter isso em
mente sepu compartilhada pessoal você tá
locando um espaço nela mas outras
pessoas também estão fazendo o uso desse
espaço tá bom muito importante essa
noção aí e aqui pessoal no caso eu não
vou u
e máquinas Army tá ainda não uso
máquinas arm lá no curso eu expliquei
para vocês o motivo tá porque ainda não
são todos os sistemas que são
compatíveis eu acredito que o arm Vai
sim ser o futuro ele ainda não é o
presente mas estamos na transição entre
o arm e seu presente o arm e seu futuro
o arm ainda é o futuro o presente
pessoal se você for pegar a maioria dos
sistemas que vocês vão utilizar ainda
são baseados em Intel AMD tá então ainda
ainda não tem porquê é claro que cada um
um vocês podem instalar com arm e
escolher imagens na web aí que tenha
suporte para o arm não tem problema tá
mas o padrão que do curso vai ser por
enquanto o Intel AMD e você pode pegar
uma GPU uma uma CPU dedicada pode ver
pessoal que nesse caso aqui né Ela não é
uma máquina inteira dedicada o que a h
tá fazendo é ela falar assim ó luí você
contratou lá uma máquina com duas cpus
só que em uma tred imagina um haack
pessoal que vai ter lá 64 vcpus 128
vcpus tem uma pancada de CPU lá na
compartilhada você não tem duas só para
você você pode utilizar duas então você
tá no mesmo ciclo de CPU ali numa mesma
pegada você mais outras várias máquinas
no talo executando as aplicações Quando
você vai pra dedicada aí você realmente
aluga trades só para você se você usar
Beleza se você não usar mas ninguém usa
aquilo ali é literalmente só seu Então
essa é a diferença aqui da da vcpu
dedicada e da vcpu compartilhada tá bom
Ó aqui at aqui até eles explicam né ó e
você vai ter um preço muito melhor então
Olha só pessoal a máquina base de de CPU
compartilhada a máquina base Vamos
colocar aqui a de a de set vai ó 3v cpus
e 4 GB de memória pessoal 7€ e é é um
cafezinho que você toma aí né então por
mês né isso daqui quando você parte pra
dedicada começa a brincadeira com 11 mas
aí pessoal eu eu vou ser sincero com
vocês que eu já prefiro sempre utilizar
CPU dedicada tá porque os seus sistemas
ficam muito mais rápido você tá bem
menos suscetível a a ter queda de
performance principalmente máquina de
produção vai criar um teste beleza usar
o CPU compartilhado vai P para rodar
mesmo uma máquina que vai participar de
um lançamento que você v utilizar em
produção mesmo com seu cliente já pega
pessoal o preço da hler é imbatível
então não tem motivo não vejo motivo
Olha só R 100 aqui vamos vamos
arredondar para R 100 com R 100 você tem
duas cpus inteirinha sua e 8 GB de
memória é muito preço muito bom pessoal
para um lançamento aí que o pessoal fica
brincando de fazer seis em S pro cara
pagar R 100 não é nada né então você
escolhe aí eu recomendo muito que vocês
escolham ali a a opção da CPU dedicada
tá aqui no curso por fim de dáo vou
pegar compartilhada mesmo Olha só o
Network aqui pessoal apareceu nosso
amigo networking aqui ó pessoal no curso
mais pra frente eu vou explicar com mais
calma PR vocês mas dentro de um swarm
você não precisa necessariamente que
todas as máquinas tenham um IP público
né explico né porque você não
necessariamente precisa que as máquinas
tenham acesso a internet é muito comum
na organização ali de máquina que você
opte por servidor de banco de dados não
ter IP público Então você vai acessar o
seu Manager e do seu Manager você vai
acessar a máquina pela SSH pela rede
interna isso aí pessoal uma camada muito
grande de proteção Só que você tem que
ter experiência e saber utilizar isso
daí tá então eu recomendo que no começo
vamos vamos escolher sim tá um IP paraa
sua máquina e olha que legal assim que
eu que eu cliquei aqui que eu quero um
IP v4 eu já posso escolher aquele meu IP
que tá vazio lá que eu já pré loquei eu
vou desabilitar o ipv6 eu vou repetir
aqui pessoal desabilitar o ipv6 olha
aqui quem tá assistindo aqui ó
desabilitar o ipv6 tá e eu vou clicar
aqui que eu vou utilizar uma rede
privada e eu vou selecionar a minha rede
Então eu estou criando a minha máquina
eu estou tendo a opção de escolher o IP
primário dela e já estou colocando essa
essa máquina na minha vpc na minha rede
interna da headster ó pessoal
maravilhoso isso aqui esse recurso de
rede da headster é maravilhoso e aqui de
novo se você quiser colocar a sua chave
é é agora a hora você vai cadastrar aqui
a sua chave da sua máquina fica seu
critério tá se você quiser pessoal eu
até recomendo que você faça isso nesse
momento aqui porque você já vai colocar
a chave direto lá tá mas a gente vai
fazer isso mais para frente no outro
curso de instalação e configuração dos
Servidores mesmo né a parte mais prática
como que funciona isso daí e aí entra
Pessoal a questão do volume Tá eu vou
explicar depois com mais calma para
vocês o volume mas basicamente pessoal
um volume n aqui para vocês ó um volume
é uma é uma capacidade a mais que você
pode colocar é como se fosse um HD
externo que você anexa na sua VPS então
a sua VPS vai ter aqui no meu caso deixa
deixa eu voltar para vocês verem aqui ó
na parte de de armazenamento 80 GB 80 GB
pessoal pro sistema operacional pro
docker pros contêiners pra parte mais
útil pra parte que vai utilizar o
sistema mesmo mas você pode vir aqui e
dependendo do seu caso você pode
adicionar um
volume que que é o volume pessoal você
vai anexar um outro HD como se fosse um
HD externa a sua S E aí você vai poder
direcionar itens para lá no docker por
exemplo eu posso pegar os bancos de
dados e colocar lá no digital otion eu
nunca tive uma experiência boa de
armazenar banco de dados em volume mas
aqui caso você queira você pode est na
HN eu já tive vários cenários vários
casos de uso ali de pessoas que
armazenaram o banco de dados em volume
deu certo tá então no curso lá na frente
no curso de docker eu vou mostrar para
vocês quem for usar o Minio e alguns
itens né o o nin por exemplo você quem
quer usar o NN com muito log muito legal
você ter essa opção aqui e olha como é
que é baratíssimo pessoal para eu poder
colocar 100 GB 100 GB a mais para poder
colocar o meu banco de dados armazenar
backup a gente vai ver isso no curso
como que você armazena backup em volumes
né mais é pra frente pro final do curso
mas a gente vai ver isso aí no curso 4 €
é um é literalmente o preço de um
cafezinho tá gente então aqui ó eu posso
chamar de
back né Eu Vou Chamar esse meu volume de
backup e aqui tem um ponto muito
importante
pessoal né quando você for escolher aqui
o seu volume não eu vou criar aqui o
volume ele vai ser cobrado junto com a
máquina né então aqui no caso ó deixa
pegar para vocês verem aqui ó tô pegando
100 GB 100 GB o nome do meu volume aqui
é back Ó lá ele fala né que o volume ele
tá associado ao local da máquina então
além dos 80 GB que eu tenho da máquina
eu tô optando aqui por T 100 vou por 100
não vou comendo tá gente porque é para
poder para gravar aula só então vou
clicar aqui ó criei o volume comprei lá
né Assinei o volume e tá lá meu volume ó
Prontinho meu volume aqui
ó da ele só fala ele só fala né o seu
volume é avaliar é disponível num
armazenamento de SSD tá vendo ó você
pode ter você pode aumentar fazer um
upgrade seu volume você pode criar
backup dele fazer tudo então muito legal
pessoal isso aqui é um recurso avançado
totalmente opcional eu vou mostrar para
vocês isso aí lá na frente só no curso
tá pessoal inclusive você pode não pegar
isso aqui agora você pode anexar um
volume depois na sua VPS eu tô mostrando
aqui só as opções mesmo para vocês tá
então no caso aqui ó eu não vou nem
criar a minha máquina com volume só para
mostrar para vocês como é que funciona
mas eu Já reservei ali esse volume aí e
o legal pessoal que o volume ele eu
posso conectar ele na máquina e
desconectar da máquina e Conectar em
outra máquina Então imagina uma situação
onde você tem ali um banco de dados
funcionando e deu um deu um PTI na
máquina quebrou a máquina e você quer
pegar esse esse esse volume Onde estão
os backups e você vai criar um servidor
novo e você vai anexar esse volume de b
no servidor novo e vai poder ir lá e
restaurar os seus bancos de dados né
Isso aqui é fantástico pessoal mas você
pode muito bem até incentivo que você
crie já a sua a sua VPS sem volume por
enquanto tá lá na frente do curso eu vou
explicar isso para vocês e o Firewall tá
aqui ó eu vou clicar no meu Firewall
conforme vocês forem criando seus
firewalls vão aparecendo ali mas no caso
aqui eu vou aplicar o base criei máquina
pessoal eu já crio máquina com com
Firewall ativo tá E aqui entra a questão
do backup backup pessoal Olha só o
backup o backup custa 20 20% do servidor
do preço do Servidor pessoal não não tem
por vocês não habilitar o backup tá
desculpa falar mas não tem por pessoal
caro é você perder o seu banco de dados
20% de uma máquina que já tá num preço
Imbatível aí pelo amor de Deus então não
tem motivo não tem desculpa pessoal pelo
amor de Deus tá Para de ficar e contando
ali contando moeda na hora de pegar
servidor não é assim que funciona
pessoal essa brincadeira aqui ela não é
assim tá bom então ó na
rner custa 20% da sua máquina mas o
backup é diário a gente viu no digital
oan que o backup lá é semanal aqui não a
brincadeira aqui é mais séria aqui é
diário então todo santo dia você vai ter
uma cópia inteira do seu servidor
pessoal Putz não não dá nem para falar
pessoal tá pelo amor de Deus contrata
com backup tá
bom E aqui tem o o placement group né No
meu caso aqui eu vou criar um Manager
então eu vou escolher que eu quero
colocar Eu quero seguir aqui o meu
placement group de managers né então
todos os meus managers eu vou escolher o
placement group Manager que vai me
assegurar que não tem nenhum Manager na
mesma máquina que outro tá os meus
managers estão em máquinas físicas
separadas Tá bom quando eu for criar o
workers lá no curso lá na frente eu vou
escolher aqui o Playman grupo de workers
tá bom E aqui tem os labels né pessoal
os labels eu não não utilizo Label Mas
você pode utilizar ele para grupar o
servidor para organizar um pouco mais
mas eu acho que é muito Cloud config
pessoal você pode colocar um script aqui
que vai ser inicializado vai Rod dá para
você toda vez que a sua máquina for
criada Tá então não vou utilizar também
aqui tá no curso mais paraa frente a
gente vai ver isso daí e aqui pessoal
tem o server name lá mais paraa frente
no curso eu vou mostrar como é que você
muda o nome da máquina mas se você já
criar a máquina com o nome vamos supor
Manager
1 01 aqui ó né Eu gosto de colocar zero
porque eu posso ter mais de 10 né então
Manager 01 ele já já vai criar também
essa máquina com esse nome eu só vou
remover aqui o meu vídeo da atela
pessoal para que vocês possam ver aqui o
botãozinho ó Então olha lá pessoal a
brincadeira inteira aqui ó vai me custar
incríveis 8€ por mês 7€ da máquina 1.4
de backup pessoal 1.4 de backup então
habilitem o backup pelo amor de Deus tá
então vou clicar aqui e vou comprar vou
adquirir essa máquina aqui pessoal eu
não sei se se é só comigo mas já
aconteceu de demorar 10 minutos para
poder criar VPS para mim já criou em
menos de minuto ó lá menos de 1 minuto
ela já criou então é muito relativo aí
tá de acordo aonde você tá o recurso que
você
escolheu enfim né às vezes vai num
piscar de olhos às vezes demora um pouco
mais tá bom então ten paciência aí com a
hestner nesse sentido então criou a
minha máquina pessoal tá aqui prontinha
a minha máquina né uma vez que você
seguiu que esse passo a passo você já
vai ter sua máquina disponível para uso
aí
pessoal uma vez que eu tenho a minha
máquina criada eu vou repassar com vocês
aqui alguns pontos porque muda né porque
a gente criou alguns itens eu não tinha
a máquina associada agora eu tenho a
máquina associada Só Quero mostrar para
vocês mesmo aqui para fim didático Então
olha só se eu clicar aqui no meu IP
primário agora tá mostrando ó que o meu
IP primário tá alocado no Manager se eu
venho no placement group pode ver que
agora ele mostra eu posso clicar no meu
plac group ele mostra que eu tenho aqui
um Manager né ele mostra o IP ele mostra
máquina que é tá se tá respeitando
Seguindo aqui o meu o meu placement
group se eu V em volumes como eu não
criei Eu não associei o volume à máquina
ainda tá aqui tá bom Mas tá mostrando
que eu já tenho aqui o meu volume
reservado tá se eu venho aqui no float
IP também não associei o float IP na
criação da máquina você tem que fazer
isso depois tá bom mas daqui a pouco a
gente fala mais sobre isso em Networks
tá aqui o minha Network mostrando que eu
tenho um recurso associado a ela tá S eu
abrir que eu vou ver que o meu Manager 1
tá dentro do do da minha network e o
mesmo com o fal né ó deixa eu abrir aqui
o fal eu vou ver aqui no researches na
mininha researches que o meu servidor tá
dentro dessas regrinhas de Firewall só
para vocês verem como é que o painel da
hler mostra então se você abrir o
servidor daqui a pouco a vai vai abrir o
servidor e vai ver todas as opções mas
se você for nas opções você também
consegue acompanhar Quais são os
servidores que estão Associados aqui a
ela então aqui eu vou entrar na minha
VPS né Eu vou acessar aqui ó Manager 1
você dá um clique no nome dela e você
cai aqui nessa tela que te dá uma visão
geral um overview da sua VPS aqui ele
mostra pessoal e informações básicas
vcpu memória disco o o o quanto tá
custando Então essa minha máquina já
custou 01 cavo 1 centavo aí a minha
máquina o quanto de tráfego já circulou
por ela então eu tenho 20 teras para
essa máquina aqui né Ó então quanto que
você trafegou e o preço total final dela
pessoal quem for adicionar solicitar aí
ajuda no grupo for chamar algum tipo de
suporte tira um print dessa máquina
dessa dessa tela aqui só pra gente poder
ver que máquina que é porque às vezes
falta muito recurso na máquina Às vezes
pode ser alguma coisa que aqui já já
ajuda a gente a a detectar tá bom E aqui
ó você vai ter vai ver nesse activities
né Ó você vai ver ó que eu criei a
máquina ó então apliquei o o o Firewall
na máquina criei a máquina habilitei
backup e anexei uma rede nessa máquina
aqui então é legal ver aquela opção ali
porque ele mostra ele vai mostrar para
você ali a esse activity né esse esse
log de atividade da máquina aqui tá
mostrando que o backup tá habilitado
aqui tá mostrando o meu placement group
tá E tá mostrando que eu tenho aqui uma
rede pública tá ó na minha máquina aqui
também mostra o Data Center né o país a
cidade e a zona aonde ele tá colocando
aqui colocando essa minha máquina
segunda apaa pessoal
grafs aqui tá vazia que acabei de criar
a máquina Mas é por aqui que você vai
acompanhar em tempo real tá você pode
vir no Live aqui ó e acompanhar
estatísticas de uso da CPU tá eh e eh de
disco né de uso de consumo de disco de
rede então é importantíssimo olhar isso
daqui não é o painel dos sonhos tá
pessoal aqui falta alguns itens eu
queria ter aqui que tivesse aqui de
memória o stock de memória e de consumo
de disco Vamos torcer para que eles
coloquem isso futuramente pra gente tá
mas você pode inclusive vir aqui e
solicitar o histórico de 12 horas de 24
horas uma semana isso aqui é legal para
você detectar aí Picos de uso da sua
aplicação tá então já é um painel legal
para você poder acompanhar aí
estatísticas da sua máquina aqui em
backups como eu habilitei o backup da
minha máquina eu posso vir aqui ó e
rodar agora manualmente um backup porque
eu não tenho nenhum backup ainda então
eu recomendo pessoal que assim que você
criou o seu servidor acabou de criar Já
faz um backup dele para que você já
tenha aqui um backup do seu servidor tá
ó aqui eles falam né infelizmente eles
não mostram o digital o mostra a janela
de backup que eles não mostram Mas você
vai poder acompanhar isso daí quando
tiver em produção aqui o seu backup o
horário que eles fazem o seu backup tá
bom E aqui também fica o status do seu
backup aqui eu tenho o SnapShot por está
fazendo um backup aqui ele não não
habilitou para mim a opção do SnapShot
né ele tá falando que um backup tá sendo
criado Mas você pode vir a qualquer
momento e criar um SnapShot também tá o
backup pessoal aqui também é importante
da gente conversar sobre isso né o
backup é uma cópia automática da sua
máquina em intervalos fixos de tempo
então todo dia de noite com certeza de
noite você vai ter um backup aí só que
ao longo do dia eh você pode fazer
algumas tarefas você pode fazer um
upgrade de um sistema você pode começar
uma nova campanha ou você pode terminar
um projeto Então você não quer esperar
até a noite para para dizer que aquilo
ali tá Tá salvo tá protegido então você
pode vir aqui e fazer um SnapShot ele
vai realmente tirar um print da sua
máquina vamos dizer assim ele vai copiar
tudo a sua máquina naquele ponto ali o
SnapShot e o backup você pode restaurar
na própria máquina ou você pode criar
outras máquinas a partir deles tá então
é muito importante também e essa
observar ação aí então aqui ol Ele criou
o backup como não fiz nada nem acessei A
máquina ainda então tem menos de 1 GB
300 m utilizando eu posso vir aqui
pessoal ó deixa eu só tirar aqui minha
da tela Ó lá eu
posso dar um rebuild Ou seja eu posso
pegar essa própria máquina que eu tô com
meu Manager 1 e voltar ao backup nessa
própria máquina eu posso criar um
SnapShot e eu ou eu posso criar um novo
servidor com esse backup E aí aí entra
pessoal aquela questão que eu falei para
vocês de eu poder ter controle do IP
primário porque eu poderia criar um novo
servidor apago essa Manager 1 eu vou
criar um novo servidor e eu vou atribuir
o mesmo IP primário para esse novo
servidor isso aí pessoal ó Fantástico tá
lembrando sempre né pessoal que o ideal
é você converter para um SnapShot antes
de apagar a máquina tá bom o backup ele
é associado à sua máquina o SnapShot não
é associado à sua máquina tá
bom aqui no SnapShot pessoal então a
mesma coisa tá que eu tá falando para
vocês o SnapShot você vai pegar num
ponto específico do dia e vai fazer um
backup e vai fazer uma cópia dele ali a
diferença pessoal que o backup já tá
incluso no seu preço você já assinou ele
por 20% do valor da máquina o SnapShot
não eles cobram por giga tá ó então aqui
ó
0.011 € por giga por mês tá bom então se
você fizer um SnapShot ele vai ser
cobrado à parte recomendo muito que
vocês coloquem no no nome do Snapshot a
situação atual da máquina então aqui ó
nova Ou seja é a máquina nova esse meu
SnapShot aqui é a minha máquina nova Ah
luí eu vou atualizar aqui o meu nhn
então você vai vir aqui e vai colocar a
versão atual do seu nhn não a versão
nova que você vai atualizar a versão que
tá nele então vai ser lá o meu Manager 1
por exemplo
nhn
1.7 1.1 1 desculpa pess
1.6.1 Beleza então sei que eu vou fazer
o upgrade Se der algum pepino der algum
problema ali eu vou poder voltar é o meu
backup pra versão anterior se eu perdi
alguma coisa perdi 10 minutos da minha
vida esperando voltar aqui o backup tá
voltar aqui o SnapShot Então pessoal
muito importante fazer isso daqui muita
gente falha miseravelmente nisso Vocês
que são meus alunos não podem falhar
nisso pelo amor de Deus tá pessoal você
euv o puxa a orelha Se eu ficar sabendo
eu xingo tá então é importante ter isso
daqui tá gente é muito importante a
gente ter esse SnapShot aqui isso aqui
vai salvar a pele de vocês em muitas
situações tá backup diário habilitar
Qualquer Custo backup diário e também
tem SnapShot pessoal não faz essa
economia aqui ó essa economia você não
se faz tá gente vocês já estão
economizando só pelo simples fato de tá
utilizando aqui a rer tá o que el que
ela entrega é muito maior do que ela
cobra Tá bom então vamos vamos conversar
vamos começar a conversa a partir disso
tá então tá aqui o meu SnapShot ó mesma
coisa ali então aqui as opções do meu
SnapShot eu posso fazer um rebuild né eu
posso reconstruir essa minha máquina que
voltar esse SnapShot aqui na minha
máquina né eu posso criar um novo
servidor E olha que legal pessoal eu não
tenho isso no backup eu posso pegar
SnapShot e jogar em outro projeto então
imina que você criou uma máquina base
ali criou uma máquina básica você já
aplicou alguma customização nela você
pode usar como um template por exemplo
né ó e você pode habilitar aqui esse
protection ainda que é para impedir
mesmo que outras pessoas apaguem esse
Snapshot ele vai encher o saque de
confirmação ali para você poder apagar
Então pessoal habilitar backup e se
possível fazer um SnapShot antes de
fazer alguma atualização Luiz mas vai me
cobrar Beleza cara deixa um dia aqui né
atualiza o NN atualiza o chat útil que
você quiser atualizar faz SnapShot antes
e deixa Ah luí usei por um dia dois dias
pessoal vai cobrar centavo seu aqui né
Aí você vem aqui né e e e remove tá
então esse esse meu backup aqui ó vai me
custar 11 centavos nem isso né ó porque
é um é 11 por giga não deu nenhum giga
esse esse SnapShot aqui então Não tem
motivo para fazer isso tá gente aqui na
aba networking você vai ver o ipv4 que
tá associado aqui né ao seu IP primário
o DNS reverso dele eu não peguei o ipv6
não tem ipv6 você vai ver aqui a sua
rede primária a sua rede e privada né a
rede interna que no nosso caso aqui foi
a Network 1 né e eu não vou não não
ionei até então um IP flutuante né mas
eu tenho lá o meu IP web ó Então nesse
momento aqui ó eu posso vir aqui clicar
nesse IP e eu vou associar esse meu IP
flutuante aqui aí pessoal Olha que
interessante que acontece isso aqui é
uma coisa da H que eu acredito que com o
tempo a equipe de produto dele vai mexer
o IP flutuante foi adicionado à máquina
tá mas eu preciso e configurar ele eu
preciso ir lá no no meu servidor e rodar
esse comandinho aqui aqui ó Então vou
copiar esse comandinho aqui tá eu vou
vir aqui em cima ó no terminal Eu vou
acessar o console aqui da
rner e através desse console aqui né ele
vai permitir que eu deixa eu entrar aqui
acho que ele mandou para minha senha por
e-mail ele vai permitir que
eu atribua realmente configure realmente
esse IP flutuante Deixa eu só abrir aqui
o e-mail para vocês verem porque assim
que você criar sua máquina eles vão te
enviar esse eil a senha dessa máquina
por e-mail Deixa eu só abrir certinho
aqui então tá aqui pessoal ó esse aqui é
o e-mail da rner informando que o meu
Manager 1 foi criado tá aqui o IP dele
né o IP v4 dele o usuário e a senha ó
então aqui ó é é root e essa senha eu
vou usar esse o login aqui vai ser
Rot vou apertar enter na hora de digitar
a senha eu vou colar aqui ó vamos ver se
vai ah preciso trocar a senha então
vamos trocar a
senha vou redigitar a minha
senha e beleza entrei aqui na minha na
minha máquina e eu vou rodar aquele
comandinho
lá do do fluat IP Deixa eu ver se eu
tenho aqui o comandinho show
instructions aqui ó Então vou copiar ele
aqui e eu vou acessar o meu terminal e
eu vou colar aqui ó lembrando pessoal
que aqui na na H é totalmente opcional
isso daqui tá então rodeia o comandinho
posso fechar que agora sim o meu float
IP tá configurado aqui tá pessoal mas de
novo super opcional só o fato de poder
gerenciar o seu IP primário já é o
suficiente tá eu só fiz aqui porque eu
fiz o curso do digital otion para deixar
os cursos iguais tá bom E aqui pessoal
nessa máquina específica toda a máquina
que você criar as suas portas já vão vir
bloqueadas é padrão do provedor então
como eu estou na máquina eu posso vir
aqui ó e clicar e e descrever Tá eu vou
deixar um prompt para vocês também aqui
pro GPT para vocês poder escrever um
texto bonito ali e colocar aqui pedindo
para poder liberar as portas tá bom E
aqui você tem informações de tráfego de
entrada e de saída da sua VPS
aqui em fals eu habilitei um fal né
então ele já mostra quais são as regras
de Fal associadas a essa máquina aqui
você pode criar mais F atribuir mais Fir
essa máquina aqui em volume Eu não
anexei o volume ainda né eu criei lá o
volume mas eu não anexei então eu criei
o volume eu vou vir aqui em atach eu vou
escolher lá o meu Backup Ó e aqui
pergunta pessoal o tipo de montagem que
eu vou fazer o Mount do Linux no Linux
pessoal você conecta um um disco E você
tem que montar esse disco para ele tá
útil né então sempre deixa pessoal como
automático se você pôr como manual você
vai ter que rodar um monte de comando
para quem é iniciante não é tão simples
assim fazer um monte então aqui ó
Fantástico tem a opção de montagem
automática eu vou clicar aqui ó ele vai
anexar esse volume vai rodar um
comandinho lá para mim para adicionar
esse essa pastinha backup no meu sistema
de arquivo Tá bom então super simples
aqui por exemplo Ó você pode vir aqui
ainda no seu volume e dar um resize nele
tudo né ó aqui a configuração né Ó quem
quiser fazer a configuração manual e
todo o volume pessoal eu vou acessar de
novo aqui para vocês verem Por que eu
fechei mas vou abrir de novo aqui o meu
SSH só mostrar para vocês aonde fica lá
o volume
tá vamos esperar ele dar aqui ó ó lá já
tá logado já então ele fica aqui ó em
barra
mnt e não vai aparecer a barra quer ver
que tô com m
aí barra
mnt dar um LS aqui e vai mostrar lá ó lá
CD
HC volume tal tal tal tá aqui o meu
volume
ó á vai mostrar o volume aqui ó 10 GB ó
montado em mnt HC volume tá aqui o
nomezinho do volume que a hestner montou
para mim hestner Cloud né HC volume tal
tal tal então aqui nessa p lá no docker
por exemplo eu posso mapear os volumes
do docker para vir para essa pasta aqui
então eu não tô salvando nada na VPS tô
no volume se eu quiser depois trocar de
máquina eu posso só simplesmente vir
aqui né eu posso desatar detache volume
ele vai como se ele fosse desplugar esse
HD externo aqui da máquina e eu vou
poder adicionar isso aqui em outra
máquina né então Fantástico esse recurso
aqui de você poder ter esses volumes na
máquina tá bom aqui no power pessoal
mesma regra tá evitar esse Power cle
aqui que ele dá um hard reset na máquina
aqui é certeza Dee dar problema tá
evitar mesmo usar isso daqui você pode
desligar a máquina por aqui ou pode dar
um Power off Olha só pessoal tem uma
diferença aqui ó o shutdown ele vai
mandar o acpi É como se você fosse na
máquina apertasse o botão né ó ele vai
fazer um soft eh shutdown tá então ele
vai Power off o shutdown aqui né ó essa
opção aqui o shutdown ele vai dar um
soft shutdown a gente já a gente já viu
sobre isso no curso do digital hoa ele
vai informar pro seu Linux que olha eu
quero desligar o Linux vai vai vai
desativar os serviços e assim que
desativar os serviços o Linux vai deixar
com que ele seja desligado então a
chance de perder informação ali é muito
menor praticamente zero sen não zero tá
porque você tá realmente desligando o
serviço vai desligar o banco de dados
vai desligar os serviços E aí sim você
vai desligar a máquina o Power off ele
vai lá e dá um tira tomada né ele dá um
um ele ele não faz isso então ele não
vai fazer esse soft shutdown aqui ele
vai e aqui fala né provavelmente você
vai perder informação Esse aqui nem
pensar mas esse Power off aqui ó também
ficar bem atento eu tô vendo aqui ó que
aqui em cima é Power off também ó então
não usa isso aqui tá gente vai desligar
a máquina vem aqui no shutdown porque
que que que vai acontecer no no caso do
nosso docker swarm você vai apertar aqui
o shutdown tá ele vai mandar um sinal
pro docker o docker vai derrubar todos
os stacks que estão rodando E aí assim
que o stack derrubar o docker vai
reiniciar a máquina quando a máquina for
reiniciada o docker automaticamente
inicia para vocês de novo os stacks lá é
assim que ele funciona tá o serviço do
docker manipula os stacks o shutdown vai
pedir pro serviço do docker para que ele
Desligue tá então essa aqui é a maneira
mais segura de você fazer isso evitar
Então pessoal usar isso daqui tá o Power
off vem aqui no shutdown aqui no res
você vai poder
e caso dê né muit Difícil acontecer
pessoal Mas pode ser que aconteça uma
atualização mal feita que você faz no
sistema operacional e você precisa dar
boot aí pelo moldo de de aqui de de de
reparação né então ele vai carregar um
Linux na memória e você vai ter acesso
ao disco mas é muito difícil mas é bom
saber que tem essa opção aqui aqui em
ISO images você pode utilizar outras
imagens Linux para trabalhar na sua
máquina então você pode ter um controle
maior na hora que você poder fazer uma
instalação diferente aqui tá nunca usei
pessoal até porque eu sempre vou usar lá
o debia na hora que cria mas depois que
você criar uma VPS você pode vir aqui e
formatar ela vamos dizer assim e aqui
tem o rescale no
rescale se você marcar aqui a opção CPU
e Run only vai ser legal porque aqui
você vai conseguir fazer essa opção de
você poder fazer um upgrade na máquina
Então você vai poder vir aqui e fazer um
upgrade tá Recomendo muito que você
eh desligue a máquina lá pelo shutdown
Desliga você vem aqui e faça isso tá ó
então é importante ter essa noção de
você poder inclusive mudar com uma CPU
dedicada uma CPU compartilhada n aqui
pessoal se você marcar essa opção de CPU
e r only você vai poder fazer um
downgrade para discos do mesmo tamanho
você não pode fazer um downgrade para um
disco menor que você tem se você
desmarcar aqui ele vai fazer o upgrade
do disco também e ele não tem mais volta
para trás tá então quer só fazer um
upgrade temporário é importante que você
e utiliza aqui o o CPU e RAM only tá o
rebuild vai literalmente refazer a sua
máquina e aqui é legal pessoal porque lá
no finalzinho ó vai aparecer o seu
SnapShot então você pode formatar a
máquina com diversos tipos de Linux
diferente aqui ou você pode formatar sua
máquina com o próprio SnapShot e aqui no
delete pessoal você teria que primeiro
desligar a sua
máquina né então você desliga sua
máquina você vai poder vir aqui apagar e
aí a rner cessa a cobrança vai perder
tudo mas a hatler cessa a
cobrança pessoal aqui vou dar uma tensão
especial pro rescale eu vou até desligar
aqui a minha máquina que eu acho que não
deixa fazer com a máquina ligada deixa
eu ver
rodix você pode mudar Só CPU tal o seu
servidor precisa estar desligado então
vou vir aqui em Power e eu vou rodar o
shutdown D um
shutdown el vai desligar a minha máquina
e eu vou poder fazer o rescale fazer um
upgrade minha máquina e só para reforçar
pessoal CPU e RAM only você vai poder
fazer um upgrade da sua máquina Então
pode ver pessoal que aqui ó eu tô com o
CPX 2 eu posso ir pro 31 41 51 E como
eles têm o mesmo disco eu vou poder
voltar depois pro 21 de novo agora caso
você esteja por exemplo no CPX 111 tá
vendo que tem 40 GB então se eu tiver
nele eu posso vir para qualquer um mas
eu não vou poder voltar para ele eu vou
poder no voltar aqui pro CPX 21 mesmo tá
então é importante isso daí e o mesmo
acontece aqui tá pessoal com máquinas de
CP deic então você pode por um período
utilizar aqui essas máquinas aqui né ó
então é importante você entender essa
questão aí de você poder fazer esse
upgrade é muito importante isso aqui
pessoal Você tem uma aqui a Hat você
pode ver tanto na parte de CPU
compartilhada você consegue ter até 16 e
32 Gb aqui na na dedicada você chega a
oito dedicadas e 32 GB tá então
importante aí de vocês eh entender que
você tem um teto muito grande aí na rner
para poder trabalhar com isso daí aqui
como eu tô nos Estados Unidos você pode
ver que ele não dá tanta opção se você
hospedar na Alemanha você vai conseguir
vir aqui até o final tá então na
Alemanha quando você tá na Alemanha você
vai poder ter muito mais opções aqui
então que no meu caso ó eu vou vir aqui
pro 31 quro cpus
4 cpus e 8 GB eu vou apertar aqui o
rescale E aí ele vai fazer para mim esse
processo de upgrade né ele vai ele não
vai mudar nada de lugar ele simplesmente
vai vai me permitir
utilizar agora qu cpus e 8 GB de memória
Luiz mas o curso do swarm eu preciso
fazer isso não pessoal no swarm você
pode escalar isso aqui a gente chama né
escalar escalar na vertical Então você
vai aumentando máquina né e o swarm
permite que você escale bem na
horizontal então no swarm como eu tô
dando mais máquina eu poderia até mesmo
criar mais réplicas o NN é fantástico
com isso chatu é fantástico com isso
Type bostic é fantástico com isso de
você poder escalar melhor seu serviço né
então eu posso fazer esse esse essa
escalada
eh vertical e depois também utilizar
horizontal no caso do swarm eu posso
inclusive pôr mais máquinas mesmo na
minha rede e mandar o swarm utilizar
essas máquinas como se fosse uma máquina
só né então depois você pode
simplesmente ir lá e remover uma máquina
para reduzir custo Então Lu estou no
lançamento vem aqui faz isso ou
simplesmente Coloca mais uma máquina no
seu swarm aí mas aqui pessoal por
exemplo não a minha máquina já ligou tá
aqui tá rodando já minha máquina eu
posso vir aqui no meu rescale e como eu
marquei a opção CPU e Run only e os
discos tem o mesmo tamanho eu posso vir
aqui ó e voltar caso eu queira né não
vou conseguir voltar com a máquina tá
ligada Deixa eu desligar a máquina vocês
verem aqui ã Power
shutdown E aí eu vou poder então e
alocar esse serviço e voltar aqui tá eu
não vou fazer pessoal só para não
demorar muito aqui na aula mas eu tô
desligando aqui a máquina assim que a
máquina desligar ele vai permitir ó lá
desligou a máquina ele vai permitir aqui
que eu faça esse
downgrade da minha VPS e volte ali a
pagar ali S conto por mês né então Olha
só pessoal por um período de tempo eu
posso usar mais recursos depois eu posso
vir aqui e voltar pro recurso anterior
isso aqui pessoal Fantástico para quem
vai fazer lançamento para quem vai vai
trabalhar aí
com em alguns períodos do ano que você
vai ter realmente muita carga de
trabalho você pode vir aqui pegar uma
máquina maior durante aquele período
acabou aquele período você pode voltar
aqui e pegar uma máquina menor no caso
aqui é uma máquina de ó né e continuar
aqui com seu HD de 80 né ó e mantém lá
volume mantém float IP mantém IP
primário mantém Fire Wall Red tudo
igualzinho você realmente só muda isso
daqui ou melhor ainda né Você pode vir
para uma CPU dedicada então luí est no
Lan cara vem aqui pega uma CP indicada
depois você volta né então ficar bem
atento isso daí pessoal olha aqui ó é
importante aqui falar para vocês que o
rescale ele só é possível de servers da
mesma arquitetura você não pode mudar a
arquitetura então não tem como eu ir
para um arm e voltar pro Intel Tá bom
mas eu posso ficar navegando aqui entre
dedicado e e compartilhado caso seja
necessário aí tá isso aqui é um recurso
Fantástico é bom que vocês assistam essa
aula aqui com atenção para fixar isso
daqui porque pode ajudar muito vocês aí
principalmente quem faz lançamento quem
trabalha com em épocas do ano mais
específicas tem que ter uma carga maior
de trabalho aí vocês podem ficar
brincando aí com a a a capacidade do
Servidor e aqui uma vez que eu tô aqui
na minha máquina é como eu adicionei um
volume na máquina pode ver que ele
mostra 80 GB mais 10 GB aqui né legal
ter essa opção aqui e aqui Pessoal agora
é você realmente acessar a máquina já
mostrei que que você vai acessar a
máquina por aqui né você tem tem esse
console web eu sou partidário da
utilização de console web tá eu uso no
digital h eu uso em todos os provedores
Tá bom eu acho muito mais prático muito
mais prático tá mas você é livre para
poder acessar a sua máquina aí pelo IP
primário dela tá bom pessoal usar aqui o
IP primário evitar de utilizar o float
IP como SSH Então você vai acessar pelo
seu terminal aí com esse IP aqui tá
então basicamente é isso é obrigado aí
por vocês terem assistido aqui o curso
do rner Tá super simples super fácil é
um painel muito bom é um painel muito
legal eu sou fã mesmo aqui dessa empresa
eu acho que vai ajudar muito vocês aí
principalmente com capacidade
computacional para você poder hospedar
os seus bancos de dados suas aplicações
os aplicações de marketing de suporte
tudo que a promov vai oferece aqui tanto
com o instalador quanto aqui com a o o
curso do docker né os stacks ali para
quem quer gerenciar aqui mais a fundo Um
abraço pessoal