# 🏗️ INFRAESTRUTURA SUPABASE COMPLETA - SISTEMA DE EXTRAÇÃO VIRAL

**Data:** 2025-01-24  
**Versão:** v1.0 - Arquitetura Completa de Banco de Dados e Infraestrutura  
**Autor:** Augment Code Orchestrator V5.0  
**Projeto:** WebAgent (Supabase ID: nnpxxdyhamnsxqljumun)  
**Escopo:** Infraestrutura completa para sistema de extração e análise viral  

---

## 🎯 EXECUTIVE SUMMARY

Esta documentação estabelece a **infraestrutura completa** para o sistema de extração viral usando **Supabase** como backend principal. O projeto WebAgent será transformado em uma plataforma robusta de análise de conteúdo viral com banco de dados PostgreSQL, storage de mídia, edge functions e integração Docker.

### PRINCIPAIS COMPONENTES:

**1. BANCO DE DADOS POSTGRESQL:**
- **Esquema Completo** - 15 tabelas principais + views otimizadas
- **Row Level Security (RLS)** - Segurança granular por usuário
- **Índices Avançados** - Performance otimizada para consultas complexas
- **Triggers e Functions** - Automação de processos críticos

**2. SUPABASE STORAGE:**
- **Buckets Organizados** - Separação por tipo de mídia
- **CDN Global** - Links públicos otimizados
- **Políticas de Acesso** - Controle granular de permissões
- **Compressão Automática** - Otimização de espaço

**3. EDGE FUNCTIONS:**
- **APIs Customizadas** - Endpoints especializados
- **Processamento de Dados** - Análise em tempo real
- **Integração Externa** - Conectores para redes sociais
- **Webhooks** - Notificações automáticas

**4. INTEGRAÇÃO DOCKER:**
- **Desenvolvimento Local** - Ambiente completo dockerizado
- **CI/CD Pipeline** - Deploy automatizado
- **Monitoramento** - Logs e métricas integradas
- **Escalabilidade** - Configuração para produção

---

## 🗄️ ARQUITETURA DE BANCO DE DADOS

### ESQUEMA PRINCIPAL - TABELAS CORE:

```sql
-- =====================================================
-- SISTEMA DE USUÁRIOS E AUTENTICAÇÃO
-- =====================================================

-- Perfis de usuário (extensão do auth.users)
CREATE TABLE public.profiles (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    full_name TEXT,
    avatar_url TEXT,
    role user_role DEFAULT 'user',
    subscription_tier subscription_tier DEFAULT 'free',
    api_quota_daily INTEGER DEFAULT 1000,
    api_quota_used INTEGER DEFAULT 0,
    quota_reset_date DATE DEFAULT CURRENT_DATE,
    preferences JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tipos ENUM
CREATE TYPE user_role AS ENUM ('admin', 'premium', 'user');
CREATE TYPE subscription_tier AS ENUM ('free', 'pro', 'enterprise');
CREATE TYPE platform_type AS ENUM ('twitter', 'youtube', 'instagram', 'tiktok', 'linkedin');
CREATE TYPE content_type AS ENUM ('tweet', 'video', 'post', 'story', 'reel', 'short');
CREATE TYPE extraction_status AS ENUM ('pending', 'processing', 'completed', 'failed', 'cancelled');

-- =====================================================
-- SISTEMA DE EXTRAÇÃO E PROJETOS
-- =====================================================

-- Projetos de extração
CREATE TABLE public.projects (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    platforms platform_type[] DEFAULT '{}',
    keywords TEXT[] DEFAULT '{}',
    hashtags TEXT[] DEFAULT '{}',
    settings JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Extrações realizadas
CREATE TABLE public.extractions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID REFERENCES public.projects(id) NOT NULL,
    user_id UUID REFERENCES public.profiles(id) NOT NULL,
    platform platform_type NOT NULL,
    query_params JSONB NOT NULL,
    status extraction_status DEFAULT 'pending',
    total_found INTEGER DEFAULT 0,
    total_processed INTEGER DEFAULT 0,
    error_message TEXT,
    execution_time_ms INTEGER,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- CONTEÚDO EXTRAÍDO
-- =====================================================

-- Conteúdo viral extraído
CREATE TABLE public.viral_content (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    extraction_id UUID REFERENCES public.extractions(id) NOT NULL,
    platform platform_type NOT NULL,
    content_type content_type NOT NULL,
    external_id VARCHAR(255) NOT NULL,
    url TEXT NOT NULL,
    title TEXT,
    description TEXT,
    author_username VARCHAR(255),
    author_display_name TEXT,
    author_followers_count BIGINT,
    author_verified BOOLEAN DEFAULT false,
    content_text TEXT,
    hashtags TEXT[] DEFAULT '{}',
    mentions TEXT[] DEFAULT '{}',
    media_urls TEXT[] DEFAULT '{}',
    media_types TEXT[] DEFAULT '{}',
    engagement_metrics JSONB DEFAULT '{}',
    viral_score DECIMAL(5,2) DEFAULT 0,
    sentiment_score DECIMAL(3,2),
    sentiment_label VARCHAR(20),
    language_code VARCHAR(10),
    location_data JSONB,
    published_at TIMESTAMP WITH TIME ZONE,
    extracted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Índices compostos para performance
    CONSTRAINT unique_platform_external_id UNIQUE (platform, external_id)
);

-- Métricas de engajamento detalhadas
CREATE TABLE public.engagement_metrics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    content_id UUID REFERENCES public.viral_content(id) NOT NULL,
    likes_count BIGINT DEFAULT 0,
    shares_count BIGINT DEFAULT 0,
    comments_count BIGINT DEFAULT 0,
    views_count BIGINT DEFAULT 0,
    saves_count BIGINT DEFAULT 0,
    engagement_rate DECIMAL(5,4) DEFAULT 0,
    viral_velocity DECIMAL(8,2) DEFAULT 0,
    reach_estimate BIGINT DEFAULT 0,
    impressions_estimate BIGINT DEFAULT 0,
    measured_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- ANÁLISE E INTELIGÊNCIA
-- =====================================================

-- Análises de tendências
CREATE TABLE public.trend_analysis (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID REFERENCES public.projects(id) NOT NULL,
    analysis_type VARCHAR(50) NOT NULL,
    time_period INTERVAL NOT NULL,
    platforms platform_type[] NOT NULL,
    keywords TEXT[] DEFAULT '{}',
    hashtags TEXT[] DEFAULT '{}',
    total_content_analyzed INTEGER DEFAULT 0,
    trending_topics JSONB DEFAULT '{}',
    sentiment_distribution JSONB DEFAULT '{}',
    engagement_patterns JSONB DEFAULT '{}',
    viral_predictions JSONB DEFAULT '{}',
    insights JSONB DEFAULT '{}',
    confidence_score DECIMAL(3,2) DEFAULT 0,
    analysis_date DATE DEFAULT CURRENT_DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Relatórios executivos
CREATE TABLE public.executive_reports (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID REFERENCES public.projects(id) NOT NULL,
    user_id UUID REFERENCES public.profiles(id) NOT NULL,
    report_type VARCHAR(50) NOT NULL,
    period_start DATE NOT NULL,
    period_end DATE NOT NULL,
    platforms_analyzed platform_type[] NOT NULL,
    total_content_pieces INTEGER DEFAULT 0,
    summary_metrics JSONB DEFAULT '{}',
    platform_performance JSONB DEFAULT '{}',
    top_content JSONB DEFAULT '{}',
    recommendations JSONB DEFAULT '{}',
    charts_data JSONB DEFAULT '{}',
    generated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- STORAGE E MÍDIA
-- =====================================================

-- Arquivos de mídia armazenados
CREATE TABLE public.media_files (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    content_id UUID REFERENCES public.viral_content(id),
    bucket_name VARCHAR(100) NOT NULL,
    file_path TEXT NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_type VARCHAR(50) NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    public_url TEXT,
    thumbnail_url TEXT,
    metadata JSONB DEFAULT '{}',
    is_processed BOOLEAN DEFAULT false,
    uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- SISTEMA DE MONITORAMENTO
-- =====================================================

-- Logs de atividade do sistema
CREATE TABLE public.activity_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id),
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50) NOT NULL,
    resource_id UUID,
    details JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Métricas de performance
CREATE TABLE public.performance_metrics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    metric_type VARCHAR(50) NOT NULL,
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(12,4) NOT NULL,
    tags JSONB DEFAULT '{}',
    measured_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- CONFIGURAÇÕES E CACHE
-- =====================================================

-- Cache de consultas frequentes
CREATE TABLE public.query_cache (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    cache_key VARCHAR(255) UNIQUE NOT NULL,
    cache_data JSONB NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    hit_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Configurações do sistema
CREATE TABLE public.system_settings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value JSONB NOT NULL,
    description TEXT,
    is_public BOOLEAN DEFAULT false,
    updated_by UUID REFERENCES public.profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

---

## 📊 ÍNDICES E OTIMIZAÇÕES

### ÍNDICES ESTRATÉGICOS PARA PERFORMANCE:

```sql
-- =====================================================
-- ÍNDICES PARA CONSULTAS FREQUENTES
-- =====================================================

-- Viral content - consultas por plataforma e score
CREATE INDEX idx_viral_content_platform_score ON public.viral_content 
    (platform, viral_score DESC, published_at DESC);

-- Viral content - busca por hashtags
CREATE INDEX idx_viral_content_hashtags ON public.viral_content 
    USING GIN (hashtags);

-- Viral content - busca textual
CREATE INDEX idx_viral_content_text_search ON public.viral_content 
    USING GIN (to_tsvector('portuguese', coalesce(title, '') || ' ' || coalesce(description, '') || ' ' || coalesce(content_text, '')));

-- Extractions - status e data
CREATE INDEX idx_extractions_status_date ON public.extractions 
    (status, created_at DESC);

-- Engagement metrics - performance temporal
CREATE INDEX idx_engagement_metrics_content_date ON public.engagement_metrics 
    (content_id, measured_at DESC);

-- Projects - usuário ativo
CREATE INDEX idx_projects_user_active ON public.projects 
    (user_id, is_active, created_at DESC);

-- Activity logs - auditoria
CREATE INDEX idx_activity_logs_user_date ON public.activity_logs 
    (user_id, created_at DESC);

-- Performance metrics - monitoramento
CREATE INDEX idx_performance_metrics_type_date ON public.performance_metrics 
    (metric_type, measured_at DESC);

-- Query cache - limpeza automática
CREATE INDEX idx_query_cache_expires ON public.query_cache 
    (expires_at);
```

---

## 🔒 ROW LEVEL SECURITY (RLS)

### POLÍTICAS DE SEGURANÇA GRANULAR:

```sql
-- =====================================================
-- ATIVAR RLS EM TODAS AS TABELAS
-- =====================================================

ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.extractions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.viral_content ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.engagement_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.trend_analysis ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.executive_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.media_files ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.activity_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.performance_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.query_cache ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.system_settings ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- POLÍTICAS DE ACESSO
-- =====================================================

-- Profiles - usuários podem ver/editar apenas seu próprio perfil
CREATE POLICY "Users can view own profile" ON public.profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.profiles
    FOR UPDATE USING (auth.uid() = id);

-- Projects - usuários podem acessar apenas seus projetos
CREATE POLICY "Users can view own projects" ON public.projects
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can manage own projects" ON public.projects
    FOR ALL USING (auth.uid() = user_id);

-- Extractions - acesso baseado no projeto
CREATE POLICY "Users can view own extractions" ON public.extractions
    FOR SELECT USING (
        auth.uid() = user_id OR 
        EXISTS (SELECT 1 FROM public.projects WHERE id = project_id AND user_id = auth.uid())
    );

-- Viral content - acesso baseado na extração
CREATE POLICY "Users can view content from own extractions" ON public.viral_content
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.extractions e 
            JOIN public.projects p ON e.project_id = p.id 
            WHERE e.id = extraction_id AND p.user_id = auth.uid()
        )
    );

-- Admins têm acesso total
CREATE POLICY "Admins have full access" ON public.profiles
    FOR ALL USING (
        EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 'admin')
    );

-- Sistema settings - apenas admins podem modificar
CREATE POLICY "Only admins can modify system settings" ON public.system_settings
    FOR ALL USING (
        EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 'admin')
    );

-- Leitura pública para settings públicas
CREATE POLICY "Public can read public settings" ON public.system_settings
    FOR SELECT USING (is_public = true);
```

---

## 🔧 CORREÇÕES DE GAPS IDENTIFICADOS

### CAMPOS AUSENTES ADICIONADOS À TABELA VIRAL_CONTENT:

```sql
-- =====================================================
-- CORREÇÃO DE GAPS: CAMPOS YOUTUBE ESPECÍFICOS
-- =====================================================

ALTER TABLE public.viral_content ADD COLUMN IF NOT EXISTS video_duration INTEGER; -- segundos
ALTER TABLE public.viral_content ADD COLUMN IF NOT EXISTS video_category VARCHAR(100);
ALTER TABLE public.viral_content ADD COLUMN IF NOT EXISTS video_tags TEXT[] DEFAULT '{}';
ALTER TABLE public.viral_content ADD COLUMN IF NOT EXISTS video_thumbnail_url TEXT;
ALTER TABLE public.viral_content ADD COLUMN IF NOT EXISTS video_quality VARCHAR(20); -- 720p, 1080p, etc
ALTER TABLE public.viral_content ADD COLUMN IF NOT EXISTS video_fps INTEGER;
ALTER TABLE public.viral_content ADD COLUMN IF NOT EXISTS video_codec VARCHAR(50);

-- =====================================================
-- CORREÇÃO DE GAPS: CAMPOS INSTAGRAM ESPECÍFICOS
-- =====================================================

ALTER TABLE public.viral_content ADD COLUMN IF NOT EXISTS post_type VARCHAR(50); -- photo, video, carousel, reel, story
ALTER TABLE public.viral_content ADD COLUMN IF NOT EXISTS aspect_ratio VARCHAR(20); -- 1:1, 9:16, 16:9
ALTER TABLE public.viral_content ADD COLUMN IF NOT EXISTS filter_used VARCHAR(100);
ALTER TABLE public.viral_content ADD COLUMN IF NOT EXISTS is_sponsored BOOLEAN DEFAULT false;
ALTER TABLE public.viral_content ADD COLUMN IF NOT EXISTS product_tags TEXT[] DEFAULT '{}';
ALTER TABLE public.viral_content ADD COLUMN IF NOT EXISTS story_highlights TEXT[] DEFAULT '{}';

-- =====================================================
-- CORREÇÃO DE GAPS: CAMPOS TWITTER ESPECÍFICOS
-- =====================================================

ALTER TABLE public.viral_content ADD COLUMN IF NOT EXISTS tweet_source VARCHAR(100); -- Twitter Web App, iPhone, etc
ALTER TABLE public.viral_content ADD COLUMN IF NOT EXISTS is_reply BOOLEAN DEFAULT false;
ALTER TABLE public.viral_content ADD COLUMN IF NOT EXISTS reply_to_tweet_id VARCHAR(255);
ALTER TABLE public.viral_content ADD COLUMN IF NOT EXISTS is_retweet BOOLEAN DEFAULT false;
ALTER TABLE public.viral_content ADD COLUMN IF NOT EXISTS original_tweet_id VARCHAR(255);
ALTER TABLE public.viral_content ADD COLUMN IF NOT EXISTS quote_tweet_id VARCHAR(255);
ALTER TABLE public.viral_content ADD COLUMN IF NOT EXISTS conversation_id VARCHAR(255);

-- =====================================================
-- CORREÇÃO DE GAPS: CAMPOS TRANSCRIÇÃO/CONTEÚDO
-- =====================================================

ALTER TABLE public.viral_content ADD COLUMN IF NOT EXISTS transcript_text TEXT;
ALTER TABLE public.viral_content ADD COLUMN IF NOT EXISTS transcript_language VARCHAR(10);
ALTER TABLE public.viral_content ADD COLUMN IF NOT EXISTS auto_generated_transcript BOOLEAN DEFAULT false;
ALTER TABLE public.viral_content ADD COLUMN IF NOT EXISTS keywords_extracted TEXT[] DEFAULT '{}';
ALTER TABLE public.viral_content ADD COLUMN IF NOT EXISTS content_summary TEXT;

-- =====================================================
-- CORREÇÃO DE GAPS: CAMPOS MÉTRICAS AVANÇADAS
-- =====================================================

ALTER TABLE public.viral_content ADD COLUMN IF NOT EXISTS engagement_rate_24h DECIMAL(5,4);
ALTER TABLE public.viral_content ADD COLUMN IF NOT EXISTS viral_velocity_score DECIMAL(8,2);
ALTER TABLE public.viral_content ADD COLUMN IF NOT EXISTS influence_score DECIMAL(8,2);
ALTER TABLE public.viral_content ADD COLUMN IF NOT EXISTS reach_estimate BIGINT;
ALTER TABLE public.viral_content ADD COLUMN IF NOT EXISTS impressions_estimate BIGINT;

-- =====================================================
-- TABELAS ESPECIALIZADAS AUSENTES
-- =====================================================

-- Tabela para transcrições detalhadas (YouTube)
CREATE TABLE IF NOT EXISTS public.transcripts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    content_id UUID REFERENCES public.viral_content(id) NOT NULL,
    language_code VARCHAR(10) NOT NULL,
    is_auto_generated BOOLEAN DEFAULT false,
    transcript_segments JSONB NOT NULL, -- [{start, duration, text}]
    full_text TEXT NOT NULL,
    word_count INTEGER DEFAULT 0,
    confidence_score DECIMAL(3,2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela para comentários detalhados
CREATE TABLE IF NOT EXISTS public.content_comments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    content_id UUID REFERENCES public.viral_content(id) NOT NULL,
    external_comment_id VARCHAR(255) NOT NULL,
    parent_comment_id UUID REFERENCES public.content_comments(id),
    author_username VARCHAR(255) NOT NULL,
    author_display_name TEXT,
    comment_text TEXT NOT NULL,
    likes_count INTEGER DEFAULT 0,
    replies_count INTEGER DEFAULT 0,
    is_pinned BOOLEAN DEFAULT false,
    is_heart_by_creator BOOLEAN DEFAULT false,
    sentiment_score DECIMAL(3,2),
    sentiment_label VARCHAR(20),
    published_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    CONSTRAINT unique_external_comment UNIQUE (content_id, external_comment_id)
);

-- Tabela para performance de hashtags
CREATE TABLE IF NOT EXISTS public.hashtag_performance (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    hashtag VARCHAR(255) NOT NULL,
    platform platform_type NOT NULL,
    usage_count INTEGER DEFAULT 0,
    total_engagement BIGINT DEFAULT 0,
    avg_viral_score DECIMAL(5,2) DEFAULT 0,
    trending_score DECIMAL(8,2) DEFAULT 0,
    first_seen TIMESTAMP WITH TIME ZONE,
    last_seen TIMESTAMP WITH TIME ZONE,
    peak_usage_date DATE,
    related_hashtags TEXT[] DEFAULT '{}',
    top_content_ids UUID[] DEFAULT '{}',
    analysis_date DATE DEFAULT CURRENT_DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    CONSTRAINT unique_hashtag_platform_date UNIQUE (hashtag, platform, analysis_date)
);

-- Tabela para analytics de autores/criadores
CREATE TABLE IF NOT EXISTS public.author_analytics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    username VARCHAR(255) NOT NULL,
    platform platform_type NOT NULL,
    display_name TEXT,
    bio TEXT,
    followers_count BIGINT DEFAULT 0,
    following_count BIGINT DEFAULT 0,
    posts_count INTEGER DEFAULT 0,
    is_verified BOOLEAN DEFAULT false,
    account_type VARCHAR(50), -- personal, business, creator
    avg_engagement_rate DECIMAL(5,4) DEFAULT 0,
    avg_viral_score DECIMAL(5,2) DEFAULT 0,
    total_viral_content INTEGER DEFAULT 0,
    influence_score DECIMAL(8,2) DEFAULT 0,
    content_categories TEXT[] DEFAULT '{}',
    posting_frequency DECIMAL(4,2) DEFAULT 0, -- posts per day
    best_posting_times INTEGER[] DEFAULT '{}', -- hours of day
    audience_demographics JSONB DEFAULT '{}',
    collaboration_brands TEXT[] DEFAULT '{}',
    last_analyzed TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    CONSTRAINT unique_author_platform UNIQUE (username, platform)
);

-- Tabela para tendências virais temporais
CREATE TABLE IF NOT EXISTS public.viral_trends (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    trend_type VARCHAR(50) NOT NULL, -- hashtag, keyword, topic, audio, format
    trend_value TEXT NOT NULL,
    platform platform_type NOT NULL,
    trend_date DATE NOT NULL,
    usage_count INTEGER DEFAULT 0,
    engagement_total BIGINT DEFAULT 0,
    unique_creators INTEGER DEFAULT 0,
    viral_content_count INTEGER DEFAULT 0,
    trend_velocity DECIMAL(8,2) DEFAULT 0, -- crescimento por hora
    peak_hour INTEGER, -- hora do pico
    geographic_distribution JSONB DEFAULT '{}',
    age_demographics JSONB DEFAULT '{}',
    related_trends TEXT[] DEFAULT '{}',
    trend_category VARCHAR(100),
    is_emerging BOOLEAN DEFAULT false,
    confidence_score DECIMAL(3,2) DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    CONSTRAINT unique_trend_platform_date UNIQUE (trend_type, trend_value, platform, trend_date)
);

-- =====================================================
-- ÍNDICES ADICIONAIS PARA PERFORMANCE
-- =====================================================

-- Índices para novos campos em viral_content
CREATE INDEX IF NOT EXISTS idx_viral_content_platform_type ON public.viral_content (platform, post_type, viral_score DESC);
CREATE INDEX IF NOT EXISTS idx_viral_content_engagement_rate ON public.viral_content (engagement_rate_24h DESC, published_at DESC);
CREATE INDEX IF NOT EXISTS idx_viral_content_keywords ON public.viral_content USING GIN (keywords_extracted);
CREATE INDEX IF NOT EXISTS idx_viral_content_video_duration ON public.viral_content (video_duration DESC) WHERE video_duration IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_viral_content_transcript ON public.viral_content (transcript_language, auto_generated_transcript);

-- Índices para transcripts
CREATE INDEX IF NOT EXISTS idx_transcripts_content_language ON public.transcripts (content_id, language_code);
CREATE INDEX IF NOT EXISTS idx_transcripts_auto_generated ON public.transcripts (is_auto_generated);
CREATE INDEX IF NOT EXISTS idx_transcripts_word_count ON public.transcripts (word_count DESC);

-- Índices para comments
CREATE INDEX IF NOT EXISTS idx_comments_content_id ON public.content_comments (content_id, published_at DESC);
CREATE INDEX IF NOT EXISTS idx_comments_author ON public.content_comments (author_username);
CREATE INDEX IF NOT EXISTS idx_comments_sentiment ON public.content_comments (sentiment_score DESC);
CREATE INDEX IF NOT EXISTS idx_comments_engagement ON public.content_comments (likes_count DESC, replies_count DESC);

-- Índices para hashtag performance
CREATE INDEX IF NOT EXISTS idx_hashtag_performance_trending ON public.hashtag_performance (platform, trending_score DESC, analysis_date DESC);
CREATE INDEX IF NOT EXISTS idx_hashtag_performance_usage ON public.hashtag_performance (hashtag, platform, usage_count DESC);
CREATE INDEX IF NOT EXISTS idx_hashtag_performance_viral ON public.hashtag_performance (avg_viral_score DESC);

-- Índices para author analytics
CREATE INDEX IF NOT EXISTS idx_author_analytics_influence ON public.author_analytics (platform, influence_score DESC);
CREATE INDEX IF NOT EXISTS idx_author_analytics_engagement ON public.author_analytics (platform, avg_engagement_rate DESC);
CREATE INDEX IF NOT EXISTS idx_author_analytics_verified ON public.author_analytics (platform, is_verified, followers_count DESC);

-- Índices para viral trends
CREATE INDEX IF NOT EXISTS idx_viral_trends_platform_date ON public.viral_trends (platform, trend_date DESC, trend_velocity DESC);
CREATE INDEX IF NOT EXISTS idx_viral_trends_emerging ON public.viral_trends (is_emerging, confidence_score DESC);
CREATE INDEX IF NOT EXISTS idx_viral_trends_category ON public.viral_trends (trend_category, platform);
```

---

## 🚀 TRIGGERS E FUNCTIONS AUTOMÁTICAS

### AUTOMAÇÃO DE PROCESSOS CRÍTICOS:

```sql
-- =====================================================
-- FUNCTION: ATUALIZAR TIMESTAMP AUTOMATICAMENTE
-- =====================================================

CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Aplicar trigger em todas as tabelas com updated_at
CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON public.profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON public.projects
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_query_cache_updated_at BEFORE UPDATE ON public.query_cache
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- FUNCTION: CALCULAR VIRAL SCORE AUTOMATICAMENTE
-- =====================================================

CREATE OR REPLACE FUNCTION calculate_viral_score()
RETURNS TRIGGER AS $$
DECLARE
    score DECIMAL(5,2) := 0;
    engagement_data JSONB;
    follower_factor DECIMAL(3,2) := 1;
    time_factor DECIMAL(3,2) := 1;
BEGIN
    -- Extrair métricas de engajamento
    engagement_data := NEW.engagement_metrics;

    -- Calcular fator de seguidores (normalizado)
    IF NEW.author_followers_count > 0 THEN
        follower_factor := LEAST(LOG(NEW.author_followers_count + 1) / 10, 2.0);
    END IF;

    -- Calcular fator temporal (conteúdo mais recente tem peso maior)
    IF NEW.published_at IS NOT NULL THEN
        time_factor := GREATEST(1 - EXTRACT(EPOCH FROM (NOW() - NEW.published_at)) / 86400 / 30, 0.1);
    END IF;

    -- Fórmula do viral score
    score := (
        COALESCE((engagement_data->>'likes_count')::BIGINT, 0) * 1.0 +
        COALESCE((engagement_data->>'shares_count')::BIGINT, 0) * 3.0 +
        COALESCE((engagement_data->>'comments_count')::BIGINT, 0) * 2.0 +
        COALESCE((engagement_data->>'views_count')::BIGINT, 0) * 0.1
    ) * follower_factor * time_factor / 1000;

    NEW.viral_score := LEAST(score, 99.99);
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER calculate_viral_score_trigger
    BEFORE INSERT OR UPDATE ON public.viral_content
    FOR EACH ROW EXECUTE FUNCTION calculate_viral_score();

-- =====================================================
-- FUNCTION: RESET QUOTA DIÁRIA
-- =====================================================

CREATE OR REPLACE FUNCTION reset_daily_quota()
RETURNS TRIGGER AS $$
BEGIN
    -- Reset quota se a data mudou
    IF NEW.quota_reset_date < CURRENT_DATE THEN
        NEW.api_quota_used := 0;
        NEW.quota_reset_date := CURRENT_DATE;
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER reset_daily_quota_trigger
    BEFORE UPDATE ON public.profiles
    FOR EACH ROW EXECUTE FUNCTION reset_daily_quota();

-- =====================================================
-- FUNCTION: LOG DE ATIVIDADES AUTOMÁTICO
-- =====================================================

CREATE OR REPLACE FUNCTION log_activity()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.activity_logs (
        user_id,
        action,
        resource_type,
        resource_id,
        details
    ) VALUES (
        auth.uid(),
        TG_OP,
        TG_TABLE_NAME,
        COALESCE(NEW.id, OLD.id),
        jsonb_build_object(
            'old_data', to_jsonb(OLD),
            'new_data', to_jsonb(NEW),
            'timestamp', NOW()
        )
    );

    RETURN COALESCE(NEW, OLD);
END;
$$ language 'plpgsql';

-- Aplicar em tabelas críticas
CREATE TRIGGER log_projects_activity AFTER INSERT OR UPDATE OR DELETE ON public.projects
    FOR EACH ROW EXECUTE FUNCTION log_activity();

CREATE TRIGGER log_extractions_activity AFTER INSERT OR UPDATE OR DELETE ON public.extractions
    FOR EACH ROW EXECUTE FUNCTION log_activity();

-- =====================================================
-- FUNCTION: LIMPEZA AUTOMÁTICA DE CACHE
-- =====================================================

CREATE OR REPLACE FUNCTION cleanup_expired_cache()
RETURNS void AS $$
BEGIN
    DELETE FROM public.query_cache
    WHERE expires_at < NOW();

    -- Log da limpeza
    INSERT INTO public.performance_metrics (
        metric_type,
        metric_name,
        metric_value,
        tags
    ) VALUES (
        'cache',
        'cleanup_expired_entries',
        (SELECT COUNT(*) FROM public.query_cache WHERE expires_at < NOW()),
        jsonb_build_object('timestamp', NOW())
    );
END;
$$ language 'plpgsql';

-- Agendar limpeza automática (via pg_cron se disponível)
-- SELECT cron.schedule('cleanup-cache', '0 */6 * * *', 'SELECT cleanup_expired_cache();');
```

---

## 📁 SUPABASE STORAGE CONFIGURATION

### ESTRUTURA DE BUCKETS E POLÍTICAS:

```sql
-- =====================================================
-- BUCKETS DE STORAGE
-- =====================================================

-- Bucket para imagens de perfil
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'avatars',
    'avatars',
    true,
    5242880, -- 5MB
    ARRAY['image/jpeg', 'image/png', 'image/webp']
);

-- Bucket para mídia viral (imagens)
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'viral-images',
    'viral-images',
    true,
    52428800, -- 50MB
    ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif']
);

-- Bucket para mídia viral (vídeos)
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'viral-videos',
    'viral-videos',
    true,
    524288000, -- 500MB
    ARRAY['video/mp4', 'video/webm', 'video/quicktime']
);

-- Bucket para relatórios e exports
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'reports',
    'reports',
    false,
    104857600, -- 100MB
    ARRAY['application/pdf', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'text/csv']
);

-- Bucket para backups
INSERT INTO storage.buckets (id, name, public, file_size_limit)
VALUES (
    'backups',
    'backups',
    false,
    1073741824 -- 1GB
);

-- =====================================================
-- POLÍTICAS DE STORAGE
-- =====================================================

-- Avatars - usuários podem fazer upload de seus próprios avatars
CREATE POLICY "Users can upload own avatar" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'avatars' AND
        auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Users can view all avatars" ON storage.objects
    FOR SELECT USING (bucket_id = 'avatars');

CREATE POLICY "Users can update own avatar" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'avatars' AND
        auth.uid()::text = (storage.foldername(name))[1]
    );

-- Viral media - acesso baseado no projeto
CREATE POLICY "Users can upload viral media" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id IN ('viral-images', 'viral-videos') AND
        EXISTS (
            SELECT 1 FROM public.projects p
            WHERE p.user_id = auth.uid() AND
            p.id::text = (storage.foldername(name))[1]
        )
    );

CREATE POLICY "Users can view own viral media" ON storage.objects
    FOR SELECT USING (
        bucket_id IN ('viral-images', 'viral-videos') AND
        EXISTS (
            SELECT 1 FROM public.projects p
            WHERE p.user_id = auth.uid() AND
            p.id::text = (storage.foldername(name))[1]
        )
    );

-- Reports - apenas o dono pode acessar
CREATE POLICY "Users can manage own reports" ON storage.objects
    FOR ALL USING (
        bucket_id = 'reports' AND
        auth.uid()::text = (storage.foldername(name))[1]
    );

-- Backups - apenas admins
CREATE POLICY "Only admins can access backups" ON storage.objects
    FOR ALL USING (
        bucket_id = 'backups' AND
        EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 'admin')
    );
```

---

## ⚡ EDGE FUNCTIONS SPECIFICATION

### FUNCTIONS PARA PROCESSAMENTO E APIs:

**1. FUNCTION: viral-content-processor**
```typescript
// supabase/functions/viral-content-processor/index.ts
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

interface ContentProcessingRequest {
  extraction_id: string
  content_data: any[]
  processing_options: {
    sentiment_analysis: boolean
    viral_score_calculation: boolean
    media_download: boolean
    duplicate_detection: boolean
  }
}

serve(async (req) => {
  try {
    const { extraction_id, content_data, processing_options }: ContentProcessingRequest = await req.json()

    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Processar cada item de conteúdo
    const processed_content = []

    for (const item of content_data) {
      let processed_item = { ...item }

      // Análise de sentimento
      if (processing_options.sentiment_analysis) {
        const sentiment = await analyzeSentiment(item.content_text)
        processed_item.sentiment_score = sentiment.score
        processed_item.sentiment_label = sentiment.label
      }

      // Download de mídia
      if (processing_options.media_download && item.media_urls?.length > 0) {
        const media_files = await downloadMedia(item.media_urls, extraction_id)
        processed_item.local_media_files = media_files
      }

      // Detecção de duplicatas
      if (processing_options.duplicate_detection) {
        const is_duplicate = await checkDuplicate(item.external_id, item.platform)
        if (is_duplicate) continue // Pular duplicatas
      }

      processed_content.push(processed_item)
    }

    // Inserir conteúdo processado no banco
    const { data, error } = await supabase
      .from('viral_content')
      .insert(processed_content)

    if (error) throw error

    // Atualizar status da extração
    await supabase
      .from('extractions')
      .update({
        status: 'completed',
        total_processed: processed_content.length,
        completed_at: new Date().toISOString()
      })
      .eq('id', extraction_id)

    return new Response(JSON.stringify({
      success: true,
      processed_count: processed_content.length,
      extraction_id
    }), {
      headers: { 'Content-Type': 'application/json' }
    })

  } catch (error) {
    return new Response(JSON.stringify({
      success: false,
      error: error.message
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    })
  }
})

async function analyzeSentiment(text: string) {
  // Implementar análise de sentimento
  // Pode usar APIs como Google Cloud Natural Language, AWS Comprehend, etc.
  return { score: 0.5, label: 'neutral' }
}

async function downloadMedia(urls: string[], extraction_id: string) {
  // Implementar download e upload para Supabase Storage
  return []
}

async function checkDuplicate(external_id: string, platform: string) {
  // Verificar se já existe no banco
  return false
}
```

**2. FUNCTION: trend-analyzer**
```typescript
// supabase/functions/trend-analyzer/index.ts
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

interface TrendAnalysisRequest {
  project_id: string
  time_period: string // '24h', '7d', '30d'
  platforms: string[]
  analysis_type: 'hashtags' | 'keywords' | 'sentiment' | 'engagement'
}

serve(async (req) => {
  try {
    const { project_id, time_period, platforms, analysis_type }: TrendAnalysisRequest = await req.json()

    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Calcular período de análise
    const period_hours = time_period === '24h' ? 24 : time_period === '7d' ? 168 : 720
    const start_date = new Date(Date.now() - period_hours * 60 * 60 * 1000)

    // Buscar conteúdo do período
    const { data: content, error } = await supabase
      .from('viral_content')
      .select(`
        *,
        engagement_metrics(*)
      `)
      .in('platform', platforms)
      .gte('published_at', start_date.toISOString())
      .eq('extractions.project_id', project_id)

    if (error) throw error

    let analysis_result = {}

    switch (analysis_type) {
      case 'hashtags':
        analysis_result = analyzeHashtagTrends(content)
        break
      case 'keywords':
        analysis_result = analyzeKeywordTrends(content)
        break
      case 'sentiment':
        analysis_result = analyzeSentimentTrends(content)
        break
      case 'engagement':
        analysis_result = analyzeEngagementTrends(content)
        break
    }

    // Salvar análise no banco
    const { data: saved_analysis } = await supabase
      .from('trend_analysis')
      .insert({
        project_id,
        analysis_type,
        time_period: `${period_hours} hours`,
        platforms,
        total_content_analyzed: content.length,
        trending_topics: analysis_result.trending_topics || {},
        sentiment_distribution: analysis_result.sentiment_distribution || {},
        engagement_patterns: analysis_result.engagement_patterns || {},
        viral_predictions: analysis_result.viral_predictions || {},
        insights: analysis_result.insights || {},
        confidence_score: analysis_result.confidence_score || 0.5
      })
      .select()
      .single()

    return new Response(JSON.stringify({
      success: true,
      analysis: saved_analysis,
      summary: analysis_result
    }), {
      headers: { 'Content-Type': 'application/json' }
    })

  } catch (error) {
    return new Response(JSON.stringify({
      success: false,
      error: error.message
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    })
  }
})

function analyzeHashtagTrends(content: any[]) {
  const hashtag_counts = new Map()
  const hashtag_engagement = new Map()

  content.forEach(item => {
    item.hashtags?.forEach(hashtag => {
      hashtag_counts.set(hashtag, (hashtag_counts.get(hashtag) || 0) + 1)

      const engagement = item.engagement_metrics?.[0]
      if (engagement) {
        const total_engagement = (engagement.likes_count || 0) +
                               (engagement.shares_count || 0) +
                               (engagement.comments_count || 0)
        hashtag_engagement.set(hashtag,
          (hashtag_engagement.get(hashtag) || 0) + total_engagement)
      }
    })
  })

  const trending_hashtags = Array.from(hashtag_counts.entries())
    .sort((a, b) => b[1] - a[1])
    .slice(0, 20)
    .map(([hashtag, count]) => ({
      hashtag,
      count,
      total_engagement: hashtag_engagement.get(hashtag) || 0,
      avg_engagement: (hashtag_engagement.get(hashtag) || 0) / count
    }))

  return {
    trending_topics: { hashtags: trending_hashtags },
    insights: {
      total_unique_hashtags: hashtag_counts.size,
      most_viral_hashtag: trending_hashtags[0]?.hashtag,
      engagement_leader: trending_hashtags.sort((a, b) => b.avg_engagement - a.avg_engagement)[0]?.hashtag
    },
    confidence_score: Math.min(content.length / 100, 1.0)
  }
}

function analyzeKeywordTrends(content: any[]) {
  // Implementar análise de palavras-chave
  return { trending_topics: {}, insights: {}, confidence_score: 0.5 }
}

function analyzeSentimentTrends(content: any[]) {
  // Implementar análise de tendências de sentimento
  return { sentiment_distribution: {}, insights: {}, confidence_score: 0.5 }
}

function analyzeEngagementTrends(content: any[]) {
  // Implementar análise de padrões de engajamento
  return { engagement_patterns: {}, insights: {}, confidence_score: 0.5 }
}
```

**3. FUNCTION: report-generator**
```typescript
// supabase/functions/report-generator/index.ts
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

interface ReportRequest {
  project_id: string
  report_type: 'executive' | 'detailed' | 'comparison'
  period_start: string
  period_end: string
  platforms: string[]
  format: 'json' | 'pdf' | 'excel'
}

serve(async (req) => {
  try {
    const { project_id, report_type, period_start, period_end, platforms, format }: ReportRequest = await req.json()

    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Buscar dados para o relatório
    const { data: content } = await supabase
      .from('viral_content')
      .select(`
        *,
        engagement_metrics(*),
        extractions!inner(project_id)
      `)
      .eq('extractions.project_id', project_id)
      .in('platform', platforms)
      .gte('published_at', period_start)
      .lte('published_at', period_end)

    // Gerar métricas do relatório
    const report_data = generateReportMetrics(content, report_type)

    // Salvar relatório no banco
    const { data: saved_report } = await supabase
      .from('executive_reports')
      .insert({
        project_id,
        report_type,
        period_start,
        period_end,
        platforms_analyzed: platforms,
        total_content_pieces: content?.length || 0,
        summary_metrics: report_data.summary_metrics,
        platform_performance: report_data.platform_performance,
        top_content: report_data.top_content,
        recommendations: report_data.recommendations,
        charts_data: report_data.charts_data
      })
      .select()
      .single()

    // Gerar arquivo se necessário
    let file_url = null
    if (format !== 'json') {
      file_url = await generateReportFile(saved_report, format)
    }

    return new Response(JSON.stringify({
      success: true,
      report: saved_report,
      file_url,
      download_expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24h
    }), {
      headers: { 'Content-Type': 'application/json' }
    })

  } catch (error) {
    return new Response(JSON.stringify({
      success: false,
      error: error.message
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    })
  }
})

function generateReportMetrics(content: any[], report_type: string) {
  const total_content = content?.length || 0
  const total_engagement = content?.reduce((sum, item) => {
    const metrics = item.engagement_metrics?.[0]
    return sum + (metrics?.likes_count || 0) + (metrics?.shares_count || 0) + (metrics?.comments_count || 0)
  }, 0) || 0

  const platform_stats = content?.reduce((stats, item) => {
    if (!stats[item.platform]) {
      stats[item.platform] = { count: 0, engagement: 0, viral_score: 0 }
    }
    stats[item.platform].count++

    const metrics = item.engagement_metrics?.[0]
    stats[item.platform].engagement += (metrics?.likes_count || 0) + (metrics?.shares_count || 0) + (metrics?.comments_count || 0)
    stats[item.platform].viral_score += item.viral_score || 0

    return stats
  }, {}) || {}

  // Calcular médias
  Object.keys(platform_stats).forEach(platform => {
    const stats = platform_stats[platform]
    stats.avg_engagement = stats.engagement / stats.count
    stats.avg_viral_score = stats.viral_score / stats.count
  })

  const top_content = content?.sort((a, b) => (b.viral_score || 0) - (a.viral_score || 0)).slice(0, 10) || []

  return {
    summary_metrics: {
      total_content,
      total_engagement,
      avg_engagement: total_content > 0 ? total_engagement / total_content : 0,
      avg_viral_score: content?.reduce((sum, item) => sum + (item.viral_score || 0), 0) / total_content || 0
    },
    platform_performance: platform_stats,
    top_content: top_content.map(item => ({
      id: item.id,
      title: item.title,
      platform: item.platform,
      viral_score: item.viral_score,
      url: item.url,
      published_at: item.published_at
    })),
    recommendations: generateRecommendations(platform_stats, top_content),
    charts_data: {
      platform_distribution: Object.keys(platform_stats).map(platform => ({
        platform,
        count: platform_stats[platform].count,
        engagement: platform_stats[platform].engagement
      })),
      viral_score_timeline: generateTimelineData(content)
    }
  }
}

function generateRecommendations(platform_stats: any, top_content: any[]) {
  const recommendations = []

  // Recomendar plataforma com melhor performance
  const best_platform = Object.keys(platform_stats).reduce((best, platform) => {
    return platform_stats[platform].avg_viral_score > platform_stats[best]?.avg_viral_score ? platform : best
  }, Object.keys(platform_stats)[0])

  if (best_platform) {
    recommendations.push({
      type: 'platform_focus',
      title: `Focar em ${best_platform}`,
      description: `${best_platform} apresenta o melhor desempenho médio de viral score`,
      priority: 'high'
    })
  }

  // Analisar horários de publicação dos top content
  const top_hours = top_content.map(item => new Date(item.published_at).getHours())
  const hour_frequency = top_hours.reduce((freq, hour) => {
    freq[hour] = (freq[hour] || 0) + 1
    return freq
  }, {})

  const best_hour = Object.keys(hour_frequency).reduce((best, hour) => {
    return hour_frequency[hour] > hour_frequency[best] ? hour : best
  }, Object.keys(hour_frequency)[0])

  if (best_hour) {
    recommendations.push({
      type: 'timing',
      title: `Publicar às ${best_hour}h`,
      description: `Conteúdo viral tende a ser publicado às ${best_hour}h`,
      priority: 'medium'
    })
  }

  return recommendations
}

function generateTimelineData(content: any[]) {
  // Agrupar por dia e calcular métricas
  const daily_data = content?.reduce((data, item) => {
    const date = new Date(item.published_at).toISOString().split('T')[0]
    if (!data[date]) {
      data[date] = { count: 0, total_viral_score: 0, total_engagement: 0 }
    }
    data[date].count++
    data[date].total_viral_score += item.viral_score || 0

    const metrics = item.engagement_metrics?.[0]
    data[date].total_engagement += (metrics?.likes_count || 0) + (metrics?.shares_count || 0) + (metrics?.comments_count || 0)

    return data
  }, {}) || {}

  return Object.keys(daily_data).sort().map(date => ({
    date,
    count: daily_data[date].count,
    avg_viral_score: daily_data[date].total_viral_score / daily_data[date].count,
    total_engagement: daily_data[date].total_engagement
  }))
}

async function generateReportFile(report_data: any, format: string) {
  // Implementar geração de PDF/Excel
  // Retornar URL do arquivo no Storage
  return null
}
```

---

## 🐳 DOCKER INTEGRATION

### DOCKER COMPOSE PARA DESENVOLVIMENTO LOCAL:

```yaml
# docker-compose.yml
version: '3.8'

services:
  # Supabase Local Development
  supabase-db:
    image: supabase/postgres:15.1.0.117
    container_name: webagent-supabase-db
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: your-super-secret-and-long-postgres-password
      POSTGRES_PORT: 5432
    ports:
      - "54322:5432"
    volumes:
      - supabase-db-data:/var/lib/postgresql/data
      - ./supabase/migrations:/docker-entrypoint-initdb.d
    networks:
      - webagent-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Supabase Studio (Dashboard Local)
  supabase-studio:
    image: supabase/studio:20240101-ce42139
    container_name: webagent-supabase-studio
    environment:
      SUPABASE_URL: http://supabase-kong:8000
      SUPABASE_REST_URL: http://localhost:54321/rest/v1/
      SUPABASE_ANON_KEY: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
      SUPABASE_SERVICE_KEY: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU
    ports:
      - "54323:3000"
    networks:
      - webagent-network
    depends_on:
      supabase-db:
        condition: service_healthy

  # PostgREST API
  supabase-rest:
    image: postgrest/postgrest:v12.0.1
    container_name: webagent-supabase-rest
    environment:
      PGRST_DB_URI: **************************************************************************************/postgres
      PGRST_DB_SCHEMAS: public,storage,graphql_public
      PGRST_DB_ANON_ROLE: anon
      PGRST_JWT_SECRET: your-super-secret-jwt-token-with-at-least-32-characters-long
      PGRST_DB_USE_LEGACY_GUCS: "false"
      PGRST_APP_SETTINGS_JWT_SECRET: your-super-secret-jwt-token-with-at-least-32-characters-long
      PGRST_APP_SETTINGS_JWT_EXP: 3600
    ports:
      - "54321:3000"
    networks:
      - webagent-network
    depends_on:
      supabase-db:
        condition: service_healthy

  # Supabase Auth (GoTrue)
  supabase-auth:
    image: supabase/gotrue:v2.132.3
    container_name: webagent-supabase-auth
    environment:
      GOTRUE_API_HOST: 0.0.0.0
      GOTRUE_API_PORT: 9999
      API_EXTERNAL_URL: http://localhost:54321
      GOTRUE_DB_DRIVER: postgres
      GOTRUE_DB_DATABASE_URL: ********************************************************************************************/postgres
      GOTRUE_SITE_URL: http://localhost:3000
      GOTRUE_URI_ALLOW_LIST: "*"
      GOTRUE_DISABLE_SIGNUP: "false"
      GOTRUE_JWT_ADMIN_ROLES: service_role
      GOTRUE_JWT_AUD: authenticated
      GOTRUE_JWT_DEFAULT_GROUP_NAME: authenticated
      GOTRUE_JWT_EXP: 3600
      GOTRUE_JWT_SECRET: your-super-secret-jwt-token-with-at-least-32-characters-long
      GOTRUE_EXTERNAL_EMAIL_ENABLED: "true"
      GOTRUE_MAILER_AUTOCONFIRM: "true"
      GOTRUE_SMTP_ADMIN_EMAIL: <EMAIL>
      GOTRUE_SMTP_HOST: supabase-inbucket
      GOTRUE_SMTP_PORT: 2500
      GOTRUE_SMTP_USER: fake_mail_user
      GOTRUE_SMTP_PASS: fake_mail_password
      GOTRUE_SMTP_SENDER_NAME: fake_sender
    ports:
      - "54324:9999"
    networks:
      - webagent-network
    depends_on:
      supabase-db:
        condition: service_healthy

  # Supabase Storage
  supabase-storage:
    image: supabase/storage-api:v0.46.4
    container_name: webagent-supabase-storage
    environment:
      ANON_KEY: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
      SERVICE_KEY: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU
      POSTGREST_URL: http://supabase-rest:3000
      PGRST_JWT_SECRET: your-super-secret-jwt-token-with-at-least-32-characters-long
      DATABASE_URL: ***********************************************************************************************/postgres
      FILE_SIZE_LIMIT: 52428800
      STORAGE_BACKEND: file
      FILE_STORAGE_BACKEND_PATH: /var/lib/storage
      TENANT_ID: stub
      REGION: stub
      GLOBAL_S3_BUCKET: stub
      ENABLE_IMAGE_TRANSFORMATION: "true"
      IMGPROXY_URL: http://supabase-imgproxy:5001
    ports:
      - "54325:5000"
    volumes:
      - supabase-storage-data:/var/lib/storage
    networks:
      - webagent-network
    depends_on:
      supabase-db:
        condition: service_healthy
      supabase-rest:
        condition: service_started

  # Image Proxy para transformações
  supabase-imgproxy:
    image: darthsim/imgproxy:v3.8.0
    container_name: webagent-supabase-imgproxy
    environment:
      IMGPROXY_BIND: ":5001"
      IMGPROXY_LOCAL_FILESYSTEM_ROOT: /
      IMGPROXY_USE_ETAG: "true"
      IMGPROXY_ENABLE_WEBP_DETECTION: "true"
    volumes:
      - supabase-storage-data:/var/lib/storage:ro
    networks:
      - webagent-network

  # Kong API Gateway
  supabase-kong:
    image: kong:2.8.1
    container_name: webagent-supabase-kong
    environment:
      KONG_DATABASE: "off"
      KONG_DECLARATIVE_CONFIG: /var/lib/kong/kong.yml
      KONG_DNS_ORDER: LAST,A,CNAME
      KONG_PLUGINS: request-transformer,cors,key-auth,acl,basic-auth
      KONG_NGINX_PROXY_PROXY_BUFFER_SIZE: 160k
      KONG_NGINX_PROXY_PROXY_BUFFERS: 64 160k
    ports:
      - "54320:8000/tcp"
      - "54326:8443/tcp"
    volumes:
      - ./supabase/config/kong.yml:/var/lib/kong/kong.yml:ro
    networks:
      - webagent-network
    depends_on:
      - supabase-auth
      - supabase-rest
      - supabase-storage

  # WebAgent Application
  webagent-app:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: webagent-app
    environment:
      NODE_ENV: development
      SUPABASE_URL: http://supabase-kong:8000
      SUPABASE_ANON_KEY: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
      SUPABASE_SERVICE_KEY: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU
      DATABASE_URL: *********************************************************************************/postgres
    ports:
      - "3000:3000"
      - "9229:9229" # Debug port
    volumes:
      - ./src:/app/src
      - ./public:/app/public
      - node_modules:/app/node_modules
    networks:
      - webagent-network
    depends_on:
      supabase-kong:
        condition: service_started
    develop:
      watch:
        - action: sync
          path: ./src
          target: /app/src
        - action: rebuild
          path: package.json

  # Redis para cache
  redis:
    image: redis:7-alpine
    container_name: webagent-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - webagent-network
    command: redis-server --appendonly yes

  # Nginx para proxy reverso
  nginx:
    image: nginx:alpine
    container_name: webagent-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    networks:
      - webagent-network
    depends_on:
      - webagent-app
      - supabase-kong

volumes:
  supabase-db-data:
    driver: local
  supabase-storage-data:
    driver: local
  redis-data:
    driver: local
  node_modules:
    driver: local

networks:
  webagent-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
```

### DOCKERFILE MULTI-STAGE:

```dockerfile
# Dockerfile
# =====================================================
# STAGE 1: Base Dependencies
# =====================================================
FROM node:18-alpine AS base

# Install system dependencies
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    git \
    curl \
    bash

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./
COPY yarn.lock* ./

# =====================================================
# STAGE 2: Development Dependencies
# =====================================================
FROM base AS development

# Install all dependencies (including dev)
RUN npm ci --include=dev

# Copy source code
COPY . .

# Expose ports
EXPOSE 3000 9229

# Start development server with hot reload
CMD ["npm", "run", "dev"]

# =====================================================
# STAGE 3: Production Build
# =====================================================
FROM base AS build

# Install only production dependencies
RUN npm ci --only=production && npm cache clean --force

# Copy source code
COPY . .

# Build application
RUN npm run build

# =====================================================
# STAGE 4: Production Runtime
# =====================================================
FROM node:18-alpine AS production

# Create app user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001

# Set working directory
WORKDIR /app

# Copy built application
COPY --from=build --chown=nextjs:nodejs /app/.next ./.next
COPY --from=build --chown=nextjs:nodejs /app/public ./public
COPY --from=build --chown=nextjs:nodejs /app/package.json ./package.json
COPY --from=build --chown=nextjs:nodejs /app/node_modules ./node_modules

# Switch to non-root user
USER nextjs

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/api/health || exit 1

# Start application
CMD ["npm", "start"]
```

---

## 📊 VIEWS E CONSULTAS OTIMIZADAS

### VIEWS MATERIALIZADAS PARA PERFORMANCE:

```sql
-- =====================================================
-- VIEW: DASHBOARD EXECUTIVO
-- =====================================================

CREATE MATERIALIZED VIEW public.executive_dashboard AS
SELECT
    p.id as project_id,
    p.name as project_name,
    p.user_id,
    COUNT(DISTINCT e.id) as total_extractions,
    COUNT(DISTINCT vc.id) as total_content,
    AVG(vc.viral_score) as avg_viral_score,
    SUM((vc.engagement_metrics->>'likes_count')::BIGINT) as total_likes,
    SUM((vc.engagement_metrics->>'shares_count')::BIGINT) as total_shares,
    SUM((vc.engagement_metrics->>'comments_count')::BIGINT) as total_comments,
    COUNT(DISTINCT vc.platform) as platforms_used,
    MAX(e.created_at) as last_extraction,
    ARRAY_AGG(DISTINCT vc.platform) as active_platforms
FROM public.projects p
LEFT JOIN public.extractions e ON p.id = e.project_id
LEFT JOIN public.viral_content vc ON e.id = vc.extraction_id
WHERE p.is_active = true
GROUP BY p.id, p.name, p.user_id;

-- Índice para a view materializada
CREATE UNIQUE INDEX idx_executive_dashboard_project ON public.executive_dashboard (project_id);
CREATE INDEX idx_executive_dashboard_user ON public.executive_dashboard (user_id);

-- =====================================================
-- VIEW: TOP CONTENT VIRAL
-- =====================================================

CREATE MATERIALIZED VIEW public.top_viral_content AS
SELECT
    vc.*,
    p.name as project_name,
    p.user_id,
    em.likes_count,
    em.shares_count,
    em.comments_count,
    em.views_count,
    em.engagement_rate,
    ROW_NUMBER() OVER (PARTITION BY vc.platform ORDER BY vc.viral_score DESC) as platform_rank,
    ROW_NUMBER() OVER (ORDER BY vc.viral_score DESC) as global_rank
FROM public.viral_content vc
JOIN public.extractions e ON vc.extraction_id = e.id
JOIN public.projects p ON e.project_id = p.id
LEFT JOIN public.engagement_metrics em ON vc.id = em.content_id
WHERE vc.viral_score > 5.0
ORDER BY vc.viral_score DESC;

-- Índices para performance
CREATE INDEX idx_top_viral_platform_rank ON public.top_viral_content (platform, platform_rank);
CREATE INDEX idx_top_viral_global_rank ON public.top_viral_content (global_rank);
CREATE INDEX idx_top_viral_user ON public.top_viral_content (user_id);

-- =====================================================
-- VIEW: ANÁLISE DE TENDÊNCIAS
-- =====================================================

CREATE MATERIALIZED VIEW public.trending_hashtags AS
SELECT
    hashtag,
    COUNT(*) as usage_count,
    AVG(vc.viral_score) as avg_viral_score,
    SUM((vc.engagement_metrics->>'likes_count')::BIGINT) as total_likes,
    COUNT(DISTINCT vc.platform) as platforms_count,
    COUNT(DISTINCT DATE(vc.published_at)) as days_active,
    MAX(vc.published_at) as last_seen,
    ARRAY_AGG(DISTINCT vc.platform) as platforms
FROM public.viral_content vc,
     UNNEST(vc.hashtags) as hashtag
WHERE vc.published_at >= NOW() - INTERVAL '30 days'
GROUP BY hashtag
HAVING COUNT(*) >= 5
ORDER BY usage_count DESC, avg_viral_score DESC;

-- Índice para busca rápida
CREATE INDEX idx_trending_hashtags_usage ON public.trending_hashtags (usage_count DESC);
CREATE INDEX idx_trending_hashtags_score ON public.trending_hashtags (avg_viral_score DESC);

-- =====================================================
-- VIEW: PERFORMANCE POR PLATAFORMA
-- =====================================================

CREATE MATERIALIZED VIEW public.platform_performance AS
SELECT
    platform,
    COUNT(*) as total_content,
    AVG(viral_score) as avg_viral_score,
    PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY viral_score) as median_viral_score,
    MAX(viral_score) as max_viral_score,
    AVG((engagement_metrics->>'likes_count')::BIGINT) as avg_likes,
    AVG((engagement_metrics->>'shares_count')::BIGINT) as avg_shares,
    AVG((engagement_metrics->>'comments_count')::BIGINT) as avg_comments,
    AVG((engagement_metrics->>'views_count')::BIGINT) as avg_views,
    COUNT(DISTINCT author_username) as unique_authors,
    COUNT(DISTINCT DATE(published_at)) as active_days,
    MIN(published_at) as first_content,
    MAX(published_at) as latest_content
FROM public.viral_content
WHERE published_at >= NOW() - INTERVAL '90 days'
GROUP BY platform
ORDER BY avg_viral_score DESC;

-- =====================================================
-- FUNCTION: REFRESH VIEWS MATERIALIZADAS
-- =====================================================

CREATE OR REPLACE FUNCTION refresh_materialized_views()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY public.executive_dashboard;
    REFRESH MATERIALIZED VIEW CONCURRENTLY public.top_viral_content;
    REFRESH MATERIALIZED VIEW CONCURRENTLY public.trending_hashtags;
    REFRESH MATERIALIZED VIEW CONCURRENTLY public.platform_performance;

    -- Log da atualização
    INSERT INTO public.performance_metrics (
        metric_type,
        metric_name,
        metric_value,
        tags
    ) VALUES (
        'system',
        'materialized_views_refreshed',
        1,
        jsonb_build_object('timestamp', NOW())
    );
END;
$$ language 'plpgsql';

-- Agendar refresh automático (via pg_cron se disponível)
-- SELECT cron.schedule('refresh-views', '*/15 * * * *', 'SELECT refresh_materialized_views();');
```

---

## 🔍 CONSULTAS AVANÇADAS E ANALYTICS

### QUERIES OTIMIZADAS PARA ANÁLISE:

```sql
-- =====================================================
-- QUERY: ANÁLISE DE CRESCIMENTO VIRAL
-- =====================================================

WITH viral_growth AS (
    SELECT
        DATE(published_at) as date,
        platform,
        COUNT(*) as content_count,
        AVG(viral_score) as avg_score,
        SUM((engagement_metrics->>'likes_count')::BIGINT) as total_likes,
        LAG(COUNT(*)) OVER (PARTITION BY platform ORDER BY DATE(published_at)) as prev_count,
        LAG(AVG(viral_score)) OVER (PARTITION BY platform ORDER BY DATE(published_at)) as prev_score
    FROM public.viral_content
    WHERE published_at >= NOW() - INTERVAL '30 days'
    GROUP BY DATE(published_at), platform
    ORDER BY date DESC, platform
)
SELECT
    date,
    platform,
    content_count,
    avg_score,
    total_likes,
    CASE
        WHEN prev_count IS NOT NULL THEN
            ROUND(((content_count - prev_count)::DECIMAL / prev_count * 100), 2)
        ELSE NULL
    END as growth_rate_percent,
    CASE
        WHEN prev_score IS NOT NULL THEN
            ROUND((avg_score - prev_score), 2)
        ELSE NULL
    END as score_change
FROM viral_growth
WHERE date >= CURRENT_DATE - INTERVAL '7 days';

-- =====================================================
-- QUERY: DETECÇÃO DE CONTEÚDO VIRAL EMERGENTE
-- =====================================================

WITH recent_content AS (
    SELECT
        *,
        EXTRACT(EPOCH FROM (NOW() - published_at)) / 3600 as hours_since_published,
        (engagement_metrics->>'likes_count')::BIGINT +
        (engagement_metrics->>'shares_count')::BIGINT * 3 +
        (engagement_metrics->>'comments_count')::BIGINT * 2 as engagement_score
    FROM public.viral_content
    WHERE published_at >= NOW() - INTERVAL '24 hours'
),
viral_velocity AS (
    SELECT
        *,
        CASE
            WHEN hours_since_published > 0 THEN
                engagement_score / hours_since_published
            ELSE engagement_score
        END as velocity
    FROM recent_content
)
SELECT
    id,
    title,
    platform,
    author_username,
    viral_score,
    engagement_score,
    velocity,
    hours_since_published,
    url,
    hashtags
FROM viral_velocity
WHERE velocity > (
    SELECT PERCENTILE_CONT(0.9) WITHIN GROUP (ORDER BY velocity)
    FROM viral_velocity
)
ORDER BY velocity DESC
LIMIT 20;

-- =====================================================
-- QUERY: ANÁLISE DE SENTIMENTO POR HASHTAG
-- =====================================================

WITH hashtag_sentiment AS (
    SELECT
        hashtag,
        COUNT(*) as total_mentions,
        AVG(sentiment_score) as avg_sentiment,
        STDDEV(sentiment_score) as sentiment_stddev,
        COUNT(CASE WHEN sentiment_label = 'positive' THEN 1 END) as positive_count,
        COUNT(CASE WHEN sentiment_label = 'negative' THEN 1 END) as negative_count,
        COUNT(CASE WHEN sentiment_label = 'neutral' THEN 1 END) as neutral_count,
        AVG(viral_score) as avg_viral_score
    FROM public.viral_content vc,
         UNNEST(vc.hashtags) as hashtag
    WHERE vc.published_at >= NOW() - INTERVAL '7 days'
      AND vc.sentiment_score IS NOT NULL
    GROUP BY hashtag
    HAVING COUNT(*) >= 10
)
SELECT
    hashtag,
    total_mentions,
    ROUND(avg_sentiment::NUMERIC, 3) as avg_sentiment,
    ROUND(sentiment_stddev::NUMERIC, 3) as sentiment_volatility,
    ROUND((positive_count::DECIMAL / total_mentions * 100), 1) as positive_percent,
    ROUND((negative_count::DECIMAL / total_mentions * 100), 1) as negative_percent,
    ROUND((neutral_count::DECIMAL / total_mentions * 100), 1) as neutral_percent,
    ROUND(avg_viral_score::NUMERIC, 2) as avg_viral_score,
    CASE
        WHEN avg_sentiment > 0.1 THEN 'Positive Trend'
        WHEN avg_sentiment < -0.1 THEN 'Negative Trend'
        ELSE 'Neutral Trend'
    END as sentiment_trend
FROM hashtag_sentiment
ORDER BY total_mentions DESC, avg_viral_score DESC;

-- =====================================================
-- QUERY: IDENTIFICAÇÃO DE INFLUENCIADORES
-- =====================================================

WITH author_metrics AS (
    SELECT
        author_username,
        platform,
        COUNT(*) as content_count,
        AVG(viral_score) as avg_viral_score,
        MAX(viral_score) as max_viral_score,
        SUM((engagement_metrics->>'likes_count')::BIGINT) as total_likes,
        SUM((engagement_metrics->>'shares_count')::BIGINT) as total_shares,
        SUM((engagement_metrics->>'comments_count')::BIGINT) as total_comments,
        AVG(author_followers_count) as avg_followers,
        COUNT(DISTINCT DATE(published_at)) as active_days,
        ARRAY_AGG(DISTINCT hashtags) as used_hashtags
    FROM public.viral_content
    WHERE published_at >= NOW() - INTERVAL '30 days'
      AND author_username IS NOT NULL
    GROUP BY author_username, platform
    HAVING COUNT(*) >= 3
),
influence_score AS (
    SELECT
        *,
        (avg_viral_score * content_count * LOG(avg_followers + 1)) / 1000 as influence_score
    FROM author_metrics
)
SELECT
    author_username,
    platform,
    content_count,
    ROUND(avg_viral_score::NUMERIC, 2) as avg_viral_score,
    ROUND(max_viral_score::NUMERIC, 2) as max_viral_score,
    total_likes,
    total_shares,
    total_comments,
    avg_followers,
    active_days,
    ROUND(influence_score::NUMERIC, 2) as influence_score,
    CASE
        WHEN influence_score > 100 THEN 'High Influence'
        WHEN influence_score > 50 THEN 'Medium Influence'
        ELSE 'Low Influence'
    END as influence_level
FROM influence_score
ORDER BY influence_score DESC
LIMIT 50;

-- =====================================================
-- QUERY: PREVISÃO DE TENDÊNCIAS
-- =====================================================

WITH daily_trends AS (
    SELECT
        DATE(published_at) as date,
        hashtag,
        COUNT(*) as daily_count,
        AVG(viral_score) as daily_avg_score
    FROM public.viral_content vc,
         UNNEST(vc.hashtags) as hashtag
    WHERE published_at >= NOW() - INTERVAL '14 days'
    GROUP BY DATE(published_at), hashtag
),
trend_analysis AS (
    SELECT
        hashtag,
        date,
        daily_count,
        daily_avg_score,
        LAG(daily_count, 1) OVER (PARTITION BY hashtag ORDER BY date) as prev_count,
        LAG(daily_count, 7) OVER (PARTITION BY hashtag ORDER BY date) as week_ago_count,
        AVG(daily_count) OVER (PARTITION BY hashtag ORDER BY date ROWS BETWEEN 6 PRECEDING AND CURRENT ROW) as week_avg
    FROM daily_trends
),
trending_predictions AS (
    SELECT
        hashtag,
        date,
        daily_count,
        daily_avg_score,
        week_avg,
        CASE
            WHEN prev_count IS NOT NULL AND prev_count > 0 THEN
                (daily_count - prev_count)::DECIMAL / prev_count
            ELSE 0
        END as daily_growth_rate,
        CASE
            WHEN week_ago_count IS NOT NULL AND week_ago_count > 0 THEN
                (daily_count - week_ago_count)::DECIMAL / week_ago_count
            ELSE 0
        END as weekly_growth_rate,
        CASE
            WHEN week_avg > 0 THEN daily_count / week_avg
            ELSE 1
        END as trend_momentum
    FROM trend_analysis
    WHERE date = CURRENT_DATE - INTERVAL '1 day'
)
SELECT
    hashtag,
    daily_count as yesterday_count,
    ROUND(daily_avg_score::NUMERIC, 2) as avg_viral_score,
    ROUND(week_avg::NUMERIC, 1) as week_average,
    ROUND((daily_growth_rate * 100)::NUMERIC, 1) as daily_growth_percent,
    ROUND((weekly_growth_rate * 100)::NUMERIC, 1) as weekly_growth_percent,
    ROUND(trend_momentum::NUMERIC, 2) as momentum_factor,
    CASE
        WHEN daily_growth_rate > 0.5 AND weekly_growth_rate > 1.0 THEN 'Explosive Growth'
        WHEN daily_growth_rate > 0.2 AND weekly_growth_rate > 0.5 THEN 'Strong Growth'
        WHEN daily_growth_rate > 0.1 OR weekly_growth_rate > 0.2 THEN 'Moderate Growth'
        WHEN daily_growth_rate < -0.2 OR weekly_growth_rate < -0.3 THEN 'Declining'
        ELSE 'Stable'
    END as trend_prediction
FROM trending_predictions
WHERE daily_count >= 5
ORDER BY weekly_growth_rate DESC, daily_growth_rate DESC
LIMIT 30;
```

---

## 🚨 MONITORAMENTO E ALERTAS

### SISTEMA DE ALERTAS AUTOMÁTICOS:

```sql
-- =====================================================
-- FUNCTION: DETECTAR CONTEÚDO VIRAL EMERGENTE
-- =====================================================

CREATE OR REPLACE FUNCTION detect_viral_content()
RETURNS TABLE(
    content_id UUID,
    viral_score DECIMAL,
    growth_rate DECIMAL,
    alert_level TEXT
) AS $$
BEGIN
    RETURN QUERY
    WITH recent_content AS (
        SELECT
            vc.id,
            vc.viral_score,
            vc.published_at,
            EXTRACT(EPOCH FROM (NOW() - vc.published_at)) / 3600 as hours_old,
            (vc.engagement_metrics->>'likes_count')::BIGINT +
            (vc.engagement_metrics->>'shares_count')::BIGINT * 3 as engagement
        FROM public.viral_content vc
        WHERE vc.published_at >= NOW() - INTERVAL '6 hours'
    )
    SELECT
        rc.id,
        rc.viral_score,
        CASE
            WHEN rc.hours_old > 0 THEN rc.engagement / rc.hours_old
            ELSE rc.engagement::DECIMAL
        END as growth_rate,
        CASE
            WHEN rc.viral_score > 50 THEN 'CRITICAL'
            WHEN rc.viral_score > 25 THEN 'HIGH'
            WHEN rc.viral_score > 10 THEN 'MEDIUM'
            ELSE 'LOW'
        END as alert_level
    FROM recent_content rc
    WHERE rc.viral_score > 10
    ORDER BY rc.viral_score DESC;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- FUNCTION: ALERTAS DE QUOTA DE API
-- =====================================================

CREATE OR REPLACE FUNCTION check_api_quota_alerts()
RETURNS TABLE(
    user_id UUID,
    username VARCHAR,
    quota_used INTEGER,
    quota_limit INTEGER,
    usage_percent DECIMAL,
    alert_type TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        p.id,
        p.username,
        p.api_quota_used,
        p.api_quota_daily,
        (p.api_quota_used::DECIMAL / p.api_quota_daily * 100) as usage_percent,
        CASE
            WHEN p.api_quota_used >= p.api_quota_daily THEN 'QUOTA_EXCEEDED'
            WHEN p.api_quota_used >= p.api_quota_daily * 0.9 THEN 'QUOTA_WARNING'
            WHEN p.api_quota_used >= p.api_quota_daily * 0.8 THEN 'QUOTA_ALERT'
            ELSE 'QUOTA_OK'
        END as alert_type
    FROM public.profiles p
    WHERE p.api_quota_used >= p.api_quota_daily * 0.8;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- FUNCTION: ALERTAS DE PERFORMANCE DO SISTEMA
-- =====================================================

CREATE OR REPLACE FUNCTION check_system_performance()
RETURNS TABLE(
    metric_name TEXT,
    current_value DECIMAL,
    threshold_value DECIMAL,
    alert_level TEXT,
    description TEXT
) AS $$
DECLARE
    avg_response_time DECIMAL;
    error_rate DECIMAL;
    active_extractions INTEGER;
    storage_usage DECIMAL;
BEGIN
    -- Calcular métricas de performance
    SELECT AVG(metric_value) INTO avg_response_time
    FROM public.performance_metrics
    WHERE metric_name = 'api_response_time_ms'
      AND measured_at >= NOW() - INTERVAL '1 hour';

    SELECT AVG(metric_value) INTO error_rate
    FROM public.performance_metrics
    WHERE metric_name = 'error_rate_percent'
      AND measured_at >= NOW() - INTERVAL '1 hour';

    SELECT COUNT(*) INTO active_extractions
    FROM public.extractions
    WHERE status IN ('pending', 'processing');

    -- Retornar alertas
    RETURN QUERY
    SELECT
        'avg_response_time'::TEXT,
        COALESCE(avg_response_time, 0),
        500::DECIMAL,
        CASE
            WHEN avg_response_time > 1000 THEN 'CRITICAL'
            WHEN avg_response_time > 500 THEN 'WARNING'
            ELSE 'OK'
        END,
        'Average API response time in milliseconds'::TEXT

    UNION ALL

    SELECT
        'error_rate'::TEXT,
        COALESCE(error_rate, 0),
        5::DECIMAL,
        CASE
            WHEN error_rate > 10 THEN 'CRITICAL'
            WHEN error_rate > 5 THEN 'WARNING'
            ELSE 'OK'
        END,
        'Error rate percentage'::TEXT

    UNION ALL

    SELECT
        'active_extractions'::TEXT,
        active_extractions::DECIMAL,
        100::DECIMAL,
        CASE
            WHEN active_extractions > 200 THEN 'CRITICAL'
            WHEN active_extractions > 100 THEN 'WARNING'
            ELSE 'OK'
        END,
        'Number of active extractions'::TEXT;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- TRIGGER: ALERTA AUTOMÁTICO PARA CONTEÚDO VIRAL
-- =====================================================

CREATE OR REPLACE FUNCTION viral_content_alert()
RETURNS TRIGGER AS $$
BEGIN
    -- Enviar alerta se viral score for muito alto
    IF NEW.viral_score > 50 THEN
        INSERT INTO public.activity_logs (
            action,
            resource_type,
            resource_id,
            details
        ) VALUES (
            'VIRAL_ALERT',
            'viral_content',
            NEW.id,
            jsonb_build_object(
                'viral_score', NEW.viral_score,
                'platform', NEW.platform,
                'title', NEW.title,
                'url', NEW.url,
                'alert_level', 'HIGH',
                'timestamp', NOW()
            )
        );
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER viral_content_alert_trigger
    AFTER INSERT OR UPDATE ON public.viral_content
    FOR EACH ROW
    WHEN (NEW.viral_score > 25)
    EXECUTE FUNCTION viral_content_alert();
```

---

## 📈 MÉTRICAS E KPIs

### DASHBOARD DE MÉTRICAS PRINCIPAIS:

```sql
-- =====================================================
-- VIEW: KPIs PRINCIPAIS DO SISTEMA
-- =====================================================

CREATE OR REPLACE VIEW public.system_kpis AS
WITH base_metrics AS (
    SELECT
        COUNT(DISTINCT p.id) as total_projects,
        COUNT(DISTINCT p.user_id) as active_users,
        COUNT(DISTINCT e.id) as total_extractions,
        COUNT(DISTINCT vc.id) as total_content,
        AVG(vc.viral_score) as avg_viral_score,
        COUNT(DISTINCT vc.id) FILTER (WHERE vc.viral_score > 10) as viral_content_count,
        SUM((vc.engagement_metrics->>'likes_count')::BIGINT) as total_likes,
        SUM((vc.engagement_metrics->>'shares_count')::BIGINT) as total_shares,
        SUM((vc.engagement_metrics->>'comments_count')::BIGINT) as total_comments,
        COUNT(DISTINCT vc.platform) as platforms_active,
        COUNT(DISTINCT DATE(vc.published_at)) as content_days
    FROM public.projects p
    LEFT JOIN public.extractions e ON p.id = e.project_id
    LEFT JOIN public.viral_content vc ON e.id = vc.extraction_id
    WHERE p.is_active = true
      AND vc.published_at >= NOW() - INTERVAL '30 days'
),
growth_metrics AS (
    SELECT
        COUNT(DISTINCT vc.id) as content_last_7d,
        COUNT(DISTINCT e.id) as extractions_last_7d,
        AVG(vc.viral_score) as avg_score_last_7d
    FROM public.extractions e
    LEFT JOIN public.viral_content vc ON e.id = vc.extraction_id
    WHERE e.created_at >= NOW() - INTERVAL '7 days'
),
prev_growth_metrics AS (
    SELECT
        COUNT(DISTINCT vc.id) as content_prev_7d,
        COUNT(DISTINCT e.id) as extractions_prev_7d,
        AVG(vc.viral_score) as avg_score_prev_7d
    FROM public.extractions e
    LEFT JOIN public.viral_content vc ON e.id = vc.extraction_id
    WHERE e.created_at >= NOW() - INTERVAL '14 days'
      AND e.created_at < NOW() - INTERVAL '7 days'
)
SELECT
    -- Métricas principais
    bm.total_projects,
    bm.active_users,
    bm.total_extractions,
    bm.total_content,
    ROUND(bm.avg_viral_score::NUMERIC, 2) as avg_viral_score,
    bm.viral_content_count,
    bm.total_likes,
    bm.total_shares,
    bm.total_comments,
    bm.platforms_active,

    -- Métricas de crescimento
    gm.content_last_7d,
    gm.extractions_last_7d,
    ROUND(gm.avg_score_last_7d::NUMERIC, 2) as avg_score_last_7d,

    -- Taxas de crescimento
    CASE
        WHEN pgm.content_prev_7d > 0 THEN
            ROUND(((gm.content_last_7d - pgm.content_prev_7d)::DECIMAL / pgm.content_prev_7d * 100), 1)
        ELSE NULL
    END as content_growth_rate,

    CASE
        WHEN pgm.extractions_prev_7d > 0 THEN
            ROUND(((gm.extractions_last_7d - pgm.extractions_prev_7d)::DECIMAL / pgm.extractions_prev_7d * 100), 1)
        ELSE NULL
    END as extraction_growth_rate,

    -- Métricas de qualidade
    CASE
        WHEN bm.total_content > 0 THEN
            ROUND((bm.viral_content_count::DECIMAL / bm.total_content * 100), 1)
        ELSE 0
    END as viral_content_percentage,

    CASE
        WHEN bm.total_content > 0 THEN
            ROUND(((bm.total_likes + bm.total_shares + bm.total_comments)::DECIMAL / bm.total_content), 1)
        ELSE 0
    END as avg_engagement_per_content,

    -- Timestamp da atualização
    NOW() as updated_at

FROM base_metrics bm
CROSS JOIN growth_metrics gm
CROSS JOIN prev_growth_metrics pgm;

-- =====================================================
-- FUNCTION: CALCULAR ROI DE PROJETOS
-- =====================================================

CREATE OR REPLACE FUNCTION calculate_project_roi(project_uuid UUID)
RETURNS TABLE(
    project_id UUID,
    total_content INTEGER,
    viral_content INTEGER,
    total_engagement BIGINT,
    avg_viral_score DECIMAL,
    roi_score DECIMAL,
    performance_grade TEXT
) AS $$
BEGIN
    RETURN QUERY
    WITH project_metrics AS (
        SELECT
            p.id,
            COUNT(vc.id) as content_count,
            COUNT(vc.id) FILTER (WHERE vc.viral_score > 10) as viral_count,
            SUM(
                (vc.engagement_metrics->>'likes_count')::BIGINT +
                (vc.engagement_metrics->>'shares_count')::BIGINT +
                (vc.engagement_metrics->>'comments_count')::BIGINT
            ) as engagement_total,
            AVG(vc.viral_score) as score_avg,
            COUNT(DISTINCT e.id) as extraction_count
        FROM public.projects p
        LEFT JOIN public.extractions e ON p.id = e.project_id
        LEFT JOIN public.viral_content vc ON e.id = vc.extraction_id
        WHERE p.id = project_uuid
        GROUP BY p.id
    )
    SELECT
        pm.id,
        pm.content_count::INTEGER,
        pm.viral_count::INTEGER,
        COALESCE(pm.engagement_total, 0),
        ROUND(COALESCE(pm.score_avg, 0)::NUMERIC, 2),
        -- ROI Score baseado em múltiplos fatores
        ROUND((
            COALESCE(pm.score_avg, 0) * 0.4 +
            CASE WHEN pm.content_count > 0 THEN (pm.viral_count::DECIMAL / pm.content_count * 100) * 0.3 ELSE 0 END +
            CASE WHEN pm.content_count > 0 THEN LOG(pm.engagement_total + 1) * 0.3 ELSE 0 END
        )::NUMERIC, 2) as roi_score,
        CASE
            WHEN pm.score_avg > 20 AND pm.viral_count > pm.content_count * 0.3 THEN 'A+'
            WHEN pm.score_avg > 15 AND pm.viral_count > pm.content_count * 0.2 THEN 'A'
            WHEN pm.score_avg > 10 AND pm.viral_count > pm.content_count * 0.1 THEN 'B'
            WHEN pm.score_avg > 5 THEN 'C'
            ELSE 'D'
        END as performance_grade
    FROM project_metrics pm;
END;
$$ LANGUAGE plpgsql;
```

---

## 🔧 CONFIGURAÇÃO E DEPLOY

### SCRIPTS DE INICIALIZAÇÃO:

```bash
#!/bin/bash
# scripts/setup-supabase.sh

set -e

echo "🚀 Configurando Infraestrutura Supabase WebAgent..."

# Verificar dependências
command -v docker >/dev/null 2>&1 || { echo "❌ Docker não encontrado. Instale o Docker primeiro."; exit 1; }
command -v docker-compose >/dev/null 2>&1 || { echo "❌ Docker Compose não encontrado."; exit 1; }

# Criar diretórios necessários
mkdir -p supabase/{migrations,functions,config,storage}
mkdir -p nginx/ssl
mkdir -p logs

# Gerar chaves JWT se não existirem
if [ ! -f .env.local ]; then
    echo "📝 Gerando configurações locais..."

    JWT_SECRET=$(openssl rand -base64 32)
    POSTGRES_PASSWORD=$(openssl rand -base64 32)

    cat > .env.local << EOF
# Supabase Configuration
SUPABASE_URL=http://localhost:54320
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
SUPABASE_SERVICE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU

# Database
DATABASE_URL=postgres://postgres:${POSTGRES_PASSWORD}@localhost:54322/postgres
POSTGRES_PASSWORD=${POSTGRES_PASSWORD}

# JWT
JWT_SECRET=${JWT_SECRET}

# Application
NODE_ENV=development
PORT=3000
EOF

    echo "✅ Arquivo .env.local criado com configurações seguras"
fi

# Criar configuração do Kong
cat > supabase/config/kong.yml << 'EOF'
_format_version: "1.1"

consumers:
  - username: anon
    keyauth_credentials:
      - key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
  - username: service_role
    keyauth_credentials:
      - key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU

acls:
  - consumer: anon
    group: anon
  - consumer: service_role
    group: service_role

services:
  - name: auth-v1-open
    url: http://supabase-auth:9999/verify
    routes:
      - name: auth-v1-open
        strip_path: true
        paths:
          - /auth/v1/verify
    plugins:
      - name: cors

  - name: auth-v1-open-callback
    url: http://supabase-auth:9999/callback
    routes:
      - name: auth-v1-open-callback
        strip_path: true
        paths:
          - /auth/v1/callback
    plugins:
      - name: cors

  - name: auth-v1
    _comment: "GoTrue: /auth/v1/* -> http://supabase-auth:9999/*"
    url: http://supabase-auth:9999/
    routes:
      - name: auth-v1-all
        strip_path: true
        paths:
          - /auth/v1/
    plugins:
      - name: cors
      - name: key-auth
        config:
          hide_credentials: false
      - name: acl
        config:
          hide_groups_header: true
          allow:
            - anon
            - service_role

  - name: rest-v1
    _comment: "PostgREST: /rest/v1/* -> http://supabase-rest:3000/*"
    url: http://supabase-rest:3000/
    routes:
      - name: rest-v1-all
        strip_path: true
        paths:
          - /rest/v1/
    plugins:
      - name: cors
      - name: key-auth
        config:
          hide_credentials: true
      - name: acl
        config:
          hide_groups_header: true
          allow:
            - anon
            - service_role

  - name: storage-v1
    _comment: "Storage: /storage/v1/* -> http://supabase-storage:5000/*"
    url: http://supabase-storage:5000/
    routes:
      - name: storage-v1-all
        strip_path: true
        paths:
          - /storage/v1/
    plugins:
      - name: cors
      - name: key-auth
        config:
          hide_credentials: false
      - name: acl
        config:
          hide_groups_header: true
          allow:
            - anon
            - service_role
EOF

# Criar migração inicial
cat > supabase/migrations/001_initial_schema.sql << 'EOF'
-- Executar o schema completo definido na documentação
-- (Conteúdo do schema SQL seria inserido aqui)
EOF

# Configurar Nginx
cat > nginx/nginx.conf << 'EOF'
events {
    worker_connections 1024;
}

http {
    upstream supabase {
        server supabase-kong:8000;
    }

    upstream webagent {
        server webagent-app:3000;
    }

    server {
        listen 80;
        server_name localhost;

        # WebAgent Application
        location / {
            proxy_pass http://webagent;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Supabase API
        location /api/supabase/ {
            rewrite ^/api/supabase/(.*) /$1 break;
            proxy_pass http://supabase;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Health check
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
    }
}
EOF

echo "🐳 Iniciando containers Docker..."
docker-compose up -d

echo "⏳ Aguardando serviços ficarem prontos..."
sleep 30

# Verificar se os serviços estão rodando
echo "🔍 Verificando status dos serviços..."
docker-compose ps

# Executar migrações
echo "📊 Executando migrações do banco de dados..."
# docker-compose exec supabase-db psql -U postgres -d postgres -f /docker-entrypoint-initdb.d/001_initial_schema.sql

echo "✅ Setup concluído!"
echo ""
echo "🌐 Serviços disponíveis:"
echo "   - WebAgent App: http://localhost:3000"
echo "   - Supabase Studio: http://localhost:54323"
echo "   - Supabase API: http://localhost:54321"
echo "   - PostgreSQL: localhost:54322"
echo ""
echo "📝 Próximos passos:"
echo "   1. Acesse o Supabase Studio para configurar o projeto"
echo "   2. Execute as migrações do banco de dados"
echo "   3. Configure as políticas RLS"
echo "   4. Deploy das Edge Functions"
echo ""
echo "🔧 Para parar os serviços: docker-compose down"
echo "🗑️  Para limpar tudo: docker-compose down -v"
```

---

## 📋 CHECKLIST DE IMPLEMENTAÇÃO

### FASES DE DEPLOY:

**FASE 1: SETUP INICIAL** ✅
- [x] Configuração do ambiente Docker
- [x] Setup do Supabase local
- [x] Criação do schema de banco de dados
- [x] Configuração de RLS e políticas
- [ ] Testes de conectividade

**FASE 2: DESENVOLVIMENTO** 🔄
- [ ] Implementação das Edge Functions
- [ ] Configuração do Storage
- [ ] Setup das views materializadas
- [ ] Implementação dos triggers
- [ ] Testes unitários

**FASE 3: INTEGRAÇÃO** ⏳
- [ ] Integração com APIs externas
- [ ] Setup do sistema de cache
- [ ] Configuração de monitoramento
- [ ] Testes de integração
- [ ] Performance testing

**FASE 4: PRODUÇÃO** ⏳
- [ ] Deploy para Supabase Cloud
- [ ] Configuração de DNS
- [ ] Setup de SSL/TLS
- [ ] Monitoramento em produção
- [ ] Backup e recovery

---

## 🎯 CONCLUSÃO

Esta infraestrutura Supabase fornece uma **base sólida e escalável** para o sistema de extração viral WebAgent. Com **PostgreSQL otimizado**, **Edge Functions especializadas**, **Storage organizado** e **Docker integration**, o projeto está preparado para:

- **Processar milhões** de posts virais
- **Analisar tendências** em tempo real
- **Escalar automaticamente** conforme demanda
- **Manter alta disponibilidade** 24/7
- **Garantir segurança** de dados sensíveis

### PRÓXIMOS PASSOS:
1. **Executar setup** com script automatizado
2. **Implementar Edge Functions** específicas
3. **Configurar monitoramento** avançado
4. **Deploy em produção** no Supabase Cloud
5. **Otimizar performance** baseado em métricas reais

---

**🔗 LINKS ÚTEIS:**
- [Supabase Documentation](https://supabase.com/docs)
- [PostgreSQL Performance](https://www.postgresql.org/docs/current/performance-tips.html)
- [Docker Best Practices](https://docs.docker.com/develop/best-practices/)
- [Edge Functions Guide](https://supabase.com/docs/guides/functions)

---

*Documentação criada por **Augment Code Orchestrator V5.0** - Sistema de Infraestrutura Completa para Extração Viral*
```
