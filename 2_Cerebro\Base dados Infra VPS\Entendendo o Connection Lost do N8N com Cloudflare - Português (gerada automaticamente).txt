﻿Olá nesse vídeo eu vou falar com vocês
sobre um problema que vem acontecendo
com alguns usuários do ENEM na versão 1
a 1.0.5 e a 1.1 que aquela mensagem que
aparece de Connection
pessoal deixa bem claro para vocês que
quem é membro da promovo web Talvez não
tenha visto essa mensagem porque o nosso
setup ele já vem com websoft configurado
já há muito tempo porque Outras
aplicações usam um chat útil usa
beisebol também usa muitas explicações
hoje usam websockets para poder fazer
uma interface mais responsiva mais
rápida ali né então quem utiliza para o
novo web não percebeu esse problema só
que na sexta-feira o meu amigo Cláudio
Balbino agradecer ao Cláudio lá da
comunidade vamos automatizar Ele me
trouxe um cenário
de um cliente dele que usa promover tava
usando ele é promover e que tava dando a
mensagem do Conection Lost eu falei Opa
vamos ver o que que é isso daí né e
consegui identificar um padrão ali né
Inclusive eu abri uma ixi eu comentei
numa Ixi hoje lá no github do nethene
até inclusive com um print da tela dele
mostrando né o cenário ainda não tinha
um print meu aqui mas eu consegui
identificar esse problema quem tá usando
Coldplay e ativa a nuvem do Close flaire
o proxy do closefe ativamente Ali
começou a receber essa mensagem aí do
Connection Lost né então é importante
entender pessoal que não é um problema
de configuração a nível de servidor
tanto faz se você usa com promover ou
não usa para promover você vai receber
essa mensagem porque é uma limitação do
próprio cloudifler né então aqui nessa
nessa eixo que eu abri eu comentei com o
pessoal que dona Gene com o John e com o
neto que eles nessa questão aqui do
proxy quando você habilita para o óxido
de Flair ele vai manter sua conexão
aberta inicialmente eu coloquei aqui 60
segundos mas ele mantém por 100 segundos
inclusive pessoal isso é documentado
aqui no Cloud Flair né então aqui ele
fala qualquer conexão htp é feita por
mais de 100 segundos sem trafegar dados
Ele vai cortar a conexão e é justamente
do que acontece né então se você deixar
o seu workflow aqui aberto
e por 100 segundos não trafegar nenhum
dado no websocket o Chrome corta conexão
aparece para você aqui a mensagem de
Connection Lost né E quem quiser fazer o
teste pessoal vou mostrar para vocês
aqui o caminho é só você clicar aqui
inspecionar
você vai vir aqui em Network vai clicar
aqui em WS que vai filtrar somente os
websockets e vai recarregar a página
você vai ver que vai aparecer aqui ó um
push eu vou ter que criar um cronômetro
aqui ó para vocês verem enquanto eu vou
explicando que eu vou deixar ele rodando
eu estou com cloudifler desconectado tá
então a gente vai estar desconectado se
você for olhar que eles não registros Ó
o meu web workflose aqui ó tá com o
Coldplay o proxy desabilitado que que
acontece pessoal quando não usa o cloud
Flair eu não estou usando o navegador
entra em contato direto com o meu
servidor web E aí eu não tenho para
trabalhar ali nesse meio se o meu
servidor web já está configurado para
aceitar conexões do websocket né então
no caso do Trap aqui já vem ativamente
isso se você usa o indivinex por exemplo
tem que ir lá e fazer uma configuração
né mas caso você não tem Coldplay Então
essa conexão é aberta e ela fica aberto
o tempo necessário enquanto você tiver
com flor aberto a sua conexão vai ficar
aberta
caso você habilite a nuvem do cloudifler
acontece o seguinte ele é um proxy
também então o seu navegador se conecta
com Coldplay se conecta no seu servidor
web o seu servidor não tem um time out
mais tem então se você ficar sem
trafegado mata conexão E aí dá aquela
mensagem para vocês então eu fiz
bastante teste Fiz alguns testes ontem
Fiz alguns testes hoje também Chega essa
conclusão e comentei aqui no Git hublet
ainda pode ver pessoal que eu já tô aqui
quando bater aqui um minuto e 40 vai dar
100 segundos né E você vai ver que ele
não vai criar uma linha mais aqui ó ou
seja conexão tá ativa por mais de 100
segundos sem receber nenhum dado não
mexer nada aqui do meu workflow né Tá
tranquilo que falou Ah vai bater aqui um
minuto 40 e ele não vai criar uma linha
ou seja ele não vai matar a conexão
porque o Enem assim que a conexão é
cancelada ela é cortada automaticamente
ele cria uma nova conexão
Então você só recebe uma mensagem de
Connection Lost mas não atrapalha aí o
seu dia a dia né mas enfim não é um
comportamento legal tá mexendo ali você
vê lá um Connection Lost né não é esse
objetivo tem que deixar a ferramenta
trabalhar direito né Então pode ir ó já
tô aqui há dois minutos já deu bateu
mais de 100 segundos aí e não matou a
minha conexão porque eu tô com uma
nuvenzinha né conectado ali então se
você clicar no seu websoft por exemplo
você clicar em readers você vai ver que
não tem nenhum reader informando aqui
que essa conexão tá passando pelo tá
passando diretamente lá para o meu Trap
E aí não tem problema então vou vir aqui
ó e eu vou habilitar agora o cloud
flaire nesse meu domínio então eu vou
ligar o proxy aqui ó proxy com proxy né
Vou salvar
e eu vou dar uns F5 aqui tá gente eu vou
recarregar aqui essa página algumas
vezes para poder efetivamente fazer a
conexão ali com o Coldplay né então
vamos ver algo que aparecia aqui ameaçar
o river de novo para vocês verem então
agora vamos ver ainda ainda não tá
passando por dificuldade deixa eu limpar
aqui a sessão
e eu vou limpar também o Cash ele vai
até deslogar eu acho do meu channe quem
quiser reproduzir esse teste aqui é
legal até que você entendeu Como é que
as coisas funcionam tá gente o websock
só é aberto quando você tá com o work
ativo Então como eu não tô com agora
estou ativo Vamos ver que isso vai vir
algum alguma informação do Coldplay não
veio ainda então ainda não tá passando
lá pelo proxy do Coldplay enquanto eu
resolvo isso aqui pessoal eu vou
continuar explicando para vocês então
como o clother não proxy reverso ele vai
fazer esse filtro né então você vai
configurar o seu servidor o seu servidor
tá devidamente configurado porém ainda
não vai funcionar direito porque oferta
no meio atrapalhando e pessoal o que que
aconteceu nesse período esse meio tempo
aí né eu comentei aqui na eixo né do
github e até um outro amigo ali do
Portugal também comentou que ele
desabilitou o próprio grupo de flerta
que ele funciona mas não é esse
comportamento quer eu uso para o de Flap
até segurança para ter o proxy
habilitado né e prontamente o tanto de
off quanto net Roy Eles já criaram até
uma porrequest aqui eu passei para eles
ali um artigo no stakehover flow
mostrando a solução foi a melhor pessoa
que a gente comprou ali né de criar um
ping então já que o problema do Coldplay
é a conexão ficar aberta sem trafegar
dados então e como só vai trafegar dados
na hora que eu executar o workflow eu
posso muito bem tá montando meu workflow
tá carregando eu tô ativo na tela mas
sem apertar o botão executar E aí pode
dar o connect ao Lost Então qual que vai
qual que foi a solução que eles
encontraram aqui e eu gostei da solução
eles vão fazer um ping então aqui por
exemplo até o Network criou aqui uma
Quest
falando que ele corrigiu ali que ele vai
fazer um ping na no websoft a cada x
segundos né Para poder não resolver esse
problema aí do
clodoflair né E como é que é legal
pessoal perguntei preço ele ia publicar
isso no valor Rubi Ele criou uma tag
específica no docker Hub só para poder
testar eu já testei deu certo tá essa
essa que ele fez então na próxima
versão do nethene já vai ver essa
correção né de a conexão ficar aberta
por mais que eu não esteja executando
mas eu tô ali interagindo com a tela ele
vai ficar pingando o websocket só para o
Claudio Flair não considerar que aquela
conexão tá parada né Não só cor de Farma
qualquer tipo de proxy reverso que faça
esse tipo de filtro Então foi uma
solução elegante
que eles encontraram ali né para poder
resolver esse problema né então eu vou
poder continuar utilizando todos os
recursos de segurança que não é para o
novo Vibe tem curso de Flyer para você
poder proteger o seu n né para você
poder deixar ele mais seguro aqui
pessoal pode ver qualquer recarreguei
aqui a página deu tempo do proxy
aplicado então você pode ver aqui ó que
até tem um server aqui ó agora tá vendo
como cloudler né então o que que eu vou
fazer eu vou recarregar essa página
e eu vou clicar em iniciar aqui ó
apareceu o push ali eu cliquei no
iniciar eu vou contar aqui um minuto e
40 Tá eu vou avançar aqui o vídeo para
economizar o tempo de vocês mas assim
que chegar próximo de muito 40 vocês vão
ver que ele vai criar mais uma linha ali
pessoal então tá chegando perto aqui de
um minuto 40 que são 100 segundos e você
vai ver que essa conexão que ela vai ser
morta para poder que vai criar uma nova
conexão aqui com o seu websolut vai
aparecer a mensagem lá em cima conecte
um Lost ela vai chegar aqui no número 40
ele vai matar essa conexão e vai criar
outra né confirmando ali a nossa lá ele
matou a conexão já criou outra
justamente no minuto 40 né então é isso
que tá atrapalhando mesmo você
configurando o servidor mesmo se você tá
tudo ajustado ainda assim um Coldplay
vai criar essa esse comportamento né
então é bom entender que a galera da net
aí fez um bom trabalho de prontamente
responder aqui né a essa questão aqui
desse comentário que eu fiz sobre esse
ponto
de ajuste né de clube de fé tem esse
item documentado e eles prontamente
criaram que essa Quest e já vai
vir aí nas próximas versões do docker do
docker então eu gostei muito achei que
foi bem interessante essa abordagem
deles né eles responderem assim tão
prontamente né E quem tá com esse
problema aí por enquanto eu aconselho a
desabilitar a nuvem do Gold Player até
sair a próxima versão aí você vai poder
habilitar novamente a nuvem Flair para
poder utilizar os recursos né que dá
para mover pessoal a gente tem o curso
de fé onde você pode aplicar regras de
segurança no seu webrou aqui aplicar
regra de segurança no seu próprio
projeto aprender como é que você define
regras né por completo Como que você
move o seu domínio quando ele tiver
procuro de flerta também e então fica
esse recado Tá gente dá para você não
aparece o conectiológico o nosso
servidor web já é pronto para isso já há
muito tempo mas se você usa Coldplay ele
vai atrapalhar você então é só
desabilitar ele por enquanto e aguardar
o rins aí Nessa versão do netine com
essa correção tá então é só acompanhar
que o nosso grupo eu vou deixar para
vocês aqui na descrição o nosso grupo do
WhatsApp também o grupo do telegram para
que vocês entrem e acompanha ali o
andamento
pessoal um abraço e até a próxima