﻿automatizador tudo beleza Hugo aqui da
Auto Tic e nesse vídeo eu vou te mostrar
algo que provavelmente você já viu ou
verá em um outro canal ou vídeo aqui do
YouTube que é como instalar o n
diretamente do seu servidor em menos de
dois minutos usando apenas uma linha de
código tá você não vai precisar ter
domínio cepenho nada disso só vai
precisar mesmo ter um VPS né que é um
servidor privado um servidor virtual
privado Inclusive eu vou te mostrar como
você cria um servidor desse na rede Tá
bom então desde já já curta aí o vídeo
se inscreva no canal ative as
notificações também para continuar
recebendo notificações acerca do ntm
aqui no canal da autoestima Tá bom mas
vamos partir agora para a prática
focando aqui na tela do meu notebook eu
estou com o painel da redenes aberto tá
para você poder fazer o cadastro basta
você clicar no link que vai estar aí na
descrição ou nos comentários fixados do
vídeo tá E aí você vai receber um
crédito de 20 Euros para poder puxar aí
os primeiros meses de uso dos Servidores
Tá bom então primeira coisa a ser feita
é criar um projeto né aqui na rede Você
pode criar vários projetos no caso eu
criei aqui o curso setup e ntn tá E aí
você vai clicar aqui em add serve ok
você vai escolher a localização do teu
servidor Geralmente eu escolher esse
aqui né que a Finlândia te chama
operacional Ubuntu de preferência A
20.04 tá porque a 22.04 geralmente ela
tem algumas interrupções por questão de
configuração de Kernel né e há 20 pontos
04 ela roda mais liso Ok
a configuração servidor pode ser a mais
básica possível então escolha essa cx11
tá bom
E para finalizar você só vai dar um nome
aqui para o seu servidor no caso aqui eu
vou colocar nitm tá bom e vou dar um
create
ele vai criar agora aqui o servidor
ó
é muito rápido
e ele vai enviar para você os dados de
acesso do servidor por e-mail tá ó
é que vai ter o IP do servidor
o usuário que é root e a senha tá então
primeira coisa que a gente vai fazer
aqui é acessar o servidor já para poder
fazer alteração lá de senha né que é uma
medida de segurança e em seguida a gente
executar o nosso instalador tá então
eu vou copiar aqui o IP e aí a gente não
precisa também de um cliente SSH né como
um bit vai você pode usar o próprio
terminal do seu sistema operacional para
isso ok então aqui no Windows né é o CMD
eu vou abrir aqui
já vou jogar aquele para o lado tá E aí
qual é o comando aqui Inclusive eu já
vou até deixar aqui o relógio né Para
comprovar para você que é menos de dois
minutos a instalação do onition no seu
servidor tá então eu vou colocar aqui
primeiro comando
SSH opa
SSH tá aí vou colocar o usuário que é
root
arroba e aqui eu colo o IP do servidor
vou dar um enter
aí aqui eu vou digitar Yes ok
E aí ele vai pedir para eu informar a
senha qual é a senha é a que tá lá no
e-mail certo eu copiei aqui
volta aqui para o meu prompt né E aí
cola aqui com o botão direito dou enter
pronto aí vai pedir para informar
novamente a senha atual clico com o
botão direito do enter E aí vou informar
uma nova senha
beleza sem alterada aí eu posso dar um
Clear aqui ok e agora a única coisa que
iremos fazer é executar o nosso
instalador através dessa URL aqui ó tá
aliás desse comando que inclusive também
vai estar aí na descrição do vídeo tá
que é o nosso instalador do ntn em
docker Tá ok então ó simplesmente vou
colar aqui e vou dar um enter tá e ele
vai começar a instalação já vou rodar
aqui o nosso tempo aqui para você ver
que em menos de dois minutos ele vai
instalar o ntn e o Enem já vai ficar
disponível para você poder utilizar tá
Enquanto isso você pode tomar uma água
um cafezinho e deixar aqui que ele vai
fazer tudo automático para você tá
ó em menos de um minuto ele já fez a
configuração prévia do Servidor e agora
já está instalando o n e n Ok seja menos
de um minuto ele já começou a instalar o
n
daqui a pouquinho já vai concluir ó
um minuto e 10 segundos
Pronto Olha só terminou né Se a gente
fosse desconsiderar aqui né esses 14
segundos aqui seria basicamente um
minuto ou seja um pouquinho mais de um
minuto que levou para instalar e
configurar o Enem para você
automaticamente tá E aí você pode vir
aqui ó é clicar em control e depois
clicar aqui ó em cima da URL né que fica
disponível seu ntn que é o IP do seu
servidor na porta 5678 tá então quando
eu clicar aqui ó
ele já vai abrir aqui
a o assistente né do ntn para você criar
aqui o seu cadastro tá bom pronto e aí
eu coloco aqui eu vou colocar qualquer
dados aqui tá só para exemplificar você
vai colocar o seu e-mail a um e-mail
válido você vai colocar o seu nome
sobrenome
e uma senha forte tá é tem que ter no
mínimo 8 caracteres é uma tem que ser
maiúscula né tem que ter números e
semelhante tem que ter caracteres
especiais também tá bom Vou botar aqui
uma qualquer também
Beleza eu vou dar um nexo aqui
E aí vou responder aqui um questionário
bem rápido do ntn tá
pode responder aqui também qualquer
coisa se você quiser
e pronto bonitinho aqui já está pronto
tá até botar já o nome aqui ó meu
primeiro
E aí para você saber se o seu Enem de
fato está funcionando perfeitamente
primeira coisa é testar o webbook
então você pode adicionar aqui o note do
Hulk tá E aí você copia aqui a URL de
teste do Web Hulk e já clica aqui em
Listen Apple ficar ali em modo de escuta
e aí você
abre uma nova aba cola aqui a URL e Dá
um enter tá para ver se o Web Hulk vai
escutar sendo que repare que é já que a
gente não configuramos domínio já que a
gente não configurou o domínio é a gente
vai ter que informar aqui o IP do
servidor ele vai deixar como local roxo
tá mas se você botar louca roxa não vai
funcionar Ok geralmente quando você tá
rodando ali é localmente né no seu
próprio computador como isso aqui está
no servidor então no local no local roxo
eu vou colocar o IP
do Servidor tá bom E aí sim ele vai
funcionar
ó apareceu aqui ó workflow foi startado
e quando a gente vem aqui no
Pronto ele recebeu lá a o evento né do
acesso ao URL ou seja o enem está
funcionando
perfeitamente Beleza então é isso aí tá
lembrando que esse instalador né para
instalar o Enem de forma simples né
claro que o ntn não vai ficar 100%
configurado como por exemplo um conexão
segura né com o seu domínio aqui
personalizado tá porque é porque o
objetivo aqui é você poder instalar o
Enem da forma mais simples sem precisar
ter nenhum recurso só o servidor mesmo
tá bom mas se você quiser aprender mais
a como configurar o seu n né Não só
instalar mas a configurar né de uma
forma mais profissional mais robusta nós
temos o curso completo de setup tá onde
você vai aprender como eu falei não só
instalar mas
no seu próprio servidor seja na tecno
tecnologia docker ou em node GS tá aqui
nesse instalador a gente usou docker Tá
bom mas também você pode instalar e
configurar em node de S beleza e aí você
vai passar aqui pelos módulos do curso
né é que envolvem infraestrutura ou se a
gente vai te ensinar a criar o servidor
né como a gente mostrou aqui Vamos
ensinar como fazer a configuração do
subdomínios né Para que o seu N pode
possa ter lá um domínio né Por exemplo
ntn ponto pulando.com.br né temos o
módulo onde a gente passa aqui pela
configuração em docker né então a gente
vai ensinar você a instalar o porteño Né
o portener é como se fosse um
administrador ali de container esse
instalador aqui que você está usando é
um container é um n é em formato de
container em doc tá é claro você pode
gerenciar ele pelo console né ou você
pode usar uma interface né mais amigável
que é o cortene Né para você poder
gerenciar ali o seu container que é
muito mais fácil a manutenção tá e a
gente vai te ensinar a criar os
templates né Para Sempre que você for
subir um container já tem ali um script
Pronto né a gente vai ensinar como criar
a estecas é você fazer o Deploy desse
container Vamos mostrar os testes da
instância para ver se tá tudo ok se tá
funcionando Vamos ensinar como você
criar multi Instância Ou seja você ter
múltiplos ntms né múltiplas instâncias
de ntn em um único servidor cada
Instância ficaria em um container tá
Essa é a vantagem do docker porque ele
separa por container múltiplas
aplicações Ok vamos ensinar também como
você atualizar a versão né seja fazer um
upgrade da sua versão do n ou fazer um
downgrade
que é retroceder uma versão
também Vamos ensinar como fazer todo
esse processo de setup também node de S
tá é claro que Note S tem algumas
configurações a mais aqui mas o conceito
é o mesmo tá bom E também o módulo de
configurações adicionais né você
aprender a configurar o seu smtp a
habilitar o log do enetn a saber qual é
a diferença da execução Man e da
execução on tá E também aprender a
colocar o seu n em modo fila tá isso
aqui é para que o seu Enem possa
comportar
grandes demandas de execuções né sem
travar sem ficar lento Ou seja é você
saber escalar o seu ntn a gente ensina
também aqui em nosso curso tá bom aí
aqui você vai ter as respostas para as
principais dúvidas né que muitos alunos
têm né antes mesmo de se matricular no
curso Então eu recomendo você ler tá bom
E aqui temos a matrícula né então se
você quer se inscrever para poder
aprender de fato a dominar essa parte de
infra do ntn você pode estar se
matriculando pelo valor de 894 à vista
ou parcelado em até 12 vezes de 89 e 22
centavos com uma pequena correção de
juros tá lembrando que para esse mês de
maio nós temos um cupom com 20% de
desconto Inclusive eu vou deixar o
código aí na descrição do vídeo onde
você vai poder se matricular com esse
desconto Tá bom então é isso aí Espero
que você tenha gostado desse vídeo
novamente eu peço aí o teu like se você
ainda não deu like no início do vídeo dá
um like aí agora e se inscreva no canal
para você estar acompanhando outros
insights incríveis como esses a respeito
do ntn tá bom um forte abraço e até mais