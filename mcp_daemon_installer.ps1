# MCP Daemon Installer - Augment Code Orchestrator V5.0
# Instala e configura o MCP Daemon Centralizado como serviço Windows

param(
    [switch]$Install,
    [switch]$Uninstall,
    [switch]$Start,
    [switch]$Stop,
    [switch]$Status
)

$ServiceName = "MCPDaemon"
$ServiceDisplayName = "MCP Daemon Centralizado"
$ServiceDescription = "Servidor centralizado para gerenciar todos os servidores MCP"
$WorkingDirectory = $PWD.Path
$PythonScript = Join-Path $WorkingDirectory "mcp_daemon_centralizado.py"

Write-Host "=== MCP DAEMON INSTALLER ===" -ForegroundColor Cyan

function Install-MCPDaemon {
    Write-Host "Instalando MCP Daemon como serviço Windows..." -ForegroundColor Yellow
    
    # Verificar se Python está instalado
    try {
        $pythonVersion = python --version 2>&1
        Write-Host "✓ Python encontrado: $pythonVersion" -ForegroundColor Green
    }
    catch {
        Write-Host "✗ Python não encontrado! Instale Python primeiro." -ForegroundColor Red
        return
    }
    
    # Instalar dependências Python
    Write-Host "Instalando dependências Python..." -ForegroundColor Yellow
    pip install psutil websockets asyncio
    
    # Criar script de wrapper para o serviço
    $WrapperScript = @"
import sys
import os
import subprocess

# Adicionar diretório atual ao path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Executar daemon
if __name__ == "__main__":
    from mcp_daemon_centralizado import main
    main()
"@
    
    $WrapperPath = Join-Path $WorkingDirectory "mcp_daemon_service.py"
    $WrapperScript | Out-File -FilePath $WrapperPath -Encoding UTF8
    
    # Criar configuração do serviço usando NSSM
    Write-Host "Configurando serviço Windows..." -ForegroundColor Yellow
    
    # Baixar NSSM se não existir
    $nssmPath = Join-Path $WorkingDirectory "nssm.exe"
    if (-not (Test-Path $nssmPath)) {
        Write-Host "Baixando NSSM..." -ForegroundColor Yellow
        Invoke-WebRequest -Uri "https://nssm.cc/release/nssm-2.24.zip" -OutFile "nssm.zip"
        Expand-Archive "nssm.zip" -DestinationPath "."
        Copy-Item "nssm-2.24\win64\nssm.exe" $nssmPath
        Remove-Item "nssm.zip", "nssm-2.24" -Recurse -Force
    }
    
    # Instalar serviço
    & $nssmPath install $ServiceName python $WrapperPath
    & $nssmPath set $ServiceName DisplayName $ServiceDisplayName
    & $nssmPath set $ServiceName Description $ServiceDescription
    & $nssmPath set $ServiceName AppDirectory $WorkingDirectory
    & $nssmPath set $ServiceName Start SERVICE_AUTO_START
    
    Write-Host "✓ Serviço MCP Daemon instalado com sucesso!" -ForegroundColor Green
    Write-Host "Use 'Start-Service $ServiceName' para iniciar" -ForegroundColor Cyan
}

function Uninstall-MCPDaemon {
    Write-Host "Removendo serviço MCP Daemon..." -ForegroundColor Yellow
    
    # Parar serviço se estiver rodando
    Stop-Service $ServiceName -ErrorAction SilentlyContinue
    
    # Remover serviço
    $nssmPath = Join-Path $WorkingDirectory "nssm.exe"
    if (Test-Path $nssmPath) {
        & $nssmPath remove $ServiceName confirm
    }
    
    Write-Host "✓ Serviço MCP Daemon removido!" -ForegroundColor Green
}

function Start-MCPDaemon {
    Write-Host "Iniciando MCP Daemon..." -ForegroundColor Yellow
    Start-Service $ServiceName
    Write-Host "✓ MCP Daemon iniciado!" -ForegroundColor Green
}

function Stop-MCPDaemon {
    Write-Host "Parando MCP Daemon..." -ForegroundColor Yellow
    Stop-Service $ServiceName
    Write-Host "✓ MCP Daemon parado!" -ForegroundColor Green
}

function Get-MCPDaemonStatus {
    Write-Host "Status do MCP Daemon:" -ForegroundColor Cyan
    
    try {
        $service = Get-Service $ServiceName -ErrorAction Stop
        Write-Host "Serviço: $($service.Status)" -ForegroundColor $(if ($service.Status -eq "Running") { "Green" } else { "Yellow" })
        
        # Verificar processos
        $mcpProcesses = Get-Process python -ErrorAction SilentlyContinue | Where-Object {
            $_.CommandLine -match "mcp_daemon"
        }
        Write-Host "Processos MCP Daemon: $($mcpProcesses.Count)" -ForegroundColor White
        
        # Verificar porta
        $connection = Get-NetTCPConnection -LocalPort 7999 -ErrorAction SilentlyContinue
        if ($connection) {
            Write-Host "Porta 7999: Ativa" -ForegroundColor Green
        } else {
            Write-Host "Porta 7999: Inativa" -ForegroundColor Red
        }
        
        # Verificar log
        $logPath = Join-Path $WorkingDirectory "mcp_daemon.log"
        if (Test-Path $logPath) {
            $lastLines = Get-Content $logPath -Tail 5
            Write-Host "`nÚltimas linhas do log:" -ForegroundColor Cyan
            $lastLines | ForEach-Object { Write-Host "  $_" -ForegroundColor Gray }
        }
    }
    catch {
        Write-Host "Serviço não encontrado ou não instalado" -ForegroundColor Red
    }
}

# Executar ação baseada nos parâmetros
if ($Install) {
    Install-MCPDaemon
}
elseif ($Uninstall) {
    Uninstall-MCPDaemon
}
elseif ($Start) {
    Start-MCPDaemon
}
elseif ($Stop) {
    Stop-MCPDaemon
}
elseif ($Status) {
    Get-MCPDaemonStatus
}
else {
    Write-Host "Uso:" -ForegroundColor White
    Write-Host "  .\mcp_daemon_installer.ps1 -Install    # Instalar serviço" -ForegroundColor Cyan
    Write-Host "  .\mcp_daemon_installer.ps1 -Start      # Iniciar serviço" -ForegroundColor Cyan
    Write-Host "  .\mcp_daemon_installer.ps1 -Stop       # Parar serviço" -ForegroundColor Cyan
    Write-Host "  .\mcp_daemon_installer.ps1 -Status     # Ver status" -ForegroundColor Cyan
    Write-Host "  .\mcp_daemon_installer.ps1 -Uninstall  # Remover serviço" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Exemplo de instalação completa:" -ForegroundColor Yellow
    Write-Host "  .\mcp_daemon_installer.ps1 -Install" -ForegroundColor White
    Write-Host "  .\mcp_daemon_installer.ps1 -Start" -ForegroundColor White
    Write-Host "  .\mcp_daemon_installer.ps1 -Status" -ForegroundColor White
}

# Criar configuração para VSCode
if ($Install) {
    Write-Host "`nCriando configuração para VSCode..." -ForegroundColor Yellow
    
    $vscodeConfig = @{
        "mcp" = @{
            "daemon_mode" = $true
            "daemon_url" = "ws://localhost:7999"
            "servers" = @{
                "memory" = @{
                    "proxy" = $true
                }
                "everything" = @{
                    "proxy" = $true
                }
                "github" = @{
                    "proxy" = $true
                }
                "sequential-thinking" = @{
                    "proxy" = $true
                }
            }
        }
    }
    
    $configPath = Join-Path $WorkingDirectory "vscode_mcp_config.json"
    $vscodeConfig | ConvertTo-Json -Depth 3 | Out-File $configPath -Encoding UTF8
    
    Write-Host "✓ Configuração VSCode criada: $configPath" -ForegroundColor Green
    Write-Host ""
    Write-Host "PRÓXIMOS PASSOS:" -ForegroundColor Cyan
    Write-Host "1. Copie o conteúdo de vscode_mcp_config.json para suas configurações do VSCode" -ForegroundColor White
    Write-Host "2. Remova configurações MCP do Claude Desktop" -ForegroundColor White
    Write-Host "3. Reinicie VSCode e Claude Desktop" -ForegroundColor White
    Write-Host "4. Execute: .\mcp_daemon_installer.ps1 -Status para verificar" -ForegroundColor White
}
