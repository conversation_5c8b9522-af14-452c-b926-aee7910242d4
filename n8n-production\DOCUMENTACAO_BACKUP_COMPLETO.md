# 🔄 **SISTEMA DE BACKUP COMPLETO N8N**

## 📋 **VISÃO GERAL**

Sistema completo de backup e restore para N8N que preserva **TODOS** os dados:
- ✅ **Workflows** (todos os fluxos criados)
- ✅ **Credenciais** (todas as credenciais salvas)
- ✅ **Execuções** (histórico completo)
- ✅ **Configurações** (settings do sistema)
- ✅ **PostgreSQL** (banco de dados completo)
- ✅ **Redis** (cache, filas, sessões)
- ✅ **Volumes Docker** (arquivos locais)
- ✅ **Configurações** (docker-compose, .env)

---

## 🚀 **SCRIPTS DISPONÍVEIS**

### **1. 🔄 Backup Completo**
```bash
./scripts/backup-n8n-complete.sh
```
- Cria backup completo de todos os dados
- Compacta automaticamente
- Gera manifesto detalhado
- Remove backups antigos

### **2. 🔄 <PERSON>ore <PERSON>**
```bash
./scripts/restore-n8n-complete.sh nome_do_backup
```
- Rest<PERSON>ra todos os dados de um backup
- Para serviços automaticamente
- Valida integridade
- Reinicia serviços

### **3. ⏰ Configurar Backup Automático**
```bash
./scripts/setup-backup-automation.sh
```
- Menu interativo para agendamento
- Configuração de cron jobs
- Opções de retenção
- Logs automatizados

### **4. 📦 Gerenciar Backups**
```bash
./scripts/manage-backups.sh
```
- Listar backups existentes
- Verificar integridade
- Remover backups específicos
- Estatísticas detalhadas

---

## 📁 **ESTRUTURA DO BACKUP**

### **Conteúdo Completo:**
```
n8n_backup_YYYYMMDD_HHMMSS/
├── 📄 BACKUP_MANIFEST.md           # Manifesto detalhado
├── 🗄️  postgresql_dump.sql.gz       # Banco PostgreSQL completo
├── 🔄 redis_dump.rdb               # Cache Redis completo
├── 📁 volumes/                     # Volumes Docker
│   ├── n8n_data.tar.gz           # Dados N8N (workflows, credenciais)
│   ├── postgres_data.tar.gz      # Dados PostgreSQL
│   └── redis_data.tar.gz          # Dados Redis
├── 📁 configs/                     # Configurações
│   ├── docker-compose.yml        # Configuração Docker
│   ├── .env                       # Variáveis de ambiente
│   └── outros arquivos de config
└── 📁 metadata/                    # Metadados do sistema
    ├── docker_version.txt
    ├── n8n_version.txt
    ├── system_info.txt
    └── backup_timestamp.txt
```

---

## 🔧 **INSTALAÇÃO E CONFIGURAÇÃO**

### **1. Preparar Sistema de Backup:**
```bash
# Criar diretório de backups
mkdir -p ./backups

# Tornar scripts executáveis (Linux/Mac)
chmod +x scripts/backup-n8n-complete.sh
chmod +x scripts/restore-n8n-complete.sh
chmod +x scripts/setup-backup-automation.sh
chmod +x scripts/manage-backups.sh
```

### **2. Configurar Backup Automático:**
```bash
# Executar configurador interativo
./scripts/setup-backup-automation.sh

# Opções disponíveis:
# - Diário às 2:00 AM (Recomendado)
# - A cada 6 horas
# - Semanal (Domingo às 3:00 AM)
# - Personalizado
```

### **3. Testar Sistema:**
```bash
# Criar primeiro backup
./scripts/backup-n8n-complete.sh

# Verificar backup criado
./scripts/manage-backups.sh
```

---

## 📊 **CONFIGURAÇÕES AVANÇADAS**

### **Variáveis de Ambiente:**
```bash
# Diretório de backup (padrão: ./backups)
export BACKUP_DIR="/caminho/para/backups"

# Retenção em dias (padrão: 30)
export RETENTION_DAYS="60"

# Configurações PostgreSQL
export POSTGRES_USER="n8n_user"
export POSTGRES_DB="n8n"
```

### **Agendamentos Cron Recomendados:**
```bash
# Backup diário às 2:00 AM
0 2 * * * /caminho/para/backup-wrapper.sh

# Backup a cada 6 horas
0 */6 * * * /caminho/para/backup-wrapper.sh

# Backup semanal (Domingo às 3:00 AM)
0 3 * * 0 /caminho/para/backup-wrapper.sh
```

---

## 🔍 **COMO USAR**

### **Criar Backup Manual:**
```bash
# Backup completo agora
./scripts/backup-n8n-complete.sh

# Backup será salvo em: ./backups/n8n_backup_YYYYMMDD_HHMMSS.tar.gz
```

### **Restaurar Backup:**
```bash
# Listar backups disponíveis
./scripts/manage-backups.sh

# Restaurar backup específico
./scripts/restore-n8n-complete.sh n8n_backup_20240125_140000

# ⚠️ ATENÇÃO: Irá sobrescrever todos os dados atuais!
```

### **Verificar Integridade:**
```bash
# Verificar se backup está íntegro
./scripts/manage-backups.sh
# Escolher opção 3: Verificar integridade
```

### **Gerenciar Backups:**
```bash
# Menu completo de gerenciamento
./scripts/manage-backups.sh

# Opções disponíveis:
# 1. Listar backups
# 2. Informações detalhadas
# 3. Verificar integridade
# 4. Remover backup específico
# 5. Limpeza automática
# 6. Estatísticas
# 7. Criar backup agora
# 8. Alterar diretório
```

---

## 🛡️ **SEGURANÇA E BOAS PRÁTICAS**

### **Recomendações de Segurança:**
1. **Armazenamento Externo**: Copie backups para local externo
2. **Criptografia**: Considere criptografar backups sensíveis
3. **Teste Regular**: Teste restore periodicamente
4. **Monitoramento**: Configure alertas para falhas
5. **Retenção**: Mantenha múltiplas versões

### **Exemplo de Backup Externo:**
```bash
# Sincronizar com storage externo
rsync -av ./backups/ user@servidor:/backup/n8n/

# Ou usar cloud storage
aws s3 sync ./backups/ s3://meu-bucket/n8n-backups/
```

### **Script de Verificação Automática:**
```bash
#!/bin/bash
# Verificar último backup
LATEST_BACKUP=$(ls -t ./backups/n8n_backup_*.tar.gz | head -1)
if [ -z "$LATEST_BACKUP" ]; then
    echo "ERRO: Nenhum backup encontrado!"
    exit 1
fi

# Verificar se backup é recente (últimas 25 horas)
if [ $(find ./backups -name "n8n_backup_*.tar.gz" -mtime -1 | wc -l) -eq 0 ]; then
    echo "AVISO: Último backup tem mais de 24 horas!"
fi
```

---

## 🚨 **TROUBLESHOOTING**

### **Problemas Comuns:**

#### **1. Erro: "Docker não está rodando"**
```bash
# Verificar status do Docker
docker info

# Iniciar Docker (Linux)
sudo systemctl start docker

# Iniciar Docker (Windows/Mac)
# Abrir Docker Desktop
```

#### **2. Erro: "Containers não estão rodando"**
```bash
# Verificar status dos containers
docker-compose ps

# Iniciar containers
docker-compose up -d
```

#### **3. Erro: "Falha no backup PostgreSQL"**
```bash
# Verificar conectividade
docker-compose exec postgres pg_isready -U n8n_user -d n8n

# Verificar logs
docker-compose logs postgres

# Verificar variáveis de ambiente
docker-compose config
```

#### **4. Erro: "Espaço em disco insuficiente"**
```bash
# Verificar espaço disponível
df -h

# Limpar backups antigos
./scripts/manage-backups.sh
# Escolher opção 5: Limpeza automática

# Limpar volumes Docker não utilizados
docker system prune -v
```

#### **5. Backup corrompido**
```bash
# Verificar integridade
./scripts/manage-backups.sh
# Escolher opção 3: Verificar integridade

# Testar extração manual
tar -tzf ./backups/backup_suspeito.tar.gz
```

### **Logs de Backup:**
```bash
# Ver logs do backup automático
tail -f ./backups/backup.log

# Ver logs específicos do container
docker-compose logs -f n8n
docker-compose logs -f postgres
docker-compose logs -f redis
```

---

## 📈 **MONITORAMENTO**

### **Verificações Recomendadas:**
- ✅ Backup executado nas últimas 24h
- ✅ Tamanho do backup consistente
- ✅ Integridade dos arquivos
- ✅ Espaço em disco suficiente
- ✅ Logs sem erros

### **Script de Monitoramento:**
```bash
#!/bin/bash
# health-check-backup.sh

echo "=== VERIFICAÇÃO DE BACKUP ==="

# Verificar último backup
LATEST=$(ls -t ./backups/n8n_backup_*.tar.gz 2>/dev/null | head -1)
if [ -n "$LATEST" ]; then
    AGE=$(find ./backups -name "$(basename "$LATEST")" -mtime -1 | wc -l)
    if [ $AGE -gt 0 ]; then
        echo "✅ Backup recente encontrado: $(basename "$LATEST")"
    else
        echo "⚠️ Último backup tem mais de 24h: $(basename "$LATEST")"
    fi
else
    echo "❌ Nenhum backup encontrado!"
fi

# Verificar espaço em disco
DISK_USAGE=$(df . | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -lt 90 ]; then
    echo "✅ Espaço em disco OK: ${DISK_USAGE}%"
else
    echo "⚠️ Espaço em disco baixo: ${DISK_USAGE}%"
fi

echo "=== VERIFICAÇÃO CONCLUÍDA ==="
```

---

## 🎯 **RESUMO DE COMANDOS**

```bash
# Backup manual
./scripts/backup-n8n-complete.sh

# Restore
./scripts/restore-n8n-complete.sh nome_backup

# Configurar automação
./scripts/setup-backup-automation.sh

# Gerenciar backups
./scripts/manage-backups.sh

# Verificar status
./scripts/health-check-complete.sh
```

**🔒 IMPORTANTE**: Sempre teste o processo de restore em ambiente de desenvolvimento antes de usar em produção!
