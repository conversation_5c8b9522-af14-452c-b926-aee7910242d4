const express = require('express');
const { createBullBoard } = require('@bull-board/api');
const { BullAdapter } = require('@bull-board/api/bullAdapter');
const { ExpressAdapter } = require('@bull-board/express');
const Queue = require('bull');

// Configurar Bull Board UI
const serverAdapter = new ExpressAdapter();
serverAdapter.setBasePath('/ui');

// Criar fila de jobs do n8n (deve corresponder ao prefixo usado pelo n8n)
const jobsQueue = new Queue('jobs', {
  redis: {
    host: process.env.REDIS_HOST || 'redis',
    port: process.env.REDIS_PORT || 6379,
  },
  prefix: 'bull' // Prefixo usado pelo n8n
});

// Registrar filas no Bull Board
const { addQueue, removeQueue, setQueues, replaceQueues } = createBullBoard({
  queues: [new BullAdapter(jobsQueue)],
  serverAdapter,
});

// Iniciar servidor Express
const app = express();
app.use('/ui', serverAdapter.getRouter());

// Rota de health check
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// Rota raiz
app.get('/', (req, res) => {
  res.redirect('/ui');
});

const PORT = process.env.PORT || 3002;
app.listen(PORT, () => {
  console.log(`🚀 Bull Board rodando em http://localhost:${PORT}/ui`);
  console.log(`📊 Health check disponivel em http://localhost:${PORT}/health`);
});
