﻿pessoal primeiro agradecer a vocês todos
aí que estão aqui hoje pra gente poder
fazer esse laboratório aqui esse
bate-papo a ideia pessoal da gente ter
laboratório agora na promov web e esse
laboratório hoje tá aberto para todo
mundo né Ele é um pouco diferente né as
câmeras são diferentes tudo diferente
aqui mas é pra gente poder testar mesmo
sistemas testar release sempre ser uma
coisa ou algum sistema que é novo mesmo
né eu vou abordar mais sistemas na
promov web agora e até me cont algum
update que seja importante e e acho que
hoje assim se a gente for pegar mesmo
assim de de 0 a 10 ali Acho que o o o a
Evolution e o NN são os que eu mais
tenho
eh instalação hoje E aí eu nada melhor
do que a gente começar por eles né
pessoal a Evolution vai vai ter uma uma
mudança né ao longo desse mês aí né o
Davis também tá aqui ele vai a gente vai
fazer uma série de eventos uma série de
bate-papos sobre a a mudança né E essa
essa essa mudança pessoal ela ela não
vai impactar tanto no modo como vocês
usam a Evolution mas vai mudar no modo
como vocês instalam principalmente Então
acho que vai ter um ajuste ou outro ali
em relação ao uso sim são grandes
melhorias mas eh a gente vai ter uma a
gente vai dar um passo para trás aí no
sentido
de de como que vocês instalo né até o d
aqui o d tá sem o microfone hoje mas
pessoal a semana inteira vai ter Live
então vocês ficam tranquilo tá tá esse
conteúdo aqui vai ficar gravado para
quem é membro da promov web você que não
é membro da promov web não sei porque
que você não é membro da promov web
ainda mas tá na hora aí de entrar tá
gente e assim o davon fez pessoal uma
coisa que a gente conversou até o Ed
também tá aqui acho que o tá aqui né a
gente conversou já há bastante tempo né
é que a gente tem que dar tempo para as
coisas né pessoal as coisas acontecem
com o tempo que é normalizar payload
ficou muito melhor agora mas isso aí vai
ser assunto para ao longo da semana
melhorou muito a integração né Muito a
integração a integração que eu digo
assim como é que ele se comunic com type
bot como é que se comunica com o chat ut
isso é legal porque acabou que a galera
usou mesmo isso daí né hoje eu tenho
muito mais gente chegando a promov web
por causa de usar Evolution com type bot
não é só porque quer usar uma API de
WhatsApp então é legal que para vocês
verem a atenção que ele dá né de de
Observar isso tanto na comunidade aberta
quanto na comunidade dele também e agora
com a comunidade dele acho tá percebendo
mais ainda né como é que a galera
realmente usa no dia a dia ali a
ferramenta né e vai ter a mesma dor que
eu tenho né de da gente ter o tem que
ser responsável com a galera aí de falar
assim ó hoje pessoal na promovo web
acabei olhei antes de entrar aqui são
3.109 instalações que eu tenho de nhn só
no nosso painel então eu tenho que fazer
essa semana aqui ao longo de todo o
período Beta da Evolution 2 eu vou fazer
Live com vocês eu vou tirar a dúvida de
vocês tá para poder auxiliar vocês
porque eu sei que vocês usam isso aí em
produção um dos meus papéis aqui um dos
compromissos da promov web é vocês fazer
direito né sempre eu sempre bato muito
na tecla vamos fazer rápido vamos mas
faz rápido direito para você não deixar
débito técnico não deixar rápido para
trás aí porque isso aí lá na frente vai
atrapalhar você vai ficar muito pior vai
perder muito mais tempo lá na frente
arrumando alguma coisa que você fez
errado agora né a a ti é assim a infra
sempre foi assim né a gente tem que ter
um certo cuidado com o que a gente faz
então hoje eu vou conversar com vocês
aqui
sobre essas mudanças da Evolution eu
tenho aqui um stack pessoal foi um stack
até que eu peguei na comunidade ali o o
davon montou um grupo né pra gente poder
conversar um pouco mais ali sobre a
Evolution então eu vou disponibilizar
esse stack hoje eu acredito que durante
a semana esse stack vai rodar também aí
nos nos outros grupos Tá mas eu vou
começar por ele porque aqui é o
documento stack eu comento as linhas né
ainda não tá 100% tá e agradecer também
a galera que ontem à noite conseguiu
fazer à noite eu conseguir mexer tanto
ontem mas agradec ali o warion que mexeu
bastante o David que mexeu bastante
pessoal ontem dedicou um tempo legal ali
pr pra api e ao longo acho acredito eu
do período todo Beta todos os os
influenciadores aí toda a galera que tem
comunidade vai fazer um trabalho grande
com vocês né e achei legal a iniciativa
do Davidson de reunir a galera e falar
assim gente vamos fazer um conteúdo aí
para poder a Evolution crescer pessoal
porque hoje nas minhas análises aqui
Type Bot Type bot em primeiro número de
buscas quem tiver o sem Rush aí dá uma
olhadinha Type bot nhn Evolution é o top
TR aí mas assim top 3 gigante mesmo de
busca né então a gente tem que ser um
pouco mais responsável com vocês aí acho
que é o que vocês esperam da gente
também né aí pessoal tem ess aqui deixa
eu para vocês verem aqui já vou falar
para vocês o que foi que mudou aqui na
Evolution o primeiro pessoal é que agora
o vai utilizar eu gostei muito disso a
imagem no mesmo repositório então a
gente conversou né tem outros sistemas
que acabam Quando lança uma versão Major
lançam em outro repositório ou então
criam outra imagem fica complicado né
pessoal eu gerencio vocês sabem que eu
gerencio a imagem do Matic e lá na
imagem do malk a gente tem esse trabalho
de manter sempre a mesma imagem só mudar
a tag né então muito muito legal isso
daqui foi um ponto muito legal mesmo do
Davidson de fazer isso daqui eu acredito
Ô deis você tá sem fone Mas se você
puder confirmar aí a gente vai ter um
Alfa um beta e um release candidate né
então acho que a gente ainda tem três
versões vamos dizer assim antes de ter
uma versão e estável da Evolution né
acho cons chegar aqui repete consegu
conseguiu fica à vontade aí
David pode Repete a pergunta por favor a
gente vai ter alfa beta e o relas
candidate né antes de sair a estável
Uhum é mais ou menos isso eu tô pensando
em fazer fazer o Alfa agora para
resolver o o que já funcionava na
Evolution ter certeza tá funcionando o
Beta vai ser para testar mesmo Aí em
algumas versões diferentes aí a gente
joga o RC aí para começar a chamar a
atenção do pessoal para entrar pra gente
lançar em produção
hum
pess é é aquele alerta de sempre que eu
falo com vocês né o galera da comunidade
do Pr voeb já tá meio que cansado de
ouvir isso aí mas eu tenho que falar
sempre né Eh não
usem é para testar não é para usar tá
gente ainda enquanto for alfa beta todos
tempo que é alfa beta o Davis tem Total
Liberdade de mudar o que ele quiser
ainda né então assim é um período que
ele tá ainda testando avaliando coisas
eh e a gente entende que o software tá
nesse momento então ele tá ali surgindo
a versão dois vamos dizer que o Davis já
tá com 90% Pronto já pelo que ele
mostrou ontem pra gente mas sempre tem
aqueles 10% que é uma liberdade que ele
como desenvolvedor tem de de mexer nela
por isso is o que eu vou falar para
vocês não usem produção ainda porque
pode ser que mude não é que vai mudar
mas pode ser que mude Então a gente tem
que ser responsável com o software né e
evitar isso daí até porque eu quero
mesmo que ele faça o que ele quiser lá
para poder melhorar a ferramenta não
quero que não quero que ele fique preso
né pessoal então essa fase alfa e beta
quando sair algum release candidate né
que a gente chama de Rc geralmente
quando a gente chega no release
candidate pelo próprio nome já diz né já
é uma versão que não vai mais mudar
tanto No pior dos casos vai corrigir
alguma coisa né antes de sair no final
mesmo se ele foros seguir assim vai ser
vai ser bom pra gente né mas geralmente
o software tem o alfa que é só mesmo
para quem gosta de
sofrer Depois tem o Beta que são para
fortes emoções mas já funciona né o
relas candidate já é um pré stavel aí já
é uma coisa que assim beleza numa
situação que você precisa o NN tem lá o
a tag next né que são release candidat
que eles lancham eles chamam de
pré-release né que dá para usar mas não
é o recomendado mas às vezes você
precisa utilizar ela para corrigir algum
problema até que saí o final mesma
versão estável que é o que a gente vai
começar mesmo a a a sugerir pros alunos
Ó gente vamos usar aqui porque aí aí
compensa vocês usarem então para ficar
registrado acho que todo mundo vai falar
sobre isso também ao longo da do mês aí
né Vamos evitar de colocar em produção
não coloca para um cliente seu ainda tá
muito cedo para fazer isso mas faz o seu
laboratório faz o seu teste faz ali o
seu o seu ambiente ali para você poder
brincar com ele
né pode falar
David Opa beleza
pessoal eu tô eu tô mutado ainda não né
não tá tá tá tá tô ouvindo show de bola
gente o acho que o Jefferson né que
falou no início aí que não conseguiu
rodar é porque realmente tá bem
diferente tá inclusive até o o docker aí
tudo a gente teve que subir bem
diferente por causa das das dependências
novas né que agora a gente usa Prisma e
etc s então basicamente aí onde o Luiz
tá mostrando aí na tela eh o Command não
precisa mais essa linha de Command aí
pode tirar ela já tá lá né no docker fy
né é ele tá executando com entry Point
agora então ele tem uma aqui ver
serzinho lá que ele tá rodando Então não
precisa do comand
aí deixa eu pegar aqui o repositório
também gente até até bom para mostrar
para eles tá
bom hoje eu vou liberar a aula que a
gente fala disso do docker né O que que
é o como que que é o entry Point
Fabrício tem que saber Fabrício já fez
os dois esse curso umas duas vezes já
cara você tem que saber o que que é um
ou outro aí é deixa eu ver eu abro o
github El já abre até o o m já cara aí
pessoal é assim deixa eu pegar aqui tá
no tá no 2.0 né David é tá eu tenho a
Brent aí 2.0 E aí lá na no ridm dela tem
a explicação de como instalar com npm
para quem quiser já instalar e testar
via npm direto e ainda falta subir para
ir a steck né mas eu mandei lá no grupo
no grupo de Criadores ontem isso aqui ó
aqui no docker pessoal é esseo daqui né
o o davs ou não ainda
ah docker F tá na raiz aqui né é o
docker f tá na raiz é que acontece
pessoal no caso do Davis como ele
especificou aqui ó o entry Point e aqui
acho que é um dos primeiros pontos que a
gente tem que abordar é que agora
Evolution vai ter um banco de dados né e
é um banco de dados diferente do que era
o mongo né o mongo como é banco no Cico
a estrutura dele você pode ir montando
com o tempo ou ou conforme o uso é a
ideia desse tipo de banco É essa mesmo
né até porque eu tem dependendo do
sistema que vocês for utilizar ele não é
tão linear assim não é tão estruturado
assim né então agora não agora a gente
vai ter um postgree aqui então já tem
que ser uma estrutura já é um banco
relacional ele tem uma tabela tipo de
dado tem um monte de coisa tem
versionamento de campo então quando você
for iniciar Evolution agora pessoal
daqui a pouquinho eu vou falar mais
sobre por com vocês ao longo da semana
também e ele vai fazer isso daqui né ele
vai fazer esse Deploy Database aqui ele
vai antes de iniciar a Evolution ele vai
carregar as migrations vai verificar ali
se tem alguma coisa para poder
executar é interessante porque o
Davidson fez até ontem no no grupo gerou
um um debate al sobre isso daí né e ele
já vai criar o banco nessa dele criar o
banco ele vai criar o banco vai rodar as
migrations então para quem usa por
exemplo chat o é um dos poucos que ainda
usa essa estratégia de você fazer o
update e o próprio entry Point não não
ser o responsável por executar para
vocês a migration E no caso chatu é até
ok Porque você controla mais a ideia
deles eles sempre até eles coloca em
reliz às vezes olha roda lá para você
observar lá esse último update do chatot
cria índice é um índice grande então
para quem tem uma instalação mais antiga
vai perceber um desempenho muito grande
ali e um uma qued desempenho muito
grande na hora de rodar a migration
Então como impacta o o start da
aplicação no caso do shat ut não convém
mas aquilo é na evolu pessoal como vai
ser um a estrutura do banco também é
simples é um banco até que grande o
David Caprichou bem no banco ali mas é
simples né David uma estrutura Simples
então assim Acho que não tem não vai ter
problema de subir a aplicação e rodar a
migration ao mesmo tempo eu optei fazer
dessa forma né e não deixar um comando
para rodar justamente para simplificar
mais aí para simplificar não tem tipo
assim a pessoa não quer não sabe acessar
ou tem que ter um passo a mais entendeu
então eu deixei dessa forma porque assim
basicamente é uma migration que ele vai
rodar ali do banco eh provavelmente
muito dificilmente vai ter mudanças Vai
ter outras né então não tem tipo assim
não vai est impactando realmente em
performance no momento que ele viu que
já rodou essa m ele vai ignorar ela né
então só na primeira vez rodar rodar
vantagens do Prisma né Davis isso com
certeza ele grava lá no banco se vocês
olharem tem um arquivo Prisma migration
lá uma tabela que ele grava o nome da
migration então se ele ver que lá ele
não não roda de novo iso para quem para
quem eu uso o laravel aí por exemplo é
mais ou menos o que é o o eloquent lá né
para quem usa o reios é o Active Record
Então esse Prisma aí é um Então pessoal
o que tem de diferente aqui é isso aqui
agora então melhorou Isso aqui é uma
isso aqui já é do ponto de vista técnico
já é uma melhoria técnica na ferramenta
tem quem tem na comunidade do docker
principalmente quem é a favor com o
Contra esse Esse modelo no nosso caso no
caso do da Evolution principalmente Como
tem muita gente ali que não é técnico em
docker e e eu tô nessa briga de fazer
esse trabalho de fazer a galera entender
né por isso que eu lanço o curso duas
vezes por ano de docker Porque sempre
tem gente entrando na comunidade eu vejo
que muita gente precisa aprender é bom
entender como é que funciona né então o
que que o Davis decidiu ali Como tem
muita gente que não é técnico até
pessoal para abrir um console e rodar um
comando o cara tem dificuldade então ele
optou por colocar aqui então vamos dizer
assim a Evolution faz tudo para você
literalmente ela vai subir ela vai ver o
banco para que você não tenha que fazer
isso eu achei muito legal isso daí
Porque eu consigo pegar um cara que não
é tão técnico e ele vai ter sucesso para
rodar a ferramenta porque a própria
ferramenta já é capaz de rodar isso daí
né a gente tem muita dificuldade disso
com chat ut chatot dá um trabalhinho a
mais porque o cara faz o update e o
update não roda migration eu tenho que
ir lá na na no console dele e rodar o
comandinho lá né mas ponto pro D
Parabéns isso aí cara essa ideia aqui é
é para simplificar mesmo esse ponto foi
legal público é no code né comentar aqui
o commod então o commod não vai precisar
ele acabou colocando no entry point né o
o toda essa essa esse essa lógica da
aplicação então fica legal nesse sentido
mesmo pessoal aqui então falando da
imagem falamos da dessa estrutura eh a
variável de ambiente pessoal no geral
para quem usa da promov A tá mais
acostumado com esses comentários né tudo
comentário aqui eu separo os os blocos
tudo né mas eh a server URL Manteve
idioma Manteve autenticação o o David
removeu a jwt aquela dor de cabeça que a
gente
tinha né ainda bem é bom não ter mesmo
viu de porque vai vai que alguém quer
usar melhor não tem precisa dessa
variável mais ap PK aí é já pega já pega
né É já já pega ali diretão já então
vocês vão continuar adicionando a sua
apq aqui né Pessoal lembrando sempre né
que você responsável pela segurança da
sua aplicação mantendo em segurança a
sua apq né não é coisa que você vai
publicar em grupo você não vai ficar
disono em live nada disso tá então bom
ficar atento com isso daí ainda mais
essa que é a Global essa aqui o cara
Mexe em tudo na sua instalação aí aqui
pessoal tem esse provider que o David
explicou que é uma é uma uma colab que
ele fez lá com o Kleber né ô da do COD
chat né de é uma maneira que ele tem de
salvar os arquivos temporários da da api
né
é o que que ele fez aí ele criou aquele
Storage que fazia né aquela gravar as
instâncias ali na pasta da instance que
tinha na na cod chat na Evolution o que
que ele fez ele pegou aquela aquela
lógica né de salvar em arquivo e tudo e
criou uma API para isso porque eh o
processo de http e é o mesmo de leitura
de disco V dizer assim até mais rápido
do que a própria leitura de disco então
ficou bem mais estável o processo de
conexão dele a grande diferença foi o
quê ele fez em então tipo assim tá muito
rápido e muito muito muito estável
entendeu então para quem não quer usar o
banco para salvar essas coisas Esse
provider aí é uma excelente alternativa
depois a gente pode fazer um vídeo aí
falando disso aí
sim aí então é mais mas
assim vamos dizer que o padrão vai ser
usar o banco b a sugestão vai ser usar o
banco e não usar o provider seria isso
né é o provider eu coloquei aí
justamente porque o Cléber pediu né ô
vamos testar vamos ver como é que é essa
função aí então eu fiz isso porque pra
gente poder testar mesmo validar um
pouquinho mas o padrão mesmo que a gente
vai seguir na Evolution 2 é o banco aí
que tipo assim a gente optei por ter uma
linha só de instalação para evitar dor
de cabeça né fáil É talvez um sistema
embarcado eu eu vou entrar agora nessa
questão de usar arme aqui né então
talvez seja legal porque daí não
precisaria ter o post G né mas mas no
uso 99% da situação acho que po vai ser
a melhor opção mesmo aqui não mudou nada
né David essa questão aqui de fazer a
configuração do Business mudou nada são
as mesmas variáveis aqui né sim mesma
variável rcode não mudou nada também em
relação a anterior o RT também não mudou
nada em relação a anterior a a diferença
aqui pessoal oi o só adicionou os
eventos globais né mas já tinha na 8.
zer já é
isso aí entra no entramos na maior
mudança né é assim pessoal o o Davis
colocou o Mungo até a gente até a gente
deu uma sugestão né deis lá atrás F Ah
põe o mongo aí cara o mongo é mais fácil
pra galera poder não precisa mexer com
tabela mexer com nada disso né e salvo o
Jon lá mesmo é tudo Jon mesmo que ele
mexe né mas com o tempo pessoal para
instalação pequena e média Beleza o
mongo o mongo ele controla legal conexão
ele tem uma uma uma uma pegada bem
interessante nesse sentido né até tem o
mongo o mongo Atlas né porque muita
gente usa o mongo Atlas para poder
utilizar também que na promov eu sempre
sugiro usar o mongo soft host mesmo para
pequenas operações ali
Mas acontece que o Deon mesmo percebeu e
eu também tenho muito relato ali da
Galera acho que a gente tem aqui o o
Jefferson também que tem grandes
instalações de Evolution aí o mongo ele
vai perdendo performance nessa maneira
que a Evolution que a Evolution funciona
de ficar o tempo inteiro buscando
informação nele né então o post gri
Pessoal hoje é padrão é um padrão né no
curso mesmo que que vou publicar hoje do
docker ele eu falo né Por como que a
galera começou a utilizar mesmo aqui o
post GR
a comunidade openarch abraçou el de vez
o mais Kelly já perdeu o espaço lá atrás
por causa da compra da Sun depois da
compra da Oracle né mas o marid Deb
ficou um bom tempo ainda deu um respiro
grande pro mundo do My Kell mas não
adianta cara hoje é tudo no post gri né
pela escalabilidade dele pela ele não é
simples a gente não pode falar
simplicidade dele porque ele ele é um
banquinho complexo de lidar mas é legal
porque você tem um banco que se você tem
um sistema pequeno ele dá conta e se
você crescer ele vai continuar dando
conta ainda né sem que você precise
fazer grandes modificações nele o
default dele já é um default muito bom
então é legal porque na Evolution vocês
vão ter uma tabela muito mais
estruturada organizada daqui a pouquinho
eu vou abrir para vocês verem aqui a
tabela tudo né Eu acho que todo mundo
vai ganhar com isso daí né e até mesmo
para quem for montar algum tipo de SAS
para quem vai montar painel vai poder
fazer um muito bom uso da dos dados das
tabelas ali do do do Mongo do do post né
você comentar David
Não na verdade é isso aí mesmo o que um
ponto que eu acho legal de falar sobre a
questão do post é aquela coisa porque
post né aí não my Kell algum outro assim
eu até mantiva também ten a
compatibilidade com my Kell aí mas tem
um ponto interessante a ser considerado
que eu acan tô acompanhando muito nos
últimos meses aí as comunidades da BS
ali trocando muita ideia com o próprio
perp Shell que é o desenvolvedor hoje da
bos né E todos eles que estão realmente
usando esse processo em alta escala em
empresas realmente grandes usam o posts
para fazer esse processo aí com a bos
então a performance tá muito melhor com
com a o posts e é o que eles estão
recomendando hoje então não tem porque a
gente ficar ali batendo cabeça com mongo
né como você mencionou aí que de certa
forma o mongo com operações mais
complexas começa a dar dor de cabeça
então eu decidi seguir esse conselho
mesmo que realmente de fato tá muito
melhor a performance tá bem bem
diferente mesmo a o uso do sistema aí
com com pos
Ah sim com
certeza
viu deixa eu perguntar uma gente posso
posso luí Mandei assim ó o que que eu
percebi Devid eu tive tem uma instalação
de um cliente meu que é que é um chat
ut todo mundo já sabe que o chatot Às
vezes ele é um pouco ele é
enjoadinho o que que tá acontecendo o
chatot o cliente tem uma loja de locação
de ferramenta E o que e ele hoje ele
manda por exemplo a ferramenta que é um
um um uma foto com legenda o chatot não
tem essa funcionalidade eu criei algumas
macros ou seja o cara pega e a macro a
sequência envia a imagem e envia um
texto Então quando ele quer enviar um
arqu um uma imagem uma ferramenta para
um cliente ele vai lá e roda Macro o que
que eu percebi muitas vezes a imagem não
vai dispara Mas como ele não tem
intervalo entre o disparo do do do do do
da imagem para depois disparar o texto
ele saem quase praticamente juntos e ele
não entrega as duas mensagens ao mesmo
tempo
perder perde muita mensagem será que
talvez vai por causa do Mongo isso não é
uma coisa que o mongo talvez tenha Cara
não sei eu vi eu já fui reportado esse
problema e fii de analisar ainda porque
tem que ver como é que eu tô que o chatu
tá me entregando essa automação entendeu
porque se ele não também ele manda
simultâneo como tá lá sem informação não
tem muito o que eu fazer desse lado de
cá para resolver entendeu porque aquela
coisa se chegou as duas ao mesmo tempo o
que que é mais rápido ser enviado uma
imagem ou um texto texto Então ele manda
o que é mais rápido ali no processo
então talvez ele esteja atropelando aí
por causa disso é tá perdendo muita
muita mensagem de de nesses envios perde
muito mensagem de imagem isso eu tava
pensando assim talvez usar um um
esqueminha que é meio gambiarra né mas é
tipo um template string você mandar um
texto ali escrito da forma certa ali o
Evolution vai entender como um
delay pois é tá aí ó eu vou pensar nisso
obrigado não era basicamente isso que eu
tô o cara tá me pedindo o que que posso
fazer eu pensei onde eu tentei subir a
2.0 para fazer teste na minha instalação
para ver se eu conseguia resolver não
consegui bati cabeça não instalei aí
não Outro ponto também pessoal de que
que é legal isso aqui é que assim o post
Gia é mais fácil de vocês acharem por
exemplo até uma gerenciada deles Então
nesse curso de Doc agora a gente vai ter
o nível um aonde ele é todinho self host
de qual que vocês usam hoje com
instalador com tudo né tudo na na mesma
VPS mas no nível dois eu vou mostrar
como é que vocês podem ter autta
escalabilidade eh e autta
disponibilidade também vocês colocarem
algumas coisas para fora e geralmente o
primeiro que a gente manda embora é o
banco então é legal porque vocês vão
poder ter um banco por exemplo aí um uma
um digital otion que vende um bom
gerenciado Amazon que vende bom
gerenciado não é caro mas vocês vão
poder ter um lugar mais seguro para
salvar porque daí torna vamos dizer
assim a gente vai falar ao longo da
semana também isso aí no curso de docker
também vou abordar bastante isso vai ter
um modo uma semana só de Evolution ali
com vocês para poder debulhar mesmo né
como eu vou estar mais envolvido agora
nesse processo eu vou fazer com vocês
isso daí que é assim vocês podem tornar
totalmente descartável a sua instalação
então é legal pessoal o ponto positivo o
maior ponto positivo que eu acho disso
daqui é que V vamos pegar o swarm lá
você vai ter um swarm a gente vai
colocar agora na na promov por exemplo a
opção de você poder rodar su arm em em
diversas íes inclusive na sua casa aí
uma maquinazinha velha que você tem uma
máquina boa que você tem então vou poder
ter uma Evolution rodando no IP
e tudo dela tudo dela tá tá no meu
postgree vamos supor que queimou lá o IP
então eu eu mato essa o docker já vem já
cria na outra e agora agora eu tô na
mesma conexão com os mesmos dados com as
mesmas sessões igual tudo igualzinho só
que agora eu tô no outro IP né então
WhatsApp eu acho que pro WhatsApp tá ok
isso porque o celular funciona assim né
então você entra no elevador no elevador
É o novo IP que você tem n mas até para
quem vai fazer para em massa por exemplo
até para você poder fazer algum tipo de
automação em cima disso de ir colocando
eh eh máquinas caso o IP seja banido não
não só ao número que às vezes cai no IP
né mas é uma é uma uma Cascatinha né
começa com número depois vem outro
número daqui a pouco vem o IP então é
legal porque vocês vão poder ter mais
liberdade de de de de não dá para rodar
duas Evolution mesmo tempo mas vocês vão
poder ter a flexibilidade de eh escolher
o melhor Lar melhor local é local para
poder rodar ela e é legal também porque
durante o update durante atualização
vocês não vão perder sessão tudo porque
vai ficar tudo salvo no post gre e é
sempre bom reforçar eu reforço nos
cursos falar gente essa sessão Depende
de dois lados depende do seu lado aqui
da Evolution de fazer o serviço dela e a
gente sabe que ela fa o servo dela
direitinho e também Depende do próprio
WhatsApp Às vezes o WhatsApp quer
desconectar ele desconecta por mais que
a Evolution tja tudo direitinho vai não
é é o o são dois donos é o Davidson e o
e o zuk Às vezes o Davidson fala assim ó
tá tudo certo aqui mas o zuk Fala Não
não quero mais não entendeu então são eh
pensar nisso também tá pessoal n Isso
aqui vai trazer mais confiança ainda
muito mais confiança vocês vão poder
lidar com muitas opções na Evolution a
nível de banco de dados então tenho
certeza que não é uma mudança pessoal um
tanto brusca né sair de um Mongo e para
um posto gri né são dois bancos 100%
diferente mas é totalmente justificável
Eu acho que isso aqui pessoal ó de todos
os recursos Com certeza que o tá
colocando aqui eu acho que esse é um dos
principais né tanto pro usuário mais
iniciante eh que já vai ter um post gri
do seu nhan já vai ter um post gree no
curso também eu falo um pouco sobre isso
um pouco mais de opções variantes em
relação a isso mas já tá mais em casa
também é menos um sistema para você liar
é menos uma coisa para você aprender é
menos uma coisa para dar problema ali né
então vai ficar legal nesse sentido
também fala aí Marcos bom dia aí eh o ry
Davidson aí pensou como é que vai
funcionar o desempenho das conexões com
o post post consome vamos dizer assim eu
tô fazendo uma operação agora que tem 25
instâncias ali né conexões Ô Marcos Pelo
que eu vi ali é uma conexão por
Instância né David e nesse laboratório a
gente vai fazer uma um laboratório com
PG bouncer ver como é que ele fica
também para poder ajudar nisso daí sabe
ô por porque cada conexão do post cara
US de memória só para ter a conexão fora
o que vai circular ali é o o que eu
mudei foi o que a otimizei né o uso de
credenciais da bos com a com o banco que
o mongo por exemplo ele pegava todos os
arquivos de prequ e créditos e tudo e
jogava lá no no Mongo e ficava buscando
isso o tempo todo com posts o que que eu
fiz eu tô sabendo apenas as credis e as
prys elas acabam sendo vamos dizer assim
não não é necessário salvar no momento
que ela não tem mais ali o arquivo você
deletou alguma coisa assim o WhatsApp
ele vai criar novamente o que eu tô
fazendo é só o quê para evitar que fique
perdendo esses arquivos aí normalmente
que se você faz um restart na na conexão
aí na na Evolution eh um restart no
contêiner né eu falo eu vou usar o
regist também para salvar essas
informações aí caso você prefira então
tipo assim o que realmente é importante
tá lá no banco que é as credenciais que
quando você inicia ele busca aquilo
conecta no WhatsApp que o que vai já
diminuir bruscamente a quantidade de
vezes que ele vai consultar ali no post
tá esse é o primeiro ponto então já o
otimizou aí o uso do banco aí pela bos
no usando pos Ah já é bom já é bom e com
isso fica mais fica mais estável o
processo da api como um todo sabe então
tipo assim não não o risco de deadlock
essas coisas assim é bem menor inclusive
o Luiz falou aí eu já eu montei aqui um
um cenário com com PG e de bouncing aqui
com um clusterz inho né de de porce
também para testar como é que vai ser o
o uso aqui Inclusive eu até turbinei
aqui as as configurações também para
evitar qualquer perda aí de de leitura
bom Aqui também pessoal Outro ponto em
cima da dessa configuração que você vai
ter um banco né então eu vou explicar
depois para vocês né nos cursos mais
paraa frente tudo isso daqui mas é legal
que você vai poder Conar conversou né da
conexão né então no mesmo banco um para
quem vai fazer um SAS é legal para quem
vai para quem quer gerenciar de uma
maneira melhor um backup só também é
legal no mesmo banco você vai poder ter
as suas as várias evolutions conectadas
né dav Cada uma com o seu c name né isso
exato você consegue usar só para todas
as as que você tem é é como se fosse uma
chave estrangeira no banco né então ele
vai ser uma chave ali que tudo que tiver
a chave Evolution ele sabe que só
aqueles dados são dessa instalação aqui
uma outra instalação que você tem de um
outro cliente então talvez poderia até
usar o mesmo banco aí você vem aqui e
põe por exemplo o nome do cliente né
então é no caso o nome da instalação
como um todo né então achei legal também
isso daqui porque é uma é uma
flexibilidade legal para trabalhar né e
e e eu eu particularmente achei
interessante e o mesmo Vale pro Rabbit
né que você poder vir aqui mudar também
o nome da Exchange você de alguma
maneira pode eh fazer algum tipo
de de otimização nesse sentido né mas
legal isso aqui esse aqui é um ponto
ponto importante aí Eh manda aí
Rodrigo vou tá procurando você t
conseguindo me ouvir T ouvindo Tô
ouvindo é que o microfone aqui é do o
microfone do laptop talvez não seja tão
bom quanto o meu externo eu esqueci o
externo aqui em algum lugar eu tava
procurando não
achei eu tenho
testado a Evolution peguei aquela última
versão né acho que é a 1.8 com chat wo e
eu ia colocar o mongo agora mas vou dar
vou esperar por causa do do posg eu já
trabalho com banco relacional prefiro eu
prefiro 1 vezes porque o po para mim é
uma coisa que eu já tô bem acostumado há
anos eu uso ele então para mim até mais
fácil eh eu tenho observado que não sei
se vai melhorar isso é uma coisa que eu
queria perguntar para vocês eu ia até
botar a mão o DB agora para ver se
melhorava eu tô com um cliente Só usando
tô testando e eu tô observando que o
chaut pé tá perdendo a conexão de hora
em hora com a Instância eu não sei o que
tá acontecendo ele agora disse que não
tem sessão aqui eu não sei o que tá
acontecendo se é por causa disso se não
é porque eu ainda tô armazenando em
texto ainda é o o mongo resolvi esse
problema do no session ali né então sem
sem o banco não sei se o d chegou a
observar melhor is aí porque não era
nenhum bug né cara né era uma limitação
mesmo da escrita leitura de arquivo ali
parece né E era mais rápido do que a
escrita do arquivo né tem aquele negócio
do do Linux que Max file size essas
coisas assim quantidade de arquivos
aberto Será que era
isso no caso assim é um negócio muito
rápido néel que entendo isso daí né esse
processo dele pegar a chave escrever
chave não escrever chave ali então
acabava que demorava muito porque é um
Jon grandinho até né então acabou pelo
mongo tinha um desempenho melhor nisso
daí Acredito eu que agora vai ter um
desempenho melhor ainda então se você
colocar um um banco geralmente ô Rodrigo
a maioria pelo menos dos casas que a
gente vê aqui dos alunos aqui tudo do
mentorado aqui eh resolve o problema do
no session entendeu Eu Cheguei a pensar
nisso eu tô usando aquela tua instalação
lá do promov Web só não sei como é que
eu vou fazer lá para botar o pos lá
dentro vai no é ali ali eu vou atualizar
essa semana o instalador porque assim ó
quem quem já tem instalação eu vou
montar um botão lá para você poder
converter a sua da da um para dois e
quem for criar uma nova poder escolher
também se que é o um ou se que é o dois
porque o dois vai manter por um tempo a
um ainda né Essa semana eu vou ter um
trabalhinho de de ajustar ali mas vai
ter um um conversor para vocês ali
também sabe para ajudar para ajudar
nesse sentido a ideia aí pessoal como a
gente já trabalha com as tags ali no
tanto no github quanto no no docker eh
tipo assim a V1 vai existir para sempre
né que você só usar a versão anterior
ali e vai continuar funcionando a
diferença é só essa hoje lá no Git mesmo
de forma padrão tá v1 e a brint V2 tá
numa numa brint ap parte né então daqui
a pouco talvez um mês ou dois meses aí
de de testes e esse processo a gente vai
leva de forma
oficial eu tô pensando em utilizar assim
até de forma imediata Porque para mim é
uma demanda aqui que tá pegando muito
cara eu não tô conseguindo usar entendeu
mas é é ainda ainda seria melhor você
colocar o mongo na um viu depois joga o
steack na comunidade ali ô Rodrigo que a
gente mostra para você eu até acho que
na lá no curso de docker eu não vi não
lá no inst no curs de docker tem eu acho
as variáveis aí você manda que eu mando
para você a aula lá para você me lembra
me lembra a comunidade ali eu mando para
você edita lá no no instalador põe as
variáveis do Mongo e já pelo menos por
enquanto você resolve isso aí até sair
mesmo que é dois não é deixa eu o
negócio que eu coloquei no chat aí para
vocês não sei se viram lá do do chat lá
porque o contato quando conecta lá
aparece o Evolution e tal e e manda
scanear o q code eu senti que o cara
ficou meio confuso ficou perguntando o
que que era Evolution né E aí como muda
aqui ele tem uma variável que muda o
nome do navegador lá de Chrome para o
para o que você quiser e tal se teria
uma variável também para definir o nome
do contato lá no chat ser legal isso aí
deitos porque a nível de instalação né
poder customizar né Tem tem isso já na
verdade tem o fluxo do n8n lá na pasta
extras e ele é justamente para isso você
customizar a foto do bot o nome que vai
est ali tá meio que um White Lazinho
dessa automação se eu não me engano o
Rafael do AST online que ensinou isso lá
no no canal dele tá mas tem justamente
você fazer isso eu ia falar justamente
isso eu vi um vídeo do Rafael ensinando
fazer inclusive ele dá o fluxo do ntn
para você ele já automaticamente muda o
nome do contato é não na verdade pessoal
assim eu vi que eu consigo eu já fiz até
aqui eu consigo mudar por api eu conecto
na api do chatot e mudo eu tô dizendo
assim como variável de ambiente já para
sair por padrão
entendeu é isso isso talvez eu consiga
me trabalhar melhor tbe na trazer aquela
aquela função mesmo para dentro do da
Evolution como a configuração it Label
ali da tua conexão vamos dizer assim
aham mas na v1 mesmo para tu resolver
isso já de imediato usa aqueles fluxos
lá doon funar valeu
valeu pessoal Outro ponto também que é
interessante aqui né David que o o
Reds ele ele ainda existe né Essas
primeiras o que que mudou essa primeira
configuração de cche aí não tem mais né
que é essa redes enable aí não existe
mais variáveis e ela foi tá configurada
nessa segunda opção de baixo aí agora
porque assim o sistema ele não usa mais
o Redi só para só paraa Instância como
era antes ele usa para outras funções lá
dentro como cash de grupo metadata faz
cash de algumas informações pro chatot
tem muita coisa que ele é utilizado lá
dentro então meio que hoje é um
pré-requisito já ou ativar ele ou de
forma local ali embaixo que elear a
memória do do sistema E aí assim aí
pessoal outra outra aí entra um ponto
importante que
assim até vou vou falar mais sobre isso
também né mas até onde convém vocês
manterem um Heads para isso porque como
boa parte do processo o post gri faz e a
outra parte do processo eu vou poder ter
opção de ter arquivo eu não sei até onde
um manter um Heads porque o Heads
pessoal ele ele ainda mais se você
dividir o Heads cont outas coisa ele
corrompe ele quebra dá um monte de
problema nele lá então até onde eu acho
que de início para quem vai ter uma
instalação básica não não precisaria do
Heads eu né Acho que daria para usar de
boa o arquivo né É pode usar esse local
enable aí que ele vai usar em memória as
informações que ele precisa eu acho que
vou exatamente P menos aqui eu vou vou
partir dessa premissa que é menos um
sistema pra gente poder manter Então a
gente vai ter evol post Gris só por
enquanto mais pra frente a gente entra
na parte do Heads que eu acho que vai
fazer mais sentido para quem tem coisas
maiores aí né mas eu acho que de início
assim e como o falou otimizou muito essa
questão do arquivo ali com aquela aqu
aquele serviço de troca lá eu acho que
vai ficar legal usar o arquivo por
enquanto mas eu não não pretendo entrar
na parte do Heads Por enquanto não tá
gente pessoal aqui ó no Type bot a gente
vai vai ter bastante conteúdo sobre isso
também mas eu acho que não vou fazer
nessa semana ainda minha ideia essa
semana é a gente falar mais mesmo do
processo de instalação e fazer Unos
Laboratórios mas chatot e type bot aqui
vocês vão habilitar né se vocês vão
querer carregar eles ou não só que é
importante pessoal do ponto de vista da
otimização do sistema então por exemplo
se que muitos de vocês só usam uma API
então é legal porque Ah luí eu vou
montar um SAS aqui para oferecer
Evolution Beleza você pode falar assim
mas você vai usar o tbot vai oferecer
tbot e o chatot não Então pessoal não
vai nem carregar o módulo Então vai usar
menos recursos Ah luí eu vou usar o tbot
não vou usar o chatot então você pode
vir aqui ó e desabilitar o chatot Ah eu
só vou usar o chat não vou usar tbot vem
aqui e desabilita então vocês vão ter
uma flexibilidade de não carregar coisa
desnecessária né não é não é um problema
mas eu sei que para quem usa o negócio
no nível mais hard Isso aqui faz
diferença né de de texto aqui acho que
pro usuário pequeno uma VPS normal ali
beleza ter isso daqui não não vai fazer
diferença mas de novo né pessoal eu sei
que vocês usam as coisas no limite ou é
muito grande e aí faz diferença ou você
estão numa máquina de 1 GB e aí faz
diferença também né eu brigo eu brigo
muito no curso com isso aí vai ó seu
celular cara o seu celular não pode ter
mais memória pro seu servidor né não tá
não tá certo isso daí né então eu sei
que a galera gosta de de abusar um pouco
dos recursos ali né Mas é interessante
porque eh de novo pessoal é bom ter
opção né Por mais que a opção seja
utilizar Mas é bom ter a opção Ah luí
nesse caso aqui cara não vou nem
carregar o módulo para não ter mesmo
problema Beleza então vocês vão ter essa
liberdade aí né e e nos próximos
encontros a gente vai ver que entrou
Muito mais coisa dentro do tbot Muito
mais coisa dentro do chat ú ali tem
certeza que vai ajudar bastante vocês aí
aqui pessoal tem umas variáveis também
no chat ut que é é para você poder
importar
eh as conversas né David poder importar
o histórico ali
né então você vem aqui conecta no seu
chatot no banco do do seu chatot e assim
que você escanear ali ela vai chupinhar
tudo ali o código O as conversas e vai
importar para você lá né isso então é
legal também porque eu sei que vocês
gostam disso né Eh a diferença só aqui
pessoal é que assim vai ser eh é um chat
ult só né David não tem como você
controlar isso por Instância Então nesse
caso aí você conecta ele no chatot né
que aí você pode usar ele lá dentro por
SAS por exemplo como um SAS mas na hora
de criar a Instância você tem a opção lá
de dizer se vai importar ou não naquela
Instância tá
temele desse chatot aqui né isso então
então assim eh eu sou partid também de
utilizar um um uma Evolution para cada
coisa para não para ficar mais debug
também né mas cogitar também isso aqui
isso aqui é um assunto que a gente vai
ter que debater um pouco mais sobre ele
uhum na parte do Web Hook n não mudou
nada aqui
né não de tá normal são as mesmas as
mesmas de sempre chegou a mudar o
payload Davidson aqui mudou o payload
também para ficar mais flat tem lá não o
o payload É porque é o que que ele chega
recebe mesmo ali da estrutura do
WhatsApp né então é muita informação não
tem como não dar de uma forma M meso
simplificada eh a única coisa que mudou
acho que era uma um parâmetro lá que a
gente chamava de aer que era o nome da
instância agora tá como Instância ID tá
só isso que mudou lá na da mensagem ISO
É isso aí ISO Aí a gente faz também uma
live depois só de só do que mudou né só
do Break Change né isso aqui dat
aproveitar o dat aqui você falou de
manhã hoje que não precisa mais né
dessas variáveis aqui nenhuma delas
Davidson não nenhuma delas porque a
gente não tá mais salvando realmente a a
Instância em si em Storage então isso aí
eu desativei só acho que só essas duas
primeiras aí D instance tem instance que
não é não tem a ver com Story mas mas
assim mas vamos supor assim ó por
exemplo aqui ó eu poder definir um
intervalo para ele apagar do Mongo do do
do post GR não vai ter o é o cleanup eu
vou fazer o seguinte ele eu vou vou
pensar como é que eu posso estar fazendo
isso daí Porque eu talvez eu consigo
trabalhar com aquele modelo de ttl mesmo
fazer uma uma exclusão de realmente de
arquivos antigos Porque nessa
configuração aí ele simplesmente a cada
Du horas por exemplo ele deleta tudo da
pasta então mesmo chegou agora ele
deleta então eu eu vou ver melhor aí
qual sua configuração e fazer realmente
assim ó vou pegar tudo que for mais
antigo que 2 horas aí ele vai mantendo
ali sempre 2 horas de de histórico por
exemplo Entendi então eu vou trabalhar
de novo nessas nessas funcionalidades
ainda então aqui eu posso tirar tudo
aqui então é pode por enquanto elas se
quiser deixar mas hoje não não tem
função essas variáveis
aí a não ser que não US o post gree né é
isso hoje a a a forma de não usar o POG
é usando a o provider lá do do do kber
né então ISO aí não não não não
influenciaria isso aqui ou isso aqui
influencer aí também lá não isso aí é
praticamente tá sem função lá dentro do
código agora função mesmo é aí pessoal
esse stack aqui pessoal vou lapidar mais
ele aí ao longo do tempo né Hoje é um
laboratório só pra gente poder fazer um
primeiro contato com a ferramenta mesmo
aí mas você fique tranquilo que eu vou
trazer Oi a gente tá em alfa né vai
mudar muita coisa ainda vai mudar ainda
aqui então assim eu vou eu vou deixar
para vocês ali o de se Dev publicar
também ao longo da semana aí na
comunidade mas
eh Lembrando que o de falou a né não é
fixo ainda tá vamos ficar esperto aqui
coisas podem entrar coisas podem sair né
mas pelo menos dá para ter uma noção de
como funciona um ponto interessante aqui
pessoal que a gente conversou até ontem
e o dav já tinha se preocupado é que
esse banco Evolution aqui quem for
instalar por exemplo C quem for instalar
o o próprio nen também não faz né eles
não criam o banco de de dados Então eu
tenho o banco de dados e tenho a
estrutura do banco de dados são duas
coisas diferentes Então quem for fazer
manualmente no próprio curso de docker
né A gente vai ver lá vamos instalar o
nhn vamos no post g e criar lá o Create
Database nhn para criar o banquinho o
David já resolveu nosso problema se o
banco não existir ele cria então é legal
também para um usuário mais Lego que não
tem as manhas de conectar Ali vai Vai
facilitar também né David então ele não
só faz as migrations mas também verifica
se o próprio banco existe então é uma
etapa menos que a gente tem que se
preocupar aí é generate do Prisma do
Prisma né então assim ele já já roda ali
e Beleza então é legal porque é um é um
é uma etapa menos que vocês vão ter que
lidar aí tá então tem que ter tem que
ter no seu no seu swarm rodando aí um um
post
gri eu acho que eu não tenho aqui deixa
eu ver se eu tenho um post grie aqui
acho que eu tenho só o o stack dele aqui
ah
postgree deixa eu pegar cim que coloquei
nele esse daqui já é o é o stack do
Curso Novo vamos lá vou rodar ele é vou
rodar el em outra máquina od de qualquer
versão né Qualquer versão ele vai
funcionar né do postgre não vai não
precisa de nenhuma específica né versão
Pra cuida disso pra gente já faz tudo lá
né é post GR senha Beleza então vamos
copiar o stack
aqui Pessoal lembrando que essa essa C
aqui vai ficar gravada e lá na
comunidade eu vou ter um único to tópico
pra gente poder conversar só sobre essa
esse laboratório de hoje aí fica mais
fácil que todo mundo que entrar agora e
quem for entrar depois também vai poder
vir aqui acompanhar Eu Vou guiar vocês
depois como é que vai funcionar a
comunidade nova mas a gente vai eu vou
organizar melhor pensando em quem tá
agora aqui assistindo e no futuro Também
quem tiver vendo isso daqui poder vir
aqui e funcionar pessoal só para
finalizar também aqui ó galera do do do
swarm né então não posso rodar replicado
o a Evolution poderia até rodar como
Global seria uma uma opção né usando
usando post gri rodaria perfeitamente
como Global Global pessoal ele roda uma
Instância em cada node em cada em cada
worker né então se esse node cair
automaticamente o docker já põe ele no
outro então o replicado como um eu posso
especificar um lugar para ele rodar o
Global como um ele roda uma Instância em
cada um tá bom eh da maioria que a gente
utiliza que o único que vai rodar como
global é o a gente do painer né mas
futuramente Quem sabe né D dá fazer
algum ajuste em cima disso né No meu
caso que eu vou rodar no meu Manager
mesmo e e sempre né pessoal como boa
prática defina limites de memória e CPU
paraas aplicações para você poder fazer
métrica né hoje no nosso curso novo hoje
à tarde eu vou vai ter um módulo de
métrica Como que você monitora servidor
então isso aqui vai ser interessante
para que você possa fazer monitoramento
também eh liberar esses recursos
acompanhar sua máquina para saber como
que você vai oferecer eh mais recursos
ou até mesmo tirar recursos não não
estão sendo utilizados para que outro
sistema seu possa utilizar né mas nunca
rodem o docker sem utilizar algum tipo
de limitação da aplicação porque senão
duas aplicações que usam tudo o servidor
cai né então a gente é bom a gente ficar
atento né E aqui também na parte do TRF
não mudou nada tá não vai ter nenhum
tipo de de mudança que vocês tenham que
se preocupar que não tá bom eh pode
perguntar aí Rodrigo
não tava pensando aqui numa situação que
se falou negócio de balanceamento eu
pensei no seguinte se tivesse uma na
Instância tivesse uma coluna lá no banco
no futuro que vai dizer qual o rosto que
ela tá pelo menos você consegue separar
tipo uma estância tá conectada num
servidor a outra estância no outro no
outro para poder distribuir ter várias
conexões em servidoras diferentes não
que vai ter a mesmo Zap em vários
servidores Mas será que pelo menos não
dá para botar os números diferentes em
cada lugar e o cara vai vai atirando
para um ou para outro não sei eu tô
pensando aqui porque eu já cheguei a
fazer isso com smtp geralmente difícil
viu no caso do WhatsApp né deles porque
cada vez que ele subir a aplicação ele
considera como um cliente não é isso
então se eu subir a mesma aplicação o
mesmo cliente com os mesmos dados eu
acho que WhatsApp desconecta a gente né
não número diferente que eu tô falando
ah não aí você pode aí você pode entendi
entendeu número diferente só para você
fazer assim não colocar todos os
WhatsApps num rost só para você
distribuir a carga de WhatsApps
diferentes mas aí teria o mesmo banco e
a e o Manager da Evolution lá consegue
enxergar tudo entendeu Entendi você
coloca os números em máquinas diferentes
em instâncias diferentes VPS né mas você
gerencia todos num lugar só mas acho é
mais fácil ter instalação diferente
mesmo viu Rodrigo para lidar melhor com
isso daí Aí teria que ter um banco para
cada uma
né esse é isso que eu tô pensando
entendeu não você pode usar até
expliquei aqui hoje já você pode usar o
mesmo banco você só muda essa variável
aqui ó que é o aqui ó uma variável no na
configuração andando aí é o client name
aqui ó Connection client name então você
pode ter um um único postgree e ter
várias instalações nesse mesmo post GR
essa variável que separa cada uma delas
endeu Ah então tem várias Evolution e o
mesmo banco e o mesmo banco poderia ser
assim entendeu então ficou mais prático
para você você geren para quem vai usar
um banco gerenciado por exemplo é é a
melhor opção entendeu sim eu tô até
testando isso aqui também ó algumas
evolutions no mesmo banco desse formato
aí e utilizando o metabase ali para
fazer para mostrar os dados quantas
conexões quanto estão usando porque é o
mesmo banco os dados legal deixa eu
rodar aqui ó para poder ver se vai rodar
certinho O ruim do ao vivo pessoal é que
o ao vivo Você sabe né pessoal faz aul
fal comigo aí sabe que é o gaguejo passa
cachorro espirro tulo né acontece tudo
no ao vivo né mas eu gosto de fazer eu
prefiro fazer assim até até pro pessoal
ver que a gente que é tentativa e erro
mesmo né nessa parte de muito do desses
Laboratórios vão ser tentativa e erro só
que o legal que eu que vou errar aqui
vocês vão ficar Livres disso aí vocês só
vão ver o trouxo aqui errando entendeu
ou não porque hoje hoje vocês perdem
tempo com isso né A minha ideia é fazer
deixa que eu perco o tempo vocês só
acompanh mas é é muito tentativo e erro
pessoal não tem muito deixa eu ver aqui
ó
rodou então meu banco tá no meu tá no
outro servidor meu banco tá no meu no
meu banco da Zoom e o meu Manager tá
aqui tá com rodando a Evolution rodou lá
ó beleza vamos vamos olhar vamos olhar
que os logs mudaram deixa eu dar um zoom
aqui na tela ficar mais fácil para
vocês Outro ponto também que o Davidson
colocou lá na release dele a vou fazer
um vídeo essa semana sobre o release
também é que tá menos verboso significa
pessoal que assim ele não é que ele tá
ele tá escrevendo que é necessário PR
gente antes ele escrevia muito mais do
que a gente precisava para saber né e
gerava um probleminha ali nas primeiras
versões principalmente depois eu eu no
caso web que depois eu mudei lá a
variável também que é para ele escrever
menos então a gente tinha um problema aí
muito quando surgiu Evolution né de a de
de ter um arquivo de log eles ele
acabava com o servidor né então agora
vai ajudar também nisso daí Porque para
quem é menos para quem é menos técnico e
que às vezes nem entende que vai ter um
arquivo de l ali sendo inscrito o tempo
inteiro né vai vai ajudar também esse
cara a a se prevenir também nesses
problemas aí né Manda aí o o Elber tudo
Bomber Bom dia beleza eu peguei o Bond
um pouco andando só queria checar uma
informaçãozinha e uma outra pergunta é
primeiro o myq então na versão 2 vai
sair 100% é isso né só queria ter essa
certeza porque já tem coisa funcionando
e é perdão o mongo perdão vai sair
100% perfeito e Lu uma última cois
aquele negocinho do não tem nada a ver
com isso aqui monitoramento externo de
VPS fui fazer aqui não encontrei aquele
link que saiu do ar que você passou uma
vez pra gente testar deixei temporário
lá mas vou publicar o curso inteiro hoje
aí dá para você ah perfeito perfeito Tá
bom mas o o e o só uma última coisinha
que eu acabei de lembrar o essa versão
do Mongo eh geralmente ela vai ficar com
suporte Depois que lançar dois
oficialmente Por quanto tempo só para eu
ter uma ideia Ah o para m preparar aqui
fazer só uma uma uma manutenção né David
não vai acho que não vai só se tiver que
uma atualizar uma BS lá uma coisa mínima
eu acho né dav por um tempo né mas assim
ele deve manter por um tempo mas assim
até até pro David manter Open pessoal
manter um já é difícil é tá sem dúvida
mas assim ó F sábado sábado saiu a
versão 8.1 né que foi para corrigir um
detalhe no Type bot lá que tava
imposibilitando o pessoal usar e
provavelmente o que tiver de tipo assim
bug que veio de lá vai sair na V2 né
então tipo assim alguns ajustes por
exemplo questão de áudio que agora já dá
para acelerar o áudio já tem wireframe
essas coisas assim já tá indo direto
para V2 justamente paraa questão que o
Luiz tá falando eh é muito complicado
manter o código versionado dessa forma e
trabalhando e manter os dois atualizados
né ainda mais porque vai ser tudo num
num código só vamos dizer
assim
é aí pessoal Aqui ó pode ver que ele
já achou no meu caso aqui eu tô com Reds
deixa eu ver acho que eu tô com Heads
nessa nesse meu teste aqui ele achou um
Heads no log ali Heads
Heads Heads tá falso ali
ó beleza Heads rodou aparentemente rodou
aqui DAE deu um htp um aqui ó significa
subiu né
de ele rodou aqui as migration ó então
ele rodou a primeira migration tá vendo
Então é importante ter isso daqui né
Deixa eu atualizar esse serviço aqui
para poder Vamos dar um update nele
para ele fazer o processo de novo então
no primeiro processo que ele fez pessoal
não tinha o banco ele criou o banco ó lá
Database created tá vendo lá então ele
falou para mim aqui ó criei o banco
então é legal vocês observarem também
isso daí né Lembrando que openarch
Pessoal vocês são os responsáveis tem
que acompanhar ele opch é tudo menos
funcionar sozinho né ó aqui ele já não
falou que criou né ó vamos ver aqui
ah ó lá ele já achou o banco ó para ver
que mudou a
a a regra Aqui ó pode ver que aqui na
primeira vez que eu rodei ele falou ó
ele achou uma uma migration que não tem
ó lá essa migration foi aplicada então
no nosso novo curso eu vou explorar
bastante com vocês com todos os sistemas
que vocês têm que ficar muito de olho
nisso daqui tá eh ainda mais a maioria
que são feitos são feitos em node a
maioria é feito com Prisma então vai
ficar meio que no padrão para vocês
observarem isso aqui ele já falou ó que
ele já achou uma migração lá e não teve
que aplicar nada ó
então assim beleza falou ó a versão que
tá rodando é a mesma que eu tenho aqui
então eu não preciso mexer no seu banco
tá então um ponto positivo também para
pro Davidson eí pro prisma de de exibir
essa informação pra gente né isso aqui
pessoal na hora de fazer um debug na
hora de vocês observarem melhor a
aplicação é importante né o o Type bot é
cheio disso daí né o Type bot tá com até
com um bug recente em cima disso daí de
não dele dar uma mensagem lá que ele se
enrola na hora de criar migration outro
ponto pessoal que aí é do do post gri
também como ele não tem um Lock ali o
mais Kell Um dos problemas do My Kell ao
mesmo tempo que é um problema é um
benefício ele dá um Lock na tabela então
ele para tudo é igual o jo Cléber ele
fala para para para para para para para
ele para tudo todo mundo cai eu vou
criar um campo aqui aí o bonitão vai lá
cria um campo na tabela a hora que ele
termina aquilo todo mundo pode voltar se
por algum motivo O processo foi
interrompido ele dá um HB ele fala
beleza não quer mais tava Tava
terminando você não quer mais não então
ele apaga então o mais Kell tem ess o
post gis já é mais complicado em relação
a isso né então ele se ele tiver no meio
de um processo para quem já fez upgrade
de chatot ou de Type bot ou do próprio
nateen e no meio ali deu algum problema
sabe que ele ele ele é um pouco mais
complicado em relação a isso então
durante o processo de update pessoal
agora que a Evolution também vai ter um
banco vocês de novo vão ter que ser
responsáveis em relação a isso né não é
só mais cuidado da Evolution é também
cuidado do post gree e do versionamento
do banco então vai ter um pontinho a
mais ali que a gente vai ter que vai ter
que conversar né nessa regravação do
curso eu vou falar mais sobre isso de
todos os sistemas porque todos eles têm
isso daqui praticamente Maic tem Shut
tem tbot tem nhn tem agora Evolution tem
a maioria tem o Call tem todos eles têm
ali né acho que só o WordPress que não
tem um um nível de versionamento nesse
sentido mas mais um ponto para vocês tá
pessoal ficar de olho aí é bobeirinha
não é uma coisa que acontece todo dia
mas agora temos a ilidade né você tá
embaixo do do teto aí ele pode cair aqui
pessoal eu abri aqui um PG admin deixa
eu ver aqui as minhas o Windows ele se
chama Windows Mas você abre a tela no
lugar eu tô aqui nessa tela mexendo aqui
ó ele abre a tela no outro Ai Senhor Ele
tem dificuldade com as Windows cara eu
não gosto cara olha Deus eu tô tô
num esforço tão grande cara eu peguei
uma máquina muito boa fal assim ó para
não ter para ninguém falar você pegou
uma máquina ruim também peguei uma
máquina muito boa cara mas não consigo
cara
ó lá pessoal ó então aqui ó no vamos
pegar aqui o o Evolution cadê Aqui ó
Evolution Olha que belezinha que tá hein
cara putz tem isso aqui no tems aqui no
post G Vai ser maravilhoso né vou até
fazer uma extensão Zinha do curso depois
pro superp base que vai ter coisa legal
pro superp base para vocês aqui para
usar no Evolution para quem tá na tá na
vibe do sup base
aí ó criou a tabelinha aqui ó 18 tabelas
he de você tá Você
tá animado aí hein
bicho provavelmente não vai mudar tanto
não
tá estutura em si tipo não vai ser como
lá no início que todas todo dia tenho
uma atualização pessoal não vou conectar
aqui
porque sei que como é que a galera é V
conectar antes de mim mas basicamente
aqui é o seguinte ao longo é ao longo da
da semana a gente vai fazer mais Live aí
eu já deixo um conectado pra gente poder
ver esse processo aí tá mas aqui pessoal
tudo que vocês tiverem agora hora de uso
da Evolution vai ficar salvo aqui isso
aqui é uma diferença Grande para vocês
agora o Deus falou ó o metabase aqui
fica muito legal um apps smit aqui vai
ficar muito legal muito mesmo para você
poder para poder você vir aqui puxar
estatística puxar algum tipo de
histórico puxar métrica e até fazer
algum tipo de monitoramento né então
vocês que usam muito aí o Uptime Kuma um
dos melhores até no vídeo eu tenho aqui
um vídeo onde eu mostro na comunidade
para mim um dos os melhores up timers
que tem a okuma cara porque eu posso
conectar no banco só dar uma query e de
acordo com o resultado da query eu posso
gerar um alerta então eu posso vir aqui
e verificar fazer rodar uma querinha no
posto GR se tiver tal tal registro al
tal valor tal coisa já me gera um alerta
aqui então isso aí pessoal ó eu tenho
certeza que ao longo dos meses aí a
gente vai ter muito trabalho nas
comunidades aí para poder explorar todo
o potencial que tem isso daqui né tanto
rodando por exemplo uma Evolution com o
supabase Porque lá é um poste grito
também então eu sei que muitos de vocês
gostam do supabase né Eu sou muito
cauteloso com supabase ainda mais para
quem não é técnico que é é em maioria
esse Público aqui né mas eu vou pisar em
algumas coisas lá bem pisando em ovos
mesmo mas vou né e mas Cara vocês têm
muito recurso agora isso aqui é loucura
bicho isso aqui é coisa demais mesmo
para poder eh para poder utilizar né
pessoal todos os dados de Instância vão
ficar aqui né então a gente vai ter uma
uma live só para falar sobre o da
Evolution mesmo né mas então isso aqui
éa uma informação que meio que você não
via ou ou você abri no mongo lá e vi um
Jonão lá né e mas agora tá bonitinho
aqui ó todas as informações que vocês
enviaram lá né pela Api para criar a
Instância tá aqui tá e ô de ele não faz
o o revers né vamos supor se eu Lançar
aqui o registro ele passa a valer
Evolution
também acredito que sim viu não tem
porque a questão é só o seguinte o load
instance ele faz quando sobe o serviço
então você teria que fazer fazer um
restar Zinho lá
na é mas funcionaria sim não tem
problema funcionar Mas aquela coisa
também se tu se tu abrir aí a tabela
dela mostra as colunas aí ele tem ali tá
vendo ele tem o token da estância ali e
tem o Cent name se tu utilizar o client
name por exemplo Como subdomínio que
você usa você consegue montar a
estrutura da api que tá ali por Por Fora
Só com essas informações aí não fica
legal mesmo é eu eu digo por isso vamos
supor quem for montar um SAS por exemplo
eu prefiro muito mais vir buscar do
banco do que buscar da pi entendeu é bem
mais performático então sim fica mais
legal botar uma consulta um relatório né
então achei legal por isso cara então
assim nossa até para abre abre
possibilidades aqui aqui ficam os
contatos né Deus caso eu importe os
contatos né Eh só do chat útil ou
qualquer contato que que que eu
conversar vai entrar aqui qualquer
contato é inclusive qu passou pela
passou pela pi tá aqui é Inclusive a
sincronização também nos contatos da
agenda eles vê para aí também isso a do
chat útil que vai ser a configuração né
que vai ficar aqui agora né né então
mais um ponto pessoal também de
otimização nisso né David de novo né
para quem for montar painel for montar
Manager para quem vai montar SAS em cima
disso putz é maravilhoso cara ter isso
aqui a nível de de de banco porque nossa
PR fazer um upgrade no plano do cara
para você poder fazer uma modificação na
nos dados do cara também nossa isso aqui
é muito bom até aquele até o próprio
amigo aí o o mogle Né o Rodrigo né Deus
eu posso vir aqui e mudar também alguma
coisa aqui né em relação a isso né Uhum
os dados da conexão então pessal assim
vai ficar realmente legal as mensagens
vão ficar aqui e aí vocês agora vão
poder vir aqui e criar até mesmo a sua
própria regra de espurgo que nem o David
falou você pode criar lá um nhn umzinho
lá que vai ficar de de hora em hora
pagando tudo que tem mais de três
semanas por exemplo pro seu banco não
inflar né então é legal porque assim a
própria vol vai oferecer alguma coisa
mas você pode também vir aqui e fazer a
sua própria regra de espurgo
montar fazer fazer pesquisa no banco
para poder fazer as temo de avaliação de
mensagem Nossa cara assim fico
imaginando as possibilidades tem tem
bastante coisa legal trabalhar aqui né
de de fazer né D então assim eu tenho
certeza que isso aqui pessoal vai ser um
Nossa vai ser
um um um ba um B upgrade mesmo pra
galera aí aqui ó informações de prox ó
então vai ficar salvo também aqui as
informações do prox caso você configure
um prox Acho legal isso o Rabbit a
sessão David são são as instâncias é
isso é as são é onde estava as
credenciais da instância a credencial da
instância né esse setting aqui é aquele
que a gente faz pela api lá de é um é um
global que você falou né é esse setings
aí é aquela opção de rejeitar chamada
ignorar grupo sempre online tudinho aqui
pessoal o SQS ó os dados do seu SQS aí
para quem vai utilizar ele não vai usar
o Rabbit e os dados do Type bot aqui ó
ah o tbot tem um um melhorzinho um
legalzinho aqui né Delis que também vai
ficar para pro próximo vídeo explicar
mais a fundo ele mas aqui aqui vai ficar
as configurações do tap bot tem umas
coisinhas que vocês vão ficar louco aqui
mas eu vou deixar para fazer essa Live
depois né faz uma outra Live para falar
disso né É deixa o suspense porque mudou
tudo é aqui Aqui vai ficar pessoal todas
as conversas ativas do tbot então para
você poder montar uma estatística ó
maravilhoso né e aqui são as
configurações né Deus globais né então
aqui caso você não Especifique na sessão
é caso você não não Especifique na
configuração ele vai usar o que tá aqui
né me Global não é isso isso
mandar a pode mandar a configuração de
forma individual mas caso não mande ele
usa essa daí por default aqui ó os dados
de web Hook que vocês têm também é muito
legal isso daqui para vocês montarem né
para quem vai montar SAS né Você pode
montar até mesmo um tipo de time em cima
disso e e aqui fica o websocket eu não
uso o websocket tanto mas para quem usa
aí vai ficar também as configurações
aqui e aqui no Prisma pessoal essa
tabelinha que é uma tabelinha interna
que é onde vai ficar as migrations né
então aqui no Prisma por exemplo
consigo eu consigo acompanhar né a gente
vai vai falar mais sobre isso também na
parte de manutenção mas acompanhar as
migrei os que rodaram caso uma migre não
Rode inteira você pode vir aqui Apagar o
registro e e ajustar de novo né
Então tá bem legalzinho mas isso aqui é
assunto mais para frente mas aqui vocês
podem acompanhar né pela própria PG AD
mim ó é legal que vocês vão conseguir
ter uma visão do banco agora o quanto
que a Evolution impacta no seu
servidor num banco específico ou num
servidor específico dependendo da
Estratégia que vocês vão utilizar aí né
ó quantas conexões tem então bem legal
opa e
Então pessoal alguém algum comentário
aí lá o Van Berto falou que vai ter que
refazer todo o painel que ele tava
fazendo É isso aí Vamberto A ideia é
essa mas mas vem pro bem isso daqui né
pessoal vem pro bem né mas assim tem
bastante coisa né isso aqui Pessoal hoje
é só um laboratório mesmo para vocês
verem que ela tá funcionando rodeia
evolu para vocês verem aqui ó então
poderia conectar nela normal né Eh
lembrando também que já tem o Postman né
David s para quem vai vai rodar ela sim
para quem vai rodar ela cadê Aqui ó
Postman agência deg code ê tem que mudar
isso aí também dav lá pro atende aí tem
que mudar a organização aí vou ver se eu
consigo só editar o nome que aí já fica
mais fácil é nesse processo já já deixa
né é pessoal já tem aqui também né isso
aqui também vai ser assunto para uma
outra Call mas muda alguns payloads aqui
né davids vai ficar mais prático de
algumas alguns end points né
e mas assim se você vier na 2.0 aqui
você vai conseguir conectar e utilizar e
lembrar pessoal de todo o feedback que
você puder passar não deixa de vir acho
que no github vai ser o lugar mais
adequado para vocês poderem
poderem no Gu Hub Evolution api ainda né
não é lá não né não lá evolu por causa
que tem os outros os
outros maner documentação essas
coisas então pessoal quem puder vir
também aqui colaborar para colocar
alguma alguma is alguma coisa que achou
nessa parte de setup tá bom e você que
Prom web vai ter aqui na comunidade eu
vou depois publicar esse link lá você
poder vai ter uma trad só pra gente
poder discutir sobre o setup da
Evolution então cada Live que eu fizer
agora essa semana a gente vai ter um
lugar só para centralizar ali o
bate-papo e quem não poder assistir ao
vivo quem vai vai mexer nso aí só na
semana que vem poder ter um lugar
central ali na nossa nova comunidade
tá pessoal tem algum comentário fazer
aqui possa PR para eles aí sobre a 2.0
que eu acho que é melhor o pessoal que
tá testando a 2.0 e tudo vai mandando lá
na PR claro deixa eu voltar aqui pera aí
iso para não não acabar confundindo né
pessoal Ah tá com problema aqui eles vão
achar que tá na na EV inteira né Aqui
tem essa PR que essa por request que o
Davidson fez a a
635 Tem tudo aqui né
David toda a arte que ele fez tá aqui né
isso aqui né É aí foi tipo aquele
planejamento inicial do que eu precisava
fazer E aí eu vou adicionou algumas
coisas mas os comites estão todos aí lá
os 145 arquivos alterados tem coisa para
caramba aí coisa aí mesmo para para ver
aqui né Então pessoal assim para vocês
lembrando sempre né que é um é um Alfa
não é nem um beta ainda né tá mesmo na
fase tá bem encaminhado muito bem
encaminhado mas o David ainda tem a
liberdade de de poder modificar o que
ele quiser aqui então usem com cautela
usem com essa com essa ideia em mente né
Eh quem tiver alguma dúvida coloca ali
na comunidade pra gente poder bater o
papo ali tá bater um papo sobre isso
daqui também porque é uma mudança
pessoal que vai impactar vocês né o você
pensa em fazer algum tipo de migração de
Instância alguma coisa nesse sentido
algum utilitário ou não Ou não tem como
fazer não dá para fazer sim porque que
eu falei eu só preciso pegar as
informações do arquivo que tá lá no
mongo por exemplo e salvar no posto ISO
aí já vai dar certo fazer questão
preciso tem uma série de coisas que eu
preciso fazer agora para sair do Alfa tá
que por exemplo é resolver tipo assim
deixar ela funcionando como tava antes
alguns parâmetros ainda não estão no
ponto ainda mas principalmente quando
integrações tá para quem US chatot
principalmente chatot né a oficial
também tem alguns ajustes para ser feito
então tô ainda terminando essa parte
para conseguir sair do Alf tenho que
integrar o Minio também pra gente tirar
o base 64 lá do bolso pesado as queres
também então isso é um ponto performance
que a gente vai fazer e alguns outros
detalhes que a gente já tá aqui no no
roadmap aqui para poder fazer então até
sair o alfa eu não tô me preocupando com
migração não tá porque quem quiser
testar e tudo aí faz instalação Limpa
pelo amor de de Deus não inventa dezar
aí que não vai funcionar e quem quiser
montar também n d algum tipo de script
alguma maneira de fazer isso aí também é
bom para PR própria comunidade poder
colaborar também com isso daí né É ajuda
a gente porque que que acontece pessoal
nessa migração aí vocês vão perder as
instâncias vai desconectar as instâncias
é né então para quem for pro dois vai
vai desconectar tudo tem que conectar de
tudo de novo então pode ser que
atrapalhe vocês em algum ponto para quem
tem muita conexão é só um CR code também
né vamos
vamos dizer que é só um um Car codzinho
para escanear ali né mas mas vai gerar
um um transtorno sei que tem muita gente
que vai é mais sensível em relação a
isso né É eu vi até o pessoal falando
que já queria usar direto né para usar
já o pos e tudo aí aquela questão espera
pelo menos o Beta porque agora no vai
mudar muita coisa vai mudar muito e vai
pode quebrar ali o tempo todo tá o ideal
seria assim para quem é para quem não
vai acompanhar porque isso aqui pessoal
é uma coisa que vocês vão ter que comer
acompanhar todo dia porque uma mudança
que o de faz agora daqui a pouco já muda
né David Então tem que ficar o dia
inteiro O David é uma máquina ele não
para de de fazer as coisas então assim
ontem era domingo domingo domingo de
manhã gente vamos conversar TR 3 da
tarde falei nossa cara pelo menos não
foi na hora da missa mas
assim assim ele não para é sábado
domingo feriado tá lá né então acho que
é legal porque vocês tem que para quem
for entrar nessa de utilizar o alfa ou
Beta vai ter que ter esse compromisso de
ficar o tempo inteiro olhando todo o
comite que ele fizer
e assim Nem sempre o de vai ter tempo de
ficar publicando também que ele fez é é
um trampo mais para quem é mais técnico
tá eu vou fazer meu trabalho aqui na
comunidade que eu falei para vocês é o
meu meu o meu trabalho é esse né mas
vocês que não são da promov web e também
não são da comunidade do do do Davidson
vocês fiquem atentos aí para não dar mal
aí né David o David vai fazer lá do lado
dele vou fazer do meu aqui mas assim é é
a gente tem esse compromisso né eu eu
com a Evolution também né com a
comunidade Evolution também de sempre
alertar né deses para não para não
atrapalhar vocês pessoal porque é uma
fase que assim eu sei que a maioria vai
querer utilizar só que a gente fala ó
não põe não põe em produção né e não tem
também como a gente ajudar vocês em
produção a resolver o problema de
cliente não não vai ter esse suporte tá
gente não pelo menos aqui não vai ter só
quando sair um estável né porque é uma
eh são várias coisinhas nos próximos
encontros vocês vão ver que mudou
integração com tbot mudou para melhor
mas mudou mudou também a um payload ou
outro de mensagem que vocês vão Enviar
Então vai ter que ter um trabalhinho de
vocês mudarem os workflows NN de vocês
quem montou o sistema vai ter que
adaptar o sistema Então não é uma coisa
de hoje para amanhã Isso aqui é uma
coisa que vai demorar um tempinho ainda
até todo mundo poder embarcar mesmo na
ideia da versão dois né E como o d falou
né ele não vai abandonar a versão 1 vai
existir algum tipo de update ali ontem
mesmo saiu update pra versão 1 ainda
1.8.1 né então não precisa pessoal você
desesperar não precisa ser correndo
atrás disso quem eu acho que quem
realmente vive disso e e eu eu sei que a
maioria de vocês aqui vive disso eh aqui
da promov web a gente vai ter uma
formação inteira só sobre isso porque eu
também eu também eu venho do e-mail
marketing vocês sabem né Eu sou do
e-mail marketing minha cabeça do e-mail
marketing então como eu já tô há mais de
um ano nessa área aí eu me sinto mais à
vontade agora de pelo que eu conheço
aqui que o Davison me ensina o Edson me
ensina de poder fazer a formação disso
né pessoal mas eu entendo que assim a
galera é mais é mais como é que eu vou
dizer assim é mais é mais precoce nas
coisas né já quer usar o negócio hoje já
então vocês fica fica fica esperto
pessoal eu vou ter aqui na na promov web
o laboratório na nossa comunidade a
gente vai ter bastante assunto sobre
isso eu quero vou fazer um movimento
grande ali em cima para quem usa
Evolution Eu sei que vocês dependem
disso se de preferência faz um
laboratório também faz uma instalação a
parte testa pega um work Flow que vocês
TM adapta ó isso aqui deu certo isso
aqui deu errado pelo menos essa fase
Beta pessoal para quando ele sair sair o
mais pronto possível né então a nosso
agora o meu papel aqui eu acho que todo
mundo que é gestor de comunidade vai ser
fazer a galera eh fazer uma transição
mais suave né e e também poder encontrar
algum problema que tiver ali pro passar
pra equipe pro Davis poder trabalhar
também né Mas vocês vão gostar de olha
odeio ouso dizer que para quem usa Type
bot com chat ut vai ser o negócio de
louco né
cara e e vai ser realmente uma uma
mudança grande no mercado pessoal eu já
acho que o Type Bot já mudou o mercado
assim demais demais pelo pelo esse 1 ano
e meio que eu tô aqui eu tô eu tô vendo
isso mas esse negócio novo quees colocou
ali que vai simplificar demais a vida de
vocês Nossa demais demais mesmo eu acho
que vai vai mexer com a com a galera aí
né de vocês ficarem cada vez mais à
vontade de utilizar ferramenta mais
possibilidades menos complexidade então
assim mas de novo né pessoal tudo isso
ainda é Beta tudo isso ainda é muito
novo né tudo isso é muito tá aí ainda
está sendo desenvolvendo o Davis tem a
liberdade Total ali de modificar o que
ele quiser porque é a fase que Ele tá a
hora que chegar Ó gente a partir daqui
já não vou mexer não vou mexer mais eu
vou só corrigir a gente volta aqui a
gente continua fazer nosso trabalho e
avisar vocês mas acho que é importante
ter isso daí né então montei uma área
especial na promov web ali só pra gente
poder falar da Evolution para que eu sei
que a galera ali acaba indo indo muito
com sede no po no pote d'água né então
vamos com calma tá gente é realmente
muito empolgante vocês vão ver ao longo
dessa semana como é que é É realmente
empolgante Mas vamos esperar vamos
esperar pelo menos n Dev sair um relisa
de date aí um beta mais estável para
você começar a utilizar mesmo mas quem
tiver à vontade quem tiver a boa vontade
de colaborar de testar fica à vontade eu
vou ter o meu vou fazer minha parte aqui
quem quiser quem é da comunidade da
promov web quiser vir junto comigo todo
dia a gente vai ter praticamente todo
dia a gente vai ter algum tipo de
laboratório né e e pegar esse embalo da
evolu agora mas teremos outros
Laboratórios na área de docker né e é
isso deixa só eu finalizar