﻿Olá pessoal tudo joia aqui Luiz nesse
vídeo eu vou falar com vocês de uma
atualização do portainer tá na semana
passada eu fiz um vídeo e eu expliquei
para vocês uma questão ali de
incompatibilidade do porer com o docker
26 e eu prometi para vocês que assim que
saísse uma atualização eu ia testar eu
ia validar eu ia voltar aqui com vocês
para fazer isso então se você tá com
problema para acessar o seu porte ou
para acessar o console no seu painer
Assiste esse vídeo até o final tá
pessoal eu sou o luí tá eu sou fundador
aqui da promov web e nesse vídeo eu vou
mostrar para vocês como atualizar o
piner acabou de sair ali atualização Eu
testei tá funcionando certinho Tá mas
tenho várias observações para fazer para
vocês tá pessoal então curte esse vídeo
se inscreve aqui na promov web acessa
promov weeb.com para você poder nossos
cursos são mais de 100 cursos mais de
1000 aulas cursos de docker curso de
mtic nhn chatot Type bot tem bastante
conteúdo para você lá tá pessoal
conteúdo sério aprofundado tá bom então
vale muito a pena conhecer e convidar
vocês para participarem do nosso grupo
do WhatsApp tá então promo.com bar
WhatsApp e a nossa comunidade oficial
que fica no discord então promo.com bar
discord para você poder acessar
Independente se você é aluno ou não lá
tem espaço para todo mundo se você é
aluno promov web nessa página aqui tem
um vídeo vai mostrar para você como é
que você vai desbloquear ali mais
recursos no nosso discord mas se você
não é aluno você é muito bem-vindo e lá
a gente pode bater um papo bem legal ali
sobre os sistemas tá pessoal então aqui
ó tudo começou então no começo de Março
quando o docker lançou a versão 26 eu
até fiz um vídeo aqui eu dei a minha
opinião ali o porer deu uma desculpa que
não deu tempo de atualizar foram 3S
meses e eles disseram que não teve tempo
Beleza a gente aceita né eu achei
estranho o porer ele reage muito bem a
essas mudanças né achei eu estranhei não
ter feito e estranhei a resposta tá
gente mas todo caso tem aqui um vídeo
onde eu dou minha opinião ali eu vou
deixar para você aqui na descrição
também esse vídeo Tá mas Como prometido
que nessa Nesse artigo eles lançaram
então uma versão que aqui para poder
corrigir né então olhando aqui no
próprio github do perer a gente vai ter
aqui pessoal a versão
2.20.1 tá que é essa versão aí que vai
fazer a
correção dessa dessa incompatibilidade
tá gente e aqui é importante pessoal
deixar bem claro algumas coisas tá não é
um bug tá bom não é a gente não pode
pode considerar como um bug tá é um
Breaking Change é diferente isso que ela
é previsto tá o bug ele não é previsto
Isso aqui é uma coisa que ela é prevista
o que aconteceu foi que o porer não
acompanhou o docker o docker avisou que
ia fazer a mudança o porer sabia da
mudança o por não se adequou à mudança o
docker seguiu o calendário dele e lançou
o release Então não é o porer bugado e
não é o docker bugado tá pessoal Outro
ponto importante aqui pessoal que eu
queria conversar com vocês é que hoje a
gente vive né na era da informação é
muita informação eu tenho aqui o meu
canal eu tenho aqui as minhas mentorias
tá esse conteúdo aqui pessoal é um
conteúdo da mentoria que eu tô colocando
também no YouTube porque eu tenho uma
responsabilidade com vocês eu promovo
painer desde 2016 al 2017 antes do
painer eu usava o Ranger né Acho que
existe o Ranger ainda né mas acabei indo
pro pro universo do porer então eu tenho
aqui muita gente muito aluno desde 2016
que usa o portainer comigo Então pessoal
eu tenho essa responsabilidade de trazer
esse conteúdo aqui tá Por mais que eu
tenha ali o meu conteúdo fechado esse
daqui é um conteúdo que vai ficar ali no
fechado mas eu vou trazer também pro
YouTube aqui porque eu acho que ele é um
conteúdo de de uso comum aí pra
comunidade tá gente e que eu falei para
vocês né Tem muita gente pessoal que tem
a palavra né e acho que é importante
isso mas é importante que você vocês
filtrem Bem também um ponto que eu não
gostei não é que eu não gostei tá gente
eu não tenho gostar de nada né mas é que
muita gente sugeriu para você fazer um
downgrade do docker pessoal o docker ele
é um sistema LTS daqui a pouquinho a
gente vai conhecer um um pouco mais
sobre isso então o docker ele é muito
testado não tem motivo nenhum para você
voltar a versão do docker o docker ele
tem a característica de você ficar
legada nele que que é isso pessoal você
vai instalar um docker E você vai ficar
com com aquela versão ali e você só vai
fazer o upgrade Se você realmente tiver
alguma compatibilidade ou precisar de
algo novo que é muito difícil acontecer
eu tenho aqui docker 17 docker 20 docker
19 né não tem necessidade de ficar
atualizando fazendo upgrade no docker em
si tá gente então se não tem necessidade
de ficar fazendo um upgrade muito menos
um
downgrade tá então é importante pessoal
quando vocês forem buscar informação na
Internet vocês vejam se aquilo ali é bom
mesmo tá quem é que tá falando para você
Aquilo é uma pessoa que sabe tá pessoal
então é importante eu eu trabalho com o
docker desde 2016 né eu fiz duas
certificações de docker eu sou ativo na
comunidade há muito tempo eu sou
mantenedor oficial do docker do Maic que
tem mais de 100 milhões de download
então eu tenho o meu lugar de fala aqui
pessoal de falar assim olha gente tem
coisa que você não tem que fazer eu sou
adepto a instaladores eu eu tenho o meu
também eu sou adepto a facilitadores Mas
eu sou muito mais adepto ao trabalho
técnico Sério que não vai colocar você
em rascada e eu pedi para você fazer um
downgrade do docer pessoal é colocar
você em rascada tá então fica muito
atento com o que que vocês consomem na
internet tá gente olhando aqui pessoal
nesse nesse release então descartamos a
possibilidade de Down umade totalmente
tá E aqui é importante entender pessoal
que essa versão aqui ó ficou bem claro
no release Que el ele é um STS Então
pessoal você a gente vai ter geralmente
dois tipos de versão de sistema a gente
vai ter o STS e o LTS o STS é a sigla de
short term support o LTS é a sigla de
long term support você vai encontrar
muito LTS aí por exemplo no Debian e no
bunto distros Linux eles eles exibem
junto ao nome né junto à versão é versão
tal e essa versão é um LTS Por que
pessoal porque na na no versionamento de
software existem as versões que são são
transientes ali são versões que elas não
serão as versões finais mas eu tenho que
passar por ela tá E essa versão 20 do P
Turner é isso tá tá bem claro aqui no
release dele que essa versão
2.20.1 é uma versão s CS é uma suporte
de curto prazo quer dizer o quê quer
dizer que rapidamente ela vai ser
substituída rapidamente eu vou ter uma
outra versão aqui oficial no lugar dela
tá bom E essa versão que ainda vai vir
ela que é a versão que você deveria tá
usando em produção tá gente e por isso
que é importante vocês entenderem e
vocês terem muito cuidado com o que
vocês consomem na internet tá gente uma
versão STS o próprio perer aqui ele
indica ele fala ele ele coloca bem claro
aqui no artigo aqui no release que é uma
versão que você vai
utilizar somente para corrigir esse bug
se você não tem esse bug você não tem
motivo nenhum para usar essa versão do
painer tá pessoal isso aqui é muito
importante entender por quê Porque ela
não foi devidamente testada Ela tem ela
tem as modificações ela tem as correções
mas ainda falta um pouco de de Rodagem
para ela para que eles considerem que é
uma versão estável pra produção aí em
massa Tá bom então a dica dou para vocês
é só vai fazer o que eu vou mostrar
nesse vídeo aqui assiste até o final
pessoal que é importante vocês
aprenderem tá mas você só vai precisar
aplicar isso daqui se você for um
candidato aqui a ter esse bug e eu vou
explicar daqui a pouquinho Como que você
sabe se você é ou não o sorteado aí que
precisa ajustar isso tá Então pessoal é
importante entender que é uma versão só
para quem tá com problema por Lu ruim
não tá ruim pessoal mas é importante
entender que essas versões que são ST
STS elas não são versões Beta elas são
versões eh São releases nesse caso
específico aqui ó por ele ser uma versão
totalmente eh diferente aqui pro porte
não tava no roadmap deles Eles colocaram
até como pré-lançamento então ainda não
é nemum versão oficial ainda E é só
recomendado para quem tá com esse bug se
o você não é um candidato não é uma
pessoa que está com esse bug você não
tem que não tem que usar essa versão
aqui tá a versão pessoal atual do
painer que a gente tem hoje é a
2.19.4 essa é a última versão estável do
perer tá se você quer atualizar o seu
perer atualiza PR essa versão aqui tá
que é a a latest dele tá então o que eu
vou mostrar para vocês aqui é só para
essa situação mesmo Tá bom então é bom
que fique bem claro isso para não gerar
nenhum tipo de dúvida tá E como que você
sabe que você é um sorteado aí pessoal
aqui no seu pertain você vai abrir aqui
a home do seu perer e você vai encontrar
aqui ó deixa eu dar um zoom para vocês
verem acho que vai ficar mais fácil aqui
na listagem dos environments né ó vai
ter aqui ó swarm 20.0 tá vendo aqui ó
swarm 20 26.0 se você tá usando a versão
26.0 e você está usando a versão
qualquer versão abaixo da 2.20 do perer
você é um candidato a fazer esse
processo aqui Luiz o meu swarm aqui ó é
o 24 é o 25 é o 20 é o 23 você não
precisa modificar nada a não ser que
você atualize o seu o seu docker pra
versão 26 o que eu também não recomendo
que você faça pessoal tá funcionando no
Mex tá o seu docker Ele é bem
configurador no nosso curso de docker eu
deixo muito claro para os alunos aplica
um Firewall na máquina não deixa sua
máquina exposta proteja a sua instalação
Tá então não tem necessidade de ficar
atualizando constantemente A não ser que
seja uma coisa muito bugada que é
Difícil acontecer no universo do docker
tá gente então não tem por mesmo você
fazer atualização Mas se você quiser
fazer é fica a seu critério tá é claro
que versões mais novas tem mais
correções de erro tem mais recursos tudo
tá mas você tá funcionando para você
pessoal deixa ele funcionando para você
tá o docker de novo é um sistema base
geralmente sistema base a gente deixa
ele legado tá então tá na 26 você vai
ter que fazer esse procedimento aqui
pessoal assim que sair uma correção
oficial assim que sair na versão latest
oficial eu vou remover esse vídeo aqui
do YouTube Eu vou eu vou deixar ele na
mentoria porque ali eu tenho um log
dessas coisas tá mas é importante
entender pessoal que é uma correção
temporária tá Luiz me encaixei então eu
tô aqui na versão 26 né e qual que é o
bug pessoal Quando você vem aqui num
contêiner vamos pegar aqui o nhn quando
eu vou abrir aqui o console ele dá uma
mensagem de erro ó não foi possível
captar detalhes da imagem Esse é o bug
tá aqui no artigo do painer ele deixa
bem claro ali no no artigo que são duas
propriedades que foram removidas da api
do docker tá e para quem não sabe
pessoal o docker ele é 100% uma API
aquela aqueles comandinho que a gente
roda na linha de comando na verdade
aqueles comandinho acionam a api dele
então em teoria você pode
manipular totalmente o seu docker
através do node htp donate n tá então el
bem legal é um detalhezinho mas é bem
legal de saber que ele é bem manipulável
porque ele nativamente é uma API tá
então Pessoal esse é o bug eu vou vir
aqui ó vou clicar em console ele não ele
simplesmente me ignora e não abre tá
então o que aconteceu pessoal saiu a
versão
2.20.1 e aqui eu tenho que fazer mais
uma observação para vocês tá aqui na
promov vai pessoal Nós temos dois
modelos de uso do sistema a gente tem o
instalador e a gente tem o curso de
docker no instalador Eu ofereço para
você um facilitador ou seja com único
comandinho você vai lá e e instala o
aplicação você configura no nosso painel
você não configura ele na linha de
comando né você usa linha de comando só
para instalar atualizar todo o restante
o nosso painel que faz e temos o curs de
docker aonde eu vou ali no máximo que eu
posso de flexibilidade e customização
então de um lado você tem o painel o
instalador que ele vai te dar um swarm
mas um swarm solo ou seja um swarm que
ele vai ter só o Manager né e ele não é
tão flexível porque não tem como eu
fazer um instalador que seja flexível tá
gente a a ideia de um instalador é
facilitar e Facil and você abre mão de
muitas coisas você não vai facilitar
algo tornando ele mais complexo tá então
tudo que é mais fácil acaba sendo
consequentemente mais simples porque
você abriu mão de várias coisas que são
complexas para tornar ele fácil tá então
esse é um ponto em relação ao instalador
no CR docker não no CR docker eu explico
passo a passo e o primeiro item que
vocês vão ter de diferente aí é o
próprio painer no instalador o painer
roda no modo standalone ele é por si só
o sistema que vai gerenciar porque a
ideia do Instalador é funcionar numa
única máquina tá mas o docker swarm
pessoal ele permite que você use
diversas máquinas ali você Espalhe a sua
instalação para esse cenário lá no nosso
C docker A gente também usa aqui o porer
agente Então você vai ter o container do
porer rodando e em cada máquina do seu
swarm você vai ter uma cópia do por
agente Então essa versão aqui 2.20.1 um
ela se aplica tanto pro portainer quanto
pro agente então se você você vai ter
que atualizar o porer de qualquer tipo
de instalação que você fez aí mas se
você usou o cur docker também vai ter
que mudar do agente então ficou Claro tá
gente fez pelo C docker muda a versão do
painer e do agente fez pelo instalador
muda só do painer Tá bom então só esse
essa observação aqui pessoal você vai
ter a tag que a gente volta utilizar
aqui ó é essa daqui ó
2.20.1 então o nosso trabalho vai ser
simples eu vou entrar no meu servidor eu
vou mudar a versão do stack para essa
versão aqui e tá tudo certo tá gente eu
vou rodar ali o comandinho de update se
você é meu
aluno se você é meu aluno Você já ouviu
falar do termos tá então se você fez
pelo instalador da promov web ou por
qualquer outro instalador você vai fazer
por aqui tá você vai acessar o tmos aqui
aqui no nosso canal no YouTube tem um
vídeo falando sobre tmos tá gente é um
terminal super elegante super fácil de
trabalhar vocês vão adorar trabalhar com
ele se você fez o nosso curs de docker
você tá um pouco à frente aí da galera
né você vai você vai ver que você vai
poder vir aqui e utilizar o vs code para
gerenciar o seu servidor tá então fica
também a seu critério aqui ah Lu eu fiz
lá o curso do docker eu configurei o meu
servidor já com o vs code então vai
ficar muito mais fácil sua vida tanto
para gerenciar o servidor quanto para
você poder vir aqui e fazer mesmo aqui
essas operações aqui tá então se você
fez o nosso curso de docker é só você
abrir o seu vs code que já tá
configurado lá né eu ensinei como é que
você configura ele e você vai abrir aqui
o stack do piner tá E você vai vir aqui
onde tá a palavrinha latest e vai
digitar
2.20.1 tá bom esse aqui é me processo
para quem tá fazendo pelo curso de
docker Luiz fiz como é que eu como é que
eu vou abrir o terminal pelo vs code ó
Terminal Novo terminal tá aqui para você
poder rodar o comando Tá bom então se
você tá no futuro já e fez o nosso curso
de docker esse aqui é o caminho mais
fácil para você tá Luiz eu não sou seu
aluno eu não fiz o seu curso beleza
pessoal esse vídeo também é para você tá
então eu vou ter aqui os meus stacks eu
vou digitar um comandinho que é o
chamado nano Nano
piner pamel tá então vou ter aqui o yaml
é um tipo de arquivo chamado yamel Então
você vai ter que encontrar aí na sua
instalação não sei se você usou o meu
instalador ou se você usou algum outro
instalador você vai encontrar esse
arquivo aqui que é o seu arquivo de
stack tá então eu vou usar o nano que é
o que eu tô mais acostumado a utilizar
aqui para editar arquivo aí pessoal como
como como falei PR vocês eu só vou vir
aqui ó e trocar essa palavrinha latest
para
2.20 um então vai ficar porer barra por
terner tro ce 2 p
2.20.1 tá eu só vou trocar aqui a imagem
Eu não vou mexer mais em nada se você
fez o nosso curs de docker esse seu
stack vai ser um pouco maior porque ele
vai ter também ali o stack do agente
Então você vai também encontrar ali a
imagem do agente e vai mudar a imagem do
agente Então fez pelo meu calador ou não
fez pelo meu calador é esse processo
aqui tá eu vou apertar CRL X e vou
apertar Y para salvar a modificação que
eu fiz no arquivo Então como vocês podem
ver pessoal eu tô rodando aqui ó no meu
no meu swarm o 2.19 tá bom E como é que
eu vou fazer para atualizar agora eu vou
vir aqui pessoal e vou rodar um
comandinho que é o docker pull tá
piner barra piner TR ce 2.
2.20.1 ele vai baixar a imagem para mim
tá lá no curso docker eu explico para
vocês que é importante você baixar a
imagem antes Luiz mas se eu atualizar o
serviço atualizar o steack ele não baixa
a imagem sozinho para mim ele baixe
pessoal mas é importante fazer isso
daqui para que fique debug cável tá para
que se der um problema por exemplo na
hora de download da imagem às vezes uma
camada não vem às vezes dá algum
problema ali não vai rodar o seu
contêiner Então vamos fazer a coisa
certa no no no no caminho certo vamos
baixar a imagem e depois vamos rodar o
comandinho para que fique tudo tudo bem
claro aqui tá pessoal então ele baixou
ali ó tudo certinho não deu nenhuma
mensagem de erro tá tudo certinho agora
eu vou rodar o comando para atualizar o
meu stack ó docker stack Deploy que
acontece pessoal eu já tem o meu stack
rodando então eu vou vir aqui ó e vou
dar um parâmetro do
prune aí eu vou docker stack Deploy
traço traço prune traço C E aí eu vou
passar o quê O nome do meu stack que é
piner
ponto iam pessoal isso aqui é para é
válido para quem é quem usa o meu
instalador ou usa algum outro instalador
que é baseado no meu instalador tá gente
então vai seguir as mesmas as mesmas
regrinhas ali tá o o nosso instalador
promov web aqui ele existe desde 2021
então el não é nenhuma novidade tá
pessoal estamos alcançando aí 5.000
instalações monitoradas funcionando dos
alunos ali os alunos tem alunos que tem
mais de 100 instalação né a a ideia do
Instalador é facilitar a vida Apesar de
eu ser muito mais parti dada de que você
faça o curso do docker e fique livre de
instalador porque não é difícil pessoal
você consegue configurar um servidor em
15 minutos pelo curs de docker então não
vejo motivo para facilitar mais ainda 15
minutos não vai comer o tempo de ninguém
né Então pessoal ó docker Deploy traço o
traço prune para poder remover o serviço
e recriar ele traço C painer e pro final
eu vou pôr o nome do meu serviço que é
painer tá então eu vou pegar um serviço
que já existe e eu vou atualizar ele tá
bom o docker 16 o
docker 20 pessoal ele D essas mensagens
aqui tá ele vai dar essas mensagens aqui
o docker ele vai P uma mensagem que é um
pouco diferente e aqui pessoal é
importante também fazer um comentário tá
agora no mês que vem Maio durante esse
mês agora de abril a gente vai passar
pelo curso de nhn depois vou passar pelo
curso do M eu vou começar o curso do M
mas um pouquinho antes do curso do mtic
ali eu vou passar um duas semanas com
vocês atualizando o curso de docker tá
já Nessa versão 26 do docker tem algumas
coisinhas ali que são diferentes eu vou
incluir também essa questão do piner e
também vou incluir a questão do Traffic
tá que é um vídeo pra semana que vem
também onde eu vou falar com vocês de
novo sobre o Traffic tá então se você é
aluno Essa é a chance se você não é
aluno é hora de entrar tá Então pessoal
vou vir aqui ó eu vou dar um F5 aqui
beleza então ele o docker me informou
aqui que rodou tá então beleza Tá aqui o
porer Você viu que começou a dar un
errinho ali porque ele eu mudei de
versão né E pode ver aqui embaixo
pessoal ó
2.20.1 eu recomendo que na primeira vez
que você acessar aqui o p você dê alguns
F5 nele aqui tá você atualize bem aqui
mesmo para eliminar qualquer tipo de
cche de jav script de CSS que ele faz tá
bom
e às vezes você atualiza o perer e e ele
fica com um cche Zinho da versão
anterior Tá bom então eu vi eu vou vir
aqui ó eu vou ver até que tem a versão
aqui dele ó eu posso se eu quiser
remover essa imagem aqui eu posso vir
aqui e remover ó essa imagem tá E vamos
testar se deu certo eu vou abrir com o
Nate n eu vou clicar em
console não deu mais o erro eu vou
trocar o ele não tem best ele tem um sh
e tô aqui ó dentro do console do meu nhn
então na versão
é 26 do docker e na versão
2.20 do nhn do pertain tá pessoal então
Problema resolvido vocês viram ali que
não é difícil tá a questão principal
acho aqui é vocês editarem esse arquivo
Então você vai ter que encontrar na sua
instalação aí qual que é aqui na promov
web e nos instaladores que são baseados
no nosso o arquivo se chama painer mesmo
tá ó então é só você vir aqui editar
esse porer e você trocar aqui a imagem
se você fez procurso de docker você vai
ter que mexer com um agente também tá
mudar ali a versão dele roda o
comandinho docker stack Deploy tá aqui
lá no curso de docker também mostra esse
comando para você mas é só você rodar
Esse comando aqui que você vai ter
sucesso para fazer a
atualização aqui do do nen e pessoal
entra aqui no nosso grupo do WhatsApp
entra também no nosso grupo do discord
tá pessoal para que você possa perguntar
tá bom então então como é um conteúdo
que eu deixei ele aberto é um conteúdo
da nossa mentoria que eu vou
disponibilizar ele aberto você sinta-se
à vontade de acessar o nosso discord ou
o nosso grupo do WhatsApp para você
poder fazer as suas perguntas também
mesmo que você não seja um aluno tá se
você é aluno é só você criar lá um um
tópico no fórum que eu ajudo você a
fazer ess esse esse upgrade aí Luiz
então quer dizer vamos vamos recapitular
pessoal tá num docker anterior ao 26 não
precisa fazer isso daqui tá você não tem
esse problema tá bom luí eu criei um
servidor agora no mês de março Então tem
que fazer tá bom Luís Criei um servidor
no mês de abril vai ter que fazer tá bom
pessoal então fica atento tá eu não vou
colocar essa versão do porin como padrão
instalador porque ela é uma versão STS
pessoal eu prefiro que você fique com o
bug Mas tem uma versão que é completa do
que você usa essa versão STS que ela é
uma versão ali para tapar o buraco tá
gente então é importantíssimo ter essa
noção e também pessoal fica esperto com
os com os conselhos que vocês ouvem na
internet Ah faz o upgrade do docker faz
o downgrade do docker pessoal não faz
isso tá não faz isso e eu mostrei no
outro vídeo como é que você rodaria pela
linha de comando é muito melhor pessoal
você ter o docker mais novo e e rodar de
outra maneira o comando do que você ter
que mexer na estrutura do docker o
docker é a casa se você mexe no alicer
da casa na Fundação da casa a casa
inteira fica ruim tá essa é a analogia
então não mexa no docker se alguém falou
para você assim ah vai lá e faz ade do
docker Ah muda ali fica atento tá gente
tem muita gente que planta o sistema
difícil para depois colher consultoria
em cima tem muita gente que oferece para
você algum tipo de setup e que sabe que
vai ser limitado que sabe que vai travar
sabe que vai dar problema para quê para
depois vender para você a consultoria ah
deixa que eu faço para você aqui uma
instalação melhor então aqui na Pr
promov web não é assim promov você tem
um curso de docker todo o meu
conhecimento ali de 8 anos de docker tá
tudo no curso ali e mês que vem a gente
vai atualizar ele e vou pôr mais coisas
ainda cada nova atualização e Já é a
terceira Edição do curs de docker tá
gente a cada edição eu coloco mais coisa
tá vocês vão gostar para caramba do
curso de docker que eu vou gravar aí
para vocês tá então acho que é
importante vocês ficarem bem atentos a
isso uma uma infraestrutura bem montada
Ela traz para você bastante benefício
traz tranquilidade tá inclusive nessa
situação onde vai sair uma versão nova
do Trafic vai mudar algumas coisas Ah o
painer ali bugou aconteceu alguma coisa
ali que deixou incompatível até mesmo
pessoal Nessa situação você tem a
tranquilidade que tem alguém que tá
realmente afim de te ajudar porque você
é membro da comunidade né os próprios
alunos se ajudam eu também vou ali e
ajudo aqui a equipe da promov web vai lá
e ajuda né mas você tem essa
tranquilidade que você tem algo bem
Montado não é algo facilitador não é
algo feito para ser rápido não é algo
feito só para quebrar seu galho é algo
feito de verdade escalável tá pessoal e
até quando dá um problema desse fica
muito mais fácil muito prático pessoal
todo mundo a promov web tem o mesmo
arquiv inho no mesmo lugar com a mesma
estrutura com as mesmas variáveis tá
então assim fica muito mais prático
quando eu montei o celador há TRS anos
atrás eu pensei nisso eu falei ah eu vou
montar uma estrutura onde tá todo mundo
igual imagina pessoal hoje est batendo
aí 5.000 né Eu acho que falta em torno
de umas 200 instalações aí para bater
5.000 5.000 máquinas que são que tem a
mesma estrutura tá é claro que o login
senha é diferente o endereço é diferente
mas no restante pessoal é a mesma coisa
fora a galera aí dos os mais de 1000
alunos aí que já Conseguiram fazer o
setar pelo docker e que eu não sei né Eu
sei dos 5.000 do salador porque eu tenho
o banco de dados dele mas eu não sei de
da galera que fez o curs docker isso não
tem controle chuto aí pelo menos mais
umas 2 3.000 instalações também né A a
PR me web cresceu bastante nesse sentido
Então pessoal fica atento tá aqui eu
tenho esse compromisso de trazer para
vocês daí como eu falo de porin há muito
tempo eu vendi um curso no udem de
portin em 2017 né Eu tenho mais de 600
alunos que compraram esse curso Então eu
tenho até com eles né de de trazer esse
esse sistema desde lá de trás eu tenho
essa obrigação e eu deixei essa aula da
mentoria aqui aberta para contar para
vocês e para que vocês tenham algum tipo
de feedback algum tipo de retorno da
minha parte em relação a isso tá gente
eu me sinto responsável pelo sucesso de
vocês aí tá e você não conseguir abrir o
console eu não consigo não consigo
dormir direito não consigo ficar bem
sabendo que tem uma pancada de aluno ali
que tá com problema tá gente então deixa
deixa o like no vídeo tá pessoal comenta
o que que você achou desse vídeo comenta
se deu certo aí você fazer essa muda tá
gente faz um backup antes da sua máquina
e é isso tá gente qualquer dúvida é só
acessar ali a a promov web É só você
acessar as nossas redes tá pessoal então
vou deixar aqui ó promove.com WhatsApp
promove.com discord e se você quiser me
encontrar nas redes sociais o meu nome é
Luiz of em todas as redes sociais você
vai me encontrar no Twitter no Facebook
no Instagram todo lugar aí você vai
conseguir me alcançar através aí do Luiz
tá então fica assim pessoal agradeço
vocês aí todos vocês por terem
participado vocês viram que não é
difícil tá assiste o vídeo com mais
calma para você poder fazer aí
esse esse ajuste tá faz com calma vê o
comandinho que eu digitei ele certinho
faz um backup da máquina aí você vai lá
e roda acho que é
importante você fazer esse backup e ter
essa segurança tá pessoal então um
grande abraço não deixa de curtir o
vídeo de se inscrever na promov web de
conhecer o nosso site conhecer o nosso
projeto eu tenho certeza que você vai
gostar um grande abraço pessoal até
mais