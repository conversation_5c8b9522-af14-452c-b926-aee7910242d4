@echo off
echo ========================================
echo   SISTEMA TJSP EXCELFILEMANAGER ENTERPRISE
echo ========================================
echo.
echo Iniciando sistema de extracao de precatorios...
echo.

REM Verificar se Python esta instalado
python --version >nul 2>&1
if errorlevel 1 (
    echo ERRO: Python nao encontrado!
    echo Instale Python 3.8 ou superior
    pause
    exit /b 1
)

REM Verificar se dependencias estao instaladas
echo Verificando dependencias...
python -c "import pandas, openpyxl, fitz, psutil" >nul 2>&1
if errorlevel 1 (
    echo Instalando dependencias...
    pip install -r requirements_excel_manager.txt
    if errorlevel 1 (
        echo ERRO: Falha ao instalar dependencias!
        pause
        exit /b 1
    )
)

REM Executar testes rapidos
echo.
echo Executando testes de validacao...
python tests\teste_excel_file_manager.py >nul 2>&1
if errorlevel 1 (
    echo AVISO: Alguns testes falharam, mas continuando...
)

REM Executar sistema principal
echo.
echo ========================================
echo   INICIANDO EXTRACAO DE PRECATORIOS
echo ========================================
echo.
echo Processando PDFs...
echo Logs serao salvos em logs\
echo Planilha sera gerada em output\
echo.

python extrator_tjsp_simples.py

if errorlevel 1 (
    echo.
    echo ERRO: Falha na execucao do sistema!
    echo Verifique os logs para mais detalhes.
) else (
    echo.
    echo ========================================
    echo   EXTRACAO CONCLUIDA COM SUCESSO!
    echo ========================================
    echo.
    echo Planilha Excel gerada com sucesso!
    echo Verifique o arquivo na pasta output\
)

echo.
pause
