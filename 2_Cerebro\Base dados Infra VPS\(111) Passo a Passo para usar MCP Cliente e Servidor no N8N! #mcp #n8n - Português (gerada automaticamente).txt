﻿"Olá pessoal, tudo joia? Aqui Luiz. Nesse"
vídeo vou falar com vocês da versão 1.88
do NN que acabou de sair e que tem a
maior atualização do NN na história em
"relação a agentes de A. Então, se você"
está desenvolvendo automações com a
"gente, chatbot, seja lá o que for, dá um"
"like nesse vídeo, comenta, se inscreve"
no canal e assiste até o final para você
ver o que que eles fizeram aqui em
"relação ao MCP. Então, pessoal, olha só,"
"essa versão 1.88, ela é um pré-release,"
"então não usem ela em produção, tá? Eu"
sei que vocês vão a partir de agora
"fazer teste com ela, mas é importante"
dizer que na gravação desse vídeo ela é
"uma versão pré-release, tá bom? E aqui,"
"pessoal, antes de começar a falar o que"
"que o Enn fez, eu quero dar uma breve"
explicação para vocês em relação ao MCP
e por que o NITN fez essa essa mudança
"aqui. Pessoal, olha"
"só, lá no ano passado a Antropk lançou o"
"MCP. é um é um conceito, é uma maneira,"
"é um protocolo, é uma maneira que ela"
criou de padronizar o acesso à API.
"Então vamos considerar, pessoal, que até"
então a maneira que eu tinha de utilizar
um agente era essa daqui. Eu criava o
"meu agente, eu tinha que fazer uma"
grande engenharia de
promptolas tools chamavam então as APIs.
Eu tinha que conhecer bastante de
"prompt, eu tinha que conhecer bastante"
de API para poder ter sucesso. Isso aqui
era uma barreira muito grande até mesmo
pros iniciantes poderem construir a sua
as suas mais simples
"automações. E quem é mais avançado, quem"
já tá um bom tempo também tinha
dificuldade. E o que que fez? Ele criou
"então esse tal de MCP, que é um"
"protocolo, é uma maneira de fazer. E o"
"MCP, pessoal, nada mais é do que"
padronizar. ele vai criar um padrão e
"uma abstração em cima das APIs. Então,"
"no modelo antigo, nesse modelo aqui, eu"
falo diretamente com os agentes e eu
falo diretamente com as APIs. Na ideia
"do MCP, o meu agente fala com o MCP em"
"linguagem natural e o meu MCP, ele capta"
aquela mensagem ali e ele vê como que
ele faz para poder acessar a API e
chamar as APIs. Então eu transferi de
"você ter que criar esse prompt, você ter"
que fazer todo esse trabalho de API e de
de engenharia de prompt e transferiu
então pro MCP para que o próprio agente
de A consiga entender como é que a PI
"funciona, entender as informações que"
"ele tem, preencher os parâmetros e fazer"
as chamadas na API.
"E só que aqui, pessoal, surgiram alguns"
"problemas nesse caminho, né, de lá para"
"cá, até então, de novembro até mais ou"
"menos um mês atrás, no universo NITN não"
se tinha suporte para MCP. A questão de
"um mês, um mês e meio surgiu um"
"community node. Então, a própria"
comunidade se movimentou e criou então
um community node para permitir que o
NITN tenha suporte para MCP. Só que
também criou outro problema.
"Esse Community Node, ele faz uma"
adaptação do que foi feito para
funcionar com o cloud ou até mesmo em
outras aplicações. Então não é um
"negócio feito pro NHN. E vamos dizer,"
"pessoal, com todo respeito, tá? Berava"
"ali a gambiarra, porque eu tinha que"
importar no meu servidor ou no meu
"contêiner um pacote npm externo, mexer"
"com variável de ambiente, mexer com"
"credenciais, às vezes fazer o pilô de"
"credencial, instalar um montão de pacote"
"ali, não era legal."
Vamos colocar que o iniciante tinha
"muita dificuldade de fazer isso daí,"
além de ser inseguro. Na minha visão é
"inseguro, né? Eu não ter um contêiner"
"ali, por exemplo, e eu também não ter um"
"controle da onde tá vindo esse código,"
"quem é que tá fazendo esse código, para"
onde estão indo as minhas credenciais.
"Ficava tudo muito escuro, né? Eu não"
tinha uma clareza de como é que
funcionava.
E eu também tenho aqui outras duas
"questões. Os provedores de serviço, eu"
sou fã do Click. O Clickap não tem um um
servidor MCP ainda. Eu também uso
diariamente o MDC e o Mtic também não
tem o MCP. Então a própria comunidade
começou a criar esses servidores. E eu
"falei para vocês, é uma adaptação para"
poder ter um mínimo suporte pro uso do
NHN. É muito técnico. Não era legal. Não
"é legal, pessoal. Eu sei que muitos de"
vocês usam. Eu entendo. Eu considero que
o cara mais avançado consegue
perfeitamente utilizar isso daí. Mas
também vamos concordar que não é
"trivial, não é o no code que a gente"
"espera. Então, apesar do M que não ter o"
"MCP, eu conseguiria criar um servidor"
MCP para mim com todas as dificuldades e
percalços que existem nesse processo.
"Até ontem, pessoal, era assim, até ontem"
eu tinha que esperar alguém na
"comunidade criar um servidor, eu tinha"
que esperar a própria empresa criar o
"seu próprio MCP oficial, vamos dizer"
assim. E eu falei até ontem porque hoje
o Enin lançou agora não só um cliente
"nativo MCP, mas também um servidor MCP."
"Então agora, além de você poder consumir"
"um serviço MCP, você vai poder criar os"
seus e você vai criar o seu MCP usando
os nodes que você já conhece. Então olha
"só, pessoal, vamos pegar o caso do MTIC."
"Não existe um MCP pro MTIC, mas eu mas"
existe o Node MTIC no NHN. Então agora
esse Node MTIC eu vou poder criar um
servidor dentro do meu NHN usando o node
do MTIC. E esse meu workflow vai ser um
servidor MCP. Você pode customizar de
"acordo com as suas necessidades, com os"
seus parâmetros. Pro usuário iniciante.
"É maravilhoso. Acabou terminal, acabou a"
"instalação de pacote, acabou aquelas"
variáveis de ambiente que ninguém
entendia.
"Pro cara avançado é maravilhoso, porque"
"você pode até usar o HTP, você vai poder"
colocar num no seu MCP literalmente o
"que você quiser, da maneira que você"
"quiser nos seus termos. Então, pessoal,"
isso aqui representa uma a assim o pé na
"porta. O Enate, ele chegou com tudo,"
"falou assim: ""Gente, tá, tá legal, tá"
todo mundo utilizando o MCP é uma coisa
"que vai ficar, pessoal, isso aqui é"
"padrão."" Eu até brinquei na aula ontem,"
"falei, gente, a faculdade de programação"
ou a faculdade aí ou o curso que vocês
"fizerem que não tiver suporte para MCP,"
que não tiver suporte para esse tipo de
"operação aqui, o cara já sai de lá"
"defasado no nosso Yamakers, que já vai"
"começar, já vamos colocar o MCP no meio."
"Já vou começar com o MCP porque pessoal,"
"ele quebra a barreira, ele muda um"
paradigma. Você não tem que aprender a
"PI, isso aqui, ó, praticamente sumiu. Se"
"você souber usar o NHN, você cria o seu"
"próprio servidor MCP com segurança, com"
"escalabilidade, sem gambiarra, da"
maneira que você precisa. Então eu vou
mostrar aqui para vocês na prática como
"que funciona isso daí, tá bom?"
"Então, olha só, até então vocês tinham"
"que recorrer a esse cara aqui, né? Vocês"
instalavam lá o community node do NHN
que tinha suporte para MCP. Recorria
aqui esse repositório do GitHub. Isso
"aqui, pessoal, não tá errado não, tá?"
Isso aqui tem uma finalidade. Ele foi
feito com certeza para que você possa
"utilizar com cloud, com outras"
"aplicações, mas vindo pro universo do"
"NRTN é muito complicado de fazer, tá?"
"Até quem é mais experiente, eu que sou"
"programador, tenho experiência só de NA,"
"eu tenho quase 5 anos, eu tenho"
dificuldade e em indicar para alguém
"isso daqui, né? Sem falar que tem um"
"pequeno detalhe aqui e a gente brinca,"
"né, que as coisas moram nos detalhes,"
"né? Eh, aqui eles vão listar para vocês"
"os oficiais, os os de terceiros e os da"
"comunidade. E tem um alerta aqui, ó, que"
tá bem escondidinho e que eu não vi a
"galera lendo nos vídeos do YouTube, que"
"é o seguinte, ó."
Observação. Os servidores da comunidade
não foram testados e devem ser usados
por sua conta e risco. Eles não são
afiliados nem endossados pela Antropic.
"Ou seja, usa aí, meu irmão, se der"
"problema, a culpa é sua, a culpa não é"
"minha. É difícil, tá, gente? Eu sei que"
"o opence é assim, essa é a ideia, mas eu"
"penso muito no iniciante, né? Aqui na"
comunidade tem uma galera que é
"iniciante, fico pensando, pô, será que"
ele leu isso daqui? Será que ele
entende?
que ele tá usando um negócio que sei lá
se funciona. E outra e questão de
"segurança, como é que fica, né? Então,"
até ontem era assim. O que que vocês vão
"fazer hoje, pessoal? Hoje eu vou ter"
"isso daqui, ó. Eu vou conseguir vir aqui"
"e usar esse trigger. Então, quando você"
"for criar o seu workflow, por exemplo,"
você vai criar o seu workflow e vai
"buscar por MC MCP Server Trigger, né? E"
você vai arrastar ele. Ele tem duas
opções. Ele tem a opção ali de URL. Essa
"daqui vai ser a URL dele. E aqui,"
"pessoal, ó, você que é aluno, tem que"
"fazer o nosso curso de escalabilidade,"
porque você vai chamar muito webook.
"Agora isso daqui é uma API HTP, é a"
"mesma regra do Web Hook. Pode ver, ó,"
que ele vai lá pro endereço do webhook.
"Então você vai ter que escalar, ainda"
mais se você for criar um MCP que você
"vai utilizar com o cursor, porque sim, é"
possível você criar o seu MCP aqui e
usar aonde você quiser. Não é um MCP de
"NHN, é um MCP. Então eu posso vir aqui"
criar o MCP aqui e usar o meu cursor
"tranquilamente. Só que olha só, vai"
"ficar chamando toda a hora, então você"
vai ter que escalar. Então lá no curso
tem as aulas para você poder escalar o
"seu Natn, tá pessoal? O curso"
"completaço, é um curso do zero. Você que"
"nunca viu o Docker na vida, você vai"
aprender o Docker e vai configurar
perfeitinho o servidor. E você que já tá
"um bom tempo no mercado e quer avançar,"
"lá também tem a parte avançada, que aí é"
"top mesmo, é avançado mesmo, tá?"
"Escalabilidade, multimanager, alta"
"disponibilidade, uso de túnel, tem"
conteúdo para todo mundo. O cara que
nunca viu e o cara que já tá rodando e
"quer crescer, tá gente? Mas tem que"
"fazer isso aqui, vai sobrecarregar ainda"
mais os seus web hooks que já são
"sobrecarregados. Então não fica,"
"pessoal, sem fazer o curso ali de"
"escalabilidade do Nat, tá? Olha só"
"pessoal, eu posso pôr uma autenticação"
no meu servidor. Recomendo que vocês
"façam isso, porque é uma URL que vai"
ficar pública e ela vai acessar alguma
"coisa. Então, por favor, né, coloquem"
"autenticação aqui, tá? E o caminho, você"
pode vir aqui para o caminho. Você que
"fez o nosso curso avançado do NHN, o"
"curso básico, pessoal, eu eu vou abordar"
"o básico, bebá, para você poder começar"
"bem usar o NHN, mas no avançado tem um"
módulo de congonde você pode customizar
URLs e domínios. Então você poderia até
customizar uma URL para ficar bem
bonitão mesmo ali o seu servidor MCP e
"criar os seus workflows ali dentro, tá?"
Eu vou fazer uma atualização do curso
avançado para colocar essa questão aqui
"de MCP para vocês, tá? Então, olha só,"
"criei aqui só isso, pessoal, só tem"
isso. Tem autenticação ou não e qual que
é o endereço. E aqui entra a parte
fácil. Eu posso colocar qualquer tool
que vocês já usam no nhn. Eu vou pôr
"aqui do mic que eu falei para vocês, né?"
"Então venho aqui, ó, põ credencial. Eu"
posso pôr uma descrição ou eu posso
deixar que o próprio NateN crie para mim
uma descrição. Fica seu critério aí.
"Você que é mais avançado, com certeza"
vai querer que ele você vai querer criar
"aqui as suas próprias descrições, né,"
para poder para que o MCP entenda melhor
para que que serve essa tool que você tá
"disponibilizando para ele, né? Eu vou"
"usar aqui, ó, contact create, pessoal."
"Aqui no node do MTIC, olha só, eu"
obrigatoriamente preciso que o contato
tenha um e-mail. Assim que ele tiver um
"e-mail, eu posso criar um contato no"
"MTIC. Então, o que que eu vou fazer"
aqui? Eu vou usar essa opção. Eu sei que
muitos de vocês quando usam esse modelo
"aqui, que vamos chamar ele agora de"
"antigo, né? Esse modelo aqui de chamar"
"tu, tem que fazer o prompt preencher os"
campos. Eu sei que nesse modelo aqui
todo mundo tinha dificuldade em usar
"esse cara aqui, esses que é o modo que o"
próprio NN vai tentar buscar a
"informação para você. Esse aqui,"
"pessoal, é o famoso from aí, né? Então,"
"quando você habilitar esse cara aqui, na"
"verdade, estou colocando um from aí no"
"meu no meu node. Então, eu tô informando"
que eu quero que o próprio MCP se vire
para poder preencher os campos para mim.
"E ele é muito bom em fazer isso daí, tá"
gente? Eu vou remover isso que eu criei
aqui para para não atrapalhar o meu
"exemplo, tá? Mas olha só, eu tenho aqui"
"um create contact, né? Então pus a"
"credencial do meu mic, defini aqui a"
"minha descrição, caso você queira, né?"
Usei o research create e falei assim:
"""Olha, ô ô ô NHN, você mesmo e o MCP e o"
"modelo da Open AI lá, ses que se virem"
para poder preencher os parâmetros para
"mim. Pessoal, entende a quebra de"
paradigma que é isso daqui? que eu não
"tenho nesse meu exemplo aqui, ó, eu não"
"tenho nem prompt, tá? Bem simples, tá,"
gente? Eu sei que vocês vão incrementar
"muito mais isso daqui, mas vamos ver"
como é que só o simples já funciona. Eu
"venho aqui, ó, e eu tenho a opção search"
"contact. Então, eu vim aqui, usei a"
"opção getmen, que é onde eu faço"
"pesquisa no MTIC, e o campo search, que"
é onde eu vou pôr o parâmetro de
"pesquisa. Eu falei assim: ""Olha, NHN, eu"
"quero que vocês preencha para mim, você,"
"o MCP e o modelo, quero que vocês"
"preencham para mim"". É isso, pessoal. É"
só isso. E aqui você vai poder colocar
diversas ferramentas. Eu posso pôr o
"mouse, posso pôr HTP, eu posso chamar"
outro workflow. O que vocês já estão
acostumados a fazer num workflow normal.
Vocês só vão converter os seus agentes
normais num agente MCP para ficar
perfeito. Eu vou copiar essa URL aqui.
"Essa URL, pessoal, eu posso configurar"
aonde eu quiser agora que tenha suporte
"para MCP SSE, tá? Inclusive aqui no Nen"
agora eu tenho aqui um agente bem
"simples, tá gente? Uma um agente simplão"
"mesmo. Vou até apagar aqui. Nada, não"
"tem nenhuma configuração nele. Ó, vim"
"aqui, não tem configuração nele."
Coloquei no modelo da Open AI. E agora
"vocês vão ter esse cara aqui, né, que é"
"o MCP cliente, ó. MCP Cliente Tool. Não"
é o community node. Esse daqui é o
oficial do NHN. Pode ver que ele é até
diferente do Community Node. Lá eu tinha
"que passar caminho, parâmetro, variável"
"de ambiente, chamar um executável."
"Não é legal, né? Esse daqui, pessoal, eu"
vou colocar qual que é o endereço do meu
"SSE endp. Então, é um MCP que roda num"
servidor através de um endereço. Se você
veio aqui e configurou uma
"autenticação, você tem que replicar aqui"
no seu cliente a autenticação. Se você
for utilizar qualquer outro sistema MCP
"do Sup, qualquer outro que sair aí"
"oficial, eles vão te dar uma URL. É só"
você vir aqui e colocar URL e colocar a
autenticação. Você vai conseguir rodar o
"seu MCP. E aqui, pessoal, ó, eu não"
tenho duas tools. Eu coloquei uma tool
que vai criar contato e uma tool que vai
"pesquisar contato, né? Então, no create"
"contact, eu inclusive recomendo que"
vocês coloquem no nome da Tool o que ela
"faz, ação. Então, é uma Tool, cria"
contato e na descrição usa a imaginação.
Aí chame essa Tool para você poder criar
"o contato quando você tiver um e-mail,"
"por exemplo, né? O search contact, né?"
Então vai pesquisar contact. Eu gosto de
"nomear minhas coisas em inglês, pessoal."
"É um toque que eu tenho, tá? Mas eu sei"
"que não é obrigado a fazer assim, tá?"
"Então, vim aqui, coloquei, olha só, a"
"hora que eu vier aqui, eu posso, eu"
posso pedir pro meu MCP cliente usar
todas as tools disponíveis no meu
servidor ali. Não sei. Pode ser que você
também queira isso. Ou eu posso vir aqui
"e falar: ""Não, cara, olha, eu quero que"
"você use só o search, não quero que você"
"use o create."" Perfeito. Se você for"
"configurar no cursor, mesma coisa, vai"
listar para você todas as opções lá para
"vocês poderem utilizar. Eu vou vir aqui,"
"ó, e eu vou usar então"
o Então o que que eu tô informando pro
"meu cliente aqui? Olha, o cliente chama"
lá o meu MCP de MTIC. O meu MCP de MT é
capaz de criar um contato e pesquisar um
"contato. E agora, pessoal, quando eu"
"vier aqui, eu posso dar um bom"
dia para ele. Bom dia. Não puges nem
"memória, tá? Ou simples do simples ali,"
"ó. Eu posso vir aqui, ó, e pôr o"
seguinte: Pesquise
informações.
"Informações sobre tá, meu teclado tá"
"difícil aqui,"
pessoal. Sobre o
contato <EMAIL>.
Ó
"lá, chamou o MCP. Então ele chamou a Tu,"
ele decidiu que ele deveria chamar a
"Tool, porque a minha TU tem uma uma a"
minha o meu MCP tem uma tool lá que é de
"pesquisar contatos. Então e trouxe aqui,"
"ó, ele foi no MTIC. Se eu vier agora"
"aqui no meu MCP, eu for olhar aqui na"
"execução dele, ó, tá aqui a execução"
"dele, ó. Eu vou vir aqui, ó. Ele veio"
"aqui, não vai aparecer nada, tá, gente?"
Eu acho que com o tempo eles vão
preencher essa opção aqui pra gente. Mas
"eu posso vir aqui, ó, e ver que ele foi,"
"ó, ele pesquisou o Luizof@gmail e"
retornou um contato. Ele jogou na Open
"aí, formatou bonitinho para mim. Tá"
"aqui, ó. Aqui estão as informações do"
contato Luiz @gmail. Todos os dados que
"estão lá no MTIC. Ó lá, ó. Pessoal, isso"
aqui muda paradigma. Eu não configurei
"nada. Eu não fiz uma linha de prompt,"
não fiz nada. Eu tô usando tudo no
padrão que eu falei para vocês. Tudo no
padrão nesse cara aqui de eu vir aqui e
"fazer dessa maneira, eu eu quase que"
inviabilizo pro iniciante poder
"utilizar. E pro cara que é mais técnico,"
ele fica com pé atrás demais usar isso
"daqui também. Agora, olha a facilidade"
que tem isso daqui. Esses nodes vocês já
"conhecem. É só você vir aqui, pegar todo"
o seu Google Agenda que você usa lá para
"criar evento, pesquisar evento, alterar"
"evento, aqueles mesmos nodes. Você vem"
"aqui agora, converte no seu servidor do"
Google Agenda e você simplesmente usa.
Isso aqui vai mudar e vai mudar muito o
"Nate. Ele, pessoal, ele deu ele não deu"
"um passo, ele deu um, ele correu na"
"frente de todo mundo fazendo isso daqui,"
"sem linha de código, sem instalação de"
"pacote externo, sem nada. Tudo que você"
tem no NN a partir de agora pode virar
uma tool para você utilizar aqui no seu
no seu MCP. Matou a pau. Curtiu pessoal?
"Dá um like no vídeo, dá uma força pro"
"canal, comenta, fala: ""Luiz, cara, agora"
"sim, agora vai, né? Você que tá no"
Yamakers e você que não está no
"Yamakers, vamos usar bastante isso aqui"
"no Yamakers, tá gente? Eu vou eu vou"
fazer muito conteúdo disso com vocês. O
Yamakers vai começar com essa parte de
fundamentos de a. Vamos falar sobre
"modelo, vamos falar sobre MCP, sobre"
prompt. Então vocês vão adorar trabalhar
"com isso daqui, tá gente? E a cereja do"
bolo sabe o que que é? Eu posso vir
"aqui, né? Contei para vocês, eu posso"
copiar essa URL minha de produção aqui
"do meu MCP. E olha só, se eu abrir o"
"cursor, vamos abrir com o meu cursor. Eu"
"posso vir aqui do próprio cursor, eu vou"
"ter aqui nas configurações dele, ó. Você"
"vem aqui nas configurações, cursor,"
"preferências, cursor settings, vai ter"
"que abrir a MCP. Ó lá, ó, adicionar o"
novo MCP global. Ele cai aqui nesse
nesse Jzon que você pode editar. Vou dar
"um zoom para vocês verem aqui, pessoal."
O autocompletar dele já cria para mim
"tudinho. Pronto, já ó lá. No caso do"
"nosso caso aqui, como é um SSE, que que"
você que que você tem que fazer? Duas
coisas. Eu vou nomear o meu MCP para
"MTIC. E eu vou colar aqui, ó. Eu vou pôr"
a propriedade URL. E eu vou colar a URL
lá do meu
"SSE, a URL do meu web hook lá do NN, ó."
"Só isso. Coloquei aqui, ó. Tá lá nome do"
meu do meu servidor e a URL dele. Eu
posso processar o que eu quiser agora
"lá. Eu posso chamar Git, não sei o que"
"que você vai fazer, mas você pode agora"
usar esse servidor externo. Se você vier
"aqui, por exemplo, ó, ele já achou. Ó"
"lá, ele achou o quê? Ele achou o nosso"
MCP MTIC e lá ó as duas tu search
contact e create contact. Então agora tá
disponível ao longo do seu chat nos
agentes aqui no o meu o meu cursor agora
consegue conversar com o meu MT. Para
que que eu vou usar isso daqui? Eu não
"sei, mas é só uma po para que você veja"
que é possível até conectar agora o seu
NHN no seu cursor pro seu N oferecer
"para vocês ainda mais contexto, ainda"
"mais prompt, o que você quiser agora em"
qualquer ferramenta que você for
"utilizar, que tiver suporte para"
"MCPSE, que é o padrão definitivo, não é"
"mais isso daqui, pessoal, a partir de"
"hoje, isso daqui eu sei que muita gente"
"vai utilizar, tem muita gente que já tá"
usando. usando isso aqui até em
"produção. Enfim, cada um tem a"
"consciência aí, né, do que tá usando."
Mas isso aqui resolveu 100%. Se existe
"um node pro NHN, você pode converter"
esse Node numa tool de MCP. Se não
"existe o node, você usa o HTP e você faz"
"o que você quiser, literalmente o que"
"você quiser agora com o MCP. Então,"
"pessoal, dá um like no vídeo, se"
inscreve no canal. Aqui na descrição tem
"o link do nosso grupo do WhatsApp, o"
grupo aberto do WhatsApp para você poder
entrar e bater um papo legal ali. A
"comunidade grande, a galera é bem"
"proativa, tá? Então dá um like no vídeo,"
"se inscreve no canal e valeu, um abraço"
"e até mais. Ficou show, hein? Valeu,"