# 🔑 GUIA COMPLETO DE CREDENCIAIS E APIS - POC WEBAGENT

**Data:** 2025-01-24  
**Objetivo:** Instruções detalhadas para obter todas as credenciais necessárias  
**Prioridade:** CRÍTICO para início do PoC  

---

## 🎯 RESUMO EXECUTIVO

### 🚨 **CREDENCIAIS CRÍTICAS PARA POC (2 SEMANAS)**
1. **Gemini API Key** - OBRIGATÓRIO (análise IA)
2. **Supabase Project** - RECOMENDADO (database)
3. **YouTube API Key** - OPCIONAL (metadados)

### 💰 **ESTIMATIVA DE CUSTOS**
- **Gemini API:** $50-100 para PoC completo
- **Supabase:** FREE (até 500MB)
- **YouTube API:** FREE (quota diária)

---

## 🤖 1. GEMINI API KEY (OBRIGATÓRIO)

### 📋 **PASSO A PASSO**

#### **1.1 Acessar Google AI Studio**
```
URL: https://aistudio.google.com/
```

#### **1.2 Fazer Login**
- Use sua conta Google pessoal ou corporativa
- Aceite os termos de uso do Google AI

#### **1.3 Criar API Key**
1. Clique em **"Get API Key"** no menu lateral
2. Clique em **"Create API Key"**
3. Selecione um projeto Google Cloud (ou crie novo)
4. Copie a API Key gerada

#### **1.4 Configurar no Projeto**
```env
# No arquivo .env
GEMINI_API_KEY=AIzaSy...your-actual-key-here
```

### 💡 **DICAS IMPORTANTES**
- **Segurança:** Nunca commite a API key no Git
- **Quota:** 15 requests/minuto no free tier
- **Modelos:** Use `gemini-2.5-pro` para melhor qualidade
- **Custo:** ~$0.002 por 1K tokens de input

### 🧪 **TESTE RÁPIDO**
```python
import google.generativeai as genai

genai.configure(api_key="sua-api-key")
model = genai.GenerativeModel('gemini-2.5-pro')
response = model.generate_content("Hello, Gemini!")
print(response.text)
```

---

## 🗄️ 2. SUPABASE PROJECT (RECOMENDADO)

### 📋 **PASSO A PASSO**

#### **2.1 Criar Conta Supabase**
```
URL: https://supabase.com/
```

#### **2.2 Criar Novo Projeto**
1. Clique em **"New Project"**
2. Escolha organização (ou crie nova)
3. Configure projeto:
   - **Name:** `webagent-poc`
   - **Database Password:** Senha forte
   - **Region:** South America (São Paulo)
   - **Pricing Plan:** Free

#### **2.3 Aguardar Provisionamento**
- Tempo: ~2-3 minutos
- Status: Aguarde "Project is ready"

#### **2.4 Obter Credenciais**
1. Vá para **Settings > API**
2. Copie as seguintes informações:
   - **Project URL**
   - **anon/public key**
   - **service_role key**

#### **2.5 Configurar no Projeto**
```env
# No arquivo .env
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres
```

### 💡 **CONFIGURAÇÕES ADICIONAIS**

#### **2.6 Configurar Storage Bucket**
1. Vá para **Storage**
2. Clique em **"New Bucket"**
3. Configure:
   - **Name:** `webagent-media`
   - **Public:** true (para PoC)
   - **File size limit:** 100MB

#### **2.7 Configurar Database Schema**
```sql
-- Execute no SQL Editor do Supabase
CREATE SCHEMA IF NOT EXISTS webagent;

-- Tabela para posts extraídos
CREATE TABLE webagent.posts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    platform VARCHAR(50) NOT NULL,
    post_id VARCHAR(255) NOT NULL,
    url TEXT NOT NULL,
    title TEXT,
    description TEXT,
    author VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    extracted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB,
    viral_score DECIMAL(5,2),
    analysis_data JSONB
);

-- Índices para performance
CREATE INDEX idx_posts_platform ON webagent.posts(platform);
CREATE INDEX idx_posts_viral_score ON webagent.posts(viral_score DESC);
CREATE INDEX idx_posts_created_at ON webagent.posts(created_at DESC);
```

### 🧪 **TESTE RÁPIDO**
```python
from supabase import create_client

url = "https://your-project.supabase.co"
key = "your-anon-key"
supabase = create_client(url, key)

# Teste de conexão
result = supabase.table("posts").select("*").limit(1).execute()
print("Conexão OK:", len(result.data) >= 0)
```

---

## 📺 3. YOUTUBE DATA API (OPCIONAL)

### 📋 **PASSO A PASSO**

#### **3.1 Acessar Google Cloud Console**
```
URL: https://console.cloud.google.com/
```

#### **3.2 Criar/Selecionar Projeto**
1. Clique no seletor de projeto (topo)
2. Clique em **"New Project"** ou selecione existente
3. Configure:
   - **Project Name:** `webagent-poc`
   - **Organization:** Sua organização

#### **3.3 Habilitar YouTube Data API**
1. Vá para **APIs & Services > Library**
2. Busque por "YouTube Data API v3"
3. Clique em **"Enable"**

#### **3.4 Criar Credenciais**
1. Vá para **APIs & Services > Credentials**
2. Clique em **"Create Credentials" > API Key**
3. Copie a API Key gerada
4. (Opcional) Clique em **"Restrict Key"** para segurança

#### **3.5 Configurar no Projeto**
```env
# No arquivo .env
YOUTUBE_API_KEY=AIzaSy...your-youtube-api-key-here
```

### 💡 **QUOTAS E LIMITES**
- **Quota diária:** 10.000 units (free)
- **Custo por request:** ~1-100 units
- **Videos.list:** 1 unit por vídeo
- **Search.list:** 100 units por busca

### 🧪 **TESTE RÁPIDO**
```python
import requests

api_key = "sua-youtube-api-key"
video_id = "dQw4w9WgXcQ"  # Rick Roll para teste
url = f"https://www.googleapis.com/youtube/v3/videos"

params = {
    'part': 'snippet,statistics',
    'id': video_id,
    'key': api_key
}

response = requests.get(url, params=params)
print("Status:", response.status_code)
print("Data:", response.json())
```

---

## 🔐 4. CONFIGURAÇÃO DE SEGURANÇA

### 🛡️ **BOAS PRÁTICAS**

#### **4.1 Arquivo .env**
```env
# NUNCA commitar este arquivo!
# Adicionar ao .gitignore

# Gerar chaves secretas seguras
SECRET_KEY=sua-chave-super-secreta-aqui-mude-em-producao
JWT_SECRET_KEY=sua-chave-jwt-secreta-aqui
```

#### **4.2 Gerar Chaves Secretas**
```python
import secrets

# Gerar SECRET_KEY
secret_key = secrets.token_urlsafe(32)
print(f"SECRET_KEY={secret_key}")

# Gerar JWT_SECRET_KEY
jwt_key = secrets.token_urlsafe(32)
print(f"JWT_SECRET_KEY={jwt_key}")
```

#### **4.3 .gitignore**
```gitignore
# Environment variables
.env
.env.local
.env.production

# API Keys
**/api_keys.txt
**/credentials.json

# Logs
*.log
logs/
```

---

## 📊 5. MONITORAMENTO DE CUSTOS

### 💰 **CONFIGURAÇÃO DE ALERTAS**

#### **5.1 Google Cloud (Gemini + YouTube)**
1. Vá para **Billing > Budgets & Alerts**
2. Clique em **"Create Budget"**
3. Configure:
   - **Amount:** $50 USD
   - **Threshold:** 80%, 90%, 100%
   - **Email:** <EMAIL>

#### **5.2 Supabase**
1. Vá para **Settings > Billing**
2. Configure alertas de uso
3. Monitor: Database size, API calls, Storage

#### **5.3 No Código (Controle Automático)**
```env
# No arquivo .env
MAX_DAILY_AI_COST_USD=100
MAX_MONTHLY_AI_COST_USD=2000
COST_ALERT_THRESHOLD_USD=50
ENABLE_COST_MONITORING=true
```

---

## ✅ CHECKLIST DE CONFIGURAÇÃO

### 🔑 **CREDENCIAIS**
- [ ] **Gemini API Key** obtida e testada
- [ ] **Supabase Project** criado e configurado
- [ ] **YouTube API Key** obtida (opcional)
- [ ] **Chaves secretas** geradas
- [ ] **Arquivo .env** configurado

### 🛡️ **SEGURANÇA**
- [ ] **.gitignore** configurado
- [ ] **API Keys** não commitadas
- [ ] **Restrições de API** configuradas
- [ ] **Alertas de custo** ativados

### 🧪 **TESTES**
- [ ] **Gemini API** testada
- [ ] **Supabase** conexão testada
- [ ] **YouTube API** testada (se configurada)
- [ ] **Database schema** criado

---

## 🚀 PRÓXIMOS PASSOS

### 📅 **APÓS CONFIGURAÇÃO**
1. **Testar todas as APIs** com scripts de exemplo
2. **Configurar ambiente virtual** Python
3. **Instalar dependências** do projeto
4. **Executar primeiro PoC** YouTube extraction
5. **Implementar análise Gemini** básica

---

**🎯 STATUS:** ✅ **GUIA COMPLETO - PRONTO PARA CONFIGURAÇÃO**  
**⏱️ TEMPO ESTIMADO:** 30-45 minutos para configuração completa  
**💰 CUSTO TOTAL:** $0-5 USD para setup inicial
