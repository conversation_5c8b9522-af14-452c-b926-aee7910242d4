﻿[Música]
Fala galera beleza pedril da Nasa aqui
especialista em automação e marketing e
no vídeo especial de hoje eu vou te
ensinar como construir do zero um painel
para fazer a gestão de conexões com API
de WhatsApp de maneira gratuita
ilimitada utilizando uma interface no
code parece mentira né mas nesse vídeo
eu vou te mostrar aí como em menos de 5
minutos mesmo que você não saiba nada de
programação e utilizando o seu próprio
computador sem gastar mais nada com isso
tá bem você vai ser capaz de colocar uma
aplicação que tem um alto poder de
rentabilidade para rodar aí rapidinho
para você que me acompanha eu quero
deixar meu muito obrigado para você que
tá chegando hoje seja muito bem-vindo e
muito bem-vindo ao canal da comunidade
zdg como de costume tem passo a passo
material está disponível aí na página de
apoio na descrição deste vídeo vídeo só
que antes de continuar deixa um like no
vídeo tá curte esse vídeo porque isso é
muito importante vocês fortalecem o
canal faz com que o YouTube entenda que
este conteúdo é relevante e leve ele
para mais pessoas aí da nossa comunidade
galerinha antes de continuar vou vou
mostrar aqui os dois componentes que nós
vamos utilizar primeiro deles é
Evolution api tá que é uma API
desenvolvida aí pelo Davidson tá e
baseada em bailes né nossa nossa novo
whisk aí e vamos utilizar o appsmith D
para criar o nosso painel de gestão D pí
vocês vão ver que é bem simples bem
rápido né você não precisa saber nada de
programação para botar isso aqui para
rodar aí no seu computador galerinha
Então bora lá passo a passo primeira
coisa que você vai fazer pra a gente né
rodar a solução no nosso PC vai ser
instalar o Git né instalar o node e
instalar o NG rock tá os links para
esses esses aplicativos aí estão na
página de apoio tá também dá um Google
aí é bem simples de você encontrar essas
aplicações Beleza então instala o Git
instala o node instala o NG rock Qual é
a primeira coisa que nós vamos fazer ó a
gente tá aqui no repositório tá dentro
do github da Evolution api Então nós
vamos clonar esse repositório na nossa
máquina localmente vamos instalá-lo tá
utilizando o node né o node aí é o
Engine o motor que vai rodar essa
aplicações JavaScript pra gente né e
depois nós vamos aí e depois de instalar
vamos e criar nossa aplicação no
appsmith e Vamos conectar ela al
utilizando o NG rock tá mas vai ser bem
bem tranquilo aqui é só um overview
geral sobre o que que nós vamos fazer
beleza eu tenho que encher uma linguiça
aqui porque vai ser uma uma instalação
tão rápida tão rápida que que não tem
nem muito que a gente a gente ficar
falando aqui beleza então ó
eh vamos lá vamos clonar então instalou
o Git Então você vai abrir o Git CMD na
sua máquina aqui no terminal você vai
escrever assim ó CD desktop tá porque eu
quero apontar o meu terminal paraa área
de trabalho ou seja tudo que eu fizer
aqui a partir de agora nesse terminal tá
vai eh cair na minha área de trabalho
então o que eu vou fazer Vou Clonar
Evolution api eu acho que não ten nenhum
repositório de Evolution aqui então ele
não vai dar erro não vamos ver tá beleza
ó clonou Evolution api que a gente faz
CD Evolution Api para entrar dentro da
pasta você vai perceber que a partir
desse momento aqui dentro da sua área de
trabalho você já tem uma pastinha de
Evolution api tá bom tá aqui ó legal eh
depois de clonar e a gente tem que fazer
um pequeno detalhe dentro da pasta CCE
você vai renomear o Dev env para env e
yml né yml então vamos fazer aqui um
detalhezinho ó
renomear sece Dev
env para env e tá renomeia ali que tá na
pasta search beleza renomeou que que
você vai fazer depois de nomear e entra
aqui ó no CD Vamos botar tudo bonitinho
né porque que às vezes você perde aqui
um detalhe do vídeo né e não funciona
beleza D npm instal agora que ele vai
fazer ele vai instalar a solução
localmente no nosso computador então nós
vamos rodar a solução dentro do nosso PC
nosso PC vai precisar tá ligado tá para
ele funcionar existe uma maneira da
gente colocar isso aqui para rodar na
nuvem né é inclusive é mais interessante
né a gente colocar para rodar na nuvem
se a gente for fornecer isso aqui de
maneira comercial quiser rentabilizar
essa solução tá tem na comunidade zdg né
eu ensino como fazer isso também aqui na
no canal tem alguns vídeos né O legal na
comunidade né Você entrando pra
comunidade você vai ter ali acesso ao o
nosso suporte né vai ter acesso ali a
mais de 450 videoo aulas sobre api
sistemas né Eh tô lá no plantão de
dúvidas de segunda a sexta também então
ali dentro da comunidade Você tem todo
um ambiente né pronto para te auxiliar a
rentabilizar esse conhecimento né lá nós
temos um módulo exclusivo sobre como
criar um um negócio né com isso plano de
negócio plano de ação modelo de negócio
né precificação tá então caso você
queira aí ter um um conhecimento mais
aprofundado referente a como explorar né
esse tipo de solução comercialmente e
mesmo que você não seja programador
recomendo que você entre aí na
comunidade zdg Beleza meu PC Deve estar
um pouquinho carregado mas geralmente
essa instalação ela é mais saia tá com
menos 100% de CPU vamos ver se ele vai
vai mais rápido tá geralmente é mais
rapidinho aqui tá mas é só aguardar o
instal dele e assim que ele terminar de
instalar você vai perceber que dentro da
nossa pasta agora de Evolution api ele
vai gerar assim que ele finalizar uma
pastinha chamada nde modules tá que que
são os módulos ali necessários pra gente
rodar a solução tá se ele começar a me
enrolar demais eu vou dar um pequeno
pause porque eu tava rodando algumas
Outras aplicações aqui tô criando uma
solução Zinha para grupo né a turma tá
tá bem bem carente de solução para grupo
fica a dica aí tá então
e tô criando uma solução Zinha bem
interessante aí vou trazer em breve
vídeo no canal tá Ah não já tá
terminando node mods terminando ali de
de fazer últimas instalações
e deixa ele dar aqui o terminar nosso
install né lá como eu disse já criou um
node de modos na hora que ele finalizar
também vai criar um pack Lock de Jon
aqui com as versões para nós
pcz tá chorando tadinho D Foi show tá
beleza ó na pack de log beleza ó depois
de dar o stal eu acho que a gente botar
o start nele npm start Qualquer coisa a
gente confere no repositório não me
lembro exatamente tá beleza eu acho que
ele já tá rolando o que eu preciso só
encontrar aqui
nesse ML é qual que é a porta padrão
dele tá aqui tão informações padrão 8080
Então vamos ver se ele já tá rodando
legal
local host
808 80 8 80
8 acho que el não deu start vamos
aguardar aqui ser que npm
start
npm Deixa eu confirmar qual que é aqui o
start que a gente dá Just a
moment
moment detalhe detalhe detalhe antes do
start temos que rodar uma Build aqui ó
npm
[Música]
H Esqueci esqueci galera não preparei ai
Vamos admitir não preparei a fundo
material já peguei clonei comecei a
executar então pode ser que esses 5
minutos virem
sete tá beleza estamos buildando a
solução que a solução tá fazendo Tá
criando aqui pra gente a pasta dist em
cima da pasta search já até criou
rapidinho Ó e agora vamos dar o start
agora a gente dá o start vamos L dar o
start npm start aí Aguarda alguns
minutos ele vai começar a trazer um tant
de log pra gente tá aguenta aguenta
Aguenta Coração ah lá beleza ah bonito
isso que eu precisava ver bonitinho
verdinho entrei ó lá local host 8080
seja bem-vindo a api tá funcionando
beleza pegou essa careta aqui ó estamos
pronto pro próximo passo então ó
instalamos api clamos renome aqui
entramos na pasta install build start tá
beleza agora no appsmith tá no appsmith
que que nós vamos fazer Ó você vai criar
uma continha aqui no appsmith eu já
tenho a conta aqui você faz login direto
com Google tá vou fazer nessa aqui
mesmo beleza beleza beleza ó que que
você vai fazer ó vai fazer o
Import como você já clonou a solução
aqui dentro da sua máquina dentro da sua
máquina tem uma pastinha chamada extra e
aqui dentro tem o appsmith aqui tem o
Manager Deon você pega o Manager de Jone
importa para cá Será que se eu jogar vai
vamos ver ah vai Uh Então essa interface
zinha aqui não sei se é no code low code
tá posso e me perdoe aí se eu tiver
enganado né na verdade não sei muito bem
quais são essas essas definições que é
no code que é low code mas assim é é bem
nada de code viu cara bem simples aqui e
tem os botões já prontos né então
importou teu Manager tá pronto tá aqui
você pode criar novas páginas colocar
mudar layout tá fazer o que você quiser
importou né Você vai no
Deploy deu Deploy ó teu Manager tá
pronto aqui beleza já pronto com botão
de access tá Como é que você acessa a
gente precisa passar as credenciais de
acesso primeiro é o RL e o Global Api
para acessar aqui na interface web a
gente precisa expor o nosso serviço tá
isso aqui foi até super rápido com o NG
rock então a gente vem aqui ó eu tenho o
NG rock já baixado dá um NG
rock a solução que eu quero expor ela tá
rodando na porta 8080 então o que que eu
vou expor aqui NG rock http 8080 tá
interessante né se usar em produção se
colocar isso aqui num domínio seu
próprio né no caso eu utilizaria ali api
comunidades edg né é fica mais
profissional tá mas aqui é só a título
de de desenvolvimento teste na nossa
máquina você pode usar em produção
também tá e se você quiser dar uma cara
mais profissional você faz dessa maneira
que eu tô te falando tá para acessar Eu
acho que vai ser isso aqui ó vamos ver
né Vamos fazer o teste Qualquer coisa se
não for lá vamos nós
eh dar uma perguntar PR os
universitários beleza aqui o Emil ele
traz pra gente tá um token de api
inclusive você pode alterar isso aqui
assim que você renomear esse arquivo
alterar esse token tá que s é o token
padrão do
eh da Evolution á lá beleza ó já
conectou certo aí vamos lá que que a
gente pode fazer criar uma nova Estância
tá nome da estância teste você pode dar
um Token para ela pode pedir para gerar
QR Code Pode configurar setagem de web
hooks né quais serão os eventos que você
vai receber pro webhook né aqui no caso
você vai passar um webhook por exemplo
webhook sites tá lá na na comunidade a
gente explora bem né Evolution api
aplicada a vários tipos de sistema então
aqui eu vou trazer mais só um overview
rápido porque a minha a minha minha
intenção era que esse vídeo fosse 5
minutos pra gente instalar mas tá um
pouquinho mais mais longo aqui beleza Tá
então eop né ativa e Hook settings né se
você quer que ele rejeite ou não
ligações para o seu WhatsApp se você
quiser que ele rejeite você vai botar
uma mensagem aqui ó não aceito ligação
né então assim que alguém ligar ele
rejeita a ligação e manda essa mensagem
Você pode ignorar ou não mensagens de
grupos mostrar o sempre online e mandar
leitura de mensagem quanto a mensagem
chegar leitura de status tá Você pode
ler status e mensagens beleza
websocket tá você pode estar utilizando
a conexão de websockets aqui tem
integração com rtam iq integração com
chatot né lá na comunidade a gente
explora essa integração com chatot né as
informações todas aqui e também a gente
tem integração com type bot Type bot a
gente faz via via terminal para para
chamar o Hook via Postman né Tem um
vídeo aqui anterior no canal da
comunidade que inclusive tá bombando
nesse momento deve ter aí umas 50.000
views cadê Aqui o typeb não 40 e poucos
mil views ó aqui ó 44.000 views onde eu
ensino você a fazer isso tá E então se
você quiser conectar o Type bot nessa
nessa Evolution api tá aqui tem um vídeo
bem explicativo também temos
instaladores na comunidade zdg Beleza
então vamos vamos criar uma nova
Instância aqui teste
ã Vamos criar ela acho que não precisa
passar numa credencial não aqui
beleza ó e aí ó dá aí um Connect né ele
traz o q ele tá gerando o CRR tá ele vai
trazer o q para você você lê o q
code Ah já tá até conectado já eu acho
que eu já li
essa já li essa Instância teste antes
beleza aqui você tem os botões de
controle da sua Instância tá ó lá as
configurações Aqui tem setagem de Type
bot Ó que legal tá legal a tbot tá aqui
ó você viu né como eu explorei demais a
solução mas a minha intenção não era
trazer uma exploração da solução em si
né porque isso a gente tem conteúdo na
comunidade aqui no canal também mas
ensinar você como criar aí um painel
para gerir as suas conexões de maneira
rápida de maneira simples né você
percebeu ali que mesmo que você não seja
programador usando seu próprio
computador sem gastar mais nada nada
zero tá de reais você pode criar um
painel para gerir aí apis de uso
ilimitado e gratuito inclusive fornecer
comercialmente para garantir quem sabe
uma renda extra no final do mês né Eu
costumo dizer pra galera aí que o
digital transformou minha vida né eu tô
aí há 2 anos e meio quase 3 anos aí
produzindo conteúdo e realmente né a
título mesmo de
eh de desenvolvimento né pessoal e até
mesmo desenvolvimento eh financeiro né o
o dig tal exploração desse tipo de
sistema aqui fornecimento comercial
desse tipo de de solução isso aqui
realmente traz um ganho grande Tá então
ó regaça as manguinhas aí bota a mão na
massa e vem para cima vem pra comunidade
zdg tamamo junto pedril da Nasa até a
próxima aí qualquer dúvida é só chamar e
esqueci hein beijo no coração fiquem com
Deus amo
vocês