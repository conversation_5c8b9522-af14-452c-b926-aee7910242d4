# 🏗️ ARQUITETURA MASTER UNIFICADA - WEBAGENT SOCIAL EXTRACTION

**Data:** 2025-01-24  
**Versão:** v1.0 - Arquitetura Master Completa  
**Autor:** Augment Code Orchestrator V5.0  
**Status:** ✅ **ENTERPRISE-GRADE FINAL**  

---

## 🎯 VISÃO ARQUITETURAL UNIFICADA

Sistema enterprise-grade para extração, análise e processamento de conteúdo viral multimodal, integrando **WebAgent + IA + Supabase + MCP** em arquitetura escalável para milhões de usuários.

### 🏛️ ARQUITETURA DE 5 CAMADAS

```mermaid
graph TB
    subgraph "LAYER 1: PRESENTATION"
        UI[Web Dashboard]
        API[REST API Gateway]
        WS[WebSocket Real-time]
        Mobile[Mobile App]
    end
    
    subgraph "LAYER 2: ORCHESTRATION"
        MCPRouter[MCP Router]
        MCPRegistry[MCP Registry]
        LoadBalancer[Load Balancer]
        RateLimiter[Rate Limiter]
    end
    
    subgraph "LAYER 3: PROCESSING ENGINES"
        WebAgent[WebAgent Core]
        AIEngine[AI Processing Engine]
        MediaEngine[Media Processing Engine]
        AnalyticsEngine[Analytics Engine]
    end
    
    subgraph "LAYER 4: DATA SERVICES"
        ExtractionService[Data Extraction]
        StorageService[Storage Service]
        CacheService[Cache Service]
        QueueService[Queue Service]
    end
    
    subgraph "LAYER 5: INFRASTRUCTURE"
        Supabase[Supabase Stack]
        Docker[Docker Containers]
        Redis[Redis Cache]
        S3[Object Storage]
    end
    
    UI --> API
    API --> MCPRouter
    MCPRouter --> WebAgent
    MCPRouter --> AIEngine
    WebAgent --> ExtractionService
    AIEngine --> MediaEngine
    ExtractionService --> Supabase
    MediaEngine --> S3
    AnalyticsEngine --> Redis
```

---

## 🔧 COMPONENTES PRINCIPAIS INTEGRADOS

### 1. WEBAGENT CORE - ORQUESTRADOR PRINCIPAL

```python
# WebAgent Master Orchestrator
from typing import Dict, List, Any
import asyncio
from dataclasses import dataclass

@dataclass
class ExtractionRequest:
    """Estrutura de requisição unificada"""
    topic: str
    platforms: List[str]
    max_results: int
    filters: Dict[str, Any]
    analysis_type: str
    user_id: str
    project_id: str

class WebAgentMasterOrchestrator:
    """Orquestrador principal do sistema"""
    
    def __init__(self):
        self.mcp_router = MCPRouter()
        self.ai_engine = AIProcessingEngine()
        self.media_engine = MediaProcessingEngine()
        self.analytics_engine = AnalyticsEngine()
        self.supabase_client = SupabaseClient()
        
    async def process_viral_extraction(self, request: ExtractionRequest):
        """Pipeline completo de extração viral"""
        
        # 1. Validação e autenticação
        await self.validate_request(request)
        
        # 2. Roteamento MCP para extração
        extraction_tasks = []
        for platform in request.platforms:
            task = self.mcp_router.route_extraction(platform, request)
            extraction_tasks.append(task)
        
        # 3. Execução paralela de extração
        raw_data = await asyncio.gather(*extraction_tasks)
        
        # 4. Processamento com IA
        ai_analysis = await self.ai_engine.analyze_multimodal(raw_data)
        
        # 5. Processamento de mídia
        media_analysis = await self.media_engine.process_media(raw_data)
        
        # 6. Análise de tendências
        trend_analysis = await self.analytics_engine.analyze_trends(
            raw_data, ai_analysis, media_analysis
        )
        
        # 7. Persistência no Supabase
        result_id = await self.supabase_client.save_analysis_result({
            'request': request,
            'raw_data': raw_data,
            'ai_analysis': ai_analysis,
            'media_analysis': media_analysis,
            'trend_analysis': trend_analysis
        })
        
        # 8. Geração de relatório final
        final_report = await self.generate_executive_report(result_id)
        
        return final_report
    
    async def validate_request(self, request: ExtractionRequest):
        """Validação de requisição e quotas"""
        # Verificar quota do usuário
        user_quota = await self.supabase_client.get_user_quota(request.user_id)
        if user_quota['used'] >= user_quota['limit']:
            raise QuotaExceededException("Quota diária excedida")
        
        # Validar parâmetros
        if not request.platforms:
            raise ValidationException("Pelo menos uma plataforma deve ser especificada")
        
        # Incrementar uso de quota
        await self.supabase_client.increment_quota_usage(request.user_id)
```

### 2. MCP ROUTER - ROTEAMENTO INTELIGENTE

```python
# MCP Router para roteamento inteligente
class MCPRouter:
    """Roteador MCP com load balancing e failover"""
    
    def __init__(self):
        self.servers = {
            'youtube': [
                MCPServer('youtube-primary', 'localhost:8001'),
                MCPServer('youtube-secondary', 'localhost:8002')
            ],
            'instagram': [
                MCPServer('instagram-primary', 'localhost:8003'),
                MCPServer('instagram-secondary', 'localhost:8004')
            ],
            'twitter': [
                MCPServer('twitter-primary', 'localhost:8005'),
                MCPServer('twitter-secondary', 'localhost:8006')
            ],
            'ai-processing': [
                MCPServer('ai-primary', 'localhost:8007'),
                MCPServer('ai-secondary', 'localhost:8008')
            ],
            'media-processing': [
                MCPServer('media-primary', 'localhost:8009'),
                MCPServer('media-secondary', 'localhost:8010')
            ]
        }
        self.health_monitor = HealthMonitor()
        self.load_balancer = LoadBalancer()
    
    async def route_extraction(self, platform: str, request: ExtractionRequest):
        """Rotear extração para servidor MCP apropriado"""
        
        # Selecionar servidor saudável com menor carga
        available_servers = self.health_monitor.get_healthy_servers(platform)
        if not available_servers:
            raise ServiceUnavailableException(f"Nenhum servidor {platform} disponível")
        
        selected_server = self.load_balancer.select_server(available_servers)
        
        # Executar extração com retry automático
        try:
            result = await selected_server.extract_viral_content(request)
            return result
        except Exception as e:
            # Tentar servidor backup
            backup_server = self.load_balancer.get_backup_server(available_servers, selected_server)
            if backup_server:
                return await backup_server.extract_viral_content(request)
            else:
                raise ExtractionException(f"Falha na extração {platform}: {e}")
    
    async def route_ai_processing(self, data: Dict[str, Any], analysis_type: str):
        """Rotear processamento de IA"""
        ai_servers = self.health_monitor.get_healthy_servers('ai-processing')
        selected_server = self.load_balancer.select_server(ai_servers)
        
        return await selected_server.process_with_ai(data, analysis_type)
    
    async def route_media_processing(self, media_data: List[Dict]):
        """Rotear processamento de mídia"""
        media_servers = self.health_monitor.get_healthy_servers('media-processing')
        selected_server = self.load_balancer.select_server(media_servers)
        
        return await selected_server.process_media(media_data)
```

### 3. AI PROCESSING ENGINE - IA MULTIMODAL

```python
# AI Processing Engine integrado
class AIProcessingEngine:
    """Engine de processamento de IA multimodal"""
    
    def __init__(self):
        self.langgraph_workflow = ViralContentWorkflow()
        self.crewai_system = ViralAnalysisCrew()
        self.autogen_system = ViralAnalysisAutoGen()
        self.gemini_client = GeminiViralAnalyzer()
    
    async def analyze_multimodal(self, raw_data: List[Dict]) -> Dict[str, Any]:
        """Análise multimodal completa"""
        
        # Preparar dados para análise
        unified_data = self.unify_platform_data(raw_data)
        
        # Executar análises em paralelo
        analyses = await asyncio.gather(
            self.langgraph_workflow.run_analysis(unified_data['topic']),
            self.crewai_system.analyze_viral_content(unified_data['topic']),
            self.gemini_client.analyze_viral_content(unified_data),
            return_exceptions=True
        )
        
        # Consolidar resultados
        consolidated_analysis = {
            'langgraph_analysis': analyses[0] if not isinstance(analyses[0], Exception) else None,
            'crewai_analysis': analyses[1] if not isinstance(analyses[1], Exception) else None,
            'gemini_analysis': analyses[2] if not isinstance(analyses[2], Exception) else None,
            'consensus_score': self.calculate_consensus_score(analyses),
            'confidence_level': self.calculate_confidence(analyses)
        }
        
        return consolidated_analysis
    
    def unify_platform_data(self, raw_data: List[Dict]) -> Dict[str, Any]:
        """Unificar dados de múltiplas plataformas"""
        unified = {
            'topic': '',
            'total_content': 0,
            'platforms': {},
            'aggregated_metrics': {
                'total_engagement': 0,
                'avg_sentiment': 0,
                'viral_indicators': []
            }
        }
        
        for platform_data in raw_data:
            platform = platform_data.get('platform')
            if platform:
                unified['platforms'][platform] = platform_data
                unified['total_content'] += platform_data.get('content_count', 0)
                unified['aggregated_metrics']['total_engagement'] += platform_data.get('total_engagement', 0)
        
        return unified
    
    def calculate_consensus_score(self, analyses: List) -> float:
        """Calcular score de consenso entre análises"""
        valid_scores = []
        
        for analysis in analyses:
            if isinstance(analysis, dict) and 'viral_score' in analysis:
                valid_scores.append(analysis['viral_score'])
        
        if valid_scores:
            return sum(valid_scores) / len(valid_scores)
        return 0.0
    
    def calculate_confidence(self, analyses: List) -> float:
        """Calcular nível de confiança baseado em concordância"""
        valid_scores = []
        
        for analysis in analyses:
            if isinstance(analysis, dict) and 'viral_score' in analysis:
                valid_scores.append(analysis['viral_score'])
        
        if len(valid_scores) < 2:
            return 0.5  # Baixa confiança com poucos dados
        
        # Calcular desvio padrão
        import statistics
        std_dev = statistics.stdev(valid_scores)
        
        # Converter para confiança (menor desvio = maior confiança)
        confidence = max(0.0, 1.0 - (std_dev / 100.0))
        return confidence
```

### 4. MEDIA PROCESSING ENGINE - PROCESSAMENTO MULTIMODAL

```python
# Media Processing Engine
class MediaProcessingEngine:
    """Engine de processamento de mídia multimodal"""
    
    def __init__(self):
        self.ffmpeg_processor = ViralVideoProcessor()
        self.opencv_analyzer = ViralVisualAnalyzer()
        self.remotion_generator = ViralVideoGenerator()
    
    async def process_media(self, raw_data: List[Dict]) -> Dict[str, Any]:
        """Processamento completo de mídia"""
        
        media_analysis = {
            'video_analysis': {},
            'image_analysis': {},
            'audio_analysis': {},
            'generated_content': {}
        }
        
        # Extrair URLs de mídia
        media_urls = self.extract_media_urls(raw_data)
        
        # Processar vídeos
        if media_urls['videos']:
            video_tasks = [
                self.process_video(url) for url in media_urls['videos'][:10]  # Limitar a 10
            ]
            video_results = await asyncio.gather(*video_tasks, return_exceptions=True)
            media_analysis['video_analysis'] = [r for r in video_results if not isinstance(r, Exception)]
        
        # Processar imagens
        if media_urls['images']:
            image_tasks = [
                self.process_image(url) for url in media_urls['images'][:20]  # Limitar a 20
            ]
            image_results = await asyncio.gather(*image_tasks, return_exceptions=True)
            media_analysis['image_analysis'] = [r for r in image_results if not isinstance(r, Exception)]
        
        # Gerar conteúdo viral
        if media_analysis['video_analysis'] or media_analysis['image_analysis']:
            generated_content = await self.generate_viral_compilation(media_analysis)
            media_analysis['generated_content'] = generated_content
        
        return media_analysis
    
    async def process_video(self, video_url: str) -> Dict[str, Any]:
        """Processar vídeo individual"""
        try:
            # Download temporário
            video_path = await self.download_media(video_url)
            
            # Análise com FFmpeg
            video_info = self.ffmpeg_processor.get_video_info(video_path)
            
            # Análise visual com OpenCV
            visual_analysis = self.opencv_analyzer.analyze_video_engagement(video_path)
            
            # Extrair momentos virais
            viral_moments = self.ffmpeg_processor.detect_viral_moments(video_path)
            
            return {
                'url': video_url,
                'info': video_info,
                'visual_analysis': visual_analysis,
                'viral_moments': viral_moments,
                'processing_status': 'success'
            }
        except Exception as e:
            return {
                'url': video_url,
                'error': str(e),
                'processing_status': 'failed'
            }
    
    async def process_image(self, image_url: str) -> Dict[str, Any]:
        """Processar imagem individual"""
        try:
            # Download temporário
            image_path = await self.download_media(image_url)
            
            # Análise facial
            face_analysis = self.opencv_analyzer.analyze_face_engagement(image_path)
            
            # Análise de cores
            color_analysis = self.opencv_analyzer.analyze_color_psychology(image_path)
            
            # Detecção de texto
            text_analysis = self.opencv_analyzer.detect_text_in_image(image_path)
            
            return {
                'url': image_url,
                'face_analysis': face_analysis,
                'color_analysis': color_analysis,
                'text_analysis': text_analysis,
                'processing_status': 'success'
            }
        except Exception as e:
            return {
                'url': image_url,
                'error': str(e),
                'processing_status': 'failed'
            }
    
    def extract_media_urls(self, raw_data: List[Dict]) -> Dict[str, List[str]]:
        """Extrair URLs de mídia dos dados brutos"""
        media_urls = {
            'videos': [],
            'images': [],
            'audio': []
        }
        
        for platform_data in raw_data:
            content_items = platform_data.get('content', [])
            for item in content_items:
                # Vídeos
                if 'video_url' in item:
                    media_urls['videos'].append(item['video_url'])
                
                # Imagens
                if 'image_urls' in item:
                    media_urls['images'].extend(item['image_urls'])
                
                # Áudio
                if 'audio_url' in item:
                    media_urls['audio'].append(item['audio_url'])
        
        return media_urls
```

### 5. SUPABASE INTEGRATION - INFRAESTRUTURA UNIFICADA

```python
# Supabase Client Unificado
class SupabaseClient:
    """Cliente unificado para todas as operações Supabase"""

    def __init__(self):
        self.client = create_client(
            supabase_url=os.getenv('SUPABASE_URL'),
            supabase_key=os.getenv('SUPABASE_ANON_KEY')
        )
        self.storage = self.client.storage
        self.db = self.client.table

    async def save_analysis_result(self, analysis_data: Dict) -> str:
        """Salvar resultado completo de análise"""

        # Salvar projeto de extração
        project_data = {
            'user_id': analysis_data['request'].user_id,
            'name': f"Análise: {analysis_data['request'].topic}",
            'platforms': analysis_data['request'].platforms,
            'keywords': [analysis_data['request'].topic],
            'status': 'completed',
            'config': {
                'max_results': analysis_data['request'].max_results,
                'filters': analysis_data['request'].filters
            }
        }

        project_result = await self.db('extraction_projects').insert(project_data).execute()
        project_id = project_result.data[0]['id']

        # Salvar conteúdo viral extraído
        viral_content = []
        for platform_data in analysis_data['raw_data']:
            for content_item in platform_data.get('content', []):
                viral_item = {
                    'project_id': project_id,
                    'platform': platform_data['platform'],
                    'content_type': content_item.get('type', 'post'),
                    'external_id': content_item.get('id'),
                    'title': content_item.get('title'),
                    'description': content_item.get('description'),
                    'author_username': content_item.get('author'),
                    'content_url': content_item.get('url'),
                    'media_urls': content_item.get('media_urls', []),
                    'hashtags': content_item.get('hashtags', []),
                    'metrics': content_item.get('metrics', {}),
                    'viral_score': content_item.get('viral_score', 0),
                    'sentiment_score': content_item.get('sentiment_score')
                }
                viral_content.append(viral_item)

        if viral_content:
            await self.db('viral_content').insert(viral_content).execute()

        # Salvar análise de IA
        ai_analysis_data = {
            'project_id': project_id,
            'analysis_type': 'multimodal_ai',
            'result': analysis_data['ai_analysis'],
            'confidence_score': analysis_data['ai_analysis'].get('confidence_level', 0),
            'processing_time': analysis_data.get('processing_time', 0)
        }

        await self.db('analysis_results').insert(ai_analysis_data).execute()

        # Salvar análise de mídia
        if analysis_data.get('media_analysis'):
            media_analysis_data = {
                'project_id': project_id,
                'analysis_type': 'media_processing',
                'result': analysis_data['media_analysis'],
                'processing_time': analysis_data.get('media_processing_time', 0)
            }

            await self.db('analysis_results').insert(media_analysis_data).execute()

        return project_id

    async def get_user_quota(self, user_id: str) -> Dict[str, int]:
        """Obter quota do usuário"""
        user_result = await self.db('users').select('*').eq('id', user_id).execute()

        if user_result.data:
            user = user_result.data[0]
            return {
                'limit': user['api_quota_daily'],
                'used': user['api_quota_used'],
                'remaining': user['api_quota_daily'] - user['api_quota_used']
            }

        return {'limit': 0, 'used': 0, 'remaining': 0}

    async def increment_quota_usage(self, user_id: str, amount: int = 1):
        """Incrementar uso de quota"""
        await self.db('users').update({
            'api_quota_used': self.client.rpc('increment_quota', {'user_id': user_id, 'amount': amount})
        }).eq('id', user_id).execute()

    async def store_media_file(self, file_data: bytes, file_name: str, bucket: str = 'viral-content') -> str:
        """Armazenar arquivo de mídia"""
        try:
            result = await self.storage.from_(bucket).upload(file_name, file_data)
            if result.error:
                raise Exception(f"Erro no upload: {result.error}")

            # Obter URL pública
            public_url = self.storage.from_(bucket).get_public_url(file_name)
            return public_url

        except Exception as e:
            raise Exception(f"Falha no armazenamento: {e}")
```

### 6. DEPLOYMENT CONFIGURATION - DOCKER COMPOSE MASTER

```yaml
# docker-compose.master.yml - Configuração completa
version: '3.8'

services:
  # Supabase Stack
  supabase-db:
    image: supabase/postgres:15.1.0.147
    environment:
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: postgres
    volumes:
      - supabase-db:/var/lib/postgresql/data
      - ./supabase/migrations:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  supabase-studio:
    image: supabase/studio:20240101-ce42139
    environment:
      SUPABASE_URL: http://supabase-kong:8000
      SUPABASE_ANON_KEY: ${SUPABASE_ANON_KEY}
    ports:
      - "3000:3000"
    depends_on:
      - supabase-kong

  supabase-kong:
    image: kong:2.8-alpine
    environment:
      KONG_DATABASE: "off"
      KONG_DECLARATIVE_CONFIG: /var/lib/kong/kong.yml
    volumes:
      - ./supabase/kong.yml:/var/lib/kong/kong.yml
    ports:
      - "8000:8000"
    depends_on:
      - supabase-db

  # WebAgent Core Services
  webagent-orchestrator:
    build:
      context: ./webagent-core
      dockerfile: Dockerfile
    environment:
      SUPABASE_URL: ${SUPABASE_URL}
      SUPABASE_ANON_KEY: ${SUPABASE_ANON_KEY}
      GEMINI_API_KEY: ${GEMINI_API_KEY}
      REDIS_URL: redis://redis-cache:6379
    volumes:
      - webagent-data:/app/data
      - ./config:/app/config
    ports:
      - "8080:8080"
    depends_on:
      - supabase-db
      - redis-cache
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 2G
          cpus: '1.0'

  # MCP Servers
  mcp-youtube:
    build:
      context: ./mcp-servers/youtube
      dockerfile: Dockerfile
    environment:
      YOUTUBE_API_KEY: ${YOUTUBE_API_KEY}
      REDIS_URL: redis://redis-cache:6379
    ports:
      - "8001:8001"
      - "8002:8002"
    deploy:
      replicas: 2

  mcp-instagram:
    build:
      context: ./mcp-servers/instagram
      dockerfile: Dockerfile
    environment:
      INSTAGRAM_USERNAME: ${INSTAGRAM_USERNAME}
      INSTAGRAM_PASSWORD: ${INSTAGRAM_PASSWORD}
      REDIS_URL: redis://redis-cache:6379
    ports:
      - "8003:8003"
      - "8004:8004"
    deploy:
      replicas: 2

  mcp-twitter:
    build:
      context: ./mcp-servers/twitter
      dockerfile: Dockerfile
    environment:
      TWITTER_USERNAME: ${TWITTER_USERNAME}
      TWITTER_PASSWORD: ${TWITTER_PASSWORD}
      REDIS_URL: redis://redis-cache:6379
    ports:
      - "8005:8005"
      - "8006:8006"
    deploy:
      replicas: 2

  mcp-ai-processing:
    build:
      context: ./mcp-servers/ai-processing
      dockerfile: Dockerfile
    environment:
      GEMINI_API_KEY: ${GEMINI_API_KEY}
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      REDIS_URL: redis://redis-cache:6379
    ports:
      - "8007:8007"
      - "8008:8008"
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 4G
          cpus: '2.0'

  mcp-media-processing:
    build:
      context: ./mcp-servers/media-processing
      dockerfile: Dockerfile
    environment:
      REDIS_URL: redis://redis-cache:6379
      S3_BUCKET: ${S3_BUCKET}
      S3_ACCESS_KEY: ${S3_ACCESS_KEY}
      S3_SECRET_KEY: ${S3_SECRET_KEY}
    ports:
      - "8009:8009"
      - "8010:8010"
    volumes:
      - media-processing:/app/temp
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 8G
          cpus: '4.0'

  # Cache and Queue
  redis-cache:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    command: redis-server --appendonly yes --maxmemory 2gb --maxmemory-policy allkeys-lru

  # Load Balancer
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./nginx/logs:/var/log/nginx
    depends_on:
      - webagent-orchestrator
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'

  # Monitoring
  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus

  grafana:
    image: grafana/grafana
    ports:
      - "3001:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD}
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana:/etc/grafana/provisioning

volumes:
  supabase-db:
  redis-data:
  webagent-data:
  media-processing:
  prometheus-data:
  grafana-data:

networks:
  default:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
```

---

## 📊 MÉTRICAS E MONITORAMENTO

### HEALTH CHECKS AUTOMÁTICOS
- **Supabase**: Verificação de conectividade PostgreSQL
- **MCP Servers**: Health endpoints em cada servidor
- **Redis**: Verificação de cache e performance
- **WebAgent**: Monitoramento de recursos e latência

### ALERTAS CONFIGURADOS
- **Quota Limits**: Alertas quando usuários atingem 80% da quota
- **Server Health**: Notificações quando servidores ficam indisponíveis
- **Performance**: Alertas para latência > 5 segundos
- **Storage**: Monitoramento de uso de disco e S3

### MÉTRICAS COLETADAS
- **Requests/sec**: Volume de requisições por segundo
- **Response Time**: Tempo médio de resposta
- **Error Rate**: Taxa de erro por endpoint
- **Resource Usage**: CPU, memória, disco por serviço

---

**Status:** ✅ **ARQUITETURA MASTER UNIFICADA COMPLETA**
**Próxima Fase:** Sistema de Rastreamento e Índices
