#!/bin/bash

# ===================================================================
# CONFIGURAÇÃO DE BACKUP AUTOMATIZADO N8N
# ===================================================================

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Configurações padrão
DEFAULT_SCHEDULE="0 2 * * *"  # 2:00 AM todos os dias
DEFAULT_RETENTION="30"        # 30 dias
DEFAULT_BACKUP_DIR="./backups"

# Funções de log
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}[SETUP]${NC} $1"
}

# Banner
show_banner() {
    echo -e "${BLUE}"
    echo "====================================================================="
    echo "        🕒 CONFIGURAÇÃO DE BACKUP AUTOMATIZADO N8N"
    echo "====================================================================="
    echo -e "${NC}"
}

# Menu de configuração
show_menu() {
    echo ""
    echo "Escolha o tipo de agendamento:"
    echo ""
    echo "1) 📅 Diário às 2:00 AM (Recomendado)"
    echo "2) 🕐 A cada 6 horas"
    echo "3) 📅 Semanal (Domingo às 3:00 AM)"
    echo "4) 📅 Personalizado"
    echo "5) ❌ Desabilitar backup automático"
    echo "6) 📋 Ver agendamentos atuais"
    echo "7) 🚪 Sair"
    echo ""
}

# Configurar agendamento diário
setup_daily_backup() {
    SCHEDULE="0 2 * * *"
    DESCRIPTION="Diário às 2:00 AM"
    setup_cron_job "$SCHEDULE" "$DESCRIPTION"
}

# Configurar agendamento a cada 6 horas
setup_6hour_backup() {
    SCHEDULE="0 */6 * * *"
    DESCRIPTION="A cada 6 horas"
    setup_cron_job "$SCHEDULE" "$DESCRIPTION"
}

# Configurar agendamento semanal
setup_weekly_backup() {
    SCHEDULE="0 3 * * 0"
    DESCRIPTION="Semanal (Domingo às 3:00 AM)"
    setup_cron_job "$SCHEDULE" "$DESCRIPTION"
}

# Configurar agendamento personalizado
setup_custom_backup() {
    echo ""
    echo "Configuração personalizada do cron:"
    echo "Formato: minuto hora dia mês dia_da_semana"
    echo ""
    echo "Exemplos:"
    echo "  0 2 * * *     - Diário às 2:00 AM"
    echo "  0 */6 * * *   - A cada 6 horas"
    echo "  0 3 * * 0     - Domingo às 3:00 AM"
    echo "  30 1 1 * *    - Todo dia 1 às 1:30 AM"
    echo ""
    
    read -p "Digite o agendamento cron: " CUSTOM_SCHEDULE
    
    if [ -z "$CUSTOM_SCHEDULE" ]; then
        log_error "Agendamento não pode estar vazio!"
        return 1
    fi
    
    read -p "Digite uma descrição: " DESCRIPTION
    
    setup_cron_job "$CUSTOM_SCHEDULE" "$DESCRIPTION"
}

# Configurar job do cron
setup_cron_job() {
    local schedule="$1"
    local description="$2"
    
    log_header "CONFIGURANDO BACKUP AUTOMATIZADO"
    
    # Solicitar configurações
    echo ""
    read -p "Diretório de backup [$DEFAULT_BACKUP_DIR]: " BACKUP_DIR
    BACKUP_DIR="${BACKUP_DIR:-$DEFAULT_BACKUP_DIR}"
    
    read -p "Retenção em dias [$DEFAULT_RETENTION]: " RETENTION
    RETENTION="${RETENTION:-$DEFAULT_RETENTION}"
    
    # Criar diretório de backup
    mkdir -p "$BACKUP_DIR"
    
    # Caminho absoluto do script
    SCRIPT_PATH="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)/backup-n8n-complete.sh"
    PROJECT_PATH="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
    
    # Criar script wrapper
    WRAPPER_SCRIPT="${PROJECT_PATH}/backup-wrapper.sh"
    
    cat > "$WRAPPER_SCRIPT" << EOF
#!/bin/bash
# Wrapper script para backup automatizado N8N
# Gerado automaticamente em $(date)

# Configurações
export BACKUP_DIR="$BACKUP_DIR"
export RETENTION_DAYS="$RETENTION"

# Mudar para diretório do projeto
cd "$PROJECT_PATH"

# Executar backup
"$SCRIPT_PATH" >> "$BACKUP_DIR/backup.log" 2>&1

# Rotacionar logs (manter últimos 10)
tail -n 1000 "$BACKUP_DIR/backup.log" > "$BACKUP_DIR/backup.log.tmp" 2>/dev/null || true
mv "$BACKUP_DIR/backup.log.tmp" "$BACKUP_DIR/backup.log" 2>/dev/null || true
EOF
    
    chmod +x "$WRAPPER_SCRIPT"
    
    # Remover job existente
    crontab -l 2>/dev/null | grep -v "backup-wrapper.sh" | crontab - 2>/dev/null || true
    
    # Adicionar novo job
    (crontab -l 2>/dev/null; echo "$schedule $WRAPPER_SCRIPT # N8N Backup - $description") | crontab -
    
    log_success "Backup automatizado configurado!"
    echo ""
    echo "Configuração:"
    echo "  📅 Agendamento: $description"
    echo "  🕒 Cron: $schedule"
    echo "  📁 Diretório: $BACKUP_DIR"
    echo "  🗂️  Retenção: $RETENTION dias"
    echo "  📝 Log: $BACKUP_DIR/backup.log"
    echo ""
}

# Desabilitar backup automático
disable_backup() {
    log_header "DESABILITANDO BACKUP AUTOMATIZADO"
    
    # Remover jobs do cron
    crontab -l 2>/dev/null | grep -v "backup-wrapper.sh" | crontab - 2>/dev/null || true
    
    # Remover wrapper script
    PROJECT_PATH="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
    rm -f "${PROJECT_PATH}/backup-wrapper.sh"
    
    log_success "Backup automatizado desabilitado!"
}

# Ver agendamentos atuais
show_current_schedule() {
    log_header "AGENDAMENTOS ATUAIS"
    
    echo ""
    echo "Jobs do cron relacionados ao backup:"
    crontab -l 2>/dev/null | grep -E "(backup|N8N)" || echo "Nenhum agendamento encontrado"
    echo ""
    
    echo "Status do serviço cron:"
    if command -v systemctl &> /dev/null; then
        systemctl is-active cron 2>/dev/null || systemctl is-active crond 2>/dev/null || echo "Status não disponível"
    else
        echo "systemctl não disponível"
    fi
    echo ""
}

# Testar backup
test_backup() {
    log_header "TESTANDO BACKUP"
    
    log_info "Executando backup de teste..."
    
    SCRIPT_PATH="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)/backup-n8n-complete.sh"
    
    if [ -f "$SCRIPT_PATH" ]; then
        "$SCRIPT_PATH"
        log_success "Teste de backup concluído!"
    else
        log_error "Script de backup não encontrado: $SCRIPT_PATH"
    fi
}

# Configurar notificações (futuro)
setup_notifications() {
    log_header "CONFIGURAÇÃO DE NOTIFICAÇÕES"
    
    log_warning "Funcionalidade de notificações será implementada em versão futura."
    echo ""
    echo "Recursos planejados:"
    echo "  📧 Email em caso de falha"
    echo "  📱 Webhook para Slack/Discord"
    echo "  📊 Relatórios de status"
    echo ""
}

# Verificar pré-requisitos
check_prerequisites() {
    log_info "Verificando pré-requisitos..."
    
    # Verificar se cron está disponível
    if ! command -v crontab &> /dev/null; then
        log_error "crontab não está disponível!"
        log_info "Instale o cron: sudo apt-get install cron"
        exit 1
    fi
    
    # Verificar se script de backup existe
    SCRIPT_PATH="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)/backup-n8n-complete.sh"
    if [ ! -f "$SCRIPT_PATH" ]; then
        log_error "Script de backup não encontrado: $SCRIPT_PATH"
        exit 1
    fi
    
    log_success "Pré-requisitos verificados!"
}

# Função principal
main() {
    show_banner
    check_prerequisites
    
    while true; do
        show_menu
        read -p "Digite sua opção (1-7): " choice
        
        case $choice in
            1)
                setup_daily_backup
                ;;
            2)
                setup_6hour_backup
                ;;
            3)
                setup_weekly_backup
                ;;
            4)
                setup_custom_backup
                ;;
            5)
                disable_backup
                ;;
            6)
                show_current_schedule
                ;;
            7)
                log_info "Saindo..."
                exit 0
                ;;
            *)
                log_error "Opção inválida! Digite um número de 1 a 7."
                ;;
        esac
        
        echo ""
        read -p "Pressione Enter para continuar..."
    done
}

# Executar se for script principal
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
