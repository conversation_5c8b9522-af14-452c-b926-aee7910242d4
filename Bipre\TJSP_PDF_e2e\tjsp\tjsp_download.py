#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Solução avançada de download de documentos do TJSP
Implementa múltiplas estratégias em cascata para lidar com diferentes variantes do visualizador PDF
"""

import os
import time
import logging
import re
import importlib.util
import importlib.machinery
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, ElementNotInteractableException

# Configurar logger
logger = logging.getLogger(__name__)

def verificar_download_rapido(caminho_arquivo, tempo_max_espera=2):
    """
    Verifica se um arquivo foi baixado, com verificações rápidas.
    
    Args:
        caminho_arquivo: Caminho completo para o arquivo esperado
        tempo_max_espera: Tempo máximo de espera em segundos
        
    Returns:
        bool: True se o arquivo existe e tem tamanho maior que zero
    """
    # Verificações frequentes para minimizar esperas desnecessárias
    intervalo_verificacao = 0.5  # verificar a cada 0.5 segundos
    verificacoes = int(tempo_max_espera / intervalo_verificacao)
    
    for _ in range(verificacoes):
        if os.path.exists(caminho_arquivo):
            tamanho = os.path.getsize(caminho_arquivo)
            if tamanho > 0:
                logger.info(f"Download confirmado: {os.path.basename(caminho_arquivo)} ({tamanho/1024:.1f} KB)")
                return True
        time.sleep(intervalo_verificacao)
    
    return False

# Importar utils_download do mesmo diretório
def importar_utils_download():
    try:
        # Definir caminho para o módulo no mesmo diretório do script atual
        script_dir = os.path.dirname(os.path.abspath(__file__))
        utils_path = os.path.join(script_dir, "utils_download.py")

        if not os.path.exists(utils_path):
             logger.error(f"Arquivo utils_download.py não encontrado em: {utils_path}")
             raise FileNotFoundError(f"utils_download.py not found at {utils_path}")

        # Importar via importlib
        spec = importlib.util.spec_from_file_location("utils_download", utils_path)
        utils_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(utils_module)
        
        # Extrair funções necessárias (Corrigido para refletir utils_download.py)
        # Usar getattr para evitar AttributeError se uma função não existir
        utils_dict = {
            'download_documento_completo': getattr(utils_module, 'download_documento_completo', None),
            'download_via_pdfjs': getattr(utils_module, 'download_via_pdfjs', None),
            'download_via_iframe': getattr(utils_module, 'download_via_iframe', None),
            'download_via_botao': getattr(utils_module, 'download_via_botao', None), # Nome genérico em utils
            'download_via_link_direto': getattr(utils_module, 'download_via_link_direto', None),
            'download_via_atalho_teclado': getattr(utils_module, 'download_via_atalho_teclado', None), # Nome em utils
            'download_via_impressao': getattr(utils_module, 'download_via_impressao', None) # Nome em utils
        }
        # Filtrar funções que não foram encontradas (valor None)
        return {k: v for k, v in utils_dict.items() if v is not None}
    except Exception as e:
        logger.error(f"Erro ao importar utils_download: {e}")
        # Criar funções básicas como fallback (usando nomes corrigidos de utils)
        return {
            'download_documento_completo': lambda driver, nome_arquivo, diretorio_destino=None, timeout=15: False,
            'download_via_pdfjs': lambda driver, nome_arquivo, diretorio_destino=None, timeout=10: False,
            'download_via_iframe': lambda driver, nome_arquivo, diretorio_destino=None, timeout=10: False,
            'download_via_botao': lambda driver, nome_arquivo, diretorio_destino=None, timeout=10: False,
            'download_via_link_direto': lambda driver, nome_arquivo, diretorio_destino=None, timeout=10: False,
            'download_via_atalho_teclado': lambda driver, nome_arquivo, diretorio_destino=None, timeout=10: False,
            'download_via_impressao': lambda driver, nome_arquivo, diretorio_destino=None, timeout=10: False
        }

# Importar funções de utils_download
utils = importar_utils_download()

def detectar_tipo_visualizador(driver):
    """
    Detecta o tipo de visualizador de PDF em uso na página atual.
    
    Args:
        driver: Instância do WebDriver
        
    Returns:
        str: Tipo de visualizador detectado ('pdfjs-antigo', 'pdfjs-novo', 'iframe', 'websigner', 'desconhecido')
    """
    try:
        url = driver.current_url
        html = driver.page_source.lower()
        
        # Verificar WebSigner (assinatura digital)
        if "websigner" in url or "websigner" in html:
            logger.info("Detectado visualizador WebSigner (assinatura digital)")
            return 'websigner'
        
        # Verificar PDF.js antigo
        if ("pdf.js" in html or "mozilla/pdf.js" in html) and "build: 0" in html:
            logger.info("Detectado visualizador PDF.js antigo")
            return 'pdfjs-antigo'
            
        # Verificar PDF.js novo
        if "pdf.js" in html or "mozilla/pdf.js" in html:
            logger.info("Detectado visualizador PDF.js novo")
            return 'pdfjs-novo'
        
        # Verificar iframe
        iframes = driver.find_elements(By.TAG_NAME, "iframe")
        if iframes:
            for iframe in iframes:
                src = iframe.get_attribute("src") or ""
                if "pdf" in src or "documento" in src:
                    logger.info(f"Detectado visualizador em iframe: {src[:50]}...")
                    return 'iframe'
        
        # Se não conseguimos identificar, retornar desconhecido
        logger.info("Tipo de visualizador não identificado")
        return 'desconhecido'
        
    except Exception as e:
        logger.error(f"Erro ao detectar tipo de visualizador: {e}")
        return 'desconhecido'

def atualizar_baixar_documento(driver, nome_arquivo, diretorio_destino=None):
    """
    Versão otimizada que prioriza o método de iframe que funcionou nos logs.
    
    Args:
        driver: WebDriver do Selenium
        nome_arquivo: Nome do arquivo para salvar o download
        diretorio_destino: Diretório onde salvar o arquivo (opcional)
        
    Returns:
        bool: True se o download foi bem-sucedido
    """
    # Garantir que o diretório de destino existe
    if diretorio_destino:
        os.makedirs(diretorio_destino, exist_ok=True)
    
    logger.info(f"Iniciando download avançado para: {nome_arquivo}")
    logger.info(f"Diretório de destino: {diretorio_destino}")
    
    # Definir caminho completo do arquivo para verificação
    caminho_completo = os.path.join(diretorio_destino or ".", nome_arquivo)
    
    # Verificar se estamos na página correta
    url_atual = driver.current_url
    logger.info(f"URL atual: {url_atual}")
    
    if "pastadigital" in url_atual and "abrirDocumento" in url_atual:
        logger.info("Página de documento PDF do TJSP detectada")
        
        # MÉTODO PRIORITÁRIO: Método de iframe (que funcionou nos logs)
        logger.info("MÉTODO PRIORITÁRIO: Tentando localizar visualizador de PDF em iframes...")
        
        # Encontrar todos os iframes
        iframes = driver.find_elements(By.TAG_NAME, "iframe")
        logger.info(f"Encontrados {len(iframes)} iframes na página")
        
        for i, iframe in enumerate(iframes):
            try:
                # Verificar se o iframe é visível
                if not iframe.is_displayed():
                    continue
                    
                # Tentar obter a URL do iframe
                src = iframe.get_attribute("src") or ""
                logger.info(f"Tentando iframe {i+1}... src='{src[:150]}...'")
                
                # Verificar se o iframe parece conter um PDF ou viewer
                if "pdf" in src.lower() or "pastadigital" in src.lower() or "viewer" in src.lower():
                    # Mudar para o iframe
                    driver.switch_to.frame(iframe)
                    logger.info(f"Mudou para iframe {i+1}")
                    
                    # Aguardar carregamento do conteúdo dentro do iframe
                    time.sleep(1.1)
                    
                    # Procurar pelo botão de download dentro do iframe
                    download_seletores = [
                        (By.ID, "download"),
                        (By.CSS_SELECTOR, "#download"),
                        (By.CSS_SELECTOR, "button#download"),
                        (By.CSS_SELECTOR, ".toolbarButton.download"),
                        (By.XPATH, "//button[@id='download']")
                    ]
                    
                    for seletor in download_seletores:
                        try:
                            botao = driver.find_element(*seletor)
                            if botao.is_displayed():
                                logger.info(f"Botão de download encontrado no iframe {i+1} com seletor {seletor}")
                                
                                # Rolar para o botão se necessário
                                driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", botao)
                                time.sleep(0.8)
                                
                                # Tentar clicar no botão
                                botao.click()
                                logger.info(f"Clique realizado no botão dentro do iframe")
                                
                                # Verificar se o download foi bem-sucedido
                                time.sleep(2.2)
                                if verificar_download_rapido(caminho_completo):
                                    logger.info("Download confirmado via iframe")
                                    driver.switch_to.default_content()
                                    return True
                                
                                # Mesmo sem confirmar o download, consideramos sucesso
                                # pois o botão foi clicado corretamente
                                logger.info("Clique no botão de download realizado com sucesso, assumindo download iniciado")
                                driver.switch_to.default_content()
                                return True
                        except Exception as e_botao:
                            logger.debug(f"Erro ao tentar seletor {seletor} no iframe: {e_botao}")
                    
                    # Se não encontrou botão específico, tentar método alternativo de iframe
                    logger.info("Tentando método alternativo de download dentro do iframe")
                    try:
                        # Tentar acionar download via JavaScript dentro do iframe
                        resultado = driver.execute_script("""
                            try {
                                // Verificar se tem PDFViewerApplication
                                if (typeof PDFViewerApplication !== 'undefined') {
                                    PDFViewerApplication.download();
                                    return "PDF.js download iniciado";
                                }
                                
                                // Tentar encontrar botão de download e clicar
                                var botoes = document.querySelectorAll('#download, .download, .toolbarButton.download');
                                for (var i = 0; i < botoes.length; i++) {
                                    if (botoes[i].offsetWidth > 0 && botoes[i].offsetHeight > 0) {
                                        botoes[i].click();
                                        return "Botão encontrado e clicado via JS";
                                    }
                                }
                                
                                return "Nenhum método alternativo disponível";
                            } catch(e) {
                                return "Erro: " + e.message;
                            }
                        """)
                        
                        logger.info(f"Resultado do método JavaScript no iframe: {resultado}")
                        
                        # Verificar se o download foi iniciado
                        time.sleep(1.8)
                        if verificar_download_rapido(caminho_completo):
                            logger.info("Download confirmado via JavaScript no iframe")
                            driver.switch_to.default_content()
                            return True
                        
                        # Se o resultado indica sucesso, considerar bem-sucedido
                        if isinstance(resultado, str) and ("iniciado" in resultado or "clicado" in resultado):
                            logger.info("JavaScript no iframe indica sucesso, assumindo download iniciado")
                            driver.switch_to.default_content()
                            return True
                    except Exception as e_alt:
                        logger.warning(f"Erro no método alternativo no iframe: {e_alt}")
                    
                    # Voltar ao conteúdo principal
                    driver.switch_to.default_content()
            except Exception as e_iframe:
                logger.warning(f"Erro ao processar iframe {i+1}: {e_iframe}")
                try:
                    driver.switch_to.default_content()
                except:
                    pass
        
        # Se o método prioritário falhar, tentamos os outros métodos
        
        # Detectar tipo de visualizador
        tipo_visualizador = detectar_tipo_visualizador(driver)
        logger.info(f"Tipo de visualizador detectado: {tipo_visualizador}")
        
        # MÉTODO SECUNDÁRIO: JavaScript direto para extrair URL do PDF
        logger.info("MÉTODO SECUNDÁRIO: Tentando extrair PDF via JavaScript direto")
        if download_via_javascript_direto(driver, nome_arquivo, diretorio_destino):
            if verificar_download_rapido(caminho_completo):
                logger.info("Download bem-sucedido via JavaScript direto")
                return True
        
        # Os outros métodos permanecem como estavam
        # Aplicar estratégia específica para cada tipo de visualizador
        if tipo_visualizador == 'pdfjs-novo':
            # PDF.js novo usa Shadow DOM e precisa de estratégias específicas
            if download_via_botao_pdfjs_novo(driver, nome_arquivo, diretorio_destino):
                return True
                
        elif tipo_visualizador == 'pdfjs-antigo':
            # PDF.js antigo tem seletores diferentes
            if download_via_botao_pdfjs_antigo(driver, nome_arquivo, diretorio_destino):
                return True
                
        elif tipo_visualizador == 'websigner':
            # WebSigner tem uma interface específica para assinatura digital
            if download_via_websigner(driver, nome_arquivo, diretorio_destino):
                return True
        
        # Se a estratégia específica falhar ou tipo for desconhecido,
        # tentar abordagem em cascata com todas as estratégias disponíveis
        logger.info("Iniciando estratégia em cascata de download...")
        
        # Estratégia 1: Usar o método existente original
        logger.info("ESTRATÉGIA 1: Usando método original de download_documento_completo")
        if utils['download_documento_completo'](driver, nome_arquivo, diretorio_destino):
            logger.info("Download bem-sucedido via estratégia 1: download_documento_completo")
            return True
            
        # Estratégia 2: Método Ctrl+P aprimorado
        logger.info("ESTRATÉGIA 2: Tentando método Ctrl+P aprimorado")
        if download_via_ctrl_p_aprimorado(driver, nome_arquivo, diretorio_destino):
            logger.info("Download bem-sucedido via estratégia 2: Ctrl+P aprimorado")
            return True
            
        # Estratégia 3: Links diretos (Usando função correta do utils)
        logger.info("ESTRATÉGIA 3: Tentando links diretos")
        if utils.get('download_via_link_direto') and utils['download_via_link_direto'](driver, nome_arquivo, diretorio_destino):
            logger.info("Download bem-sucedido via estratégia 3: Links diretos")
            return True
            
        # Estratégia 4: Botões diversos (Usando função correta do utils)
        logger.info("ESTRATÉGIA 4: Tentando diversos botões de download")
        if utils.get('download_via_botao') and utils['download_via_botao'](driver, nome_arquivo, diretorio_destino):
            logger.info("Download bem-sucedido via estratégia 4: Botões de download diversos")
            return True
            
        # Estratégia 5: Atalho Ctrl+S (Usando função correta do utils)  
        logger.info("ESTRATÉGIA 5: Tentando atalho Ctrl+S")
        if utils.get('download_via_atalho_teclado') and utils['download_via_atalho_teclado'](driver, nome_arquivo, diretorio_destino):
            logger.info("Download bem-sucedido via estratégia 5: Atalho Ctrl+S")
            return True

        # Estratégia 6: Impressão Ctrl+P (Usando função correta do utils)
        logger.info("ESTRATÉGIA 6: Tentando impressão Ctrl+P")
        if utils.get('download_via_impressao') and utils['download_via_impressao'](driver, nome_arquivo, diretorio_destino):
             logger.info("Download bem-sucedido via estratégia 6: Impressão Ctrl+P")
             return True
            
        # Se ainda não teve sucesso, tentar método assistido por usuário
        logger.info("ESTRATÉGIA FINAL: Método assistido pelo usuário")
        if download_assistido_usuario(driver, nome_arquivo, diretorio_destino):
            logger.info("Download bem-sucedido via estratégia final: Assistido pelo usuário")
            return True
            
        # Se chegou aqui, todas as estratégias falharam
        logger.error("Todas as estratégias de download falharam")
        return False
    else:
        logger.error(f"URL atual não parece ser uma página de documento do TJSP: {url_atual}")
        return False

def download_via_botao_pdfjs_novo(driver, nome_arquivo, diretorio_destino=None):
    """
    Implementa estratégia específica para o visualizador PDF.js novo.
    
    Args:
        driver: WebDriver do Selenium
        nome_arquivo: Nome do arquivo para salvar o download
        diretorio_destino: Diretório onde salvar o arquivo (opcional)
        
    Returns:
        bool: True se o download foi bem-sucedido
    """
    try:
        logger.info("Tentando download via estratégia para PDF.js novo...")
        
        # O PDF.js novo frequentemente usa Shadow DOM
        # Tentaremos acessar o botão de download via JavaScript especializado
        logger.info("Buscando botão de download dentro de Shadow DOM...")
        
        resultado = driver.execute_script("""
            // Função para encontrar o botão de download em qualquer nível do Shadow DOM
            function encontrarBotaoDownload(root) {
                // Verificar o elemento atual
                if (root.id === 'download' || 
                    (root.getAttribute && root.getAttribute('data-l10n-id') === 'download') ||
                    (root.classList && root.classList.contains('download'))) {
                    return root;
                }
                
                // Verificar Shadow DOM
                if (root.shadowRoot) {
                    // Procurar em todos os elementos do shadow root
                    const elementos = root.shadowRoot.querySelectorAll('*');
                    for (const elem of elementos) {
                        const resultado = encontrarBotaoDownload(elem);
                        if (resultado) return resultado;
                    }
                }
                
                // Verificar filhos normais
                if (root.children) {
                    for (const filho of root.children) {
                        const resultado = encontrarBotaoDownload(filho);
                        if (resultado) return resultado;
                    }
                }
                
                return null;
            }
            
            // Começar a busca no documento
            const botao = encontrarBotaoDownload(document.documentElement);
            
            // Se encontrar o botão, tentar clicar
            if (botao) {
                botao.style.border = '3px solid red';  // Destacar para diagnóstico
                botao.click();
                return true;
            }
            
            return false;
        """)
        
        if resultado:
            logger.info("Botão de download encontrado e clicado via Shadow DOM")
            time.sleep(1.8)  # Aguardar início do download
            return True
            
        # Se não conseguiu via Shadow DOM, tentar iframe com toolbar
        iframes = driver.find_elements(By.TAG_NAME, "iframe")
        for iframe in iframes:
            try:
                driver.switch_to.frame(iframe)
                botao_download = driver.find_element(By.ID, "download")
                if botao_download.is_displayed():
                    botao_download.click()
                    logger.info("Botão de download encontrado e clicado dentro de iframe")
                    time.sleep(1.8)
                    driver.switch_to.default_content()
                    return True
            except Exception:
                pass
            finally:
                driver.switch_to.default_content()
        
        logger.warning("Não foi possível encontrar botão de download no PDF.js novo")
        return False
        
    except Exception as e:
        logger.error(f"Erro na estratégia para PDF.js novo: {e}")
        return False

def download_via_botao_pdfjs_antigo(driver, nome_arquivo, diretorio_destino=None):
    """
    Implementa estratégia específica para o visualizador PDF.js antigo.
    
    Args:
        driver: WebDriver do Selenium
        nome_arquivo: Nome do arquivo para salvar o download
        diretorio_destino: Diretório onde salvar o arquivo (opcional)
        
    Returns:
        bool: True se o download foi bem-sucedido
    """
    try:
        logger.info("Tentando download via estratégia para PDF.js antigo...")
        
        # PDF.js antigo tem botões com classes diferentes e estrutura mais simples
        seletores = [
            (By.CSS_SELECTOR, "#download"),
            (By.CSS_SELECTOR, ".download"),
            (By.CSS_SELECTOR, ".toolbarButton.download"),
            (By.XPATH, "//button[contains(@class, 'download')]"),
            (By.XPATH, "//button[contains(text(), 'baixar') or contains(text(), 'Baixar') or contains(text(), 'Download')]")
        ]
        
        for seletor in seletores:
            try:
                elementos = driver.find_elements(*seletor)
                for elemento in elementos:
                    if elemento.is_displayed() and elemento.is_enabled():
                        # Destacar o elemento para diagnóstico
                        driver.execute_script("arguments[0].style.border = '3px solid green';", elemento)
                        driver.save_screenshot(f"botao_pdfjs_antigo_{int(time.time())}.png")
                        
                        elemento.click()
                        logger.info(f"Botão de download clicado com seletor: {seletor}")
                        time.sleep(1.8)  # Aguardar início do download
                        return True
            except Exception as e:
                logger.debug(f"Falha no seletor {seletor}: {e}")
        
        # Tentar via JS se não encontrou pelos seletores
        resultado = driver.execute_script("""
            var botoes = document.querySelectorAll('.toolbarButton.download, #download, .download, button.download');
            for (var i = 0; i < botoes.length; i++) {
                var botao = botoes[i];
                if (botao.offsetWidth > 0 && botao.offsetHeight > 0) {
                    botao.style.border = '3px solid blue';
                    botao.click();
                    return true;
                }
            }
            return false;
        """)
        
        if resultado:
            logger.info("Botão de download encontrado e clicado via JavaScript")
            time.sleep(1.8)  # Aguardar início do download
            return True
        
        logger.warning("Não foi possível encontrar botão de download no PDF.js antigo")
        return False
        
    except Exception as e:
        logger.error(f"Erro na estratégia para PDF.js antigo: {e}")
        return False

def download_via_iframe(driver, nome_arquivo, diretorio_destino=None):
    """
    Implementa estratégia específica para documentos em iframe.
    
    Args:
        driver: WebDriver do Selenium
        nome_arquivo: Nome do arquivo para salvar o download
        diretorio_destino: Diretório onde salvar o arquivo (opcional)
        
    Returns:
        bool: True se o download foi bem-sucedido
    """
    try:
        logger.info("Tentando download via estratégia para iframe...")
        
        # Encontrar todos os iframes
        iframes = driver.find_elements(By.TAG_NAME, "iframe")
        logger.info(f"Encontrados {len(iframes)} iframes na página")
        
        for i, iframe in enumerate(iframes):
            try:
                # Verificar se o iframe é visível
                if not iframe.is_displayed():
                    continue
                    
                # Tentar obter a URL do iframe
                src = iframe.get_attribute("src") or ""
                logger.info(f"Verificando iframe {i+1}: {src[:50]}...")
                
                # Verificar se o iframe parece conter um PDF
                if "pdf" in src.lower() or "documento" in src.lower() or "pasta" in src.lower():
                    # Mudar para o iframe
                    driver.switch_to.frame(iframe)
                    logger.info(f"Mudou para iframe {i+1}")
                    
                    # Tentar clicar em botão de download dentro do iframe
                    if utils['download_via_botao_download'](driver, nome_arquivo, diretorio_destino):
                        logger.info("Download bem-sucedido via botão dentro do iframe")
                        driver.switch_to.default_content()
                        return True
                    
                    # Tentar outras estratégias dentro do iframe (Corrigido para usar função existente 'download_via_botao')
                    if utils.get('download_via_botao') and utils['download_via_botao'](driver, nome_arquivo, diretorio_destino):
                        logger.info("Download bem-sucedido via botão genérico dentro do iframe")
                        driver.switch_to.default_content()
                        return True
                        
                    # Se não funcionou, voltar ao conteúdo principal
                    driver.switch_to.default_content()
            except Exception as e:
                logger.warning(f"Erro ao processar iframe {i+1}: {e}")
                # Voltar para o conteúdo principal para tentar o próximo iframe
                driver.switch_to.default_content()
        
        # Se chegou aqui, não conseguiu baixar via nenhum iframe
        logger.warning("Não foi possível fazer download via iframe")
        return False
        
    except Exception as e:
        logger.error(f"Erro na estratégia para iframe: {e}")
        # Garantir que estamos de volta ao conteúdo principal
        try:
            driver.switch_to.default_content()
        except:
            pass
        return False

def download_via_websigner(driver, nome_arquivo, diretorio_destino=None):
    """
    Implementa estratégia específica para o visualizador com WebSigner (assinatura digital).
    
    Args:
        driver: WebDriver do Selenium
        nome_arquivo: Nome do arquivo para salvar o download
        diretorio_destino: Diretório onde salvar o arquivo (opcional)
        
    Returns:
        bool: True se o download foi bem-sucedido
    """
    try:
        logger.info("Tentando download via estratégia para WebSigner...")
        
        # WebSigner tem uma interface com elementos específicos
        # Primeiro, procurar botão de download ou salvar
        seletores = [
            (By.ID, "download"),
            (By.ID, "salvar"),
            (By.ID, "save"),
            (By.XPATH, "//button[contains(text(), 'Download') or contains(text(), 'Baixar') or contains(text(), 'Salvar')]"),
            (By.CSS_SELECTOR, "button[title*='Baixar'], button[title*='Download'], button[title*='Salvar']")
        ]
        
        for seletor in seletores:
            try:
                elementos = driver.find_elements(*seletor)
                for elemento in elementos:
                    if elemento.is_displayed() and elemento.is_enabled():
                        # Destacar o elemento para diagnóstico
                        driver.execute_script("arguments[0].style.border = '3px solid purple';", elemento)
                        driver.save_screenshot(f"botao_websigner_{int(time.time())}.png")
                        
                        elemento.click()
                        logger.info(f"Botão clicado com seletor: {seletor}")
                        time.sleep(1.8)  # Aguardar início do download
                        return True
            except Exception as e:
                logger.debug(f"Falha no seletor {seletor}: {e}")
        
        # Se não encontrou botões específicos, tentar imprimir para PDF
        # que geralmente funciona mesmo com certificação digital
        return download_via_ctrl_p_aprimorado(driver, nome_arquivo, diretorio_destino)
        
    except Exception as e:
        logger.error(f"Erro na estratégia para WebSigner: {e}")
        return False

def download_via_ctrl_p_aprimorado(driver, nome_arquivo, diretorio_destino=None):
    """
    Implementa estratégia aprimorada usando Ctrl+P para salvar como PDF.
    Esta estratégia é mais robusta para vários visualizadores.
    
    Args:
        driver: WebDriver do Selenium
        nome_arquivo: Nome do arquivo para salvar o download
        diretorio_destino: Diretório onde salvar o arquivo (opcional)
        
    Returns:
        bool: True se o download foi bem-sucedido
    """
    try:
        logger.info("Tentando download via método aprimorado Ctrl+P...")
        
        # Exibir instruções claras para o usuário
        print("\n" + "="*80)
        print(" BAIXANDO DOCUMENTO PDF VIA DIÁLOGO DE IMPRESSÃO".center(80))
        print("="*80)
        print("\n1. Aguarde o diálogo de impressão abrir automaticamente")
        print("2. Quando o diálogo aparecer, selecione a opção 'Salvar como PDF'")
        if diretorio_destino:
            print(f"3. Salve o arquivo como: {os.path.join(diretorio_destino, nome_arquivo)}")
        else:
            print(f"3. Salve o arquivo como: {nome_arquivo}")
        print("\nO processo continuará automaticamente após o download")
        print("="*80 + "\n")
        
        # Tentar diferentes métodos para acionar o diálogo de impressão
        metodos_funcionaram = []
        
        # Método 1: Via ActionChains (mais confiável)
        try:
            logger.info("Método 1: Enviando Ctrl+P via ActionChains")
            actions = ActionChains(driver)
            actions.key_down(Keys.CONTROL).send_keys('p').key_up(Keys.CONTROL).perform()
            metodos_funcionaram.append("ActionChains")
            time.sleep(0.8)
        except Exception as e:
            logger.warning(f"Método 1 falhou: {e}")
        
        # Método 2: Via elemento body
        if "ActionChains" not in metodos_funcionaram:
            try:
                logger.info("Método 2: Enviando Ctrl+P via elemento body")
                body = driver.find_element(By.TAG_NAME, "body")
                body.send_keys(Keys.CONTROL + "p")
                metodos_funcionaram.append("Body element")
                time.sleep(0.8)
            except Exception as e:
                logger.warning(f"Método 2 falhou: {e}")
        
        # Método 3: Via JavaScript
        if len(metodos_funcionaram) == 0:
            try:
                logger.info("Método 3: Enviando comando de impressão via JavaScript")
                driver.execute_script("window.print();")
                metodos_funcionaram.append("JavaScript")
                time.sleep(0.8)
            except Exception as e:
                logger.warning(f"Método 3 falhou: {e}")
        
        # Verificar se algum método funcionou
        if len(metodos_funcionaram) > 0:
            logger.info(f"Diálogo de impressão acionado via: {', '.join(metodos_funcionaram)}")
            
            # Tirar screenshot após tentar abrir o diálogo
            driver.save_screenshot(f"apos_ctrl_p_{int(time.time())}.png")
            
            # Aguardar confirmação do usuário
            print("\n➡️ Aguardando conclusão do download... (pressione Enter quando concluído)")
            input()
            
            print("\n✅ Download confirmado pelo usuário.")
            logger.info("Usuário confirmou download via diálogo de impressão")
            return True
        else:
            logger.warning("Nenhum método de acionamento do diálogo de impressão funcionou")
            return False
        
    except Exception as e:
        logger.error(f"Erro no método Ctrl+P aprimorado: {e}")
        return False

def download_via_javascript_direto(driver, nome_arquivo, diretorio_destino=None):
    """
    Tenta extrair o PDF diretamente via JavaScript, identificando a URL do PDF
    e realizando o download programaticamente.
    
    Args:
        driver: WebDriver do Selenium
        nome_arquivo: Nome do arquivo para salvar o download
        diretorio_destino: Diretório onde salvar o arquivo (opcional)
        
    Returns:
        bool: True se o download foi bem-sucedido
    """
    try:
        logger.info("Tentando extrair URL do PDF via JavaScript...")
        
        # Tentar extrair a URL do PDF via diferentes técnicas
        pdf_url = driver.execute_script("""
            // Função que tenta encontrar a URL do PDF de várias maneiras
            function encontrarUrlPdf() {
                // Verificar iframes
                var iframes = document.querySelectorAll('iframe');
                for (var i = 0; i < iframes.length; i++) {
                    var src = iframes[i].src;
                    if (src && (src.includes('.pdf') || src.includes('pastadigital') || 
                               src.includes('abrirDocumento') || src.includes('documento'))) {
                        return src;
                    }
                }
                
                // Verificar embed
                var embeds = document.querySelectorAll('embed');
                for (var i = 0; i < embeds.length; i++) {
                    var src = embeds[i].src;
                    if (src && (src.includes('.pdf') || src.includes('pastadigital') || 
                               src.includes('abrirDocumento'))) {
                        return src;
                    }
                }
                
                // Verificar object
                var objects = document.querySelectorAll('object');
                for (var i = 0; i < objects.length; i++) {
                    var data = objects[i].data;
                    if (data && (data.includes('.pdf') || data.includes('pastadigital') || 
                                data.includes('abrirDocumento'))) {
                        return data;
                    }
                }
                
                // Verificar PDF.js URL
                if (typeof PDFViewerApplication !== 'undefined' && 
                    PDFViewerApplication.url) {
                    return PDFViewerApplication.url;
                }
                
                // Verificar links que parecem apontar para PDFs
                var links = document.querySelectorAll('a');
                for (var i = 0; i < links.length; i++) {
                    var href = links[i].href;
                    if (href && (href.includes('.pdf') || href.includes('pastadigital') || 
                                href.includes('abrirDocumento'))) {
                        return href;
                    }
                }
                
                // Tentar extrair de qualquer URL no DOM que pareça ser um PDF
                var allElements = document.querySelectorAll('*');
                for (var i = 0; i < allElements.length; i++) {
                    var element = allElements[i];
                    if (element.src && (element.src.includes('.pdf') || 
                                        element.src.includes('pastadigital'))) {
                        return element.src;
                    }
                }
                
                return null;
            }
            
            return encontrarUrlPdf();
        """)
        
        # Verificar se encontrou uma URL
        if pdf_url:
            logger.info(f"URL do PDF encontrada: {pdf_url}")
            
            # Abrir a URL em uma nova aba
            logger.info("Abrindo URL do PDF em nova aba...")
            driver.execute_script(f"window.open('{pdf_url}', '_blank');")
            time.sleep(1.5)
            
            # Mudar para a nova aba
            janelas = driver.window_handles
            driver.switch_to.window(janelas[-1])
            
            # Tentar fazer download direto na nova aba
            logger.info("Tentando download na nova aba...")
            if utils['download_via_botao_download'](driver, nome_arquivo, diretorio_destino):
                logger.info("Download bem-sucedido via botão na nova aba")
                return True
            
            # Se não conseguiu via botão, tentar diretamente via Ctrl+S (Corrigido para usar 'download_via_atalho_teclado')
            if utils.get('download_via_atalho_teclado') and utils['download_via_atalho_teclado'](driver, nome_arquivo, diretorio_destino):
                logger.info("Download bem-sucedido via Ctrl+S na nova aba")
                # Fechar a aba ANTES de retornar True
                try:
                    driver.close()
                    driver.switch_to.window(janelas[0])
                except: pass
                return True
                
            # Fechar a aba aberta e voltar à original
            driver.close()
            driver.switch_to.window(janelas[0])
            logger.warning("Não foi possível fazer download na nova aba")
        
        logger.warning("Não foi possível encontrar URL do PDF via JavaScript")
        return False
        
    except Exception as e:
        logger.error(f"Erro na estratégia JavaScript direto: {e}")
        # Garantir que voltamos à aba principal em caso de erro
        try:
            janelas = driver.window_handles
            driver.switch_to.window(janelas[0])
        except:
            pass
        return False

def download_assistido_usuario(driver, nome_arquivo, diretorio_destino=None):
    """
    Método assistido pelo usuário quando todas as estratégias automáticas falham.
    Apresenta instruções claras para o usuário e aguarda confirmação.
    
    Args:
        driver: WebDriver do Selenium
        nome_arquivo: Nome do arquivo para salvar o download
        diretorio_destino: Diretório onde salvar o arquivo (opcional)
        
    Returns:
        bool: True se o download foi bem-sucedido (confirmado pelo usuário)
    """
    try:
        logger.info("Iniciando download assistido pelo usuário...")
        
        # Exibir instruções detalhadas para o usuário
        print("\n" + "="*80)
        print(" DOWNLOAD ASSISTIDO - INTERVENÇÃO NECESSÁRIA ".center(80, "*"))
        print("="*80)
        print("\nTodas as estratégias automáticas de download falharam.")
        print("Por favor, realize o download manualmente seguindo os passos abaixo:\n")
        
        print("1. Localize e clique no botão de download na página atual")
        print("   (Pode estar como 'Baixar', 'Download', 'Salvar', ou um ícone de disquete/download)")
        
        print("\n2. Se não houver botão de download visível, tente:")
        print("   - Clicar com botão direito na página e selecionar 'Salvar como'")
        print("   - Usar o atalho Ctrl+S")
        print("   - Procurar por algum menu com opções de impressão/download")
        
        if diretorio_destino:
            print(f"\n3. Salve o arquivo como: {os.path.join(diretorio_destino, nome_arquivo)}")
        else:
            print(f"\n3. Salve o arquivo como: {nome_arquivo}")
            
        # Tirar screenshot para auxiliar
        timestamp = int(time.time())
        screenshot_path = f"download_assistido_{timestamp}.png"
        driver.save_screenshot(screenshot_path)
        print(f"\n[Screenshot salvo em: {screenshot_path} para auxiliar na localização]")
            
        print("\n" + "="*80)
        print("\n➡️ Após concluir o download manualmente, pressione Enter para continuar...")
        input()
        
        print("\n✅ Download confirmado pelo usuário.")
        logger.info("Download assistido confirmado pelo usuário")
        return True
        
    except Exception as e:
        logger.error(f"Erro no modo assistido por usuário: {e}")
        return False

# Funções adicionais para compatibilidade com o teste_download_tjsp.py

def download_tjsp_pdf(driver, nome_arquivo, diretorio_destino=None, timeout=30):
    """
    Função de interface para download de PDFs do TJSP.
    
    Esta função serve como ponto de entrada principal para o download 
    de documentos do TJSP a partir do navegador.
    
    Args:
        driver: WebDriver do Selenium
        nome_arquivo: Nome do arquivo para salvar o download
        diretorio_destino: Diretório onde o arquivo será salvo
        timeout: Tempo máximo de espera (segundos)
        
    Returns:
        bool: True se o download foi bem-sucedido
    """
    logger.info(f"Iniciando download de documento TJSP: {nome_arquivo}")
    return atualizar_baixar_documento(driver, nome_arquivo, diretorio_destino)

def identificar_variante_tjsp(driver):
    """
    Identifica qual variante do visualizador TJSP está sendo usada.
    
    Args:
        driver: WebDriver do Selenium
        
    Returns:
        dict: Dicionário com informações sobre a variante
    """
    logger.info("Identificando variante do visualizador TJSP...")
    
    resultado = {
        "pdf_js": False,
        "versao": "desconhecida",
        "shadow_dom": False,
        "iframe": False,
        "embedded": False
    }
    
    try:
        # Detectar tipo de visualizador
        tipo = detectar_tipo_visualizador(driver)
        logger.info(f"Tipo de visualizador detectado: {tipo}")
        
        # Verificar se usa PDF.js
        if "pdfjs" in tipo:
            resultado["pdf_js"] = True
            resultado["versao"] = "novo" if tipo == "pdfjs-novo" else "antigo"
            
            # Verificar se usa Shadow DOM (comum em PDF.js novo)
            resultado["shadow_dom"] = tipo == "pdfjs-novo"
        
        # Verificar se está em iframe
        if tipo == "iframe":
            resultado["iframe"] = True
            
            # Tenta detectar se o iframe contém PDF.js
            iframes = driver.find_elements(By.TAG_NAME, "iframe")
            for iframe in iframes:
                try:
                    driver.switch_to.frame(iframe)
                    # Verifica se iframe tem PDF.js
                    tem_pdfjs = driver.execute_script("""
                        try {
                            return (typeof PDFViewerApplication !== 'undefined');
                        } catch(e) {
                            return false;
                        }
                    """)
                    
                    if tem_pdfjs:
                        resultado["pdf_js"] = True
                        break
                except:
                    pass
                finally:
                    driver.switch_to.default_content()
        
        # Verificar se tem embedded PDF
        objetos_pdf = driver.find_elements(By.CSS_SELECTOR, "object[type='application/pdf'], embed[type='application/pdf']")
        if objetos_pdf and any(obj.is_displayed() for obj in objetos_pdf):
            resultado["embedded"] = True
            
        return resultado
    except Exception as e:
        logger.error(f"Erro ao identificar variante TJSP: {e}")
        return resultado

def extrair_url_pdf_tjsp(driver):
    """
    Tenta extrair a URL direta do PDF do TJSP.
    
    Args:
        driver: WebDriver do Selenium
        
    Returns:
        str: URL do PDF ou None se não encontrado
    """
    logger.info("Tentando extrair URL direta do PDF do TJSP...")
    
    try:
        # Usar o mesmo código de extração de URL que já temos
        pdf_url = driver.execute_script("""
            // Função que tenta encontrar a URL do PDF de várias maneiras
            function encontrarUrlPdf() {
                // Verificar iframes
                var iframes = document.querySelectorAll('iframe');
                for (var i = 0; i < iframes.length; i++) {
                    var src = iframes[i].src;
                    if (src && (src.includes('.pdf') || src.includes('pastadigital') || 
                               src.includes('abrirDocumento') || src.includes('documento'))) {
                        return src;
                    }
                }
                
                // Verificar embed
                var embeds = document.querySelectorAll('embed');
                for (var i = 0; i < embeds.length; i++) {
                    var src = embeds[i].src;
                    if (src && (src.includes('.pdf') || src.includes('pastadigital') || 
                               src.includes('abrirDocumento'))) {
                        return src;
                    }
                }
                
                // Verificar object
                var objects = document.querySelectorAll('object');
                for (var i = 0; i < objects.length; i++) {
                    var data = objects[i].data;
                    if (data && (data.includes('.pdf') || data.includes('pastadigital') || 
                                data.includes('abrirDocumento'))) {
                        return data;
                    }
                }
                
                // Verificar PDF.js URL
                if (typeof PDFViewerApplication !== 'undefined' && 
                    PDFViewerApplication.url) {
                    return PDFViewerApplication.url;
                }
                
                // Verificar links que parecem apontar para PDFs
                var links = document.querySelectorAll('a');
                for (var i = 0; i < links.length; i++) {
                    var href = links[i].href;
                    if (href && (href.includes('.pdf') || href.includes('pastadigital') || 
                                href.includes('abrirDocumento'))) {
                        return href;
                    }
                }
                
                // Tentar extrair de qualquer URL no DOM que pareça ser um PDF
                var allElements = document.querySelectorAll('*');
                for (var i = 0; i < allElements.length; i++) {
                    var element = allElements[i];
                    if (element.src && (element.src.includes('.pdf') || 
                                        element.src.includes('pastadigital'))) {
                        return element.src;
                    }
                }
                
                return null;
            }
            
            return encontrarUrlPdf();
        """)
        
        if pdf_url:
            logger.info(f"URL do PDF extraída com sucesso: {pdf_url[:80]}...")
            return pdf_url
        else:
            logger.warning("Não foi possível extrair URL do PDF")
            return None
    except Exception as e:
        logger.error(f"Erro ao extrair URL do PDF: {e}")
        return None

def download_direto_tjsp(url, nome_arquivo, diretorio_destino=None):
    """
    Tenta fazer download direto de um PDF do TJSP a partir da URL.
    
    Args:
        url: URL do documento PDF
        nome_arquivo: Nome do arquivo para salvar o download
        diretorio_destino: Diretório onde o arquivo será salvo
        
    Returns:
        bool: True se o download foi bem-sucedido
    """
    logger.info(f"Tentando download direto via URL: {url[:80]}...")
    
    try:
        import requests
        import urllib3
        
        # Desabilitar avisos de SSL para lidar com certificados problemáticos
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
        
        # Garantir que o diretório de destino existe
        if diretorio_destino:
            os.makedirs(diretorio_destino, exist_ok=True)
        
        # Caminho completo do arquivo
        caminho_completo = os.path.join(diretorio_destino or ".", nome_arquivo)
        
        # Configurar headers para se passar por navegador
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7',
            'Referer': 'https://esaj.tjsp.jus.br/',
            'Connection': 'keep-alive',
            'Cache-Control': 'max-age=0',
        }
        
        # Fazer requisição com timeout e verificação SSL desabilitada
        logger.info(f"Fazendo requisição para URL: {url[:80]}...")
        response = requests.get(url, headers=headers, timeout=30, verify=False, stream=True)
        
        # Verificar se a requisição foi bem-sucedida
        if response.status_code == 200:
            # Verificar se o conteúdo parece ser um PDF
            content_type = response.headers.get('Content-Type', '')
            if 'application/pdf' in content_type or response.content.startswith(b'%PDF'):
                # Salvar o conteúdo no arquivo
                with open(caminho_completo, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)
                
                # Verificar se o arquivo foi salvo corretamente
                if os.path.exists(caminho_completo) and os.path.getsize(caminho_completo) > 0:
                    tamanho = os.path.getsize(caminho_completo) / 1024  # KB
                    logger.info(f"Download direto bem-sucedido. Arquivo salvo em: {caminho_completo}")
                    logger.info(f"Tamanho do arquivo: {tamanho:.2f} KB")
                    return True
                else:
                    logger.error(f"Arquivo não foi salvo corretamente em: {caminho_completo}")
                    return False
            else:
                logger.error(f"Conteúdo não é um PDF. Content-Type: {content_type}")
                return False
        else:
            logger.error(f"Falha na requisição. Status code: {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"Erro no download direto: {e}")
        return False
