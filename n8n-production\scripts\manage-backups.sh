#!/bin/bash

# ===================================================================
# GERENCIAMENTO DE BACKUPS N8N
# ===================================================================

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configurações
BACKUP_DIR="${BACKUP_DIR:-./backups}"

# Funções de log
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}[MANAGE]${NC} $1"
}

# Banner
show_banner() {
    echo -e "${CYAN}"
    echo "====================================================================="
    echo "            📦 GERENCIAMENTO DE BACKUPS N8N"
    echo "====================================================================="
    echo -e "${NC}"
}

# Menu principal
show_menu() {
    echo ""
    echo "Escolha uma opção:"
    echo ""
    echo "1) 📋 Listar backups"
    echo "2) ℹ️  Informações detalhadas de um backup"
    echo "3) ✅ Verificar integridade de um backup"
    echo "4) 🗑️  Remover backup específico"
    echo "5) 🧹 Limpeza automática (remover antigos)"
    echo "6) 📊 Estatísticas de backups"
    echo "7) 🔄 Criar backup agora"
    echo "8) 📁 Alterar diretório de backups"
    echo "9) 🚪 Sair"
    echo ""
}

# Listar backups
list_backups() {
    log_header "LISTANDO BACKUPS"
    
    if [ ! -d "$BACKUP_DIR" ]; then
        log_warning "Diretório de backup não existe: $BACKUP_DIR"
        return 1
    fi
    
    echo ""
    echo "Diretório: $BACKUP_DIR"
    echo ""
    
    # Verificar se há backups
    if ! ls "$BACKUP_DIR"/n8n_backup_*.tar.gz 1> /dev/null 2>&1; then
        log_warning "Nenhum backup encontrado"
        return 0
    fi
    
    echo "Backups disponíveis:"
    echo ""
    printf "%-25s %-15s %-20s %s\n" "NOME" "TAMANHO" "DATA" "IDADE"
    echo "--------------------------------------------------------------------------------"
    
    for backup in "$BACKUP_DIR"/n8n_backup_*.tar.gz; do
        if [ -f "$backup" ]; then
            filename=$(basename "$backup")
            size=$(du -h "$backup" | cut -f1)
            date_created=$(stat -c %y "$backup" 2>/dev/null | cut -d' ' -f1,2 | cut -d'.' -f1 || echo "N/A")
            
            # Calcular idade
            if command -v stat &> /dev/null; then
                file_time=$(stat -c %Y "$backup" 2>/dev/null || echo "0")
                current_time=$(date +%s)
                age_seconds=$((current_time - file_time))
                age_days=$((age_seconds / 86400))
                
                if [ $age_days -eq 0 ]; then
                    age="Hoje"
                elif [ $age_days -eq 1 ]; then
                    age="1 dia"
                else
                    age="$age_days dias"
                fi
            else
                age="N/A"
            fi
            
            printf "%-25s %-15s %-20s %s\n" "$filename" "$size" "$date_created" "$age"
        fi
    done
    echo ""
}

# Informações detalhadas de um backup
show_backup_info() {
    log_header "INFORMAÇÕES DETALHADAS DO BACKUP"
    
    echo ""
    read -p "Digite o nome do backup (sem .tar.gz): " backup_name
    
    if [ -z "$backup_name" ]; then
        log_error "Nome do backup não pode estar vazio!"
        return 1
    fi
    
    backup_file="${BACKUP_DIR}/${backup_name}.tar.gz"
    
    if [ ! -f "$backup_file" ]; then
        log_error "Backup não encontrado: $backup_file"
        return 1
    fi
    
    echo ""
    echo "Informações do backup: $backup_name"
    echo "=================================================================================="
    
    # Informações básicas do arquivo
    echo "📁 Arquivo: $backup_file"
    echo "📊 Tamanho: $(du -h "$backup_file" | cut -f1)"
    echo "📅 Criado: $(stat -c %y "$backup_file" 2>/dev/null | cut -d'.' -f1 || echo "N/A")"
    echo "🔒 Permissões: $(stat -c %A "$backup_file" 2>/dev/null || echo "N/A")"
    echo ""
    
    # Extrair e mostrar manifesto
    temp_dir=$(mktemp -d)
    cd "$temp_dir"
    
    if tar -xzf "$backup_file" "${backup_name}/BACKUP_MANIFEST.md" 2>/dev/null; then
        echo "📋 Manifesto do Backup:"
        echo "=================================================================================="
        cat "${backup_name}/BACKUP_MANIFEST.md"
    else
        log_warning "Manifesto não encontrado no backup"
    fi
    
    # Listar conteúdo do backup
    echo ""
    echo "📦 Conteúdo do Backup:"
    echo "=================================================================================="
    tar -tzf "$backup_file" | head -20
    
    total_files=$(tar -tzf "$backup_file" | wc -l)
    if [ $total_files -gt 20 ]; then
        echo "... e mais $((total_files - 20)) arquivos"
    fi
    
    # Limpeza
    cd - > /dev/null
    rm -rf "$temp_dir"
    
    echo ""
}

# Verificar integridade de um backup
verify_backup() {
    log_header "VERIFICANDO INTEGRIDADE DO BACKUP"
    
    echo ""
    read -p "Digite o nome do backup (sem .tar.gz): " backup_name
    
    if [ -z "$backup_name" ]; then
        log_error "Nome do backup não pode estar vazio!"
        return 1
    fi
    
    backup_file="${BACKUP_DIR}/${backup_name}.tar.gz"
    
    if [ ! -f "$backup_file" ]; then
        log_error "Backup não encontrado: $backup_file"
        return 1
    fi
    
    echo ""
    log_info "Verificando integridade de: $backup_name"
    
    # Verificar se o arquivo tar está íntegro
    if tar -tzf "$backup_file" > /dev/null 2>&1; then
        log_success "Arquivo tar está íntegro"
    else
        log_error "Arquivo tar está corrompido!"
        return 1
    fi
    
    # Extrair temporariamente para verificar conteúdo
    temp_dir=$(mktemp -d)
    cd "$temp_dir"
    
    log_info "Extraindo backup para verificação..."
    if tar -xzf "$backup_file" 2>/dev/null; then
        log_success "Backup extraído com sucesso"
        
        # Verificar arquivos essenciais
        echo ""
        echo "Verificando componentes essenciais:"
        
        # PostgreSQL
        if [ -f "${backup_name}/postgresql_dump.sql.gz" ]; then
            if gunzip -t "${backup_name}/postgresql_dump.sql.gz" 2>/dev/null; then
                log_success "✅ PostgreSQL dump está íntegro"
            else
                log_error "❌ PostgreSQL dump está corrompido"
            fi
        else
            log_warning "⚠️  PostgreSQL dump não encontrado"
        fi
        
        # Redis
        if [ -f "${backup_name}/redis_dump.rdb" ]; then
            log_success "✅ Redis dump encontrado"
        else
            log_warning "⚠️  Redis dump não encontrado"
        fi
        
        # Volumes
        if [ -d "${backup_name}/volumes" ]; then
            volume_count=$(ls "${backup_name}/volumes"/*.tar.gz 2>/dev/null | wc -l)
            if [ $volume_count -gt 0 ]; then
                log_success "✅ $volume_count volumes encontrados"
            else
                log_warning "⚠️  Nenhum volume encontrado"
            fi
        else
            log_warning "⚠️  Diretório de volumes não encontrado"
        fi
        
        # Configurações
        if [ -d "${backup_name}/configs" ]; then
            log_success "✅ Configurações encontradas"
        else
            log_warning "⚠️  Configurações não encontradas"
        fi
        
        # Manifesto
        if [ -f "${backup_name}/BACKUP_MANIFEST.md" ]; then
            log_success "✅ Manifesto encontrado"
        else
            log_warning "⚠️  Manifesto não encontrado"
        fi
        
    else
        log_error "Falha na extração do backup"
    fi
    
    # Limpeza
    cd - > /dev/null
    rm -rf "$temp_dir"
    
    echo ""
    log_success "Verificação de integridade concluída"
}

# Remover backup específico
remove_backup() {
    log_header "REMOVENDO BACKUP ESPECÍFICO"
    
    echo ""
    read -p "Digite o nome do backup (sem .tar.gz): " backup_name
    
    if [ -z "$backup_name" ]; then
        log_error "Nome do backup não pode estar vazio!"
        return 1
    fi
    
    backup_file="${BACKUP_DIR}/${backup_name}.tar.gz"
    
    if [ ! -f "$backup_file" ]; then
        log_error "Backup não encontrado: $backup_file"
        return 1
    fi
    
    # Mostrar informações do backup
    echo ""
    echo "Backup a ser removido:"
    echo "  📁 Arquivo: $backup_file"
    echo "  📊 Tamanho: $(du -h "$backup_file" | cut -f1)"
    echo "  📅 Criado: $(stat -c %y "$backup_file" 2>/dev/null | cut -d'.' -f1 || echo "N/A")"
    echo ""
    
    log_warning "⚠️  ATENÇÃO: Esta ação não pode ser desfeita!"
    read -p "Tem certeza que deseja remover este backup? (digite 'CONFIRMO'): " confirmation
    
    if [ "$confirmation" = "CONFIRMO" ]; then
        rm -f "$backup_file"
        log_success "Backup removido com sucesso!"
    else
        log_info "Remoção cancelada"
    fi
}

# Limpeza automática
cleanup_old_backups() {
    log_header "LIMPEZA AUTOMÁTICA DE BACKUPS"
    
    echo ""
    read -p "Remover backups com mais de quantos dias? [30]: " days
    days="${days:-30}"
    
    if ! [[ "$days" =~ ^[0-9]+$ ]]; then
        log_error "Número de dias deve ser um número inteiro!"
        return 1
    fi
    
    echo ""
    log_info "Procurando backups com mais de $days dias..."
    
    old_backups=$(find "$BACKUP_DIR" -name "n8n_backup_*.tar.gz" -type f -mtime +$days 2>/dev/null || true)
    
    if [ -z "$old_backups" ]; then
        log_info "Nenhum backup antigo encontrado"
        return 0
    fi
    
    echo ""
    echo "Backups que serão removidos:"
    echo "$old_backups" | while read -r backup; do
        if [ -n "$backup" ]; then
            size=$(du -h "$backup" | cut -f1)
            date_created=$(stat -c %y "$backup" 2>/dev/null | cut -d' ' -f1 || echo "N/A")
            echo "  📁 $(basename "$backup") - $size - $date_created"
        fi
    done
    
    backup_count=$(echo "$old_backups" | grep -c . || echo "0")
    total_size=$(echo "$old_backups" | xargs du -ch 2>/dev/null | tail -1 | cut -f1 || echo "N/A")
    
    echo ""
    echo "Total: $backup_count backups ($total_size)"
    echo ""
    
    log_warning "⚠️  ATENÇÃO: Esta ação não pode ser desfeita!"
    read -p "Confirma a remoção? (digite 'CONFIRMO'): " confirmation
    
    if [ "$confirmation" = "CONFIRMO" ]; then
        echo "$old_backups" | while read -r backup; do
            if [ -n "$backup" ] && [ -f "$backup" ]; then
                rm -f "$backup"
                log_info "Removido: $(basename "$backup")"
            fi
        done
        log_success "Limpeza concluída!"
    else
        log_info "Limpeza cancelada"
    fi
}

# Estatísticas de backups
show_statistics() {
    log_header "ESTATÍSTICAS DE BACKUPS"
    
    if [ ! -d "$BACKUP_DIR" ]; then
        log_warning "Diretório de backup não existe: $BACKUP_DIR"
        return 1
    fi
    
    echo ""
    echo "Diretório: $BACKUP_DIR"
    echo ""
    
    # Contar backups
    backup_count=$(ls "$BACKUP_DIR"/n8n_backup_*.tar.gz 2>/dev/null | wc -l || echo "0")
    
    if [ $backup_count -eq 0 ]; then
        log_warning "Nenhum backup encontrado"
        return 0
    fi
    
    # Calcular estatísticas
    total_size=$(du -ch "$BACKUP_DIR"/n8n_backup_*.tar.gz 2>/dev/null | tail -1 | cut -f1 || echo "N/A")
    
    # Backup mais antigo e mais recente
    oldest=$(ls -t "$BACKUP_DIR"/n8n_backup_*.tar.gz 2>/dev/null | tail -1 || echo "")
    newest=$(ls -t "$BACKUP_DIR"/n8n_backup_*.tar.gz 2>/dev/null | head -1 || echo "")
    
    echo "📊 Estatísticas Gerais:"
    echo "  📦 Total de backups: $backup_count"
    echo "  💾 Espaço total usado: $total_size"
    echo ""
    
    if [ -n "$oldest" ]; then
        echo "📅 Backup mais antigo:"
        echo "  📁 $(basename "$oldest")"
        echo "  📊 $(du -h "$oldest" | cut -f1)"
        echo "  📅 $(stat -c %y "$oldest" 2>/dev/null | cut -d'.' -f1 || echo "N/A")"
        echo ""
    fi
    
    if [ -n "$newest" ]; then
        echo "🆕 Backup mais recente:"
        echo "  📁 $(basename "$newest")"
        echo "  📊 $(du -h "$newest" | cut -f1)"
        echo "  📅 $(stat -c %y "$newest" 2>/dev/null | cut -d'.' -f1 || echo "N/A")"
        echo ""
    fi
    
    # Distribuição por idade
    echo "📈 Distribuição por idade:"
    today=$(find "$BACKUP_DIR" -name "n8n_backup_*.tar.gz" -mtime 0 2>/dev/null | wc -l)
    week=$(find "$BACKUP_DIR" -name "n8n_backup_*.tar.gz" -mtime -7 2>/dev/null | wc -l)
    month=$(find "$BACKUP_DIR" -name "n8n_backup_*.tar.gz" -mtime -30 2>/dev/null | wc -l)
    
    echo "  📅 Hoje: $today"
    echo "  📅 Última semana: $week"
    echo "  📅 Último mês: $month"
    echo ""
}

# Criar backup agora
create_backup_now() {
    log_header "CRIANDO BACKUP AGORA"
    
    SCRIPT_PATH="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)/backup-n8n-complete.sh"
    
    if [ ! -f "$SCRIPT_PATH" ]; then
        log_error "Script de backup não encontrado: $SCRIPT_PATH"
        return 1
    fi
    
    echo ""
    log_info "Executando backup..."
    "$SCRIPT_PATH"
}

# Alterar diretório de backups
change_backup_dir() {
    log_header "ALTERANDO DIRETÓRIO DE BACKUPS"
    
    echo ""
    echo "Diretório atual: $BACKUP_DIR"
    echo ""
    read -p "Digite o novo diretório: " new_dir
    
    if [ -z "$new_dir" ]; then
        log_error "Diretório não pode estar vazio!"
        return 1
    fi
    
    # Criar diretório se não existir
    mkdir -p "$new_dir"
    
    if [ -d "$new_dir" ]; then
        BACKUP_DIR="$new_dir"
        export BACKUP_DIR
        log_success "Diretório alterado para: $BACKUP_DIR"
    else
        log_error "Não foi possível criar/acessar o diretório: $new_dir"
    fi
}

# Função principal
main() {
    show_banner
    
    while true; do
        show_menu
        read -p "Digite sua opção (1-9): " choice
        
        case $choice in
            1)
                list_backups
                ;;
            2)
                show_backup_info
                ;;
            3)
                verify_backup
                ;;
            4)
                remove_backup
                ;;
            5)
                cleanup_old_backups
                ;;
            6)
                show_statistics
                ;;
            7)
                create_backup_now
                ;;
            8)
                change_backup_dir
                ;;
            9)
                log_info "Saindo..."
                exit 0
                ;;
            *)
                log_error "Opção inválida! Digite um número de 1 a 9."
                ;;
        esac
        
        echo ""
        read -p "Pressione Enter para continuar..."
    done
}

# Executar se for script principal
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
