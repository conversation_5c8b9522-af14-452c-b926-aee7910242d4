{"// CONFIGURAÇÃO CLIENTES MCP CENTRALIZADO": "Todos os clientes conectam ao servidor central", "servidor_central": {"url": "http://localhost:3232", "endpoints": {"mcp": "http://localhost:3232/mcp", "sse": "http://localhost:3232/sse", "status": "http://localhost:3232/status", "health": "http://localhost:3232/health"}}, "// CONFIGURAÇÃO AUGMENT CODE (VSCode)": "Remove todos os MCPs locais, conecta ao servidor central", "vscode_settings_otimizada": {"mcpServers": {"central-mcp-server": {"command": "node", "args": ["servidor_mcp_centralizado.js"], "env": {"NODE_OPTIONS": "--max-old-space-size=512"}}}}, "// CONFIGURAÇÃO CLAUDE DESKTOP": "Conecta via URL remota ao servidor central", "claude_desktop_config_otimizada": {"mcpServers": {"central-mcp": {"url": "http://localhost:3232/mcp", "name": "Servidor MCP Centralizado", "description": "Acesso a todos os 12 servidores MCP via servidor central"}}}, "// CONFIGURAÇÃO GEMINI CLI": "Conecta ao servidor central via MCP client", "gemini_cli_config": {"mcp_server_url": "http://localhost:3232/mcp", "connection_type": "remote", "authentication": "none"}, "// CONFIGURAÇÃO CLAUDE CODE": "Conecta ao servidor central via MCP client", "claude_code_config": {"mcp_server_url": "http://localhost:3232/mcp", "connection_type": "remote", "authentication": "none"}, "// ARQUITETURA FINAL": {"servidor_central": {"processo_unico": "node servidor_mcp_centralizado.js", "porta": 3232, "memoria_estimada": "512MB", "servidores_internos": ["memory", "everything", "github", "sequential-thinking", "context7", "21st-dev-magic", "supabase", "playwright", "google-maps", "netlify", "blowback", "windows-mcp"]}, "clientes": {"augment_code": "Inicia o servidor central automaticamente", "claude_desktop": "Conecta via URL http://localhost:3232/mcp", "gemini_cli": "Conecta via URL http://localhost:3232/mcp", "claude_code": "Conecta via URL http://localhost:3232/mcp", "outros": "Qualquer cliente MCP pode conectar via URL"}, "beneficios": ["1 processo único para todos os MCPs", "Redução de 95% no uso de RAM (4GB → 512MB)", "Redução de 98% no número de processos (92 → 1)", "Centralização de logs e monitoramento", "Facilita debug e manutenção", "Escalabilidade horizontal (Redis opcional)", "Compatibilidade total com todos os clientes"]}, "// IMPLEMENTAÇÃO PASSO A PASSO": {"passo_1": {"acao": "Compilar servidor centralizado", "comando": "tsc servidor_mcp_centralizado.ts"}, "passo_2": {"acao": "Finalizar todos os processos MCP atuais", "comando": "Get-Process node,python,claude | Where-Object {$_.ProcessName -match 'mcp|claude'} | Stop-Process -Force"}, "passo_3": {"acao": "Atualizar configuração VSCode", "arquivo": "$env:APPDATA\\Code\\User\\settings.json", "conteudo": "vscode_settings_otimizada"}, "passo_4": {"acao": "Atualizar configuração Claude <PERSON>op", "arquivo": "$env:APPDATA\\Claude\\claude_desktop_config.json", "conteudo": "claude_desktop_config_otimi<PERSON>a"}, "passo_5": {"acao": "Reiniciar VSCode", "resultado": "Servidor central inicia automaticamente"}, "passo_6": {"acao": "<PERSON><PERSON><PERSON>", "resultado": "Conecta automaticamente ao servidor central"}, "passo_7": {"acao": "Verificar funcionamento", "comandos": ["curl http://localhost:3232/health", "curl http://localhost:3232/status", "Get-NetTCPConnection -LocalPort 3232"]}}, "// MONITORAMENTO E DEBUG": {"endpoints_uteis": {"health_check": "GET http://localhost:3232/health", "status_detalhado": "GET http://localhost:3232/status", "capacidades": "GET http://localhost:3232/capabilities", "tools_disponiveis": "POST http://localhost:3232/mcp {\"method\":\"tools/list\"}", "resources_disponiveis": "POST http://localhost:3232/mcp {\"method\":\"resources/list\"}"}, "logs": {"servidor_central": "Console do processo Node.js", "vscode": "Output > MCP Servers", "claude_desktop": "Settings > Connectors > Status"}, "troubleshooting": {"servidor_nao_inicia": "Verificar porta 3232 livre: Get-NetTCPConnection -LocalPort 3232", "cliente_nao_conecta": "Verificar URL http://localhost:3232/health no browser", "tools_nao_funcionam": "Verificar logs do servidor central", "performance_lenta": "Aumentar NODE_OPTIONS --max-old-space-size"}}, "// EXTENSIBILIDADE": {"adicionar_novo_servidor": {"1": "Implementar factory method no servidor central", "2": "Adicionar configuração em serverConfigs", "3": "Reiniciar servidor central", "4": "Verificar em /status"}, "adicionar_novo_cliente": {"1": "Configurar cliente para conectar em http://localhost:3232/mcp", "2": "Implementar protocolo MCP padrão", "3": "Testar com /health endpoint"}, "escalabilidade": {"redis": "Adicionar Redis para múltiplas instâncias", "load_balancer": "Nginx para distribuir carga", "docker": "<PERSON><PERSON><PERSON><PERSON> servidor central", "kubernetes": "Deploy em cluster K8s"}}, "// SEGURANÇA": {"local_only": "Servidor aceita apenas cone<PERSON>ões localhost", "no_auth": "Sem autenticação (ambiente local)", "cors_permissive": "CORS aberto para desenvolvimento", "production_considerations": ["Adicionar autenticação OAuth 2.0", "Restringir CORS origins", "Implementar rate limiting", "Adicionar HTTPS/TLS", "Logs estruturados", "Monitoramento de saúde"]}}