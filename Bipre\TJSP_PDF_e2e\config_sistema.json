{"sistema": {"nome": "TJSP Orquestrador End-to-End", "versao": "1.0", "data_criacao": "2025-01-28", "autor": "Sistema Augment"}, "diretorios": {"tjsp": "tjsp", "extracao": "extracao", "downloads": "tjsp/downloads_completos", "input_extracao": "extracao/data/input", "output_extracao": "extracao/data/output", "logs_orquestrador": "logs_orquestrador", "backup_sincronizacao": "backup_sincronizacao"}, "arquivos_principais": {"orquestrador": "orquestrador_tjsp_e2e.py", "sincronizador": "sincronizador_arquivos.py", "processador_tjsp": "tjsp/ProcessadorTJSPUnificado_final.py", "extrator": "extracao/src/sistema_principal.py", "teste": "teste_sistema_completo.py"}, "configuracoes": {"sincronizacao": {"usar_sincronizador_avancado": true, "criar_backup": true, "verificar_integridade": true, "dias_manter_backup": 30}, "logging": {"nivel": "INFO", "formato": "%(asctime)s - %(levelname)s - %(message)s", "arquivo_rotativo": true, "max_tamanho_mb": 10}, "checkpoint": {"salvar_automatico": true, "formato_json": true, "formato_excel": true, "backup_checkpoint": true}}, "dependencias": {"python_minimo": "3.8", "bibliotecas_basicas": ["pandas", "openpyxl", "tqdm", "pathlib"], "bibliotecas_tjsp": ["selenium", "webdriver-manager", "beautifulsoup4", "requests"], "bibliotecas_extracao": ["PyPDF2", "pdfplumber", "tabula-py", "camelot-py"]}, "opcoes_menu": {"1": {"nome": "Executar apenas Download (TJSP)", "funcao": "executar_download_tjsp", "descricao": "Executa apenas o processo de download do TJSP"}, "2": {"nome": "Executar apenas Extração", "funcao": "executar_extracao", "descricao": "Executa apenas o processo de extração de PDFs"}, "3": {"nome": "Executar processo completo", "funcao": "executar_processo_completo", "descricao": "Executa download + sincronização + extração automaticamente"}, "4": {"nome": "Sin<PERSON><PERSON><PERSON><PERSON> (básico)", "funcao": "sincronizar_arquivos", "parametros": {"usar_sincronizador_avancado": false}, "descricao": "Copia PDFs de downloads para extração (modo básico)"}, "5": {"nome": "Sincronização avançada", "funcao": "sincronizar_arquivos", "parametros": {"usar_sincronizador_avancado": true}, "descricao": "Sincronização com backup, verificação de integridade e relatórios", "requer": "sincronizador_avancado"}, "6": {"nome": "Gerar relat<PERSON><PERSON> consolidado", "funcao": "gerar_relatorio_consolidado", "descricao": "Gera relatório final com estatísticas completas"}, "7": {"nome": "Ver status atual", "funcao": "mostrar_status", "descricao": "Mostra status atual do sistema e arquivos"}, "8": {"nome": "Limpeza de arquivos antigos", "funcao": "_executar_limpeza", "descricao": "Remove backups antigos para liberar espaço", "requer": "sincronizador_avancado"}, "9": {"nome": "<PERSON><PERSON>", "funcao": "sair", "descricao": "Encerra o orquestrador"}}, "mensagens": {"inicio": "🚀 TJSP ORQUESTRADOR END-TO-END v1.0", "sucesso_download": "✅ Download TJSP concluído com sucesso", "sucesso_extracao": "✅ Extração concluída com sucesso", "sucesso_sincronizacao": "✅ Sincronização concluída", "erro_sistema_nao_encontrado": "❌ Sistema não encontrado", "aviso_sincronizador_indisponivel": "⚠️ Sincronizador avançado não disponível (modo básico)"}, "validacao": {"arquivos_obrigatorios": ["tjsp/ProcessadorTJSPUnificado_final.py", "extracao/src/sistema_principal.py"], "diretorios_obrigatorios": ["tjsp", "extracao", "extracao/src"], "extensoes_suportadas": [".pdf"], "tamanho_maximo_arquivo_mb": 100}, "manutencao": {"backup_automatico": true, "limpeza_automatica": true, "monitoramento_espaco": true, "alertas_erro": true, "relatorios_periodicos": true}}