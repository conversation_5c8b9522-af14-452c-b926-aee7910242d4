﻿Se você já teve vontade de criar um
disparador de mensagens em massa no
"WhatsApp, mas achava que precisava saber"
"programar ou pagar caro por isso, esse"
vídeo é para você. Hoje eu vou te
"mostrar como qualquer pessoa, mesmo sem"
"conhecimento técnico, pode montar o seu"
próprio disparador usando ferramentas
gratuitas e de um jeito muito simples.
Você vai criar o seu próprio sistema de
disparo de mensagens em massa pelo
WhatsApp do zero e sem gastar nada. E
"para facilitar ainda mais, todo o"
material que eu usei aqui vai estar
disponível para download gratuito na
página de apoio. Então se prepara porque
no final desse vídeo você vai sair com
seu disparador rodando. Bora começar. Eu
sou o Pedrinho da NASA e você está no
canal da comunidade
[Música]
"ZDG. Ora pra ação, galera. A proposta"
"desse vídeo é bem incrível. Eh, nesse"
tutorial a gente vai criar um disparador
de mensagens de WhatsApp e em massa aí
"de graça para você utilizar, mesmo que"
"você não saiba nada de programação, tá?"
Nós vamos explorar algumas ferramentas
"bem interessantes que estão disponíveis,"
"né, utilizando inteligência artificial,"
"né, e alguns editores de código aqui que"
vão fazer todo o o trabalho o trabalho
"braçal para nós, tá? Lembrando que todo"
o material que a gente vai usar nessa
"aula, ele tá disponível numa página de"
"apoio. Então é, é só entrar aí no link"
que tá na descrição desse vídeo que você
consegue baixar tudo e executar no seu
"PC. E antes de continuar, deixa um like"
nesse vídeo e manda um comentário para
"mim dizendo se a partir de agora, com"
todas essas tecnologias você vai começar
a programar aí sistemas para WhatsApp
"também, tá? Então, basicamente nós"
estamos aqui com a Evolution instalada
"no computador, tá? E nós vamos criar"
"usando cursor, que é um um novo editor"
"de códigos, tá? Que cria pra gente toda"
"a solução, né? Eh, para criar esse"
painelzinho de disparo de mensagens.
"Então, basicamente a gente passa aqui é"
"um número,"
"né, e uma mensagem, por exemplo, teste,"
"né, e ele envia lá, ó,"
aula
"teste. Será que o número tá aqui? Tá, ó"
"lá. Tá vendo, ó? Ele tá enviando, ó."
"E como que nós vamos fazer isso, tá? Nós"
vamos fazer isso utilizando o cursor.
Nós vamos passar todas as instruções
para ele e ele vai construir o código
"pra gente. Então, a gente vai começar a"
construir o código a partir de um prompt
e nós vamos corrigir esse código ali à
"medida que ele for construindo, tá? Com"
as dicas da inteligência artificial.
"Então, a primeira coisa que a gente vai"
"precisar para rodar a nossa solução,"
"vamos começar aqui, ó."
"Eh, vamos lá. Vamos por partes. Primeira"
coisa que nós vamos precisar é fazer o
"download do Node, tá? Download do"
"cursor, que é o editor de texto que nós"
vamos utilizar. E também é o Docker
"Desktop, que o Docker Desktop é o que a"
gente vai utilizar para rodar a nossa
"Evolution, tá bem? Então, inclusive, eu"
"vou parar essa Evolution, vou deletar"
"ela daqui, ó, pra gente criar ela do"
zero. Deixa eu parar essa aqui também
"pra gente poder é ir por partes, tá"
"legal? Então, baixou o Node, baixou o"
"cursor, baixou o Docker Desktop. Todos"
"os links eles estão na página de apoio,"
"tá, desses programas. Todos eles são"
"gratuitos, né? Eh, o cursor ele é"
"gratuito para testar, então você pode"
"instalar, criar sua conta e conectar o"
"cursor, né, que você vai conseguir"
utilizar ele durante algum tempo de
"graça, tá? Para testes, para você"
entender como esse construtor de códigos
"ele é tão poderoso, tá? Então, baixou o"
"Node, baixou o cursor, baixou o desktop,"
você vai instalar Evolution. Como é que
você vai instalar Evolution? Dentro da
"página de apoio, você vai ter esse"
"arquivo Evoaula, tá? Para poder baixar."
"Dentro do Evoaula, você vai encontrar"
este arquivo que é o o arquivo da imagem
que o arquivo para construir os nossos
"contêiners Docker, tá? Então ele tem"
"várias instruções aqui, é só você baixar"
eh o Docker Compose e o ENV. Então é
"importante que você baixe essa pasta,"
descompacte aí no seu na sua área de
"trabalho, tá bem? E abra ela. Assim que"
"você abre ela, né, com o Docker Desktop"
"sendo executado, né, você vai clicar com"
botão direito e vai abrir o terminal.
Aqui dentro desse terminal que está
"apontando agora para a pasta EVOula,"
"você vai escrever o comando, ó, Docker"
"Compose up, né? Que que ele vai fazer?"
"Ele vai baixar, né, as imagens e da"
"Evolution, tá? E vai construir pra gente"
o contêiner e permitindo com que a gente
"utilize a solução. Então, aqui já está,"
"ó, a Evo, né? Como eu já tinha"
construído e eu já tinha uma conexão eh
"conectada, então se eu entrar aqui, ó,"
"ele vai dar paraa gente aqui, ó. É só um"
"momento, ó. Já criou. Beleza, ó. E se a"
"gente entrar aqui, ó, clicar neste link,"
"ó, baixa aqui, clica aqui, ó, 8088, que"
é a porta onde nós estamos rodando. Ele
"abre pra gente é o manager, tá? A página"
inicial aqui do Evolution. Como é que a
"gente acessa o manager, ó? Tá aqui, ó,"
"808 Manager, tá? Que que ele vai falar?"
"Olha, para acessar o painel de"
"Evolution, você precisa passar a API"
Global. A API Global que a gente
"utilizou, ele está nesse ENV, inclusive"
"você pode alterar depois, tá? Se você"
quiser alterar qualquer informação nesse
"ENV e subir de novo a imagem, é só"
"alterar, vir lá de novo no é no"
terminalzinho e executar de novo o
"Docker Compose Up, que ele atualiza para"
"você a imagem. Beleza? Então, passou IPI"
"Global, ó, ele já vai fazer pra gente"
"login. Pera aí, deixa eu ver porque que"
"ele Ah, ele vou dar um play aqui, ó. Eu"
"fechei o terminal, ele parou. Só dar o"
"play aqui de novo, ó. Já tá de novo."
"Beleza,"
"ó. Vamos lá. Lá já acessou, já tô com o"
"número conectado, tá? Você pode criar"
"uma estância, é que a gente pode chamar"
"ela aqui de evolution,"
"né? Entrou aqui no Evolution, né? Traz o"
"QR code, conecta, né? O primeiro passo."
"Legal. Eh, então, Instala Evolution,"
"baixa o arquivo da página de apoio,"
descompacta ele na sua área de trabalho
"com o Docker Desktop executando, vai"
"dentro dessa pasta, botão direito, abre"
"o terminal Docker Compose up, tá? Você"
vai ver eh os contêiners sendo criados e
eles sendo executados. Para acessar
"Evolution API, só vir aqui, ó, abre os"
"três contêiners, né, que estão aqui, que"
"é os bancos de dados, o gerenciador de"
"tarefas e evolutioni. E abre aqui, ó,"
local roxo 8
"8088, que é onde tá rodando o nosso"
"evolution. Para acessar aqui, passa"
"aqui, né, a sua aqui que a gente definiu"
"lá no Envi, ó. Beleza? Legal. Primeiro"
"passo bem tranquilo, né? Eh, para você"
que me acompanha aqui há algum tempo já
eh eh já tá acostumado a fazer esse
processo. E agora a gente vai para pro
"cursor, tá? Então, o cursor ele é bem"
legal. O que que nós vamos fazer agora?
"Ó, deixa eu parar essa solução."
"Basicamente, a gente vai executar o"
"cursor, né? Então, instala o cursor. O"
"cursor é esse cara aqui, ó. Você vai"
"acessar ele, vai fazer o login da sua"
"conta. Nós vamos abrir aqui, ó, Open"
Folder. Nós vamos criar esse essa essa
"aplicação dentro do Evoaula mesmo aqui,"
ó. Beleza? Dessa pastinha que tá aqui
que a gente já tá usando o Docker.
"Beleza? Ó. Eh, vamos criar aqui dentro,"
"ó. Melhor, vamos criar dentro da Vamos"
"criar aqui uma pasta aqui dentro, ó."
Vamos criar uma pasta chamada eh
composer. Comp cursor. Cursor. Beleza.
"Cursor. Então, vai ser aqui dentro dessa"
"pasta. Então, vem, abre o folder, ó. Eh,"
vamos lá na EVO aula e vem aqui no
"cursor, ó. Cursor. Beleza. Inclusive,"
depois que a gente construir a
"aplicação, eu vou deixar ela para"
"download também. Vamos aproveitar, eu"
subo esse arquivo inteiro para vocês.
Que que é o prompt que a gente vai falar
"para ele, ó? É a Evolution, pessoal. Ela"
"tem uma documentação. Então, se você"
"entrar aqui na Evolution, ó, eh, vier"
"aqui nas Docs, ele vai trazer tudo que a"
gente precisa. A gente tá usando a
"Evolution versão 2. Eh, que que a gente"
"vai precisar? Eh, das configurações de"
"API que estão aqui, ó. Legal."
E eu tava numa reunião com com o
"Davidson, né, o desenvolvedor de"
"Evolution e com os meninos do Avoip, o"
"Léo, né, e o Artur. E aí eles começaram"
"a falar: ""Nossa, você já conhece o"
"cursor, né, que é uma tecnologia"
"incrível"". Eu falei: ""Ainda não, cara,"
"vou experimentar"". Isso aí foi há dois"
dias. Aí eu baixei o cursor para testar
e falei e percebi como é incrível essa
"tecnologia, viu, gente? É, então, pra"
"gente construir a nossa solução, a gente"
vai utilizar aqui a documentação de
"Evolution. Primeiro, um para eh buscar"
"instâncias, que seria essa rota que a"
"gente precisa, e o outro para enviar"
"mensagens, que seria aqui, ó, send, tá"
"bom? Então, nós vamos fazer com essas"
"duas rotas testes, essas duas rotas"
"modelo, para que depois você, né, aí a"
ao longo da da tua da tua da tua
"atividade explorativa dentro do cursor,"
"né, consiga construir novas rotas, tá?"
Então aqui é só para dar o pontapé
inicial para você entender como tudo vai
"funcionar. Legal. Eh, que que a gente"
"vai falar por cursor? Ó, o cursor assim"
"que a gente abre ele, ó, ele a gente"
pode executar o Nem vamos abrir
"terminal, vamos deixar ele fazer tudo."
"Ele abre aqui pra gente um chat, ó. Esse"
chat ele é conectado à inteligência
artificial e nós vamos adicionar algumas
informações para ele construir a solução
"pra gente. Então, qual que é o prompt"
que eu vou falar para ele? Eu falar:
"""Olá, tudo bem? Pra inteligência"
"artificial é demais, né?"" ""Olá, tudo"
"bem?"" Que que eu vou falar para ele?"
Crie um painel de controle de mensagens
"de WhatsApp, que é esse cara que a gente"
"quer construir, ó. Legal. Esse cara"
"aqui, exatamente, que a gente quer"
construir. Nós podemos até fechar tudo
"aqui, ó. Deixar só"
"8088, que é a que a gente vai usar"
"agora. Legal. E aqui a documentação. É,"
crie para mim um painel de disparo de
"mensagens, tá? que realiza o envio eh em"
node. Tô dizendo para ele qual que é a
"linguagem que eu quero, porque ele, né,"
ele pode construir em qualquer linguagem
ali. Realiza o envio de mensagens para
"WhatsApp usando Evolution API, tá? Eh,"
que que eu vou passar para ele? Rota de
exemplo de envio de envio de mensagens.
"Então, seria essa esse cara aqui, ó, que"
basicamente essa rota que eu peguei para
"ele usar como exemplo, ela está na"
"documentação. Então eu venho aqui, ó, no"
modelo JavaScript e copio basicamente a
"rota que ele precisa utilizar, né? é o"
exemplo de rota que ele vai utilizar
"para enviar. Beleza? Então, esse cara"
"aqui, ó, é a minha evolution, vou dizer"
"pr pra inteligência artificial, ó, para"
"pro cursor, ela tá rodando em local"
"roche 8088, né, que é aqui onde ele está"
"rodando. E ela usa aqui a IPI aqui, que"
é esse número que a gente viu lá no
"nosso INV, tá? Que é basicamente esse"
"cara aqui, ó. Beleza? Legal."
"Eh, que mais a gente vai dizer para ele,"
"ó, no front, o front, que seria ali a"
interface onde a gente vai enviar a
"mensagem, no"
front de um e de um local para
selecionar a instância que pode ser
"buscada pela rota tal. Então, o que que"
"eu digo para ele, ó? E a Evolution API,"
ele permite com que eu busque todas as
"instâncias conectadas, né, através dessa"
rota. Legal. Beleza. Que que eu pego?
Vou pegar esse prom e vou mandar pro
cursor. Vamos ver que que ele vai fazer
"pra gente, ó."
"lá. Olá, tudo bem? Sim, ó. Vou ajudar"
você a criar um painel de mensagem do
WhatsApp usando node evolui. A primeira
"coisa, ó, vou montar a estrutura básica"
"do projeto, que é o package de Jzon."
"Então, ele já criou o pack de JZON pra"
"gente. Ó, o que que você pode fazer, ó?"
"Já ele tá ele tá construindo pra gente,"
"digitando toda a solução, né? Então, o"
"que que você vai fazer? Olha, ele criou"
"o pack de Jon, tá? Você dá um aprovar"
"aqui, ó, para aceitar o arquivo ou"
"aceitar esse arquivo que ele criou, ó."
"Beleza? Ele já passa pro próximo, ó."
"Agora eu vou criar o server JS, né, que"
"esse cara que ele criou aqui, ó, né? Ele"
"já ele já codou para mim tudo aqui, ó."
"Posso dar um accept file, beleza? Aí ele"
"falou, tá falando aqui, ó, agora eu vou"
"criar um HTML simples pro front end, né?"
"Então, se eu vi na pasta public, ele já"
"criou o index aqui, ó, para mim. Olha só"
"que legal, né? Posso aceitar o arquivo."
Você viu que ele tá programando tudo
"para mim, só com prompt que eu passei"
"para ele. É incrível isso aqui, cara."
"Incrível. Incrível. Eh, lá. Agora eu vou"
atualizar os arquivos eh para utilizar
"as informações estáticas. Então, ele tá"
"se criando tudo para mim. O Readm, né,"
que é aqui a é como se fosse a
"documentação para utilizar a solução,"
né? Aceitei o arquivo. O que que ele vai
"falar agora, ó? Beleza, ó. Já criei o"
painel completo de envio de mensagens.
"Agora, que que você vai fazer? Instalar"
"as dependências, né? Não vou ler tudo"
"que é longo, mas você vai ler com calma"
aí a medida que você for usando. NPM
"install, ó, dá o run. Que que ele vai"
"fazer, ó? Ele vai instalar para você a"
solução. Você não precisa nem saber que
que precisa usar o npm install. Olha que
legal. Beleza? Instalou. Que que você
"vai fazer, ó? Inicia o servidor com npm"
"start, ó."
Ó lá. Legal. Ó. Que que ele já acessa
agora? Local host 3.000 no seu
navegador. Olha que interessante. Vamos
"lá, ó. Que que ele já fez pra gente, ó?"
Ó lá. Seleciona uma instância. Olha aí.
Olha que legal. O que que a gente vai
"falar para ele agora, ó? Ele já criou"
"pra gente aqui, né? Tem duas instâncias,"
só que elas não aparecem. É que que nós
vamos falar? Olha só. Vamos lá no
"cursor. Ah, beleza. Ó, o front end foi"
"criado com sucesso, mas o"
"dropdist, que é aquela"
listinha para
selecionar a
instância está
eh está
mostrando está
"vazio. Eh, corrija. Vamos falar para"
"ele, ó. Ele tá mostrando, né? Ele tá,"
"ele listou as duas instâncias, né?"
"Dizendo que tem duas instâncias, só que"
"ele não tá trazendo o label, né? Que é"
"ali o eh o rótulo aqui da instância, né?"
"Então a gente precisa que ele selecione,"
a gente saiba qual que é. Então vamos
ver o que que ele vai fazer pra gente.
Vamos fechar esse visual
"aqui. Vamos ver o que que ele vai fazer,"
"ó. Ah, lá pode estar relacionado à forma"
que estamos tratando a resposta da PI.
"Vou modificar o código para garantir,"
"né, esse ajuste no front end. Olha só."
"Aí ele traz aqui, ó, index HTML, ó, já"
com um log pra gente. E ele também
"corrige o server JS, ó. Beleza? Ó, ele"
"tá dizendo para mim que que eu fiz, ó."
No front end eu adicionei logs para
debugar a resposta da P e no back end eu
adicionei a verificação de respostas.
"Beleza? Ó, que que nós vamos"
"fazer? Qu, eu fui para"
"lá, beleza, ó, deu um F5, ó, ele já"
atualizou. Olha que legal. Já tá
"trazendo aqui para mim, ó. Posso usar"
cursor ou evolution. Beleza? Beleza. Aí
"que vamos fazer aqui agora, ó. Eh, eu"
"vou falar para ele colocar um blur aqui,"
ó. Pera aí.
Coloque um filter blur para desfocar o
input eh
de eh onde eu
informo o
número. Vamos ver o que ele vai fazer
pra gente
"poder tirar ali, ó. Beleza? Ó,"
aceitei. Ele tá explicando o que ele
"fez, né? Aí, colocou um CSA lá. Beleza."
"Ó, olha que legal. Quem que tá conectado"
aqui? Vamos ver. Nós estamos conectado
aqui é com
"É, ah, a Cursor está"
"conectada. É teste. Agora vamos ver, ó."
Vamos enviar mensagem enviada com
sucesso. Olha que
"legal. Legal, né? Muito legal. Ele já"
"enviou aqui, ó. Ó lá. teste. Tá aí. Vai,"
"ó lá, ele não, ó, olha só que"
"interessante, ele não enviou. Que que eu"
"vou falar para ele, ó? Ele trouxe"
"mensagem teste, só que não foi enviada."
"Vamos dizer assim, ó. Eh, peguei o"
"retorno de mensagem enviada, mas a"
mensagem não foi
entregue a o
destino. Revise. Olha que
legal. Vamos esperar aqui que ele vai
"fazer, ó. Vou analisar o problema."
Primeiro vou verificar a rota e
"adicionar logs, né, para identificar o"
"problema. Olha só, tá? Já criou logs"
aqui pra gente eh no nosso backend. Isso
aqui a gente pode fechar. Fechar. Agora
eu vou jitar o frontend para mostrar
"mais detalhes, ó."
"Beleza, beleza, beleza. Ó,"
[Música]
"ô, não quero. É o star,"
"é npm start que a gente tá usando, né?"
"É, beleza. É lá. Agora vamos ver que que"
ele vai falar.
"Ó, na verdade eu acho que pode. Ó lá,"
erro. Enviaram uma mensagem. Bad request
tá dizendo para mim. Olha que
legal. Olha só. Aí que a gente vai fazer
"para vai falar aqui, ó. Agora a gente tá"
"pegando o erro, a resposta da Evolution"
"API, ó. Olha"
"só. A gente pega, marca o erro,"
"ó, e adiciona o chat."
"envia para ele,"
ó. Veja o erro de
"envio. É, ficou bem bem fácil"
"de de programar. Muito fácil, né? Quem"
"dera algum tempo atrás, quando eu"
"comecei 5 anos atrás, não tinha isso"
"aqui. A gente ralava para caramba, né?"
Tá lá. Agora o formato requisição tá
correto. Vamos ver que ele vai falar pra
"gente,"
"ó. Eh, cadê? Aqui, ó."
Deixa eu dar o npm start de
novo. Vamos lá.
"Ó lá. Erro enviar bad request. Olha só,"
ele não tá entendendo. Tá tentando
enviar tal. Olha só. Vamos mandar para
"ele aqui,"
"ó. E aqui, ó, né? Vamos pegar o erro."
"Vamos adicionar isso aqui ao chat, ó."
"Marca aqui, ó. Edit to"
chat. Continuo com o erro de envio. A
rota exemplo para
enviar mensagens é essa.
Sprin test. Se a gente vem aqui em
JavaScript que a gente tá
"utilizando,"
"ajuste, vamos ver o que ele vai dizer"
para nós. Agora entendi corretamente o
formato tava diferente de exemplo
"fornecido. Vou usar o aqui, ó, modelo"
correto. Vamos
lá. Vamos ver se o cursorzinho agora vai
entender o que a gente precisa. Tente
"enviar mensagem novamente, tá? Eh, aqui"
eu só preciso entender se ele se eu
"preciso dar um start de novo ou não, mas"
"não dar,"
"né? Beleza. Então, ó lá, agora"
"saiu. Agora saiu,"
"ó. Teste. Oh, desfocou. Teste. Ó lá. Vou"
"colocar aqui, ó. Teste"
"dois. Beleza. Ó, teste dois. Segunda"
"mensagem, ó. Vamos lá, vou mandar"
"mensagem, ó. New"
"new. Chegou, tá vendo? Beleza, ó. Legal,"
"galera. Então, é isso, né? Eh, a partir"
de agora você tem uma super ferramenta.
Você percebeu aqui que o que você vai
precisar saber é passar um prompt pro
"cursor, eh, pegar o erro, passar no chat"
e informar o que que você quer que ele
"faça, né? E a partir disso, você pode,"
com todas as outras rotas disponíveis da
"Evolution, construir novas eh novas"
interfaces de disparo. Você pode criar
um painel para conectar múltiplas
instâncias. Aí vai dar tua imaginação
"aí, né, para poder brincar com essa"
ferramenta. Beleza? E se você quiser
aprofundar os teus conhecimentos em APIs
"de WhatsApp, sistema de automação para"
"WhatsApp, vem conhecer a comunidade ZDG."
é a maior comunidade do mundo aí com
quase 7.000 alunos na sua turma básica e
"avançada, eh, e mais de 5 anos de"
estrada. Aí a gente transformando
centenas de milhares de vidas todos os
"meses aqui na internet. Um forte abraço,"
"beijo no coração de todos, fiquem com"
Deus. Qualquer dúvida só chamar. Estamos
"junto, Pedro Danas. Até a próxima. M."