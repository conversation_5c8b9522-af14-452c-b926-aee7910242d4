# 🚀 DOCUMENTAÇÃO TÉCNICA CORE - WEBAGENT SOCIAL EXTRACTION

**Data:** 2025-01-24  
**Versão:** v1.0 - Consolidação Master  
**Autor:** Augment Code Orchestrator V5.0  
**Status:** ✅ **ENTERPRISE-<PERSON><PERSON><PERSON> COMPLETO**  

---

## 🎯 VISÃO GERAL DO SISTEMA

O **WebAgent Social Extraction** é um sistema enterprise-grade para extração, análise e processamento de conteúdo viral das principais plataformas sociais (YouTube, Instagram, Twitter/X) utilizando arquitetura multimodal com IA avançada.

### 🏗️ ARQUITETURA PRINCIPAL

```
WebAgent Social Extraction System
├── Frontend Layer (Web Interface + REST API + WebSocket)
├── MCP Orchestration Layer (Router + Registry + Monitor)
├── Specialized MCP Servers (Media + AI + Data + Analytics)
├── Core Processing Engines (FFmpeg + OpenCV + Remotion + LangGraph)
├── Data Extraction Layer (Twikit + yt-dlp + Instaloader)
└── Infrastructure Layer (Supabase + Docker + Redis + S3)
```

### 📊 CAPACIDADES TÉCNICAS

- **Extração Multimodal**: Texto, imagem, vídeo, áudio de 3 plataformas
- **Processamento IA**: LangGraph, CrewAI, AutoGen, Gemini SDK
- **Infraestrutura**: Supabase PostgreSQL + Edge Functions + Storage
- **Escalabilidade**: Docker Compose + Redis Cache + Load Balancing
- **Protocolo**: MCP (Model Context Protocol) para interoperabilidade

---

## 📱 SISTEMA DE EXTRAÇÃO VIRAL

### 1. PLATAFORMAS SUPORTADAS

#### 1.1 YOUTUBE - EXTRAÇÃO COMPLETA
**Tecnologia Principal:** `yt-dlp` (85k+ stars, padrão-ouro)

```python
# Extração completa YouTube
class YouTubeExtractor:
    def __init__(self):
        self.yt_dlp = YoutubeDL(self.get_config())
        self.transcript_api = YouTubeTranscriptApi()
        self.data_api = YouTubeDataAPI(api_key)
    
    def extract_viral_content(self, query: str, max_results: int = 50):
        """Extração completa: vídeos + transcrições + comentários + métricas"""
        videos = self.data_api.search(q=query, max_results=max_results)
        
        for video in videos:
            # Metadados completos
            info = self.yt_dlp.extract_info(video['video_url'], download=False)
            
            # Transcrições automáticas
            transcript = self.transcript_api.get_transcript(video['video_id'])
            
            # Análise viral
            viral_score = self.calculate_viral_score(video, transcript)
            
            yield {
                'video_id': video['video_id'],
                'metadata': info,
                'transcript': transcript,
                'viral_score': viral_score,
                'extracted_at': datetime.utcnow()
            }
```

**Funcionalidades:**
- ✅ Download de vídeos/áudio em múltiplos formatos
- ✅ Extração de metadados completos
- ✅ Transcrições automáticas (youtube-transcript-api)
- ✅ Comentários estruturados (youtube-comment-downloader)
- ✅ Suporte a playlists e canais
- ✅ Rate limiting inteligente

#### 1.2 INSTAGRAM - ANÁLISE VISUAL
**Tecnologia Principal:** `Instaloader` (8.5k+ stars) + `Instagrapi` (4k+ stars)

```python
# Extração Instagram multimodal
class InstagramExtractor:
    def __init__(self):
        self.loader = instaloader.Instaloader()
        self.grapi = Client()  # Instagrapi para API privada
    
    def extract_viral_posts(self, hashtag: str, max_posts: int = 100):
        """Extração: posts + stories + reels + comentários + métricas"""
        hashtag_obj = instaloader.Hashtag.from_name(self.loader.context, hashtag)
        
        for post in hashtag_obj.get_posts():
            if self.is_viral_candidate(post):
                # Dados básicos
                post_data = {
                    'shortcode': post.shortcode,
                    'likes': post.likes,
                    'comments_count': post.comments,
                    'caption': post.caption,
                    'hashtags': post.caption_hashtags,
                    'media_type': 'carousel' if post.typename == 'GraphSidecar' else 'single'
                }
                
                # Comentários detalhados via Instagrapi
                comments = self.grapi.media_comments(post.mediaid)
                post_data['comments_analysis'] = self.analyze_comments(comments)
                
                # Análise visual (se imagem/vídeo)
                if post.is_video:
                    post_data['video_analysis'] = self.analyze_video_content(post)
                
                yield post_data
```

**Funcionalidades:**
- ✅ Posts, Stories, Highlights, Reels, IGTV
- ✅ Comentários e metadados completos
- ✅ Download de mídia (imagens/vídeos)
- ✅ Geotags e análise de localização
- ✅ Sistema de retry inteligente
- ✅ Suporte a API privada (Instagrapi)

#### 1.3 TWITTER/X - DADOS EM TEMPO REAL
**Tecnologia Principal:** `Twikit` (Trust Score: 7.9/10) - Sem API key

```python
# Extração Twitter/X sem API
class TwitterExtractor:
    def __init__(self):
        self.client = Client('en-US')
        self.authenticated = False
    
    async def extract_viral_tweets(self, keyword: str, max_results: int = 100):
        """Extração: tweets + trending + sentimento + métricas"""
        if not self.authenticated:
            await self.client.login(auth_info_1='username', password='password')
        
        tweets = await self.client.search_tweet(keyword, product='Latest')
        
        for tweet in tweets:
            if self.is_viral_candidate(tweet):
                # Dados estruturados
                tweet_data = {
                    'id': tweet.id,
                    'text': tweet.text,
                    'retweet_count': tweet.retweet_count,
                    'like_count': tweet.favorite_count,
                    'reply_count': tweet.reply_count,
                    'hashtags': self.extract_hashtags(tweet.text),
                    'mentions': self.extract_mentions(tweet.text),
                    'media': [media.media_url_https for media in tweet.media] if tweet.media else []
                }
                
                # Análise de sentimento
                tweet_data['sentiment'] = self.analyze_sentiment(tweet.text)
                
                # Thread completo (se aplicável)
                if tweet.in_reply_to_status_id:
                    tweet_data['thread'] = await self.get_thread(tweet)
                
                yield tweet_data
```

**Funcionalidades:**
- ✅ Busca por palavra-chave sem API key
- ✅ Trending topics em tempo real
- ✅ Threads e conversações completas
- ✅ Upload e download de mídia
- ✅ DMs e interações
- ✅ Rate limiting automático

### 2. ANÁLISE VIRAL UNIFICADA

```python
# Sistema unificado de análise viral
class ViralAnalysisEngine:
    def __init__(self):
        self.youtube_extractor = YouTubeExtractor()
        self.instagram_extractor = InstagramExtractor()
        self.twitter_extractor = TwitterExtractor()
        self.ai_analyzer = MultiModalAIAnalyzer()
    
    async def analyze_cross_platform(self, topic: str):
        """Análise viral cross-platform com IA"""
        results = {
            'topic': topic,
            'analysis_timestamp': datetime.utcnow(),
            'platforms': {}
        }
        
        # Extração paralela
        youtube_task = self.youtube_extractor.extract_viral_content(topic)
        instagram_task = self.instagram_extractor.extract_viral_posts(topic.replace(' ', ''))
        twitter_task = self.twitter_extractor.extract_viral_tweets(topic)
        
        # Processamento com IA
        youtube_data, instagram_data, twitter_data = await asyncio.gather(
            youtube_task, instagram_task, twitter_task
        )
        
        # Análise cruzada com Gemini SDK
        cross_analysis = await self.ai_analyzer.analyze_viral_patterns({
            'youtube': youtube_data,
            'instagram': instagram_data,
            'twitter': twitter_data
        })
        
        results['platforms'] = {
            'youtube': {'data': youtube_data, 'viral_score': cross_analysis['youtube_score']},
            'instagram': {'data': instagram_data, 'viral_score': cross_analysis['instagram_score']},
            'twitter': {'data': twitter_data, 'viral_score': cross_analysis['twitter_score']}
        }
        
        results['cross_platform_insights'] = cross_analysis['insights']
        results['trending_elements'] = cross_analysis['trending_elements']
        results['viral_prediction'] = cross_analysis['viral_prediction']
        
        return results
```

---

## 🤖 ARQUITETURA WEBAGENT MULTIMODAL

### 1. CORE WEBAGENT SYSTEM

**Base:** Playwright + LangGraph + Gemini 2.5 Flash

```python
# WebAgent com capacidades multimodais
class WebAgentMultimodal:
    def __init__(self):
        self.playwright_browser = None
        self.langgraph_workflow = ViralContentWorkflow()
        self.gemini_client = GeminiClient()
        self.tools = self.initialize_tools()
    
    def initialize_tools(self):
        """15 ferramentas especializadas"""
        return {
            'goto': GotoTool(),
            'click': ClickTool(),
            'type': TypeTool(),
            'scroll': ScrollTool(),
            'scrape': ScrapeTool(),
            'download': DownloadTool(),
            'tab': TabTool(),
            'wait': WaitTool(),
            'key': KeyTool(),
            'screenshot': ScreenshotTool(),
            'extract_text': ExtractTextTool(),
            'extract_links': ExtractLinksTool(),
            'extract_images': ExtractImagesTool(),
            'extract_videos': ExtractVideosTool(),
            'analyze_page': AnalyzePageTool()
        }
    
    async def navigate_and_extract(self, platform: str, target: str):
        """Navegação inteligente com análise multimodal"""
        # Configuração específica por plataforma
        config = self.get_platform_config(platform)
        
        # Navegação com anti-detecção
        await self.goto(config['base_url'])
        await self.setup_anti_detection(config)
        
        # Busca inteligente
        search_result = await self.intelligent_search(target)
        
        # Extração multimodal
        content = await self.extract_multimodal_content(search_result)
        
        # Análise com Gemini
        analysis = await self.gemini_client.analyze_content(content)
        
        return {
            'extracted_content': content,
            'ai_analysis': analysis,
            'viral_indicators': self.identify_viral_indicators(content, analysis)
        }
```

### 2. ANTI-DETECÇÃO AVANÇADA

```python
# Sistema anti-detecção para WebAgent
class AntiDetectionSystem:
    def __init__(self):
        self.user_agents = self.load_user_agents()
        self.proxy_pool = self.load_proxy_pool()
    
    async def setup_stealth_browser(self):
        """Configuração stealth completa"""
        return await playwright.chromium.launch(
            headless=False,
            args=[
                '--disable-blink-features=AutomationControlled',
                '--disable-dev-shm-usage',
                '--disable-extensions-except=/path/to/extension',
                '--load-extension=/path/to/extension',
                '--user-data-dir=/path/to/profile',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor'
            ]
        )
    
    async def randomize_behavior(self, page):
        """Comportamento humano simulado"""
        # Mouse movements aleatórios
        await self.random_mouse_movements(page)
        
        # Delays humanos
        await self.human_like_delays()
        
        # Scroll patterns naturais
        await self.natural_scrolling(page)
        
        # Interações casuais
        await self.casual_interactions(page)
```

---

## 🏗️ INFRAESTRUTURA SUPABASE ENTERPRISE

### 1. SCHEMA POSTGRESQL OTIMIZADO

```sql
-- Schema principal para sistema viral
CREATE SCHEMA IF NOT EXISTS viral_extraction;

-- Tabela principal de usuários
CREATE TABLE viral_extraction.users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    subscription_tier user_subscription_tier DEFAULT 'free',
    api_quota_daily INTEGER DEFAULT 1000,
    api_quota_used INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de projetos de extração
CREATE TABLE viral_extraction.extraction_projects (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES viral_extraction.users(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    platforms platform_type[] DEFAULT '{}',
    keywords TEXT[] DEFAULT '{}',
    status extraction_status DEFAULT 'pending',
    config JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela principal de conteúdo viral
CREATE TABLE viral_extraction.viral_content (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID REFERENCES viral_extraction.extraction_projects(id) ON DELETE CASCADE,
    platform platform_type NOT NULL,
    content_type content_type NOT NULL,
    external_id VARCHAR(255) NOT NULL,
    title TEXT,
    description TEXT,
    author_username VARCHAR(255),
    author_display_name VARCHAR(255),
    content_url TEXT NOT NULL,
    media_urls TEXT[] DEFAULT '{}',
    hashtags TEXT[] DEFAULT '{}',
    mentions TEXT[] DEFAULT '{}',
    metrics JSONB DEFAULT '{}',
    viral_score DECIMAL(5,2) DEFAULT 0.00,
    sentiment_score DECIMAL(3,2),
    extracted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices estratégicos para performance
CREATE INDEX idx_viral_content_platform ON viral_extraction.viral_content(platform);
CREATE INDEX idx_viral_content_viral_score ON viral_extraction.viral_content(viral_score DESC);
CREATE INDEX idx_viral_content_extracted_at ON viral_extraction.viral_content(extracted_at DESC);
CREATE INDEX idx_viral_content_hashtags ON viral_extraction.viral_content USING GIN(hashtags);
CREATE INDEX idx_viral_content_metrics ON viral_extraction.viral_content USING GIN(metrics);
```

### 2. ROW LEVEL SECURITY (RLS)

```sql
-- Habilitar RLS em todas as tabelas
ALTER TABLE viral_extraction.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE viral_extraction.extraction_projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE viral_extraction.viral_content ENABLE ROW LEVEL SECURITY;

-- Políticas de segurança granulares
CREATE POLICY "Users can view own data" ON viral_extraction.users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can view own projects" ON viral_extraction.extraction_projects
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view own content" ON viral_extraction.viral_content
    FOR ALL USING (
        auth.uid() IN (
            SELECT user_id FROM viral_extraction.extraction_projects 
            WHERE id = viral_content.project_id
        )
    );
```

### 3. EDGE FUNCTIONS ESPECIALIZADAS

```typescript
// viral-content-processor.ts - Edge Function
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

interface ViralContentRequest {
  content: any
  platform: string
  analysis_type: 'sentiment' | 'viral_score' | 'trend_analysis'
}

serve(async (req) => {
  try {
    const { content, platform, analysis_type }: ViralContentRequest = await req.json()
    
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? ''
    )
    
    let result = {}
    
    switch (analysis_type) {
      case 'sentiment':
        result = await analyzeSentiment(content)
        break
      case 'viral_score':
        result = await calculateViralScore(content, platform)
        break
      case 'trend_analysis':
        result = await analyzeTrends(content)
        break
    }
    
    // Salvar resultado no banco
    await supabase
      .from('viral_analysis_results')
      .insert({
        content_id: content.id,
        analysis_type,
        result,
        processed_at: new Date().toISOString()
      })
    
    return new Response(JSON.stringify(result), {
      headers: { 'Content-Type': 'application/json' }
    })
    
  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    })
  }
})

async function calculateViralScore(content: any, platform: string): Promise<number> {
  // Algoritmo de viral score específico por plataforma
  const metrics = content.metrics
  let score = 0
  
  switch (platform) {
    case 'youtube':
      score = (metrics.likes * 0.3) + (metrics.comments * 0.4) + (metrics.shares * 0.3)
      break
    case 'instagram':
      score = (metrics.likes * 0.25) + (metrics.comments * 0.35) + (metrics.saves * 0.4)
      break
    case 'twitter':
      score = (metrics.retweets * 0.4) + (metrics.likes * 0.3) + (metrics.replies * 0.3)
      break
  }
  
  return Math.min(score / 1000, 100) // Normalizar para 0-100
}
```

### 4. DOCKER COMPOSE ENTERPRISE

```yaml
# docker-compose.yml - Stack completo
version: '3.8'

services:
  # Supabase Core
  supabase-db:
    image: supabase/postgres:15.1.0.147
    environment:
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: postgres
    volumes:
      - supabase-db:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Supabase Studio
  supabase-studio:
    image: supabase/studio:20240101-ce42139
    environment:
      SUPABASE_URL: http://supabase-kong:8000
      SUPABASE_ANON_KEY: ${SUPABASE_ANON_KEY}
    ports:
      - "3000:3000"
    depends_on:
      - supabase-kong

  # WebAgent Application
  webagent-app:
    build:
      context: ./webagent
      dockerfile: Dockerfile
    environment:
      SUPABASE_URL: ${SUPABASE_URL}
      SUPABASE_ANON_KEY: ${SUPABASE_ANON_KEY}
      GEMINI_API_KEY: ${GEMINI_API_KEY}
    volumes:
      - ./webagent:/app
      - webagent-data:/app/data
    ports:
      - "8080:8080"
    depends_on:
      - supabase-db
      - redis-cache

  # Redis Cache
  redis-cache:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    command: redis-server --appendonly yes

  # Nginx Load Balancer
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - webagent-app

volumes:
  supabase-db:
  redis-data:
  webagent-data:

networks:
  default:
    driver: bridge
```

### 5. MONITORAMENTO E ALERTAS

```sql
-- Triggers automáticos para monitoramento
CREATE OR REPLACE FUNCTION update_viral_score()
RETURNS TRIGGER AS $$
BEGIN
    -- Calcular viral score automaticamente
    NEW.viral_score = (
        COALESCE((NEW.metrics->>'likes')::numeric, 0) * 0.3 +
        COALESCE((NEW.metrics->>'comments')::numeric, 0) * 0.4 +
        COALESCE((NEW.metrics->>'shares')::numeric, 0) * 0.3
    ) / 1000;

    -- Alertar se conteúdo viral emergente
    IF NEW.viral_score > 80 THEN
        INSERT INTO viral_extraction.alerts (
            type, message, content_id, created_at
        ) VALUES (
            'viral_content_detected',
            'Conteúdo viral emergente detectado: ' || NEW.title,
            NEW.id,
            NOW()
        );
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_viral_score
    BEFORE INSERT OR UPDATE ON viral_extraction.viral_content
    FOR EACH ROW EXECUTE FUNCTION update_viral_score();
```

---

## 🔧 TECNOLOGIAS INTEGRADAS

### EXTRAÇÃO DE DADOS
- **yt-dlp**: YouTube download + metadados (85k+ stars)
- **Instaloader**: Instagram completo (8.5k+ stars)
- **Twikit**: Twitter sem API (Trust Score: 7.9/10)

### PROCESSAMENTO IA
- **LangGraph**: Workflows de IA (9.5/10 confiabilidade)
- **CrewAI**: Multi-agente especializado (9.2/10)
- **Gemini SDK**: Análise multimodal (9.0/10)

### INFRAESTRUTURA
- **Supabase**: PostgreSQL + Auth + Storage + Edge Functions
- **Docker**: Containerização enterprise
- **Redis**: Cache de alta performance
- **MCP**: Model Context Protocol para interoperabilidade

---

**Status:** ✅ **DOCUMENTAÇÃO CORE CONSOLIDADA**
**Próxima Fase:** Estruturação de Tecnologias e Ferramentas
