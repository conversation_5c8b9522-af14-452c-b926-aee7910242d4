version: '3.8'

services:
  # === DATABASE ===
  postgres:
    image: postgres:16-alpine
    container_name: n8n-postgres-basic
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-n8n}
      POSTGRES_USER: ${POSTGRES_USER:-n8n_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-n8n_password_123}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-n8n_user} -d ${POSTGRES_DB:-n8n}"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - n8n-network

  # === CACHE/QUEUE ===
  redis:
    image: redis:7-alpine
    container_name: n8n-redis-basic
    command: redis-server --appendonly yes --maxmemory ${REDIS_MAXMEMORY:-256mb} --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    ports:
      - "${REDIS_PORT:-6379}:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - n8n-network

  # === N8N APPLICATION ===
  n8n:
    image: docker.n8n.io/n8nio/n8n:latest
    container_name: n8n-app-basic
    ports:
      - "${N8N_PORT:-5678}:5678"
    environment:
      # Database Configuration
      DB_TYPE: postgresdb
      DB_POSTGRESDB_HOST: postgres
      DB_POSTGRESDB_PORT: 5432
      DB_POSTGRESDB_DATABASE: ${POSTGRES_DB:-n8n}
      DB_POSTGRESDB_USER: ${POSTGRES_USER:-n8n_user}
      DB_POSTGRESDB_PASSWORD: ${POSTGRES_PASSWORD:-n8n_password_123}
      
      # Redis Configuration
      QUEUE_BULL_REDIS_HOST: redis
      QUEUE_BULL_REDIS_PORT: 6379
      QUEUE_BULL_REDIS_DB: 0
      
      # Authentication
      N8N_BASIC_AUTH_ACTIVE: ${N8N_BASIC_AUTH_ACTIVE:-true}
      N8N_BASIC_AUTH_USER: ${N8N_BASIC_AUTH_USER:-admin}
      N8N_BASIC_AUTH_PASSWORD: ${N8N_BASIC_AUTH_PASSWORD:-admin123}
      
      # General Configuration
      WEBHOOK_URL: ${WEBHOOK_URL:-http://localhost:5678}
      GENERIC_TIMEZONE: ${TIMEZONE:-America/Sao_Paulo}
      N8N_LOG_LEVEL: ${N8N_LOG_LEVEL:-info}
      N8N_LOG_OUTPUT: console
      
      # Security
      N8N_SECURE_COOKIE: ${N8N_SECURE_COOKIE:-false}
      
      # Execution
      EXECUTIONS_TIMEOUT: ${EXECUTIONS_TIMEOUT:-3600}
      EXECUTIONS_TIMEOUT_MAX: ${EXECUTIONS_TIMEOUT_MAX:-7200}
      
      # File Storage
      N8N_DEFAULT_BINARY_DATA_MODE: filesystem
      
    volumes:
      - n8n_data:/home/<USER>/.n8n
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - n8n-network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  n8n_data:
    driver: local

networks:
  n8n-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
